"""
Test script for UI and button functionality.

This script tests the UI and button functionality, including:
1. Keyboard manager
2. Button state manager
3. Callback query handling
4. Persistent menu button
"""

import unittest
import os
import sys
import logging
import asyncio
from unittest.mock import MagicMock, patch, AsyncMock
from datetime import datetime

# Add parent directory to path to import bot modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from telegram import Update, InlineKeyboardMarkup, InlineKeyboardButton, User, Message, Chat, CallbackQuery
from telegram.ext import ContextTypes

from bot.database import Database
from bot.core.menu_manager import MenuManager  # Using new menu system instead of legacy keyboard manager
from bot.features.button_state_manager import ButtonStateManager

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class TestUIButtonFunctionality(unittest.TestCase):
    """Test case for UI and button functionality."""

    def setUp(self):
        """Set up test environment."""
        # Create in-memory database
        self.database = Database(":memory:")

        # Create button state manager
        self.button_state_manager = ButtonStateManager(self.database)

        # Create menu manager (replacing legacy keyboard manager)
        self.menu_manager = MenuManager(
            database=self.database,
            config={"show_persistent_menu": True, "menu_frequency": 5}
        )

        # Add test user
        self.test_user_id = 123456789
        self.database.add_user(
            user_id=self.test_user_id,
            username="test_user",
            first_name="Test",
            last_name="User"
        )

        # Create mock update and context
        self.mock_user = MagicMock(spec=User)
        self.mock_user.id = self.test_user_id
        self.mock_user.username = "test_user"
        self.mock_user.first_name = "Test"
        self.mock_user.last_name = "User"

        self.mock_chat = MagicMock(spec=Chat)
        self.mock_chat.id = self.test_user_id

        self.mock_message = MagicMock(spec=Message)
        self.mock_message.chat = self.mock_chat
        self.mock_message.from_user = self.mock_user
        self.mock_message.reply_text = AsyncMock()

        self.mock_callback_query = MagicMock(spec=CallbackQuery)
        self.mock_callback_query.from_user = self.mock_user
        self.mock_callback_query.message = self.mock_message
        self.mock_callback_query.data = "test_callback"
        self.mock_callback_query.answer = AsyncMock()

        self.mock_update = MagicMock(spec=Update)
        self.mock_update.effective_user = self.mock_user
        self.mock_update.effective_chat = self.mock_chat
        self.mock_update.message = self.mock_message
        self.mock_update.callback_query = self.mock_callback_query

        self.mock_context = MagicMock(spec=ContextTypes.DEFAULT_TYPE)
        self.mock_context.bot = MagicMock()
        self.mock_context.bot.send_message = AsyncMock()

    def test_menu_manager_initialization(self):
        """Test menu manager initialization."""
        # Check that menu manager is initialized
        self.assertIsNotNone(self.menu_manager)

        # Check that menu manager has the correct attributes
        self.assertEqual(self.menu_manager.database, self.database)
        self.assertEqual(self.menu_manager.config["show_persistent_menu"], True)
        self.assertEqual(self.menu_manager.config["menu_frequency"], 5)

    def test_get_main_menu_keyboard(self):
        """Test get_main_menu_keyboard method."""
        # Get main menu keyboard
        keyboard = self.menu_manager.get_main_menu_keyboard()

        # Check that keyboard is an InlineKeyboardMarkup
        self.assertIsInstance(keyboard, InlineKeyboardMarkup)

        # Check that keyboard has buttons
        self.assertGreater(len(keyboard.inline_keyboard), 0)

    def test_get_persistent_menu_button(self):
        """Test get_persistent_menu_button method."""
        # Get persistent menu button
        keyboard = self.menu_manager.get_persistent_menu_button()

        # Check that keyboard is an InlineKeyboardMarkup
        self.assertIsInstance(keyboard, InlineKeyboardMarkup)

        # Check that keyboard has one button
        self.assertEqual(len(keyboard.inline_keyboard), 1)
        self.assertEqual(len(keyboard.inline_keyboard[0]), 1)

        # Check that button has the correct callback data
        button = keyboard.inline_keyboard[0][0]
        self.assertEqual(button.callback_data, "back_to_main")

        # Check that button text contains "Menu"
        self.assertIn("Menu", button.text)

    def test_button_state_manager(self):
        """Test button state manager."""
        # Save button state
        self.button_state_manager.save_button_state(
            user_id=self.test_user_id,
            menu_id="test_menu",
            state={"key": "value"}
        )

        # Get button state
        state = self.button_state_manager.get_button_state(
            user_id=self.test_user_id,
            menu_id="test_menu"
        )

        # Check that state is retrieved correctly
        self.assertIsNotNone(state)
        self.assertEqual(state["key"], "value")

    def test_record_callback(self):
        """Test record_callback method."""
        # Record callback
        self.button_state_manager.record_callback(
            user_id=self.test_user_id,
            callback_data="test_callback"
        )

        # Check that callback is recorded
        callbacks = self.button_state_manager.user_callbacks.get(self.test_user_id, [])
        self.assertIn("test_callback", callbacks)

    def test_register_error(self):
        """Test register_error method."""
        # Register error
        self.button_state_manager.register_error(
            user_id=self.test_user_id,
            callback_data="test_callback",
            error=Exception("Test error")
        )

        # Check that error is registered
        self.assertIn(self.test_user_id, self.button_state_manager.error_recovery)
        self.assertIn("test_callback", self.button_state_manager.error_recovery[self.test_user_id])

    def test_get_recovery_suggestion(self):
        """Test get_recovery_suggestion method."""
        # Register error
        self.button_state_manager.register_error(
            user_id=self.test_user_id,
            callback_data="test_callback",
            error=Exception("Test error")
        )

        # Get recovery suggestion
        suggestion = self.button_state_manager.get_recovery_suggestion(
            user_id=self.test_user_id,
            callback_data="test_callback"
        )

        # Check that suggestion is returned
        self.assertIsNotNone(suggestion)

    async def test_navigation_router(self):
        """Test navigation router functionality."""
        # Create mock navigation router
        from bot.core.navigation_router import NavigationRouter

        # Create mock VoicePalBot
        mock_bot = MagicMock()
        mock_bot.menu_manager = self.menu_manager
        mock_bot.button_state_manager = self.button_state_manager
        mock_bot.database = self.database

        # Create navigation router
        navigation_router = NavigationRouter(mock_bot)

        # Register a test handler
        async def test_handler(update, context):
            return "Test response", self.menu_manager.get_main_menu_keyboard()

        navigation_router.register_handler("test_callback", test_handler)

        # Set up mock callback query
        self.mock_callback_query.data = "test_callback"

        # Route the callback
        result = await navigation_router.route_callback(self.mock_update, self.mock_context)

        # Check that the callback was routed successfully
        self.assertTrue(result)

        # Check that the callback query was answered
        self.mock_callback_query.edit_message_text.assert_called_once()

    async def test_handle_callback_query(self):
        """Test handle_callback_query method."""
        # Create mock VoicePalBot with handle_callback_query method
        mock_bot = MagicMock()
        mock_bot.menu_manager = self.menu_manager
        mock_bot.button_state_manager = self.button_state_manager
        mock_bot.database = self.database

        # Create navigation router
        from bot.core.navigation_router import NavigationRouter
        mock_bot.navigation_router = NavigationRouter(mock_bot)

        # Mock route_callback method
        mock_bot.navigation_router.route_callback = AsyncMock(return_value=True)

        # Create handle_callback_query method
        async def handle_callback_query(update, context):
            query = update.callback_query
            await query.answer()

            # Record callback in button state manager
            self.button_state_manager.record_callback(
                user_id=query.from_user.id,
                callback_data=query.data
            )

            # Route the callback through the navigation router
            await mock_bot.navigation_router.route_callback(update, context)

        # Set handle_callback_query method
        mock_bot.handle_callback_query = handle_callback_query

        # Call handle_callback_query
        await mock_bot.handle_callback_query(self.mock_update, self.mock_context)

        # Check that callback query was answered
        self.mock_callback_query.answer.assert_called_once()

        # Check that callback was recorded
        callbacks = self.button_state_manager.user_callbacks.get(self.test_user_id, [])
        self.assertIn("test_callback", callbacks)

def run_async_test(test_func):
    """Run an async test function."""
    loop = asyncio.get_event_loop()
    loop.run_until_complete(test_func())

if __name__ == "__main__":
    # Create test suite
    suite = unittest.TestSuite()

    # Add test cases
    test_case = TestUIButtonFunctionality()
    suite.addTest(test_case)

    # Add async tests
    for method_name in dir(test_case):
        if method_name.startswith("test_") and asyncio.iscoroutinefunction(getattr(test_case, method_name)):
            run_async_test(getattr(test_case, method_name))

    # Run tests
    unittest.TextTestRunner(verbosity=2).run(suite)
