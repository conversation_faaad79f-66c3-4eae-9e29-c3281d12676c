#!/usr/bin/env python3
"""
Simple test to verify core functionality.
"""

import sys
import os
import tempfile
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_database():
    """Test database functionality."""
    print("Testing database...")
    
    try:
        from bot.database.database_manager import DatabaseManager
        
        # Create temporary database
        fd, db_path = tempfile.mkstemp(suffix='.db')
        os.close(fd)
        
        try:
            # Initialize database
            db_manager = DatabaseManager(db_path)
            db_manager.initialize_database()
            
            # Test user creation
            user_id = 12345
            db_manager.create_user(
                user_id=user_id,
                username="test_user",
                first_name="Test"
            )
            
            # Test user retrieval
            user = db_manager.get_user(user_id)
            assert user is not None
            assert user['username'] == "test_user"
            
            print("✅ Database test passed!")
            return True
            
        finally:
            try:
                os.unlink(db_path)
            except OSError:
                pass
                
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_cache():
    """Test cache functionality."""
    print("Testing cache...")
    
    try:
        from bot.performance.cache_manager import CacheManager
        from unittest.mock import Mock
        
        # Create mock Redis client
        redis_client = Mock()
        redis_client.get.return_value = None
        redis_client.set.return_value = True
        
        # Test cache manager
        cache = CacheManager(redis_client=redis_client)
        
        # Test set/get operations
        cache.set("test_key", "test_value", ttl=3600)
        value = cache.get("test_key")
        
        assert value == "test_value"
        
        print("✅ Cache test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Cache test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_encryption():
    """Test encryption functionality."""
    print("Testing encryption...")
    
    try:
        from bot.security.encryption import EncryptionManager
        
        # Test encryption manager
        encryption = EncryptionManager()
        
        # Test data encryption/decryption
        original_data = "This is sensitive data"
        encrypted_data = encryption.encrypt_data(original_data)
        decrypted_data = encryption.decrypt_data(encrypted_data)
        
        assert decrypted_data == original_data
        assert encrypted_data != original_data
        
        print("✅ Encryption test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Encryption test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_analytics():
    """Test analytics imports."""
    print("Testing analytics...")
    
    try:
        from bot.analytics.conversation_analytics import ConversationAnalytics
        from bot.analytics.user_analytics import UserAnalytics
        from bot.analytics.business_analytics import BusinessAnalytics
        
        print("✅ Analytics test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Analytics test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run simple tests."""
    print("🚀 Running Simple VoicePal Tests")
    print("=" * 40)
    
    tests = [
        test_database,
        test_cache,
        test_encryption,
        test_analytics
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            failed += 1
        print()
    
    print("=" * 40)
    print(f"Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All tests passed!")
        return True
    else:
        print("💥 Some tests failed!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
