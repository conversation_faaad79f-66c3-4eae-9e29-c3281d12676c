"""
Validation utilities for VoicePal.

This module provides specific validation functions for different parts of the application,
building on the core validation framework in security.py.
"""

import logging
import re
from typing import Dict, Any, Optional, List, Union, Tuple

from bot.core.security import InputValidator

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class MessageValidator:
    """Validator for user messages."""
    
    @staticmethod
    def validate_text_message(text: str) -> Tuple[bool, str]:
        """
        Validate a text message from a user.
        
        Args:
            text: The message text
            
        Returns:
            Tuple[bool, str]: (is_valid, error_message)
        """
        # Basic validation
        if not text:
            return False, "Message cannot be empty."
            
        # Check for reasonable length
        if len(text) > 4096:
            return False, "Message is too long. Please keep it under 4096 characters."
            
        # Check for control characters
        if any(ord(c) < 32 and c not in '\n\t\r' for c in text):
            return False, "Message contains invalid characters."
            
        # Check for spam patterns
        if re.search(r'(https?://|www\.)', text, re.IGNORECASE):
            # Message contains URLs - could be spam
            if text.count('http') > 2:
                return False, "Too many URLs in message. This looks like spam."
                
        return True, ""
        
    @staticmethod
    def validate_voice_message(duration: int, file_size: int) -> Tuple[bool, str]:
        """
        Validate a voice message from a user.
        
        Args:
            duration: Voice message duration in seconds
            file_size: Voice message file size in bytes
            
        Returns:
            Tuple[bool, str]: (is_valid, error_message)
        """
        # Check duration
        if duration > 60:
            return False, "Voice message is too long. Please keep it under 60 seconds."
            
        # Check file size
        if file_size > 1024 * 1024:  # 1 MB
            return False, "Voice message file is too large. Please keep it under 1 MB."
            
        # Check if file is empty
        if file_size == 0:
            return False, "Voice message is empty."
            
        return True, ""


class CommandValidator:
    """Validator for bot commands."""
    
    @staticmethod
    def validate_command(command: str, args: List[str] = None) -> Tuple[bool, str]:
        """
        Validate a bot command.
        
        Args:
            command: The command name (without the slash)
            args: Command arguments
            
        Returns:
            Tuple[bool, str]: (is_valid, error_message)
        """
        # Basic validation
        if not command:
            return False, "Command cannot be empty."
            
        # Check command format
        if not re.match(r'^[a-zA-Z0-9_]+$', command):
            return False, "Invalid command format."
            
        # Check command length
        if len(command) > 32:
            return False, "Command is too long."
            
        return True, ""
        
    @staticmethod
    def validate_buy_command(args: List[str]) -> Tuple[bool, str]:
        """
        Validate the /buy command arguments.
        
        Args:
            args: Command arguments
            
        Returns:
            Tuple[bool, str]: (is_valid, error_message)
        """
        # No arguments needed for buy command
        return True, ""
        
    @staticmethod
    def validate_addcredits_command(args: List[str]) -> Tuple[bool, str]:
        """
        Validate the /addcredits command arguments.
        
        Args:
            args: Command arguments
            
        Returns:
            Tuple[bool, str]: (is_valid, error_message)
        """
        # Check if we have the right number of arguments
        if len(args) != 2:
            return False, "Usage: /addcredits [user_id] [amount]"
            
        # Validate user_id
        if not args[0].isdigit():
            return False, "User ID must be a number."
            
        # Validate amount
        if not args[1].isdigit() or int(args[1]) <= 0:
            return False, "Credit amount must be a positive number."
            
        return True, ""


class PaymentValidator:
    """Validator for payment-related inputs."""
    
    @staticmethod
    def validate_payment_amount(amount: Union[int, float, str]) -> Tuple[bool, str]:
        """
        Validate a payment amount.
        
        Args:
            amount: The payment amount
            
        Returns:
            Tuple[bool, str]: (is_valid, error_message)
        """
        # Convert to float if string
        if isinstance(amount, str):
            try:
                amount = float(amount)
            except ValueError:
                return False, "Payment amount must be a number."
                
        # Check if positive
        if amount <= 0:
            return False, "Payment amount must be positive."
            
        # Check if reasonable
        if amount > 1000:
            return False, "Payment amount is too large."
            
        return True, ""
        
    @staticmethod
    def validate_credit_package(package_id: str) -> Tuple[bool, str]:
        """
        Validate a credit package ID.
        
        Args:
            package_id: The credit package ID
            
        Returns:
            Tuple[bool, str]: (is_valid, error_message)
        """
        # Basic validation
        if not package_id:
            return False, "Package ID cannot be empty."
            
        # Check format
        if not re.match(r'^[a-zA-Z0-9_]+$', package_id):
            return False, "Invalid package ID format."
            
        return True, ""


# Utility functions for common validation tasks
def validate_user_input(text: str, input_type: str = 'text', **kwargs) -> Tuple[bool, str, str]:
    """
    Validate and sanitize user input with error message.
    
    Args:
        text: The text to validate
        input_type: The type of input
        **kwargs: Additional validation parameters
        
    Returns:
        Tuple[bool, str, str]: (is_valid, sanitized_text, error_message)
    """
    sanitized = InputValidator.sanitize(text, input_type, **kwargs)
    is_valid = InputValidator.validate(sanitized, input_type, **kwargs)
    
    error_message = ""
    if not is_valid:
        if input_type == 'text':
            error_message = "Invalid text input. Please try again."
        elif input_type == 'command':
            error_message = "Invalid command format. Please try again."
        elif input_type in ('number', 'integer', 'float'):
            error_message = "Invalid number format. Please try again."
        elif input_type == 'email':
            error_message = "Invalid email address. Please try again."
        elif input_type == 'url':
            error_message = "Invalid URL. Please try again."
        elif input_type == 'phone':
            error_message = "Invalid phone number. Please try again."
        elif input_type == 'username':
            error_message = "Invalid username. Please try again."
        else:
            error_message = f"Invalid {input_type} input. Please try again."
            
    return is_valid, sanitized, error_message
