"""
<PERSON><PERSON><PERSON> to update the deployment-test branch with the fixed files.
"""

import os
import shutil
import subprocess

def run_command(command):
    """
    Run a command and return the output.
    
    Args:
        command: Command to run
        
    Returns:
        Output of the command
    """
    print(f"Running command: {command}")
    result = subprocess.run(command, shell=True, capture_output=True, text=True)
    print(f"Output: {result.stdout}")
    print(f"Error: {result.stderr}")
    return result

def main():
    """Main function."""
    # Checkout the deployment-test branch
    run_command("git checkout deployment-test")
    
    # Replace the initialization_manager.py file with the fixed version
    shutil.copy("bot/core/initialization_manager_fixed.py", "bot/core/initialization_manager.py")
    
    # Update the main.py file to use the correct dependencies
    with open("bot/main.py", "r", encoding="utf-8") as f:
        content = f.read()
    
    content = content.replace(
        'dependencies=["ai_provider", "memory_manager", "user_manager"]',
        'dependencies=["providers", "feature_managers"]'
    )
    
    with open("bot/main.py", "w", encoding="utf-8") as f:
        f.write(content)
    
    # Commit the changes
    run_command("git add bot/core/initialization_manager.py bot/main.py")
    run_command('git commit -m "Fix import issues for deployment"')
    
    # Push the changes to the deployment-test branch
    run_command("git push origin deployment-test")
    
    print("Done!")

if __name__ == "__main__":
    main()
