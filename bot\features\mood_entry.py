"""
Mood entry module for VoicePal.

This module handles direct mood entry from users.
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
import random

from telegram import (
    InlineKeyboardButton,
    InlineKeyboardMarkup,
    Update
)
from telegram.ext import ContextTypes

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class MoodEntry:
    """Handles direct mood entry from users."""

    def __init__(self, database, mood_tracker=None, config: Dict[str, Any] = None):
        """
        Initialize the mood entry handler.

        Args:
            database: Database instance
            mood_tracker: MoodTracker instance (optional)
            config: Configuration dictionary
        """
        self.database = database
        self.mood_tracker = mood_tracker
        self.config = config or {}

        # Define mood options with emoji icons
        self.mood_options = {
            "happy": {"name": "Happy", "emoji": "😊", "sentiment": "positive", "confidence": 0.9},
            "calm": {"name": "Calm", "emoji": "😌", "sentiment": "positive", "confidence": 0.7},
            "neutral": {"name": "Neutral", "emoji": "😐", "sentiment": "neutral", "confidence": 0.8},
            "sad": {"name": "Sad", "emoji": "😔", "sentiment": "negative", "confidence": 0.8},
            "angry": {"name": "Angry", "emoji": "😠", "sentiment": "negative", "confidence": 0.9},
            "anxious": {"name": "Anxious", "emoji": "😰", "sentiment": "negative", "confidence": 0.8}
        }

        # Define mood prompts
        self.mood_prompts = [
            "How are you feeling right now?",
            "What's your mood today?",
            "Check in with yourself - how are you feeling?",
            "Time for a mood check! How are you doing?",
            "Let's record your mood. How are you feeling?"
        ]

    def get_mood_entry_keyboard(self) -> InlineKeyboardMarkup:
        """
        Get mood entry keyboard.

        Returns:
            InlineKeyboardMarkup with mood options
        """
        keyboard = []
        row = []

        # Create rows with 3 buttons each
        for i, (mood_id, mood) in enumerate(self.mood_options.items()):
            row.append(
                InlineKeyboardButton(
                    f"{mood['emoji']} {mood['name']}",
                    callback_data=f"mood_entry_{mood_id}"
                )
            )

            # Create a new row after every 2 buttons
            if (i + 1) % 2 == 0:
                keyboard.append(row)
                row = []

        # Add any remaining buttons
        if row:
            keyboard.append(row)

        # Add cancel button
        keyboard.append([InlineKeyboardButton("❌ Cancel", callback_data="mood_entry_cancel")])

        return InlineKeyboardMarkup(keyboard)

    async def start_mood_entry(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """
        Start the mood entry process.

        Args:
            update: Update object
            context: Context object
        """
        # Get random prompt
        prompt = random.choice(self.mood_prompts)

        # Get mood entry keyboard
        reply_markup = self.get_mood_entry_keyboard()

        # Send prompt with keyboard
        await update.message.reply_text(prompt, reply_markup=reply_markup)

    async def handle_mood_entry_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> Tuple[str, Optional[InlineKeyboardMarkup]]:
        """
        Handle mood entry callback.

        Args:
            update: Update object
            context: Context object

        Returns:
            Tuple containing response text and optional keyboard markup
        """
        query = update.callback_query
        user_id = query.from_user.id
        callback_data = query.data

        # Extract mood from callback data
        mood_id = callback_data.replace("mood_entry_", "")

        if mood_id == "cancel":
            return "Mood entry cancelled.", None

        if mood_id in self.mood_options:
            mood = self.mood_options[mood_id]

            # Create mood data
            mood_data = {
                "sentiment": mood["sentiment"],
                "confidence": mood["confidence"],
                "source": "manual",
                "message_text": f"User manually entered mood: {mood['name']}"
            }

            # Add mood entry
            try:
                if self.mood_tracker:
                    self.mood_tracker.add_mood_entry(user_id, mood_data)
                else:
                    self.database.add_mood_entry(user_id, mood_data)

                logger.info(f"Added mood entry for user {user_id}: {mood['name']}")
            except Exception as e:
                logger.error(f"Error adding mood entry: {e}")
                import traceback
                logger.error(traceback.format_exc())

            # Create confirmation message
            message = f"Thanks for sharing! I've recorded your mood as {mood['emoji']} {mood['name']}."

            # Add follow-up question for negative moods
            if mood["sentiment"] == "negative":
                message += "\n\nIs there anything you'd like to talk about?"

                # Create keyboard with options
                keyboard = [
                    [InlineKeyboardButton("Yes, let's chat", callback_data="mood_followup_chat")],
                    [InlineKeyboardButton("No, just recording", callback_data="mood_followup_no")]
                ]
                reply_markup = InlineKeyboardMarkup(keyboard)

                return message, reply_markup

            # For non-negative moods, add a back button
            keyboard = [
                [InlineKeyboardButton("🔙 Back to Mood Diary", callback_data="show_mood_diary")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            return message, reply_markup

        return "Invalid mood selection.", None

    async def handle_mood_followup_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> Tuple[str, Optional[InlineKeyboardMarkup]]:
        """
        Handle mood follow-up callback.

        Args:
            update: Update object
            context: Context object

        Returns:
            Tuple containing response text and optional keyboard markup
        """
        query = update.callback_query
        callback_data = query.data

        if callback_data == "mood_followup_chat":
            message = (
                "I'm here to listen. What's on your mind?\n\n"
                "Feel free to share as much or as little as you'd like."
            )

            # Add a back button
            keyboard = [
                [InlineKeyboardButton("🔙 Back to Mood Diary", callback_data="show_mood_diary")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            return message, reply_markup

        elif callback_data == "mood_followup_no":
            message = (
                "No problem! Remember, I'm here whenever you need someone to talk to.\n\n"
                "You can check your mood history and insights in the Mood Diary."
            )

            # Create keyboard with mood diary option
            keyboard = [
                [InlineKeyboardButton("📊 View Mood Diary", callback_data="show_mood_diary")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            return message, reply_markup

        return "Invalid selection.", None
