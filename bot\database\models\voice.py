"""
Voice domain models for VoicePal.

This module provides the voice-related models for the VoicePal database.
"""

import logging
from typing import Dict, List, Any, Optional, ClassVar, Type, Set, Tuple
from datetime import datetime

from bot.database.models.model import Model

# Set up logging
logger = logging.getLogger(__name__)

class VoiceSetting(Model):
    """Voice setting model."""
    
    _table_name = "voice_settings"
    _primary_key = "setting_id"
    _required_columns = ["user_id", "provider", "voice_id"]
    _defaults = {
        "pitch": 1.0,
        "rate": 1.0,
        "volume": 1.0
    }
    _foreign_keys = {
        "user_id": ("users", "user_id")
    }
    
    def __init__(self,
                 setting_id: Optional[str] = None,
                 user_id: str = None,
                 provider: str = None,
                 voice_id: str = None,
                 pitch: float = 1.0,
                 rate: float = 1.0,
                 volume: float = 1.0,
                 created_at: Optional[str] = None,
                 updated_at: Optional[str] = None,
                 **kwargs):
        """Initialize voice setting model.
        
        Args:
            setting_id: Setting ID
            user_id: User ID
            provider: Voice provider
            voice_id: Voice ID
            pitch: Voice pitch
            rate: Voice rate
            volume: Voice volume
            created_at: Creation timestamp
            updated_at: Last update timestamp
            **kwargs: Additional attributes
        """
        super().__init__(
            setting_id=setting_id,
            user_id=user_id,
            provider=provider,
            voice_id=voice_id,
            pitch=pitch,
            rate=rate,
            volume=volume,
            created_at=created_at,
            updated_at=updated_at,
            **kwargs
        )
    
    def validate(self) -> List[str]:
        """Validate voice setting attributes.
        
        Returns:
            List of validation error messages (empty if valid)
        """
        errors = super().validate()
        
        # Validate pitch
        if hasattr(self, "pitch") and self.pitch is not None:
            if not isinstance(self.pitch, (int, float)) or self.pitch < 0.5 or self.pitch > 2.0:
                errors.append(f"Pitch must be a number between 0.5 and 2.0: {self.pitch}")
        
        # Validate rate
        if hasattr(self, "rate") and self.rate is not None:
            if not isinstance(self.rate, (int, float)) or self.rate < 0.5 or self.rate > 2.0:
                errors.append(f"Rate must be a number between 0.5 and 2.0: {self.rate}")
        
        # Validate volume
        if hasattr(self, "volume") and self.volume is not None:
            if not isinstance(self.volume, (int, float)) or self.volume < 0.0 or self.volume > 1.0:
                errors.append(f"Volume must be a number between 0.0 and 1.0: {self.volume}")
        
        return errors
    
    def update_voice(self, provider: str, voice_id: str) -> None:
        """Update voice provider and ID.
        
        Args:
            provider: Voice provider
            voice_id: Voice ID
        """
        self.provider = provider
        self.voice_id = voice_id
        self.updated_at = datetime.now().isoformat()
    
    def update_settings(self, pitch: Optional[float] = None, rate: Optional[float] = None,
                       volume: Optional[float] = None) -> None:
        """Update voice settings.
        
        Args:
            pitch: Voice pitch
            rate: Voice rate
            volume: Voice volume
        """
        if pitch is not None:
            self.pitch = pitch
        
        if rate is not None:
            self.rate = rate
        
        if volume is not None:
            self.volume = volume
        
        self.updated_at = datetime.now().isoformat()

class VoiceRecording(Model):
    """Voice recording model."""
    
    _table_name = "voice_recordings"
    _primary_key = "recording_id"
    _required_columns = ["user_id", "file_path"]
    _foreign_keys = {
        "user_id": ("users", "user_id"),
        "message_id": ("messages", "message_id")
    }
    
    def __init__(self,
                 recording_id: Optional[str] = None,
                 user_id: str = None,
                 message_id: Optional[str] = None,
                 file_path: str = None,
                 duration: Optional[float] = None,
                 created_at: Optional[str] = None,
                 **kwargs):
        """Initialize voice recording model.
        
        Args:
            recording_id: Recording ID
            user_id: User ID
            message_id: Message ID
            file_path: File path
            duration: Recording duration in seconds
            created_at: Creation timestamp
            **kwargs: Additional attributes
        """
        super().__init__(
            recording_id=recording_id,
            user_id=user_id,
            message_id=message_id,
            file_path=file_path,
            duration=duration,
            created_at=created_at,
            **kwargs
        )
    
    def update_duration(self, duration: float) -> None:
        """Update recording duration.
        
        Args:
            duration: Recording duration in seconds
        """
        self.duration = duration
