"""
Verify that all necessary files are included in the Git repository.

This script checks if all required files for the VoicePal bot are
properly tracked in the Git repository to ensure successful deployment.
"""

import os
import sys
import logging
import subprocess
from typing import List, Set, Tuple

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# Essential files that must be included in the repository
ESSENTIAL_FILES = [
    "run_bot.py",
    "Procfile",
    "requirements.txt",
    ".env.example",
    "bot/main.py",
    "bot/config_manager.py",
    "bot/database/core.py",
    "bot/providers/provider_factory.py",
    "bot/providers/ai/google_ai_provider.py",
    "bot/providers/ai/groq_provider.py",
    "bot/providers/tts/deepgram_provider.py",
    "bot/providers/tts/elevenlabs_provider.py",
    "bot/providers/voice/processor.py",
    "scripts/import_handler.py"
]

# Essential directories that should have files in the repository
ESSENTIAL_DIRECTORIES = [
    "bot/core",
    "bot/features",
    "bot/handlers",
    "bot/payment",
    "bot/providers",
    "bot/database",
    "scripts"
]

def get_git_tracked_files() -> Set[str]:
    """
    Get all files tracked by Git.
    
    Returns:
        Set of file paths tracked by Git
    """
    try:
        result = subprocess.run(
            ["git", "ls-files"],
            capture_output=True,
            text=True,
            check=True
        )
        return set(result.stdout.splitlines())
    except subprocess.CalledProcessError as e:
        logger.error(f"Error getting Git tracked files: {e}")
        logger.error(f"stdout: {e.stdout}")
        logger.error(f"stderr: {e.stderr}")
        return set()
    except FileNotFoundError:
        logger.error("Git command not found. Make sure Git is installed.")
        return set()

def get_git_untracked_files() -> Set[str]:
    """
    Get all files not tracked by Git.
    
    Returns:
        Set of file paths not tracked by Git
    """
    try:
        result = subprocess.run(
            ["git", "ls-files", "--others", "--exclude-standard"],
            capture_output=True,
            text=True,
            check=True
        )
        return set(result.stdout.splitlines())
    except subprocess.CalledProcessError as e:
        logger.error(f"Error getting Git untracked files: {e}")
        logger.error(f"stdout: {e.stdout}")
        logger.error(f"stderr: {e.stderr}")
        return set()
    except FileNotFoundError:
        logger.error("Git command not found. Make sure Git is installed.")
        return set()

def check_essential_files(tracked_files: Set[str]) -> Tuple[List[str], List[str]]:
    """
    Check if all essential files are tracked by Git.
    
    Args:
        tracked_files: Set of file paths tracked by Git
        
    Returns:
        Tuple containing:
        - List of missing essential files
        - List of tracked essential files
    """
    missing_files = []
    tracked_essential_files = []
    
    for file_path in ESSENTIAL_FILES:
        if file_path in tracked_files:
            tracked_essential_files.append(file_path)
        else:
            # Check if the file exists but is not tracked
            if os.path.exists(file_path):
                missing_files.append(f"{file_path} (exists but not tracked)")
            else:
                missing_files.append(f"{file_path} (file does not exist)")
    
    return missing_files, tracked_essential_files

def check_essential_directories(tracked_files: Set[str]) -> Tuple[List[str], List[str]]:
    """
    Check if all essential directories have files tracked by Git.
    
    Args:
        tracked_files: Set of file paths tracked by Git
        
    Returns:
        Tuple containing:
        - List of empty essential directories
        - List of essential directories with tracked files
    """
    empty_directories = []
    directories_with_files = []
    
    for directory in ESSENTIAL_DIRECTORIES:
        has_tracked_files = False
        
        for file_path in tracked_files:
            if file_path.startswith(directory + "/") or file_path == directory:
                has_tracked_files = True
                break
        
        if has_tracked_files:
            directories_with_files.append(directory)
        else:
            # Check if the directory exists but has no tracked files
            if os.path.exists(directory) and os.path.isdir(directory):
                empty_directories.append(f"{directory} (exists but no tracked files)")
            else:
                empty_directories.append(f"{directory} (directory does not exist)")
    
    return empty_directories, directories_with_files

def main():
    """Main function to verify Git files."""
    logger.info("Verifying Git repository files...")
    
    # Get tracked and untracked files
    tracked_files = get_git_tracked_files()
    untracked_files = get_git_untracked_files()
    
    logger.info(f"Found {len(tracked_files)} tracked files and {len(untracked_files)} untracked files")
    
    # Check essential files
    missing_files, tracked_essential_files = check_essential_files(tracked_files)
    
    # Check essential directories
    empty_directories, directories_with_files = check_essential_directories(tracked_files)
    
    # Print results
    logger.info("\n=== Git Repository Verification Results ===")
    
    logger.info("\nEssential Files:")
    if tracked_essential_files:
        logger.info("✅ Tracked essential files:")
        for file_path in tracked_essential_files:
            logger.info(f"  - {file_path}")
    
    if missing_files:
        logger.error("❌ Missing essential files:")
        for file_path in missing_files:
            logger.error(f"  - {file_path}")
    
    logger.info("\nEssential Directories:")
    if directories_with_files:
        logger.info("✅ Essential directories with tracked files:")
        for directory in directories_with_files:
            logger.info(f"  - {directory}")
    
    if empty_directories:
        logger.error("❌ Empty essential directories:")
        for directory in empty_directories:
            logger.error(f"  - {directory}")
    
    # Check for important untracked files
    important_untracked = []
    for file_path in untracked_files:
        if file_path.endswith(".py") or file_path.endswith(".md") or file_path.endswith(".json"):
            if not file_path.startswith("__pycache__") and not "venv" in file_path:
                important_untracked.append(file_path)
    
    if important_untracked:
        logger.warning("\n⚠️ Important files that are not tracked:")
        for file_path in important_untracked:
            logger.warning(f"  - {file_path}")
        logger.warning("Consider adding these files to the repository with 'git add <file>'")
    
    # Final verdict
    if missing_files or empty_directories:
        logger.error("\n❌ Repository verification failed!")
        logger.error("Some essential files or directories are missing from the Git repository.")
        logger.error("This may cause deployment issues. Please add the missing files.")
        return 1
    else:
        logger.info("\n✅ Repository verification passed!")
        logger.info("All essential files and directories are tracked in the Git repository.")
        return 0

if __name__ == "__main__":
    sys.exit(main())
