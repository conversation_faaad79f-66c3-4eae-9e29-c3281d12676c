"""
Google STT provider for VoicePal.

This module provides a provider for Google Speech-to-Text services.
"""

import os
import logging
import asyncio
import json
from typing import Dict, Any, Optional, List, Union, BinaryIO
from dataclasses import dataclass, field
from io import BytesIO

from google.cloud import speech
from google.cloud.speech import SpeechClient, RecognitionConfig, RecognitionAudio
from google.cloud import language_v1
from google.cloud.language_v1 import LanguageServiceClient, Document, EncodingType

from bot.providers.core.provider import STTProvider
from bot.providers.core.config import STTProviderConfig
from bot.providers.core.exceptions import (
    ProviderError,
    ProviderConfigError,
    ProviderAuthError,
    ProviderRateLimitError,
    ProviderTimeoutError,
    ProviderNotFoundError,
    ProviderValidationError,
    ProviderNotInitializedError
)

# Set up logging
logger = logging.getLogger(__name__)

@dataclass
class GoogleSTTConfig(STTProviderConfig):
    """Configuration for Google STT provider."""
    
    api_key: str = ""
    credentials_path: str = ""
    language: str = "en-US"
    model: str = "latest_long"
    enable_automatic_punctuation: bool = True
    enable_word_time_offsets: bool = True
    profanity_filter: bool = False
    timeout: int = 30
    
    def __post_init__(self):
        """Post-initialization."""
        self.provider_type = "stt"
        self.provider_name = "google_stt"
    
    def validate(self) -> List[str]:
        """Validate configuration.
        
        Returns:
            List of validation error messages (empty if valid)
        """
        errors = super().validate()
        
        # Validate credentials
        if not self.api_key and not self.credentials_path:
            errors.append("Either API key or credentials path must be provided")
        
        # Validate model
        valid_models = ["latest_long", "latest_short", "command_and_search", "phone_call", "video"]
        if self.model not in valid_models:
            errors.append(f"Invalid model: {self.model}. Must be one of {valid_models}")
        
        return errors

class GoogleSTTProvider(STTProvider[GoogleSTTConfig]):
    """Provider for Google STT services."""
    
    provider_type = "stt"
    provider_name = "google_stt"
    provider_version = "1.0.0"
    provider_description = "Provider for Google Speech-to-Text services"
    config_class = GoogleSTTConfig
    
    def __init__(self, config: GoogleSTTConfig):
        """Initialize provider.
        
        Args:
            config: Provider configuration
        """
        super().__init__(config)
        self.speech_client = None
        self.language_client = None
    
    def validate_config(self) -> None:
        """Validate provider configuration.
        
        Raises:
            ProviderConfigError: If configuration is invalid
        """
        errors = self.config.validate()
        if errors:
            error_message = "; ".join(errors)
            raise ProviderConfigError(f"Invalid configuration: {error_message}")
    
    def initialize(self) -> None:
        """Initialize provider.
        
        Raises:
            ProviderInitializationError: If initialization fails
        """
        try:
            # Set credentials environment variable if provided
            if self.config.credentials_path:
                os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = self.config.credentials_path
            
            # Initialize Speech client
            self.speech_client = SpeechClient()
            
            # Initialize Language client for sentiment analysis
            self.language_client = LanguageServiceClient()
            
            self.initialized = True
            logger.info(f"Initialized {self.provider_name} provider")
        except Exception as e:
            logger.error(f"Failed to initialize {self.provider_name} provider: {e}")
            raise ProviderInitializationError(f"Failed to initialize {self.provider_name} provider: {e}") from e
    
    def shutdown(self) -> None:
        """Shutdown provider.
        
        Raises:
            ProviderShutdownError: If shutdown fails
        """
        if self.speech_client:
            self.speech_client.transport.close()
        
        if self.language_client:
            self.language_client.transport.close()
        
        self.speech_client = None
        self.language_client = None
        self.initialized = False
        logger.info(f"Shutdown {self.provider_name} provider")
    
    async def speech_to_text(self, audio_data: bytes, **kwargs) -> Dict[str, Any]:
        """Convert speech to text.
        
        Args:
            audio_data: Audio data
            **kwargs: Additional arguments
            
        Returns:
            Transcription result
            
        Raises:
            ProviderError: If speech-to-text conversion fails
        """
        if not self.initialized:
            raise ProviderNotInitializedError(f"{self.provider_name} provider is not initialized")
        
        try:
            # Create recognition audio
            audio = RecognitionAudio(content=audio_data)
            
            # Determine audio encoding
            encoding = kwargs.get("encoding", RecognitionConfig.AudioEncoding.LINEAR16)
            if isinstance(encoding, str):
                encoding_map = {
                    "linear16": RecognitionConfig.AudioEncoding.LINEAR16,
                    "flac": RecognitionConfig.AudioEncoding.FLAC,
                    "mp3": RecognitionConfig.AudioEncoding.MP3,
                    "ogg_opus": RecognitionConfig.AudioEncoding.OGG_OPUS
                }
                encoding = encoding_map.get(encoding.lower(), RecognitionConfig.AudioEncoding.LINEAR16)
            
            # Create recognition config
            config = RecognitionConfig(
                encoding=encoding,
                sample_rate_hertz=kwargs.get("sample_rate", 16000),
                language_code=kwargs.get("language", self.config.language),
                model=kwargs.get("model", self.config.model),
                enable_automatic_punctuation=kwargs.get("enable_automatic_punctuation", self.config.enable_automatic_punctuation),
                enable_word_time_offsets=kwargs.get("enable_word_time_offsets", self.config.enable_word_time_offsets),
                profanity_filter=kwargs.get("profanity_filter", self.config.profanity_filter)
            )
            
            # Transcribe audio
            response = await asyncio.to_thread(
                self.speech_client.recognize,
                config=config,
                audio=audio
            )
            
            # Extract transcription
            if not response.results:
                return {"text": "", "confidence": 0.0, "words": []}
            
            result = response.results[0]
            alternative = result.alternatives[0]
            
            # Create response
            transcription = {
                "text": alternative.transcript,
                "confidence": alternative.confidence,
                "words": [
                    {
                        "word": word.word,
                        "start": word.start_time.total_seconds(),
                        "end": word.end_time.total_seconds(),
                        "confidence": alternative.confidence  # Word-level confidence not provided
                    }
                    for word in alternative.words
                ] if hasattr(alternative, "words") else []
            }
            
            return transcription
        except Exception as e:
            logger.error(f"Failed to convert speech to text: {e}")
            
            # Map exceptions to provider exceptions
            if "authentication" in str(e).lower() or "credentials" in str(e).lower():
                raise ProviderAuthError(f"Invalid credentials: {e}") from e
            elif "rate limit" in str(e).lower() or "quota" in str(e).lower():
                raise ProviderRateLimitError(f"Rate limit exceeded: {e}") from e
            elif "timeout" in str(e).lower() or "deadline" in str(e).lower():
                raise ProviderTimeoutError(f"Request timed out: {e}") from e
            elif "not found" in str(e).lower():
                raise ProviderNotFoundError(f"Resource not found: {e}") from e
            elif "invalid" in str(e).lower():
                raise ProviderValidationError(f"Invalid request: {e}") from e
            else:
                raise ProviderError(f"Failed to convert speech to text: {e}") from e
    
    async def analyze_sentiment(self, text: str, **kwargs) -> Dict[str, Any]:
        """Analyze sentiment of text.
        
        Args:
            text: Text to analyze
            **kwargs: Additional arguments
            
        Returns:
            Sentiment analysis result
            
        Raises:
            ProviderError: If sentiment analysis fails
        """
        if not self.initialized:
            raise ProviderNotInitializedError(f"{self.provider_name} provider is not initialized")
        
        try:
            # Create document
            document = Document(
                content=text,
                type_=Document.Type.PLAIN_TEXT,
                language=kwargs.get("language", self.config.language.split("-")[0])
            )
            
            # Analyze sentiment
            response = await asyncio.to_thread(
                self.language_client.analyze_sentiment,
                document=document,
                encoding_type=EncodingType.UTF8
            )
            
            # Extract sentiment
            document_sentiment = response.document_sentiment
            
            # Map score to sentiment label
            sentiment_label = "neutral"
            if document_sentiment.score >= 0.25:
                sentiment_label = "positive"
            elif document_sentiment.score <= -0.25:
                sentiment_label = "negative"
            
            # Create response
            sentiment = {
                "overall": sentiment_label,
                "score": document_sentiment.score,
                "magnitude": document_sentiment.magnitude,
                "segments": [
                    {
                        "text": sentence.text.content,
                        "sentiment": "positive" if sentence.sentiment.score >= 0.25 else ("negative" if sentence.sentiment.score <= -0.25 else "neutral"),
                        "score": sentence.sentiment.score,
                        "magnitude": sentence.sentiment.magnitude
                    }
                    for sentence in response.sentences
                ]
            }
            
            return sentiment
        except Exception as e:
            logger.error(f"Failed to analyze sentiment: {e}")
            
            # Map exceptions to provider exceptions
            if "authentication" in str(e).lower() or "credentials" in str(e).lower():
                raise ProviderAuthError(f"Invalid credentials: {e}") from e
            elif "rate limit" in str(e).lower() or "quota" in str(e).lower():
                raise ProviderRateLimitError(f"Rate limit exceeded: {e}") from e
            elif "timeout" in str(e).lower() or "deadline" in str(e).lower():
                raise ProviderTimeoutError(f"Request timed out: {e}") from e
            elif "not found" in str(e).lower():
                raise ProviderNotFoundError(f"Resource not found: {e}") from e
            elif "invalid" in str(e).lower():
                raise ProviderValidationError(f"Invalid request: {e}") from e
            else:
                raise ProviderError(f"Failed to analyze sentiment: {e}") from e
