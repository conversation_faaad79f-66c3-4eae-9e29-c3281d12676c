"""
Pytest fixtures for provider tests.

This module provides fixtures for provider tests.
"""

import os
import pytest
import tempfile
from pathlib import Path
import httpx
from unittest.mock import MagicMock, patch

from bot.providers.core.http_client import HTTPClient
from bot.providers.core.cache_service import CacheService

@pytest.fixture
def temp_cache_dir():
    """Create a temporary cache directory.
    
    Returns:
        Path to temporary cache directory
    """
    temp_dir = tempfile.mkdtemp()
    
    yield temp_dir
    
    # Clean up
    import shutil
    shutil.rmtree(temp_dir)

@pytest.fixture
def http_client():
    """Create an HTTP client.
    
    Returns:
        HTTPClient instance
    """
    return HTTPClient(base_url="https://api.example.com")

@pytest.fixture
def cache_service(temp_cache_dir):
    """Create a cache service.
    
    Args:
        temp_cache_dir: Path to temporary cache directory
        
    Returns:
        CacheService instance
    """
    return CacheService(cache_dir=temp_cache_dir)

@pytest.fixture
def mock_httpx_response():
    """Create a mock httpx response.
    
    Returns:
        Mock response
    """
    response = MagicMock()
    response.status_code = 200
    response.text = '{"result": "success"}'
    response.json.return_value = {"result": "success"}
    response.headers = {}
    
    return response

@pytest.fixture
def mock_httpx_client(mock_httpx_response):
    """Create a mock httpx client.
    
    Args:
        mock_httpx_response: Mock response
        
    Returns:
        Mock client
    """
    client = MagicMock()
    client.request.return_value = mock_httpx_response
    
    return client
