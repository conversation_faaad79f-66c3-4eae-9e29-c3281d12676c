"""
Data encryption module for VoicePal.

This module provides encryption functionality for sensitive data.
"""

import os
import base64
import logging
from typing import Optional, Union, Dict, Any
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

logger = logging.getLogger(__name__)

class DataEncryption:
    """Data encryption handler for VoicePal."""
    
    def __init__(self, master_key: Optional[str] = None):
        """
        Initialize data encryption.
        
        Args:
            master_key: Master encryption key (if None, will be generated or loaded from env)
        """
        self.master_key = master_key or self._get_or_create_master_key()
        self.fernet = self._create_fernet(self.master_key)
    
    def _get_or_create_master_key(self) -> str:
        """Get or create master encryption key."""
        # Try to get from environment
        master_key = os.getenv("VOICEPAL_MASTER_KEY")
        
        if not master_key:
            # Generate new key
            master_key = Fernet.generate_key().decode()
            logger.warning(
                "No master key found in environment. Generated new key. "
                "Set VOICEPAL_MASTER_KEY environment variable to persist encryption."
            )
        
        return master_key
    
    def _create_fernet(self, master_key: str) -> Fernet:
        """Create Fernet instance from master key."""
        try:
            # If master_key is already a valid Fernet key
            return Fernet(master_key.encode())
        except Exception:
            # Derive key from password
            salt = b'voicepal_salt_2024'  # In production, use random salt per installation
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000,
            )
            key = base64.urlsafe_b64encode(kdf.derive(master_key.encode()))
            return Fernet(key)
    
    def encrypt(self, data: Union[str, bytes]) -> str:
        """
        Encrypt data.
        
        Args:
            data: Data to encrypt
            
        Returns:
            Encrypted data as base64 string
        """
        try:
            if isinstance(data, str):
                data = data.encode('utf-8')
            
            encrypted = self.fernet.encrypt(data)
            return base64.urlsafe_b64encode(encrypted).decode('utf-8')
        except Exception as e:
            logger.error(f"Encryption failed: {e}")
            raise
    
    def decrypt(self, encrypted_data: str) -> str:
        """
        Decrypt data.
        
        Args:
            encrypted_data: Encrypted data as base64 string
            
        Returns:
            Decrypted data as string
        """
        try:
            encrypted_bytes = base64.urlsafe_b64decode(encrypted_data.encode('utf-8'))
            decrypted = self.fernet.decrypt(encrypted_bytes)
            return decrypted.decode('utf-8')
        except Exception as e:
            logger.error(f"Decryption failed: {e}")
            raise
    
    def encrypt_dict(self, data: Dict[str, Any], fields_to_encrypt: list) -> Dict[str, Any]:
        """
        Encrypt specific fields in a dictionary.
        
        Args:
            data: Dictionary to encrypt
            fields_to_encrypt: List of field names to encrypt
            
        Returns:
            Dictionary with encrypted fields
        """
        encrypted_data = data.copy()
        
        for field in fields_to_encrypt:
            if field in encrypted_data and encrypted_data[field] is not None:
                encrypted_data[field] = self.encrypt(str(encrypted_data[field]))
        
        return encrypted_data
    
    def decrypt_dict(self, data: Dict[str, Any], fields_to_decrypt: list) -> Dict[str, Any]:
        """
        Decrypt specific fields in a dictionary.
        
        Args:
            data: Dictionary to decrypt
            fields_to_decrypt: List of field names to decrypt
            
        Returns:
            Dictionary with decrypted fields
        """
        decrypted_data = data.copy()
        
        for field in fields_to_decrypt:
            if field in decrypted_data and decrypted_data[field] is not None:
                try:
                    decrypted_data[field] = self.decrypt(decrypted_data[field])
                except Exception as e:
                    logger.warning(f"Failed to decrypt field {field}: {e}")
                    # Keep original value if decryption fails
        
        return decrypted_data

class FieldEncryption:
    """Field-level encryption for database models."""
    
    def __init__(self, encryption: DataEncryption):
        """
        Initialize field encryption.
        
        Args:
            encryption: DataEncryption instance
        """
        self.encryption = encryption
        
        # Define which fields should be encrypted
        self.encrypted_fields = {
            'users': ['email', 'phone_number'],
            'conversations': ['sensitive_data'],
            'messages': ['content'],  # Only if contains sensitive info
            'user_preferences': ['api_keys'],
            'voice_recordings': ['file_path']  # Encrypt file paths for privacy
        }
    
    def encrypt_model_data(self, table_name: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Encrypt model data based on table configuration.
        
        Args:
            table_name: Name of the database table
            data: Data to encrypt
            
        Returns:
            Data with encrypted fields
        """
        fields_to_encrypt = self.encrypted_fields.get(table_name, [])
        if not fields_to_encrypt:
            return data
        
        return self.encryption.encrypt_dict(data, fields_to_encrypt)
    
    def decrypt_model_data(self, table_name: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Decrypt model data based on table configuration.
        
        Args:
            table_name: Name of the database table
            data: Data to decrypt
            
        Returns:
            Data with decrypted fields
        """
        fields_to_decrypt = self.encrypted_fields.get(table_name, [])
        if not fields_to_decrypt:
            return data
        
        return self.encryption.decrypt_dict(data, fields_to_decrypt)
    
    def is_field_encrypted(self, table_name: str, field_name: str) -> bool:
        """
        Check if a field is configured for encryption.
        
        Args:
            table_name: Name of the database table
            field_name: Name of the field
            
        Returns:
            True if field should be encrypted, False otherwise
        """
        return field_name in self.encrypted_fields.get(table_name, [])

def create_encryption_key() -> str:
    """
    Create a new encryption key.
    
    Returns:
        New encryption key as string
    """
    return Fernet.generate_key().decode()

def validate_encryption_key(key: str) -> bool:
    """
    Validate an encryption key.
    
    Args:
        key: Encryption key to validate
        
    Returns:
        True if key is valid, False otherwise
    """
    try:
        Fernet(key.encode())
        return True
    except Exception:
        return False
