"""
Payment integration for VoicePal.

This module provides functions to integrate the payment system into the main bot.
"""

import logging
from typing import Dict, Any, Optional

from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import Context<PERSON>ypes, CommandHandler, CallbackQueryHandler, PreCheckoutQueryHandler, MessageHandler, filters

from bot.payment.telegram_stars_payment import TelegramStarsPayment
from bot.handlers.payment_ui_handlers import payment_menu_command, handle_payment_callback, cancel_subscription_callback
from bot.database.extensions.payment import extend_database_for_payment
from bot.database.extensions.payment_logging import extend_database_for_payment_logging
from bot.payment.payment_error_handler import PaymentError, PaymentErrorType, handle_payment_error, log_payment_attempt

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

def integrate_telegram_stars_payment(bot_instance) -> bool:
    """
    Integrate Telegram Stars payment system into the bot.

    Args:
        bot_instance: VoicePalBot instance

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Extend database with payment methods and logging
        extend_database_for_payment(bot_instance.database)
        extend_database_for_payment_logging(bot_instance.database)

        # Get telegram config
        telegram_config = bot_instance.config_manager.get_telegram_config()

        # Get provider token from config
        provider_token = telegram_config.get("payment_provider_token", "")

        # Validate provider token
        if not provider_token:
            logger.warning("No provider token found in configuration for Telegram Stars payment")
            logger.info("For digital goods with Telegram Stars, a provider token may not be required")
            # Set a flag to indicate we're using digital goods mode (no provider token)
            digital_goods_mode = True
        else:
            logger.info("Using provider token for Telegram Stars payment")
            digital_goods_mode = False

        # Check if already using Telegram Stars payment
        if not isinstance(bot_instance.payment_system, TelegramStarsPayment):
            # Create Telegram Stars payment instance with config manager and provider token
            payment_system = TelegramStarsPayment(
                database=bot_instance.database,
                config_manager=bot_instance.config_manager,
                provider_token=provider_token,
                digital_goods_mode=digital_goods_mode
            )

            # Replace existing payment system with Telegram Stars payment
            bot_instance.payment_system = payment_system
            logger.info("Replaced existing payment system with Telegram Stars payment")
        else:
            # Check if provider token is set
            if bot_instance.payment_system.provider_token:
                logger.info("Already using Telegram Stars payment system with provider token")
            else:
                logger.info("Already using Telegram Stars payment system for digital goods (no provider token)")

        # Update feature registry to indicate Telegram Stars payment is enabled
        if hasattr(bot_instance, 'feature_registry'):
            bot_instance.feature_registry.register_feature(
                "telegram_stars_payment",
                True,
                "Telegram Stars payment system for purchasing credits"
            )

        # Register payment handlers
        _register_payment_handlers(bot_instance)

        logger.info("Telegram Stars payment system integrated successfully")
        return True
    except Exception as e:
        logger.error(f"Error integrating Telegram Stars payment system: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

# Stripe payment integration removed - using Telegram Stars only

def _register_payment_handlers(bot_instance) -> None:
    """
    Register payment handlers.

    Args:
        bot_instance: VoicePalBot instance
    """
    # Add buy command handler
    bot_instance.application.add_handler(CommandHandler("buy", buy_command))

    # Add payment menu command handler
    bot_instance.application.add_handler(CommandHandler("payment", payment_menu_command))

    # Add pre-checkout query handler
    bot_instance.application.add_handler(
        PreCheckoutQueryHandler(pre_checkout_callback)
    )

    # Add successful payment handler
    bot_instance.application.add_handler(
        MessageHandler(filters.SUCCESSFUL_PAYMENT, successful_payment_callback)
    )

    # Update callback query handler to handle payment callbacks
    _update_callback_query_handler(bot_instance)

    logger.info("Payment handlers registered")

def _update_callback_query_handler(bot_instance) -> None:
    """
    Update callback query handler to handle payment callbacks.

    Args:
        bot_instance: VoicePalBot instance
    """
    # Store original handle_callback_query method
    original_handle_callback_query = bot_instance.handle_callback_query

    # Create new handle_callback_query method
    async def enhanced_handle_callback_query(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Enhanced callback query handler with payment support."""
        query = update.callback_query
        callback_data = query.data
        user_id = query.from_user.id

        # Handle buy credits callbacks
        if callback_data.startswith("buy_credits_"):
            try:
                # Make sure bot instance is in context
                context.application.bot_data["bot_instance"] = bot_instance
                context.bot_data["bot_instance"] = bot_instance

                # Check if provider token is set
                if bot_instance.payment_system.provider_token:
                    logger.info("Using Telegram Stars payment with provider token in callback handler")
                else:
                    logger.info("Using Telegram Stars for digital goods payment in callback handler (no provider token)")

                # Extract package ID
                package_id = callback_data.replace("buy_credits_", "")
                logger.info(f"Starting payment for package {package_id} for user {user_id}")

                # Start payment
                await bot_instance.payment_system.start_payment(update, context, package_id)
                return
            except Exception as e:
                logger.error(f"Error handling buy credits callback: {e}")
                import traceback
                logger.error(traceback.format_exc())
                await query.message.reply_text(
                    "Sorry, there was an error processing your payment request. Please try again later."
                )
                return

        # Handle generic buy credits callback
        if callback_data == "buy_credits":
            try:
                # Show credit packages
                await buy_command(update, context)
                return
            except Exception as e:
                logger.error(f"Error handling buy credits callback: {e}")
                await query.message.reply_text(
                    "Sorry, there was an error showing credit packages. Please try again later."
                )
                return

        # Handle payment menu callback
        if callback_data == "payment_menu":
            try:
                # Show payment menu
                await payment_menu_command(update, context)
                return
            except Exception as e:
                logger.error(f"Error handling payment menu callback: {e}")
                await query.message.reply_text(
                    "Sorry, there was an error showing the payment menu. Please try again later."
                )
                return

        # Handle new payment UI callbacks
        payment_response = await handle_payment_callback(update, context)
        if payment_response:
            message_text, reply_markup = payment_response
            await query.message.edit_text(
                message_text,
                reply_markup=reply_markup
            )
            return

        # Handle subscription cancellation callbacks
        subscription_response = await cancel_subscription_callback(update, context)
        if subscription_response:
            message_text, reply_markup = subscription_response
            await query.message.edit_text(
                message_text,
                reply_markup=reply_markup
            )
            return

        # Call original handler for other callbacks
        await original_handle_callback_query(update, context)

    # Replace the method
    bot_instance.handle_callback_query = enhanced_handle_callback_query

    logger.info("Callback query handler updated for payment support")

async def buy_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """
    Handle /buy command.

    Args:
        update: Telegram update
        context: Callback context
    """
    try:
        # Get bot instance from context
        bot_instance = context.application.bot_data.get("bot_instance")
        if not bot_instance:
            # Try to get from context.bot_data
            bot_instance = context.bot_data.get("bot_instance")

        if not bot_instance or not hasattr(bot_instance, "payment_system"):
            logger.error("Bot instance or payment system not found in context")
            if update.message:
                await update.message.reply_text(
                    "Sorry, the payment system is not available at the moment."
                )
            elif update.callback_query:
                await update.callback_query.message.reply_text(
                    "Sorry, the payment system is not available at the moment."
                )
            return

        # Check if provider token is set
        if bot_instance.payment_system.provider_token:
            logger.info("Using Telegram Stars payment with provider token in buy_command")
        else:
            logger.info("Using Telegram Stars for digital goods payment in buy_command (no provider token)")

        # Get user ID
        user_id = update.effective_user.id

        # Get current credits
        current_credits = bot_instance.database.get_user_credits(user_id)

        # Create message
        message = (
            f"💰 *Buy Credits* 💳\n\n"
            f"Your current balance: *{current_credits} credits* ✨\n\n"
            f"Credits are used for conversations with VoicePal:\n"
            f"• 📝 Text messages cost 1 credit\n"
            f"• 🎤 Voice messages cost 3 credits\n\n"
            f"Choose a package to purchase 🛒:"
        )

        # Get payment keyboard
        reply_markup = bot_instance.payment_system.get_payment_keyboard()

        # Send message
        if update.message:
            await update.message.reply_text(
                message,
                reply_markup=reply_markup,
                parse_mode="Markdown"
            )
        elif update.callback_query:
            await update.callback_query.message.edit_text(
                message,
                reply_markup=reply_markup,
                parse_mode="Markdown"
            )

        logger.info(f"Displayed credit packages for user {user_id}")
    except Exception as e:
        logger.error(f"Error in buy_command: {e}")
        import traceback
        logger.error(traceback.format_exc())

        # Send error message
        try:
            if update.message:
                await update.message.reply_text(
                    "Sorry, there was an error processing your request. Please try again later."
                )
            elif update.callback_query:
                await update.callback_query.message.reply_text(
                    "Sorry, there was an error processing your request. Please try again later."
                )
        except Exception:
            pass

async def pre_checkout_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """
    Handle pre-checkout queries with improved error handling.

    Args:
        update: Telegram update
        context: Callback context
    """
    query = update.pre_checkout_query
    user_id = query.from_user.id

    # Extract package info from payload for logging
    try:
        payload_parts = query.invoice_payload.split('-')
        package_id = payload_parts[1] if len(payload_parts) > 1 else "unknown"
    except:
        package_id = "unknown"

    try:
        # Get bot instance from context
        bot_instance = context.application.bot_data.get("bot_instance")
        if not bot_instance:
            # Try to get from context.bot_data
            bot_instance = context.bot_data.get("bot_instance")

        if not bot_instance or not hasattr(bot_instance, "payment_system"):
            error = PaymentError(
                PaymentErrorType.PAYMENT_PROCESSING_ERROR,
                "Bot instance or payment system not found in context",
                {"user_id": user_id, "payload": query.invoice_payload}
            )
            logger.error(f"Payment system not available: {error.message}")

            # Log the error
            log_payment_attempt(
                database=context.bot_data.get("database", None),
                user_id=user_id,
                package_id=package_id,
                status="pre_checkout_failed",
                error=error
            )

            await query.answer(
                ok=False,
                error_message="Sorry, the payment system is not available at the moment."
            )
            return

        # Make sure bot instance is in context for the payment system
        context.application.bot_data["bot_instance"] = bot_instance
        context.bot_data["bot_instance"] = bot_instance

        # Log the pre-checkout attempt
        log_payment_attempt(
            database=bot_instance.database,
            user_id=user_id,
            package_id=package_id,
            status="pre_checkout_started"
        )

        # Log pre-checkout query details
        logger.info(f"Pre-checkout query: {query.invoice_payload} from user {user_id}")

        # Forward to payment system
        await bot_instance.payment_system.precheckout_callback(update, context)

        # Log successful pre-checkout
        log_payment_attempt(
            database=bot_instance.database,
            user_id=user_id,
            package_id=package_id,
            status="pre_checkout_completed"
        )

        logger.info(f"Pre-checkout callback processed for user {user_id}")
    except Exception as e:
        # Handle unexpected errors
        logger.error(f"Error in pre_checkout_callback: {e}")
        import traceback
        logger.error(traceback.format_exc())

        # Generate user-friendly error message
        user_message, error_details = handle_payment_error(e, user_id)

        # Answer pre-checkout query with error
        try:
            await query.answer(
                ok=False,
                error_message=user_message
            )
        except Exception as answer_error:
            logger.error(f"Error answering pre-checkout query: {answer_error}")

        # Try to log the error
        try:
            log_payment_attempt(
                database=context.bot_data.get("database", None),
                user_id=user_id,
                package_id=package_id,
                status="pre_checkout_error",
                error=e
            )
        except Exception as log_error:
            logger.error(f"Error logging payment error: {log_error}")

async def successful_payment_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """
    Handle successful payments with improved error handling.

    Args:
        update: Telegram update
        context: Callback context
    """
    payment = update.message.successful_payment
    user_id = update.effective_user.id

    # Extract package info from payload for logging
    try:
        payload_parts = payment.invoice_payload.split('-')
        package_id = payload_parts[1] if len(payload_parts) > 1 else "unknown"
    except:
        package_id = "unknown"

    try:
        # Get bot instance from context
        bot_instance = context.application.bot_data.get("bot_instance")
        if not bot_instance:
            # Try to get from context.bot_data
            bot_instance = context.bot_data.get("bot_instance")

        if not bot_instance or not hasattr(bot_instance, "payment_system"):
            error = PaymentError(
                PaymentErrorType.PAYMENT_PROCESSING_ERROR,
                "Bot instance or payment system not found in context",
                {"user_id": user_id, "payload": payment.invoice_payload}
            )
            logger.error(f"Payment system not available: {error.message}")

            # Log the error
            log_payment_attempt(
                database=context.bot_data.get("database", None),
                user_id=user_id,
                package_id=package_id,
                status="payment_failed",
                error=error
            )

            await update.message.reply_text(
                "Thank you for your payment! However, there was an error processing it. "
                "Please contact support with your payment ID: " + payment.telegram_payment_charge_id
            )
            return

        # Make sure bot instance is in context for the payment system
        context.application.bot_data["bot_instance"] = bot_instance
        context.bot_data["bot_instance"] = bot_instance

        # Log the payment attempt
        log_payment_attempt(
            database=bot_instance.database,
            user_id=user_id,
            package_id=package_id,
            status="payment_processing_started"
        )

        # Log successful payment details
        logger.info(f"Processing payment: {payment.invoice_payload} from user {user_id}")

        # Forward to payment system
        await bot_instance.payment_system.successful_payment_callback(update, context)

        # Log successful payment
        log_payment_attempt(
            database=bot_instance.database,
            user_id=user_id,
            package_id=package_id,
            status="payment_completed"
        )

        logger.info(f"Payment successfully processed for user {user_id}")
    except Exception as e:
        # Handle unexpected errors
        logger.error(f"Error in successful_payment_callback: {e}")
        import traceback
        logger.error(traceback.format_exc())

        # Generate user-friendly error message
        user_message, error_details = handle_payment_error(e, user_id)

        # Include payment ID in the error message for support reference
        support_message = (
            f"{user_message}\n\n"
            f"Please contact support with your payment ID: {payment.telegram_payment_charge_id}"
        )

        # Send error message to user
        try:
            await update.message.reply_text(support_message)
        except Exception as msg_error:
            logger.error(f"Error sending payment error message: {msg_error}")

        # Try to log the error
        try:
            log_payment_attempt(
                database=context.bot_data.get("database", None),
                user_id=user_id,
                package_id=package_id,
                status="payment_error",
                error=e
            )
        except Exception as log_error:
            logger.error(f"Error logging payment error: {log_error}")

def create_credits_keyboard(user_id: int, database) -> InlineKeyboardMarkup:
    """
    Create keyboard for credits command.

    Args:
        user_id: User ID
        database: Database instance

    Returns:
        InlineKeyboardMarkup with credits information
    """
    # Create keyboard
    keyboard = [
        [InlineKeyboardButton("💰 Buy Credits & Subscriptions", callback_data="payment_menu")],
        [InlineKeyboardButton("📊 Transaction History", callback_data="show_transactions")],
        [InlineKeyboardButton("🔙 Back to Menu", callback_data="back_to_main")]
    ]

    return InlineKeyboardMarkup(keyboard)

async def credits_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """
    Handle /credits command.

    Args:
        update: Telegram update
        context: Callback context
    """
    bot_instance = context.application.bot_data.get("bot_instance")
    if not bot_instance:
        await update.message.reply_text(
            "Sorry, the credits system is not available at the moment."
        )
        return

    # Get user ID
    user_id = update.effective_user.id

    # Get current credits
    current_credits = bot_instance.database.get_user_credits(user_id)

    # Create message
    message = (
        f"💰 *Credits Balance* 💰\n\n"
        f"Your current balance: *{current_credits} credits* ✨\n\n"
        f"Credits are used for conversations with VoicePal:\n"
        f"• 📝 Text messages: 1 credit\n"
        f"• 🎤 Voice messages: 3 credits\n\n"
        f"You can buy one-time credit packages or subscribe to a monthly/yearly plan.\n\n"
        f"Use the buttons below to manage your credits:"
    )

    # Get credits keyboard
    reply_markup = create_credits_keyboard(user_id, bot_instance.database)

    # Send message
    await update.message.reply_text(
        message,
        reply_markup=reply_markup,
        parse_mode="Markdown"
    )
