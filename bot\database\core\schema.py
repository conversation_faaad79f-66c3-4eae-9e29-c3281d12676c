"""
Database schema management for VoicePal.

This module handles database schema definition, validation, and migration.
"""

import os
import logging
import sqlite3
import json
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
from datetime import datetime

from bot.database.core.exceptions import (
    DatabaseSchemaError,
    DatabaseMigrationError
)
from bot.database.core.connection import DatabaseConnection

# Set up logging
logger = logging.getLogger(__name__)

# Schema version
CURRENT_SCHEMA_VERSION = 1

# Schema definitions
SCHEMA_DEFINITIONS = {
    "users": """
        CREATE TABLE IF NOT EXISTS users (
            user_id TEXT PRIMARY KEY,
            username TEXT,
            first_name TEXT,
            last_name TEXT,
            language_code TEXT DEFAULT 'en',
            credits INTEGER DEFAULT 100,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_interaction TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            is_active BOOLEAN DEFAULT 1,
            personality TEXT DEFAULT 'friendly'
        )
    """,
    
    "conversations": """
        CREATE TABLE IF NOT EXISTS conversations (
            conversation_id TEXT PRIMARY KEY,
            user_id TEXT NOT NULL,
            title TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            is_active BOOLEAN DEFAULT 1,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
        )
    """,
    
    "messages": """
        CREATE TABLE IF NOT EXISTS messages (
            message_id TEXT PRIMARY KEY,
            conversation_id TEXT NOT NULL,
            user_id TEXT NOT NULL,
            content TEXT NOT NULL,
            role TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (conversation_id) REFERENCES conversations(conversation_id) ON DELETE CASCADE,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
        )
    """,
    
    "transactions": """
        CREATE TABLE IF NOT EXISTS transactions (
            transaction_id TEXT PRIMARY KEY,
            user_id TEXT NOT NULL,
            amount INTEGER NOT NULL,
            type TEXT NOT NULL,
            status TEXT NOT NULL,
            provider TEXT,
            provider_transaction_id TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
        )
    """,
    
    "user_preferences": """
        CREATE TABLE IF NOT EXISTS user_preferences (
            preference_id TEXT PRIMARY KEY,
            user_id TEXT NOT NULL,
            key TEXT NOT NULL,
            value TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
            UNIQUE(user_id, key)
        )
    """,
    
    "schema_migrations": """
        CREATE TABLE IF NOT EXISTS schema_migrations (
            version INTEGER PRIMARY KEY,
            applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            description TEXT
        )
    """
}

# Indexes
SCHEMA_INDEXES = [
    "CREATE INDEX IF NOT EXISTS idx_conversations_user_id ON conversations(user_id)",
    "CREATE INDEX IF NOT EXISTS idx_messages_conversation_id ON messages(conversation_id)",
    "CREATE INDEX IF NOT EXISTS idx_messages_user_id ON messages(user_id)",
    "CREATE INDEX IF NOT EXISTS idx_transactions_user_id ON transactions(user_id)",
    "CREATE INDEX IF NOT EXISTS idx_user_preferences_user_id ON user_preferences(user_id)"
]

# Migrations
MIGRATIONS = [
    {
        "version": 1,
        "description": "Initial schema",
        "up": [],  # No up migration for initial schema
        "down": []  # No down migration for initial schema
    }
]

class SchemaManager:
    """Manages database schema and migrations."""
    
    def __init__(self, db_connection: DatabaseConnection):
        """Initialize schema manager.
        
        Args:
            db_connection: Database connection
        """
        self.conn = db_connection
        
    def initialize_schema(self) -> None:
        """Initialize database schema.
        
        Creates tables and indexes if they don't exist.
        
        Raises:
            DatabaseSchemaError: If schema initialization fails
        """
        try:
            # Create tables
            for table_name, create_statement in SCHEMA_DEFINITIONS.items():
                self.conn.execute(create_statement)
            
            # Create indexes
            for index_statement in SCHEMA_INDEXES:
                self.conn.execute(index_statement)
            
            # Check if schema_migrations table is empty
            cursor = self.conn.execute("SELECT COUNT(*) FROM schema_migrations")
            count = cursor.fetchone()[0]
            
            if count == 0:
                # Record initial schema version
                self.conn.execute(
                    "INSERT INTO schema_migrations (version, description) VALUES (?, ?)",
                    (1, "Initial schema")
                )
                self.conn.commit()
                
            logger.info("Database schema initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize schema: {e}")
            raise DatabaseSchemaError(f"Failed to initialize schema: {e}") from e
    
    def get_current_version(self) -> int:
        """Get current schema version.
        
        Returns:
            Current schema version
            
        Raises:
            DatabaseSchemaError: If version retrieval fails
        """
        try:
            cursor = self.conn.execute(
                "SELECT MAX(version) FROM schema_migrations"
            )
            version = cursor.fetchone()[0]
            return version or 0
        except Exception as e:
            logger.error(f"Failed to get current schema version: {e}")
            raise DatabaseSchemaError(f"Failed to get current schema version: {e}") from e
    
    def migrate(self, target_version: Optional[int] = None) -> None:
        """Migrate database schema to target version.
        
        Args:
            target_version: Target schema version (default: latest)
            
        Raises:
            DatabaseMigrationError: If migration fails
        """
        try:
            current_version = self.get_current_version()
            target_version = target_version or CURRENT_SCHEMA_VERSION
            
            logger.info(f"Current schema version: {current_version}")
            logger.info(f"Target schema version: {target_version}")
            
            if current_version == target_version:
                logger.info("Database schema is up to date")
                return
            
            with self.conn.transaction():
                if current_version < target_version:
                    # Migrate up
                    for migration in MIGRATIONS:
                        version = migration["version"]
                        if current_version < version <= target_version:
                            logger.info(f"Applying migration {version}: {migration['description']}")
                            
                            for statement in migration["up"]:
                                self.conn.execute(statement)
                            
                            self.conn.execute(
                                "INSERT INTO schema_migrations (version, description) VALUES (?, ?)",
                                (version, migration["description"])
                            )
                else:
                    # Migrate down
                    for migration in reversed(MIGRATIONS):
                        version = migration["version"]
                        if target_version < version <= current_version:
                            logger.info(f"Reverting migration {version}: {migration['description']}")
                            
                            for statement in migration["down"]:
                                self.conn.execute(statement)
                            
                            self.conn.execute(
                                "DELETE FROM schema_migrations WHERE version = ?",
                                (version,)
                            )
            
            logger.info(f"Database migrated successfully to version {target_version}")
        except Exception as e:
            logger.error(f"Migration failed: {e}")
            raise DatabaseMigrationError(f"Migration failed: {e}") from e
    
    def validate_schema(self) -> bool:
        """Validate database schema.
        
        Returns:
            True if schema is valid, False otherwise
            
        Raises:
            DatabaseSchemaError: If validation fails
        """
        try:
            # Get list of tables in database
            cursor = self.conn.execute(
                "SELECT name FROM sqlite_master WHERE type='table'"
            )
            tables = [row[0] for row in cursor.fetchall()]
            
            # Check if all required tables exist
            for table_name in SCHEMA_DEFINITIONS.keys():
                if table_name not in tables:
                    logger.warning(f"Table {table_name} does not exist")
                    return False
            
            # Check schema version
            current_version = self.get_current_version()
            if current_version != CURRENT_SCHEMA_VERSION:
                logger.warning(f"Schema version mismatch: {current_version} != {CURRENT_SCHEMA_VERSION}")
                return False
            
            logger.info("Database schema is valid")
            return True
        except Exception as e:
            logger.error(f"Schema validation failed: {e}")
            raise DatabaseSchemaError(f"Schema validation failed: {e}") from e
