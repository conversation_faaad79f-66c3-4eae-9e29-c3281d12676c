#!/usr/bin/env python3
"""
VoicePal Bot - Production Runner
===============================

Optimized production runner for VoicePal Telegram bot.
Handles graceful startup, shutdown, and error recovery.

Usage:
    python run.py              # Run in polling mode (local development)
    python run.py --webhook    # Run in webhook mode (production)
    python run.py --health     # Health check only
"""

import sys
import os
import asyncio
import signal
import logging
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from bot.main import VoicePalBot

# Import centralized logging configuration
from bot.core.logging_config import configure_logging, get_logger

# Configure logging for production
configure_logging(
    environment="production",
    log_file='voicepal.log',
    log_to_console=True
)
logger = get_logger(__name__)

class VoicePalRunner:
    """Production runner for VoicePal bot with graceful shutdown."""

    def __init__(self):
        self.bot = None
        self.running = False

    async def health_check(self) -> bool:
        """Perform health check on bot systems."""
        try:
            logger.info("🔍 Performing health check...")

            # Initialize bot
            self.bot = VoicePalBot()

            # Check database connection
            if not self.bot.database.conn:
                logger.error("❌ Database connection failed")
                return False

            # Check API providers
            if not self.bot.ai_provider:
                logger.error("❌ AI provider not initialized")
                return False

            if not self.bot.stt_provider:
                logger.error("❌ STT provider not initialized")
                return False

            if not self.bot.tts_provider:
                logger.error("❌ TTS provider not initialized")
                return False

            # Check payment system
            if not self.bot.payment_system:
                logger.error("❌ Payment system not initialized")
                return False

            logger.info("✅ Health check passed - all systems operational")
            return True

        except Exception as e:
            logger.error(f"❌ Health check failed: {e}")
            return False
        finally:
            if self.bot:
                self.bot.database.close()

    def setup_signal_handlers(self):
        """Setup graceful shutdown signal handlers."""
        def signal_handler(signum, _frame):
            logger.info(f"📡 Received signal {signum}, initiating graceful shutdown...")
            self.running = False

        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

    async def run_polling(self):
        """Run bot in polling mode (for local development)."""
        try:
            logger.info("🚀 Starting VoicePal bot in polling mode...")

            # Initialize bot
            self.bot = VoicePalBot()
            self.running = True

            # Setup signal handlers
            self.setup_signal_handlers()

            # Start polling
            logger.info("📡 Bot is now polling for updates...")

            # Use start_polling instead of run_polling to avoid event loop issues
            await self.bot.application.initialize()
            await self.bot.application.start()
            await self.bot.application.updater.start_polling(
                poll_interval=1.0,
                timeout=10,
                bootstrap_retries=3,
                read_timeout=30,
                write_timeout=30,
                connect_timeout=30,
                pool_timeout=30
            )

            # Keep running until stopped
            while self.running:
                await asyncio.sleep(1)

        except Exception as e:
            logger.error(f"❌ Error in polling mode: {e}")
            raise
        finally:
            await self.cleanup()

    async def run_webhook(self):
        """Run bot in webhook mode (for production deployment)."""
        try:
            logger.info("🚀 Starting VoicePal bot in webhook mode...")

            # Initialize bot
            self.bot = VoicePalBot()
            self.running = True

            # Setup signal handlers
            self.setup_signal_handlers()

            # Get webhook configuration
            webhook_url = os.environ.get("WEBHOOK_URL")
            port = int(os.environ.get("PORT", 8443))

            if not webhook_url:
                # Auto-detect webhook URL for common platforms
                if os.environ.get("RENDER"):
                    service_name = os.environ.get("RENDER_SERVICE_NAME", "voicepal-bot")
                    webhook_url = f"https://{service_name}.onrender.com/webhook"
                elif os.environ.get("HEROKU"):
                    app_name = os.environ.get("HEROKU_APP_NAME", "voicepal-bot")
                    webhook_url = f"https://{app_name}.herokuapp.com/webhook"
                else:
                    raise ValueError("WEBHOOK_URL environment variable is required for webhook mode")

            logger.info(f"📡 Setting webhook: {webhook_url}")
            logger.info(f"🌐 Listening on port: {port}")

            # Start webhook
            await self.bot.application.run_webhook(
                listen="0.0.0.0",
                port=port,
                url_path="/webhook",
                webhook_url=webhook_url,
                bootstrap_retries=3,
                read_timeout=30,
                write_timeout=30,
                connect_timeout=30,
                pool_timeout=30
            )

        except Exception as e:
            logger.error(f"❌ Error in webhook mode: {e}")
            raise
        finally:
            await self.cleanup()

    async def cleanup(self):
        """Cleanup resources on shutdown."""
        try:
            logger.info("🧹 Cleaning up resources...")

            if self.bot:
                # Stop updater first
                if hasattr(self.bot, 'application') and self.bot.application:
                    if hasattr(self.bot.application, 'updater') and self.bot.application.updater:
                        await self.bot.application.updater.stop()
                        logger.info("🛑 Updater stopped")

                    # Stop application
                    await self.bot.application.stop()
                    logger.info("🛑 Bot application stopped")

                    # Shutdown application
                    await self.bot.application.shutdown()
                    logger.info("🛑 Bot application shutdown")

                # Close database connection
                if hasattr(self.bot, 'database') and self.bot.database:
                    self.bot.database.close()
                    logger.info("📦 Database connection closed")

            logger.info("✅ Cleanup completed")

        except Exception as e:
            logger.error(f"❌ Error during cleanup: {e}")

async def main():
    """Main entry point."""
    runner = VoicePalRunner()

    # Parse command line arguments
    args = sys.argv[1:]

    try:
        if "--health" in args:
            # Health check mode
            success = await runner.health_check()
            sys.exit(0 if success else 1)

        elif "--webhook" in args:
            # Webhook mode (production)
            await runner.run_webhook()

        else:
            # Polling mode (default/development)
            await runner.run_polling()

    except KeyboardInterrupt:
        logger.info("👋 Shutdown requested by user")
    except Exception as e:
        logger.error(f"💥 Fatal error: {e}")
        import traceback
        logger.error(traceback.format_exc())
        sys.exit(1)
    finally:
        logger.info("🏁 VoicePal bot shutdown complete")

if __name__ == "__main__":
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ is required")
        sys.exit(1)

    # Print startup banner
    print("🎤 VoicePal Bot - Production Ready")
    print("=" * 40)

    # Run the bot with proper event loop handling
    try:
        asyncio.run(main())
    except RuntimeError as e:
        if "Cannot close a running event loop" in str(e):
            # This is expected on Windows with certain Python versions
            logger.info("Event loop closed normally")
        else:
            raise
