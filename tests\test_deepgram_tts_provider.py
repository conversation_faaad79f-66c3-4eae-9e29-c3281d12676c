"""
Test script for the Deepgram TTS provider.

This script tests the functionality of the Deepgram TTS provider,
focusing on the caching system and error handling.
"""

import os
import unittest
import sys
import logging
import tempfile
import time
from pathlib import Path
from datetime import datetime, timedelta
from unittest.mock import MagicMock, patch

# Add the parent directory to the path so we can import the bot modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from bot.providers.tts.deepgram_provider import DeepgramTTSProvider

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class TestDeepgramTTSProvider(unittest.TestCase):
    """Test cases for the DeepgramTTSProvider class."""
    
    def setUp(self):
        """Set up test environment."""
        # Use a test API key
        self.api_key = "test_api_key"
        
        # Create a temporary directory for cache
        self.temp_dir = tempfile.mkdtemp()
        
        # Create provider with short cache TTL for testing
        self.provider = DeepgramTTSProvider(
            api_key=self.api_key,
            voice_id="aura-2-thalia-en",
            cache_ttl=1,  # 1 second TTL for testing
            use_cache=True
        )
        
        # Mock the API initialization
        self.provider.api_initialized = True
    
    def tearDown(self):
        """Clean up after tests."""
        # Clean up temporary directory
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_init_with_cache_options(self):
        """Test initialization with cache options."""
        provider = DeepgramTTSProvider(
            api_key=self.api_key,
            cache_ttl=3600,
            use_cache=True
        )
        
        self.assertEqual(provider.cache_ttl, 3600)
        self.assertTrue(provider.use_cache)
        self.assertIsInstance(provider.cache, dict)
        self.assertIsInstance(provider.cache_timestamps, dict)
        self.assertTrue(os.path.exists(provider.cache_dir))
    
    def test_init_without_cache(self):
        """Test initialization without cache."""
        provider = DeepgramTTSProvider(
            api_key=self.api_key,
            use_cache=False
        )
        
        self.assertFalse(provider.use_cache)
    
    @patch('httpx.post')
    def test_generate_speech_with_cache(self, mock_post):
        """Test generate_speech with caching."""
        # Mock successful API response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.content = b"test audio content"
        mock_post.return_value = mock_response
        
        # First call should use the API
        result1 = self.provider.generate_speech("Hello world")
        
        # Verify API was called
        mock_post.assert_called_once()
        
        # Reset mock
        mock_post.reset_mock()
        
        # Second call with same text should use cache
        result2 = self.provider.generate_speech("Hello world")
        
        # Verify API was not called again
        mock_post.assert_not_called()
        
        # Results should be the same
        self.assertEqual(result1, result2)
    
    @patch('httpx.post')
    def test_cache_expiration(self, mock_post):
        """Test cache expiration."""
        # Mock successful API response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.content = b"test audio content"
        mock_post.return_value = mock_response
        
        # First call should use the API
        self.provider.generate_speech("Hello world")
        
        # Verify API was called
        mock_post.assert_called_once()
        
        # Reset mock
        mock_post.reset_mock()
        
        # Wait for cache to expire (TTL is 1 second)
        time.sleep(1.5)
        
        # Call again with same text
        self.provider.generate_speech("Hello world")
        
        # Verify API was called again after cache expired
        mock_post.assert_called_once()
    
    def test_clean_expired_cache(self):
        """Test cleaning expired cache."""
        # Add items to cache
        self.provider.cache = {
            "test1": "test_path1",
            "test2": "test_path2"
        }
        
        # Add timestamps - one expired, one not
        now = datetime.now()
        self.provider.cache_timestamps = {
            "test1": now - timedelta(seconds=10),  # Expired
            "test2": now  # Not expired
        }
        
        # Clean expired cache
        self.provider._clean_expired_cache()
        
        # Check that expired item was removed
        self.assertNotIn("test1", self.provider.cache)
        self.assertNotIn("test1", self.provider.cache_timestamps)
        
        # Check that non-expired item remains
        self.assertIn("test2", self.provider.cache)
        self.assertIn("test2", self.provider.cache_timestamps)
    
    @patch('httpx.post')
    def test_api_error_handling(self, mock_post):
        """Test API error handling."""
        # Mock failed API response
        mock_response = MagicMock()
        mock_response.status_code = 400
        mock_response.text = "Bad request"
        mock_post.return_value = mock_response
        
        # Mock fallback method
        self.provider._fallback_to_google_tts = MagicMock()
        self.provider._fallback_to_google_tts.return_value = "fallback_path"
        
        # Call generate_speech
        result = self.provider.generate_speech("Hello world")
        
        # Verify fallback was called
        self.provider._fallback_to_google_tts.assert_called_once()
        self.assertEqual(result, "fallback_path")
    
    @patch('httpx.post')
    def test_retry_logic(self, mock_post):
        """Test retry logic for API calls."""
        # Mock responses - first two fail, third succeeds
        mock_response1 = MagicMock()
        mock_response1.status_code = 500
        mock_response1.text = "Server error"
        
        mock_response2 = MagicMock()
        mock_response2.status_code = 500
        mock_response2.text = "Server error"
        
        mock_response3 = MagicMock()
        mock_response3.status_code = 200
        mock_response3.content = b"test audio content"
        
        mock_post.side_effect = [mock_response1, mock_response2, mock_response3]
        
        # Call generate_speech
        result = self.provider.generate_speech("Hello world")
        
        # Verify API was called three times
        self.assertEqual(mock_post.call_count, 3)
        
        # Verify result is not None
        self.assertIsNotNone(result)
    
    def test_get_available_voices(self):
        """Test getting available voices."""
        voices = self.provider.get_available_voices()
        
        # Check that voices exist
        self.assertIsNotNone(voices)
        self.assertIsInstance(voices, dict)
        self.assertGreater(len(voices), 0)
        
        # Check voice structure
        for voice_id, voice in voices.items():
            self.assertIn('name', voice)
            self.assertIn('language', voice)
            self.assertIn('gender', voice)
            self.assertIn('description', voice)

if __name__ == "__main__":
    unittest.main()
