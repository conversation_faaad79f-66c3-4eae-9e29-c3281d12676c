"""
Test script for MoodTracker.

This script tests the enhanced MoodTracker class functionality.
"""

import unittest
from unittest.mock import MagicMock, patch
from datetime import datetime, timedelta
from collections import Counter

from bot.features.mood_tracker import MoodTracker


class TestMoodTracker(unittest.TestCase):
    """Test cases for the MoodTracker class."""

    def setUp(self):
        """Set up test environment."""
        # Create a mock database
        self.mock_db = MagicMock()

        # Create a mock STT provider
        self.mock_stt_provider = MagicMock()

        # Create a mock AI provider
        self.mock_ai_provider = MagicMock()

        # Create a mock config
        self.mock_config = {
            "analysis_frequency": 7
        }

        # Create a MoodTracker instance
        self.mood_tracker = MoodTracker(
            database=self.mock_db,
            stt_provider=self.mock_stt_provider,
            ai_provider=self.mock_ai_provider,
            config=self.mock_config
        )

        # Sample mood history data
        self.sample_mood_history = [
            {
                "id": 1,
                "user_id": 123,
                "sentiment": "positive",
                "confidence": 0.8,
                "source": "text",
                "message_text": "I'm feeling great today!",
                "created_at": (datetime.now() - timedelta(hours=1)).strftime("%Y-%m-%d %H:%M:%S")
            },
            {
                "id": 2,
                "user_id": 123,
                "sentiment": "neutral",
                "confidence": 0.6,
                "source": "voice",
                "message_text": "Just checking in",
                "created_at": (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d %H:%M:%S")
            },
            {
                "id": 3,
                "user_id": 123,
                "sentiment": "negative",
                "confidence": 0.7,
                "source": "text",
                "message_text": "I'm feeling a bit down",
                "created_at": (datetime.now() - timedelta(days=2)).strftime("%Y-%m-%d %H:%M:%S")
            },
            {
                "id": 4,
                "user_id": 123,
                "sentiment": "positive",
                "confidence": 0.9,
                "source": "text",
                "message_text": "That's wonderful news!",
                "created_at": (datetime.now() - timedelta(days=3)).strftime("%Y-%m-%d %H:%M:%S")
            },
            {
                "id": 5,
                "user_id": 123,
                "sentiment": "positive",
                "confidence": 0.8,
                "source": "voice",
                "message_text": "I'm excited about the weekend",
                "created_at": (datetime.now() - timedelta(days=4)).strftime("%Y-%m-%d %H:%M:%S")
            }
        ]

    def test_analyze_text_mood(self):
        """Test analyze_text_mood method."""
        # Mock AI provider's sentiment analysis
        self.mock_ai_provider.supports_feature.return_value = True
        self.mock_ai_provider.analyze_sentiment.return_value = {
            "sentiment": "positive",
            "confidence": 0.8
        }

        # Mock database's add_mood_entry method
        self.mock_db.add_mood_entry.return_value = 1

        # Analyze text mood
        result = self.mood_tracker.analyze_text_mood(123, "I'm feeling great today!")

        # Check that the result is as expected
        self.assertEqual(result["sentiment"], "positive")
        self.assertEqual(result["confidence"], 0.8)
        self.assertEqual(result["source"], "text")
        self.assertEqual(result["message_text"], "I'm feeling great today!")

        # Check that the AI provider's analyze_sentiment method was called
        self.mock_ai_provider.analyze_sentiment.assert_called_once_with("I'm feeling great today!")

        # Check that the database's add_mood_entry method was called
        self.mock_db.add_mood_entry.assert_called_once()

    @patch('bot.features.mood_tracker.MoodTracker.add_mood_entry')
    async def test_analyze_voice_mood(self, mock_add_mood_entry):
        """Test analyze_voice_mood method."""
        # Mock STT provider's transcribe_with_sentiment method
        self.mock_stt_provider.supports_feature.return_value = True
        self.mock_stt_provider.transcribe_with_sentiment.return_value = {
            "transcript": "I'm feeling great today!",
            "sentiment": {
                "sentiment": "positive",
                "confidence": 0.8
            }
        }

        # Mock add_mood_entry method
        mock_add_mood_entry.return_value = True

        # Analyze voice mood
        result = await self.mood_tracker.analyze_voice_mood(123, "test.wav")

        # Check that the result is as expected
        self.assertEqual(result["sentiment"], "positive")
        self.assertEqual(result["confidence"], 0.8)
        self.assertEqual(result["source"], "voice")
        self.assertEqual(result["message_text"], "I'm feeling great today!")

        # Check that the STT provider's transcribe_with_sentiment method was called
        self.mock_stt_provider.transcribe_with_sentiment.assert_called_once_with("test.wav")

        # Check that add_mood_entry was called
        mock_add_mood_entry.assert_called_once()

    def test_get_mood_summary(self):
        """Test get_mood_summary method."""
        # Mock database's get_mood_history method
        self.mock_db.get_mood_history.return_value = self.sample_mood_history

        # Mock AI provider's supports_feature method
        self.mock_ai_provider.supports_feature.return_value = False

        # Get mood summary
        result = self.mood_tracker.get_mood_summary(123)

        # Check that the result is as expected
        self.assertEqual(result["dominant_mood"], "positive")
        self.assertIn("mood_stability", result)
        self.assertIn("mood_trend", result)
        self.assertIn("sentiment_distribution", result)

        # Check that the database's get_mood_history method was called
        self.mock_db.get_mood_history.assert_called_once_with(123, 7)

    def test_get_daily_mood_summary(self):
        """Test get_daily_mood_summary method."""
        # Mock database's get_mood_history method
        self.mock_db.get_mood_history.return_value = [self.sample_mood_history[0]]

        # Get daily mood summary
        result = self.mood_tracker.get_daily_mood_summary(123)

        # Check that the result is as expected
        self.assertEqual(result["status"], "success")
        self.assertEqual(result["dominant_mood"], "positive")
        self.assertEqual(result["entries_count"], 1)
        self.assertEqual(result["most_recent_mood"], "positive")
        self.assertIn("average_confidence", result)
        self.assertIn("sentiment_distribution", result)
        self.assertIn("mood_messages", result)

        # Check that the database's get_mood_history method was called
        self.mock_db.get_mood_history.assert_called_once_with(123, days=1)

    def test_get_weekly_mood_summary(self):
        """Test get_weekly_mood_summary method."""
        # Mock database's get_mood_history method
        self.mock_db.get_mood_history.return_value = self.sample_mood_history

        # Get weekly mood summary
        result = self.mood_tracker.get_weekly_mood_summary(123)

        # Check that the result is as expected
        self.assertEqual(result["status"], "success")
        self.assertEqual(result["overall_dominant_mood"], "positive")
        self.assertIn("mood_trend", result)
        self.assertIn("overall_sentiment_distribution", result)
        self.assertIn("daily_summary", result)

        # Check that the database's get_mood_history method was called
        self.mock_db.get_mood_history.assert_called_once_with(123, days=7)

    def test_get_monthly_mood_summary(self):
        """Test get_monthly_mood_summary method."""
        # Mock database's get_mood_history method
        self.mock_db.get_mood_history.return_value = self.sample_mood_history

        # Get monthly mood summary
        result = self.mood_tracker.get_monthly_mood_summary(123)

        # Check that the result is as expected
        self.assertEqual(result["status"], "success")
        self.assertEqual(result["overall_dominant_mood"], "positive")
        self.assertIn("mood_stability", result)
        self.assertIn("overall_sentiment_distribution", result)
        self.assertIn("weekly_summary", result)

        # Check that the database's get_mood_history method was called
        self.mock_db.get_mood_history.assert_called_once_with(123, days=30)

    def test_generate_mood_insights(self):
        """Test generate_mood_insights method."""
        # Mock database's get_mood_history method
        self.mock_db.get_mood_history.return_value = self.sample_mood_history

        # Mock AI provider's supports_feature method
        self.mock_ai_provider.supports_feature.return_value = False

        # Generate mood insights
        result = self.mood_tracker.generate_mood_insights(123)

        # Check that the result is as expected
        self.assertEqual(result["status"], "success")
        self.assertIn("insights", result)

        # Check that the database's get_mood_history method was called
        self.mock_db.get_mood_history.assert_called_once_with(123, days=30)

    def test_format_mood_summary_text(self):
        """Test format_mood_summary_text method."""
        # Create a sample mood summary
        mood_summary = {
            "status": "success",
            "dominant_mood": "positive",
            "mood_stability": "stable",
            "mood_trend": "improving",
            "sentiment_distribution": {"positive": 3, "neutral": 1, "negative": 1}
        }

        # Format mood summary as text
        result = self.mood_tracker.format_mood_summary_text(mood_summary)

        # Check that the result is as expected
        self.assertIn("Mood Summary", result)
        self.assertIn("positive", result)
        self.assertIn("stable", result)
        self.assertIn("improving", result)

    def test_format_daily_summary_text(self):
        """Test _format_daily_summary_text method."""
        # Create a sample daily mood summary
        daily_summary = {
            "status": "success",
            "date": datetime.now().strftime("%Y-%m-%d"),
            "entries_count": 3,
            "dominant_mood": "positive",
            "most_recent_mood": "positive",
            "most_recent_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "average_confidence": 0.8,
            "sentiment_distribution": {"positive": 2, "neutral": 1},
            "mood_messages": ["I'm feeling great!", "Just checking in", "That's wonderful news!"]
        }

        # Format daily summary as text
        result = self.mood_tracker._format_daily_summary_text(daily_summary)

        # Check that the result is as expected
        self.assertIn("Today's Mood Summary", result)
        self.assertIn("positive", result)
        self.assertIn("3", result)
        self.assertIn("I'm feeling great!", result)

    def test_format_weekly_summary_text(self):
        """Test _format_weekly_summary_text method."""
        # Create a sample weekly mood summary
        weekly_summary = {
            "status": "success",
            "period": "2023-01-01 to 2023-01-07",
            "days_with_entries": 5,
            "total_entries": 10,
            "overall_dominant_mood": "positive",
            "mood_trend": "improving",
            "overall_sentiment_distribution": {"positive": 6, "neutral": 3, "negative": 1},
            "daily_summary": {
                "2023-01-01": {"dominant_mood": "positive", "entries_count": 2},
                "2023-01-02": {"dominant_mood": "neutral", "entries_count": 1},
                "2023-01-03": {"dominant_mood": "positive", "entries_count": 3},
                "2023-01-04": {"dominant_mood": "positive", "entries_count": 2},
                "2023-01-05": {"dominant_mood": "negative", "entries_count": 1}
            }
        }

        # Format weekly summary as text
        result = self.mood_tracker._format_weekly_summary_text(weekly_summary)

        # Check that the result is as expected
        self.assertIn("Weekly Mood Summary", result)
        self.assertIn("positive", result)
        self.assertIn("improving", result)
        self.assertIn("Daily Breakdown", result)

    def test_format_monthly_summary_text(self):
        """Test _format_monthly_summary_text method."""
        # Create a sample monthly mood summary
        monthly_summary = {
            "status": "success",
            "period": "January 2023",
            "weeks_with_entries": 4,
            "total_entries": 20,
            "overall_dominant_mood": "positive",
            "mood_stability": "stable",
            "overall_sentiment_distribution": {"positive": 12, "neutral": 5, "negative": 3},
            "weekly_summary": {
                "January Week 1": {"dominant_mood": "positive", "entries_count": 5},
                "January Week 2": {"dominant_mood": "positive", "entries_count": 6},
                "January Week 3": {"dominant_mood": "neutral", "entries_count": 4},
                "January Week 4": {"dominant_mood": "positive", "entries_count": 5}
            }
        }

        # Format monthly summary as text
        result = self.mood_tracker._format_monthly_summary_text(monthly_summary)

        # Check that the result is as expected
        self.assertIn("Monthly Mood Summary", result)
        self.assertIn("positive", result)
        self.assertIn("stable", result)
        self.assertIn("Weekly Breakdown", result)

    def test_format_insights_text(self):
        """Test _format_insights_text method."""
        # Create a sample mood insights
        mood_insights = {
            "status": "success",
            "sentiment_distribution": {"positive": 60, "neutral": 30, "negative": 10},
            "insights": [
                "You've been feeling quite positive lately. That's great!",
                "You tend to feel most positive on Fridays.",
                "You tend to feel most negative on Mondays."
            ]
        }

        # Format insights as text
        result = self.mood_tracker._format_insights_text(mood_insights)

        # Check that the result is as expected
        self.assertIn("Mood Insights", result)
        self.assertIn("You've been feeling quite positive lately", result)
        self.assertIn("Fridays", result)
        self.assertIn("Mondays", result)


if __name__ == "__main__":
    unittest.main()
