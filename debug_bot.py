"""
Debug script for VoicePal bot.
"""

import os
import logging
import asyncio
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.DEBUG  # Set to DEBUG for more detailed logs
)
logger = logging.getLogger(__name__)

async def main():
    """Debug the VoicePal bot."""
    # Load environment variables
    load_dotenv()
    
    # Check if required environment variables are set
    required_vars = ["TELEGRAM_TOKEN", "DEEPGRAM_API_KEY"]
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        logger.error(f"Missing required environment variables: {', '.join(missing_vars)}")
        logger.error("Please set these variables in the .env file")
        return 1
    
    logger.info("Starting VoicePal bot in debug mode")
    
    try:
        # Import the bot module
        logger.info("Importing bot module...")
        from bot.main import VoicePalBot
        
        # Create bot instance
        logger.info("Creating bot instance...")
        bot = VoicePalBot()
        
        # Initialize bot
        logger.info("Initializing bot...")
        
        # Get telegram token
        telegram_config = bot.config_manager.get_telegram_config()
        token = telegram_config.get("token", "")
        logger.info(f"Using Telegram token: {token[:5]}...{token[-5:]}")
        
        # Check payment system
        if hasattr(bot, "payment_system"):
            logger.info(f"Payment system initialized: {type(bot.payment_system).__name__}")
        else:
            logger.error("Payment system not initialized")
        
        # Run the bot
        logger.info("Running bot...")
        await bot.run()
        
        # Keep the script running
        logger.info("Bot is running. Press Ctrl+C to stop.")
        while True:
            await asyncio.sleep(1)
            
    except ImportError as e:
        logger.error(f"Error importing bot module: {e}")
        return 1
    except Exception as e:
        logger.error(f"Error initializing bot: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
