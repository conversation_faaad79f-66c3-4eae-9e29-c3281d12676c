"""
Provider utilities for VoicePal.

This module provides utility functions and classes for providers, such as rate limiting,
caching, error handling, and other common operations.
"""

import os
import time
import logging
import asyncio
import functools
import hashlib
import json
import tempfile
import re
from typing import Dict, List, Any, Optional, Set, Union, Callable, TypeVar, Generic, Awaitable, cast
from dataclasses import dataclass, field
from pathlib import Path
from datetime import datetime, timedelta
from functools import wraps

from bot.providers.core.exceptions import (
    ProviderError,
    ProviderRateLimitError,
    ProviderTimeoutError
)

# Set up logging
logger = logging.getLogger(__name__)

# Type variables
T = TypeVar('T')
F = TypeVar('F', bound=Callable[..., Any])
AsyncF = TypeVar('AsyncF', bound=Callable[..., Awaitable[Any]])

def get_api_key(provider_name: str, env_var: Optional[str] = None) -> Optional[str]:
    """Get API key for a provider.

    Args:
        provider_name: Provider name
        env_var: Environment variable name (optional)

    Returns:
        API key or None if not found
    """
    # Try specific environment variable if provided
    if env_var and os.getenv(env_var):
        return os.getenv(env_var)

    # Try provider-specific environment variable
    provider_env_var = f"{provider_name.upper()}_API_KEY"
    if os.getenv(provider_env_var):
        return os.getenv(provider_env_var)

    # Try generic API key environment variable
    generic_env_var = "API_KEY"
    if os.getenv(generic_env_var):
        return os.getenv(generic_env_var)

    return None

def create_temp_file(suffix: str = ".tmp") -> str:
    """Create a temporary file.

    Args:
        suffix: File suffix

    Returns:
        Path to temporary file
    """
    temp_file = tempfile.NamedTemporaryFile(suffix=suffix, delete=False)
    temp_path = temp_file.name
    temp_file.close()
    return temp_path

def clean_text_for_tts(text: str) -> str:
    """Clean text for TTS processing.

    Args:
        text: Text to clean

    Returns:
        Cleaned text
    """
    # Remove emoji descriptions like (smile), (laughing), etc.
    text = re.sub(r'\([a-zA-Z]+\)', '', text)

    # Remove other problematic patterns that might be verbalized
    text = re.sub(r':[a-zA-Z_]+:', '', text)  # Remove :emoji_name: format

    # Remove any other special characters that might cause issues
    text = re.sub(r'[^\w\s.,?!;:\-\'"\(\)]+', '', text)

    return text.strip()

def generate_cache_key(prefix: str, data: Any) -> str:
    """Generate a cache key.

    Args:
        prefix: Key prefix
        data: Data to hash

    Returns:
        Cache key
    """
    if isinstance(data, dict):
        data_str = json.dumps(data, sort_keys=True)
    elif not isinstance(data, str):
        data_str = str(data)
    else:
        data_str = data

    key = f"{prefix}_{hashlib.md5(data_str.encode()).hexdigest()}"
    return key

def ensure_directory_exists(directory: Union[str, Path]) -> Path:
    """Ensure a directory exists.

    Args:
        directory: Directory path

    Returns:
        Path object
    """
    if isinstance(directory, str):
        directory = Path(directory)

    os.makedirs(directory, exist_ok=True)
    return directory

def get_file_extension_for_content_type(content_type: str) -> str:
    """Get file extension for a content type.

    Args:
        content_type: Content type

    Returns:
        File extension
    """
    content_type_map = {
        "audio/mpeg": ".mp3",
        "audio/mp3": ".mp3",
        "audio/wav": ".wav",
        "audio/wave": ".wav",
        "audio/x-wav": ".wav",
        "audio/webm": ".webm",
        "audio/ogg": ".ogg",
        "audio/aac": ".aac",
        "audio/flac": ".flac",
        "audio/x-flac": ".flac",
        "image/jpeg": ".jpg",
        "image/png": ".png",
        "image/gif": ".gif",
        "image/webp": ".webp",
        "video/mp4": ".mp4",
        "video/webm": ".webm",
        "application/json": ".json",
        "text/plain": ".txt",
        "text/html": ".html",
    }

    return content_type_map.get(content_type.lower(), ".bin")

def truncate_text(text: str, max_length: int = 100) -> str:
    """Truncate text to a maximum length.

    Args:
        text: Text to truncate
        max_length: Maximum length

    Returns:
        Truncated text
    """
    if len(text) <= max_length:
        return text

    return text[:max_length - 3] + "..."

def parse_language_code(language: str) -> str:
    """Parse and normalize language code.

    Args:
        language: Language code

    Returns:
        Normalized language code
    """
    if not language:
        return "en"

    # Extract primary language code (e.g., "en" from "en-US")
    primary_code = language.split('-')[0].lower()

    # Map some common language codes
    language_map = {
        "eng": "en",
        "english": "en",
        "rus": "ru",
        "russian": "ru",
        "spa": "es",
        "spanish": "es",
        "fre": "fr",
        "french": "fr",
        "ger": "de",
        "german": "de",
        "ita": "it",
        "italian": "it",
        "jpn": "ja",
        "japanese": "ja",
        "chi": "zh",
        "chinese": "zh",
    }

    return language_map.get(primary_code, primary_code)


class RateLimiter:
    """Rate limiter for API calls.

    This class provides rate limiting for API calls to prevent exceeding API rate limits.
    """

    def __init__(self, calls: int, period: float, retry_after: Optional[float] = None):
        """Initialize rate limiter.

        Args:
            calls: Maximum number of calls allowed in the period
            period: Time period in seconds
            retry_after: Time to wait before retrying after rate limit is exceeded (default: period)
        """
        self.calls = calls
        self.period = period
        self.retry_after = retry_after or period
        self.timestamps: List[float] = []
        self.lock = asyncio.Lock()

    async def acquire(self) -> None:
        """Acquire rate limit token.

        Raises:
            ProviderRateLimitError: If rate limit is exceeded
        """
        async with self.lock:
            now = time.time()

            # Remove timestamps older than the period
            self.timestamps = [ts for ts in self.timestamps if now - ts <= self.period]

            # Check if rate limit is exceeded
            if len(self.timestamps) >= self.calls:
                oldest = self.timestamps[0]
                wait_time = self.period - (now - oldest)

                if wait_time > 0:
                    raise ProviderRateLimitError(
                        f"Rate limit exceeded: {self.calls} calls per {self.period} seconds. "
                        f"Please retry after {wait_time:.2f} seconds."
                    )

            # Add current timestamp
            self.timestamps.append(now)

    async def wait(self) -> None:
        """Wait for rate limit token.

        This method waits until a rate limit token is available instead of raising an exception.
        """
        while True:
            try:
                await self.acquire()
                return
            except ProviderRateLimitError:
                # Wait before retrying
                await asyncio.sleep(self.retry_after)


def rate_limit(calls: int, period: float, retry_after: Optional[float] = None) -> Callable[[AsyncF], AsyncF]:
    """Decorator for rate limiting async functions.

    Args:
        calls: Maximum number of calls allowed in the period
        period: Time period in seconds
        retry_after: Time to wait before retrying after rate limit is exceeded (default: period)

    Returns:
        Decorated function
    """
    limiter = RateLimiter(calls, period, retry_after)

    def decorator(func: AsyncF) -> AsyncF:
        @wraps(func)
        async def wrapper(*args: Any, **kwargs: Any) -> Any:
            await limiter.acquire()
            return await func(*args, **kwargs)

        return cast(AsyncF, wrapper)

    return decorator


def retry(max_retries: int, exceptions: List[Exception], retry_delay: float = 1.0, backoff_factor: float = 2.0) -> Callable[[AsyncF], AsyncF]:
    """Decorator for retrying async functions.

    Args:
        max_retries: Maximum number of retries
        exceptions: List of exceptions to retry on
        retry_delay: Initial delay between retries in seconds
        backoff_factor: Factor to increase delay between retries

    Returns:
        Decorated function
    """
    def decorator(func: AsyncF) -> AsyncF:
        @wraps(func)
        async def wrapper(*args: Any, **kwargs: Any) -> Any:
            retries = 0
            delay = retry_delay

            while True:
                try:
                    return await func(*args, **kwargs)
                except tuple(exceptions) as e:
                    retries += 1

                    if retries > max_retries:
                        raise

                    logger.warning(
                        f"Retrying {func.__name__} after {delay:.2f} seconds "
                        f"(retry {retries}/{max_retries}): {e}"
                    )

                    await asyncio.sleep(delay)
                    delay *= backoff_factor

        return cast(AsyncF, wrapper)

    return decorator


def timeout(seconds: float) -> Callable[[AsyncF], AsyncF]:
    """Decorator for adding timeout to async functions.

    Args:
        seconds: Timeout in seconds

    Returns:
        Decorated function
    """
    def decorator(func: AsyncF) -> AsyncF:
        @wraps(func)
        async def wrapper(*args: Any, **kwargs: Any) -> Any:
            try:
                return await asyncio.wait_for(func(*args, **kwargs), timeout=seconds)
            except asyncio.TimeoutError:
                raise ProviderTimeoutError(f"Operation timed out after {seconds} seconds")

        return cast(AsyncF, wrapper)

    return decorator


class Cache:
    """Cache for API responses.

    This class provides caching for API responses to reduce API calls.
    """

    def __init__(self, max_size: int = 100, ttl: Optional[float] = None):
        """Initialize cache.

        Args:
            max_size: Maximum number of items in the cache
            ttl: Time-to-live in seconds (default: None, no expiration)
        """
        self.max_size = max_size
        self.ttl = ttl
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.lock = asyncio.Lock()

    async def get(self, key: str) -> Optional[Any]:
        """Get item from cache.

        Args:
            key: Cache key

        Returns:
            Cached item or None if not found or expired
        """
        async with self.lock:
            if key not in self.cache:
                return None

            item = self.cache[key]

            # Check if item is expired
            if self.ttl is not None and time.time() - item["timestamp"] > self.ttl:
                del self.cache[key]
                return None

            return item["value"]

    async def set(self, key: str, value: Any) -> None:
        """Set item in cache.

        Args:
            key: Cache key
            value: Item value
        """
        async with self.lock:
            # Remove oldest item if cache is full
            if len(self.cache) >= self.max_size and key not in self.cache:
                oldest_key = min(self.cache.keys(), key=lambda k: self.cache[k]["timestamp"])
                del self.cache[oldest_key]

            # Add item to cache
            self.cache[key] = {
                "value": value,
                "timestamp": time.time()
            }

    async def delete(self, key: str) -> None:
        """Delete item from cache.

        Args:
            key: Cache key
        """
        async with self.lock:
            if key in self.cache:
                del self.cache[key]

    async def clear(self) -> None:
        """Clear cache."""
        async with self.lock:
            self.cache.clear()


def cached(cache: Cache, key_func: Optional[Callable[..., str]] = None) -> Callable[[AsyncF], AsyncF]:
    """Decorator for caching async function results.

    Args:
        cache: Cache instance
        key_func: Function to generate cache key from function arguments (default: None, use args and kwargs)

    Returns:
        Decorated function
    """
    def decorator(func: AsyncF) -> AsyncF:
        @wraps(func)
        async def wrapper(*args: Any, **kwargs: Any) -> Any:
            # Generate cache key
            if key_func:
                key = key_func(*args, **kwargs)
            else:
                # Use function name, args, and kwargs as key
                key = f"{func.__name__}:{args}:{kwargs}"

            # Try to get from cache
            cached_value = await cache.get(key)
            if cached_value is not None:
                return cached_value

            # Call function and cache result
            result = await func(*args, **kwargs)
            await cache.set(key, result)

            return result

        return cast(AsyncF, wrapper)

    return decorator


class CircuitBreaker:
    """Circuit breaker for API calls.

    This class implements the circuit breaker pattern to prevent cascading failures.
    """

    def __init__(self, failure_threshold: int, recovery_timeout: float, exceptions: List[Exception]):
        """Initialize circuit breaker.

        Args:
            failure_threshold: Number of failures before opening the circuit
            recovery_timeout: Time in seconds before trying to close the circuit
            exceptions: List of exceptions to count as failures
        """
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.exceptions = tuple(exceptions)
        self.failures = 0
        self.state = "closed"  # closed, open, half-open
        self.last_failure_time = 0.0
        self.lock = asyncio.Lock()

    async def execute(self, func: Callable[..., Awaitable[T]], *args: Any, **kwargs: Any) -> T:
        """Execute function with circuit breaker.

        Args:
            func: Function to execute
            *args: Function arguments
            **kwargs: Function keyword arguments

        Returns:
            Function result

        Raises:
            ProviderError: If circuit is open
            Exception: If function raises an exception
        """
        async with self.lock:
            # Check if circuit is open
            if self.state == "open":
                if time.time() - self.last_failure_time > self.recovery_timeout:
                    # Try to close the circuit
                    self.state = "half-open"
                    logger.info("Circuit breaker state changed from open to half-open")
                else:
                    raise ProviderError(f"Circuit breaker is open. Please retry after {self.recovery_timeout - (time.time() - self.last_failure_time):.2f} seconds")

        try:
            # Execute function
            result = await func(*args, **kwargs)

            # Reset failures if circuit is half-open
            async with self.lock:
                if self.state == "half-open":
                    self.state = "closed"
                    self.failures = 0
                    logger.info("Circuit breaker state changed from half-open to closed")

            return result
        except self.exceptions as e:
            # Increment failures
            async with self.lock:
                self.failures += 1
                self.last_failure_time = time.time()

                # Open circuit if failure threshold is reached
                if self.state == "closed" and self.failures >= self.failure_threshold:
                    self.state = "open"
                    logger.warning(f"Circuit breaker state changed from closed to open after {self.failures} failures")

            raise


def circuit_breaker(failure_threshold: int, recovery_timeout: float, exceptions: List[Exception]) -> Callable[[AsyncF], AsyncF]:
    """Decorator for adding circuit breaker to async functions.

    Args:
        failure_threshold: Number of failures before opening the circuit
        recovery_timeout: Time in seconds before trying to close the circuit
        exceptions: List of exceptions to count as failures

    Returns:
        Decorated function
    """
    breaker = CircuitBreaker(failure_threshold, recovery_timeout, exceptions)

    def decorator(func: AsyncF) -> AsyncF:
        @wraps(func)
        async def wrapper(*args: Any, **kwargs: Any) -> Any:
            return await breaker.execute(func, *args, **kwargs)

        return cast(AsyncF, wrapper)

    return decorator
