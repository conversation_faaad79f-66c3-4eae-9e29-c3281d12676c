"""
Payment logger for VoicePal.

This module provides detailed logging for payment operations.
"""

import logging
import json
import os
from datetime import datetime
from typing import Dict, Any, Optional

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class PaymentLogger:
    """Payment logger for VoicePal."""
    
    def __init__(self, log_dir: str = "logs"):
        """
        Initialize the payment logger.
        
        Args:
            log_dir: Directory to store payment logs
        """
        self.log_dir = log_dir
        
        # Create log directory if it doesn't exist
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        # Create payment log file
        self.payment_log_file = os.path.join(log_dir, "payment.log")
        
        # Configure file handler
        self.file_handler = logging.FileHandler(self.payment_log_file)
        self.file_handler.setLevel(logging.INFO)
        self.file_handler.setFormatter(logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        ))
        
        # Add file handler to logger
        payment_logger = logging.getLogger("payment")
        payment_logger.addHandler(self.file_handler)
        
        logger.info(f"Payment logger initialized with log file: {self.payment_log_file}")
    
    def log_payment_attempt(self, user_id: int, package_id: str, payload: str) -> None:
        """
        Log a payment attempt.
        
        Args:
            user_id: User ID
            package_id: Credit package ID
            payload: Invoice payload
        """
        payment_logger = logging.getLogger("payment")
        payment_logger.info(
            f"Payment attempt: user_id={user_id}, package_id={package_id}, payload={payload}"
        )
    
    def log_payment_success(self, user_id: int, package_id: str, amount: float,
                           credits: int, transaction_id: str) -> None:
        """
        Log a successful payment.
        
        Args:
            user_id: User ID
            package_id: Credit package ID
            amount: Payment amount
            credits: Credits added
            transaction_id: Transaction ID
        """
        payment_logger = logging.getLogger("payment")
        payment_logger.info(
            f"Payment success: user_id={user_id}, package_id={package_id}, "
            f"amount={amount}, credits={credits}, transaction_id={transaction_id}"
        )
    
    def log_payment_error(self, user_id: int, package_id: str, error: str) -> None:
        """
        Log a payment error.
        
        Args:
            user_id: User ID
            package_id: Credit package ID
            error: Error message
        """
        payment_logger = logging.getLogger("payment")
        payment_logger.error(
            f"Payment error: user_id={user_id}, package_id={package_id}, error={error}"
        )
    
    def log_refund_attempt(self, user_id: int, transaction_id: str) -> None:
        """
        Log a refund attempt.
        
        Args:
            user_id: User ID
            transaction_id: Transaction ID
        """
        payment_logger = logging.getLogger("payment")
        payment_logger.info(
            f"Refund attempt: user_id={user_id}, transaction_id={transaction_id}"
        )
    
    def log_refund_success(self, user_id: int, transaction_id: str, amount: float,
                          credits: int) -> None:
        """
        Log a successful refund.
        
        Args:
            user_id: User ID
            transaction_id: Transaction ID
            amount: Refund amount
            credits: Credits deducted
        """
        payment_logger = logging.getLogger("payment")
        payment_logger.info(
            f"Refund success: user_id={user_id}, transaction_id={transaction_id}, "
            f"amount={amount}, credits={credits}"
        )
    
    def log_refund_error(self, user_id: int, transaction_id: str, error: str) -> None:
        """
        Log a refund error.
        
        Args:
            user_id: User ID
            transaction_id: Transaction ID
            error: Error message
        """
        payment_logger = logging.getLogger("payment")
        payment_logger.error(
            f"Refund error: user_id={user_id}, transaction_id={transaction_id}, error={error}"
        )
    
    def log_payment_event(self, event_type: str, user_id: int, data: Dict[str, Any]) -> None:
        """
        Log a generic payment event.
        
        Args:
            event_type: Event type (e.g., "invoice_created", "pre_checkout", etc.)
            user_id: User ID
            data: Event data
        """
        payment_logger = logging.getLogger("payment")
        payment_logger.info(
            f"Payment event: type={event_type}, user_id={user_id}, data={json.dumps(data)}"
        )
    
    def get_payment_logs(self, user_id: Optional[int] = None, limit: int = 100) -> str:
        """
        Get payment logs for a user.
        
        Args:
            user_id: User ID (None for all users)
            limit: Maximum number of log entries to return
            
        Returns:
            String containing payment logs
        """
        logs = []
        
        try:
            with open(self.payment_log_file, 'r') as f:
                for line in f:
                    if user_id is None or f"user_id={user_id}" in line:
                        logs.append(line.strip())
                        if len(logs) >= limit:
                            break
        except FileNotFoundError:
            return "No payment logs found."
        
        return "\n".join(logs)
