"""
Unit tests for analytics module.
"""

import pytest
import tempfile
import os
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

from bot.analytics.conversation_analytics import ConversationAnalytics
from bot.analytics.user_analytics import UserAnalytics
from bot.analytics.business_analytics import BusinessAnalytics
from bot.analytics.event_tracker import EventTracker, EventType
from bot.analytics.metrics_collector import MetricsCollector
from bot.analytics.analytics_manager import AnalyticsManager


@pytest.fixture
def temp_db():
    """Create temporary database for testing."""
    fd, path = tempfile.mkstemp()
    os.close(fd)
    
    # Create mock database
    db = Mock()
    db.execute = Mock()
    db.commit = Mock()
    db.fetchall = Mock(return_value=[])
    db.fetchone = Mock(return_value=None)
    
    yield db
    
    try:
        os.unlink(path)
    except OSError:
        pass


@pytest.fixture
def cache_manager():
    """Create mock cache manager."""
    cache = Mock()
    cache.get = Mock(return_value=None)
    cache.set = Mock()
    cache.delete = Mock()
    return cache


@pytest.fixture
def redis_client():
    """Create mock Redis client."""
    redis = Mock()
    redis.lpush = Mock()
    redis.expire = Mock()
    redis.get = Mock(return_value=None)
    redis.set = Mock()
    return redis


class TestConversationAnalytics:
    """Test conversation analytics functionality."""
    
    def test_initialization(self, temp_db, cache_manager):
        """Test conversation analytics initialization."""
        analytics = ConversationAnalytics(temp_db, cache_manager)
        assert analytics.database == temp_db
        assert analytics.cache_manager == cache_manager
    
    def test_analyze_conversation_patterns_no_data(self, temp_db, cache_manager):
        """Test conversation pattern analysis with no data."""
        temp_db.execute.return_value.fetchall.return_value = []
        
        analytics = ConversationAnalytics(temp_db, cache_manager)
        metrics = analytics.analyze_conversation_patterns(user_id=123, days=30)
        
        assert metrics.total_conversations == 0
        assert metrics.avg_conversation_length == 0.0
        assert metrics.avg_messages_per_conversation == 0.0
    
    def test_analyze_conversation_patterns_with_data(self, temp_db, cache_manager):
        """Test conversation pattern analysis with sample data."""
        # Mock conversation data
        mock_conversations = [
            {
                'conversation_id': '1',
                'created_at': datetime.utcnow().isoformat(),
                'ended_at': (datetime.utcnow() + timedelta(minutes=10)).isoformat(),
                'message_count': 5
            },
            {
                'conversation_id': '2',
                'created_at': datetime.utcnow().isoformat(),
                'ended_at': (datetime.utcnow() + timedelta(minutes=15)).isoformat(),
                'message_count': 8
            }
        ]
        
        temp_db.execute.return_value.fetchall.return_value = mock_conversations
        
        analytics = ConversationAnalytics(temp_db, cache_manager)
        metrics = analytics.analyze_conversation_patterns(user_id=123, days=30)
        
        assert metrics.total_conversations == 2
        assert metrics.avg_messages_per_conversation == 6.5
        assert metrics.avg_conversation_length > 0
    
    def test_generate_conversation_insights(self, temp_db, cache_manager):
        """Test conversation insights generation."""
        analytics = ConversationAnalytics(temp_db, cache_manager)
        
        # Mock analyze_conversation_patterns to return high engagement
        with patch.object(analytics, 'analyze_conversation_patterns') as mock_analyze:
            mock_analyze.return_value = Mock(
                user_engagement_score=0.9,
                most_active_hours=[14, 15, 16],
                conversation_topics={'greeting': 5, 'help': 3},
                sentiment_distribution={'positive': 0.7, 'neutral': 0.2, 'negative': 0.1}
            )
            
            insights = analytics.generate_conversation_insights(user_id=123, days=30)
            
            assert len(insights) > 0
            assert any(insight.insight_type == "engagement" for insight in insights)


class TestUserAnalytics:
    """Test user analytics functionality."""
    
    def test_initialization(self, temp_db, cache_manager):
        """Test user analytics initialization."""
        analytics = UserAnalytics(temp_db, cache_manager)
        assert analytics.database == temp_db
        assert analytics.cache_manager == cache_manager
    
    def test_get_user_metrics(self, temp_db, cache_manager):
        """Test user metrics calculation."""
        # Mock database responses
        temp_db.execute.return_value.fetchone.side_effect = [
            [100],  # total_users
            [10],   # active_users_daily
            [25],   # active_users_weekly
            [50],   # active_users_monthly
            [5],    # new_users_daily
            [15],   # new_users_weekly
            [30]    # new_users_monthly
        ]
        
        analytics = UserAnalytics(temp_db, cache_manager)
        
        with patch.object(analytics, '_calculate_retention_rate', return_value=0.8):
            with patch.object(analytics, '_calculate_avg_session_duration', return_value=15.5):
                with patch.object(analytics, '_calculate_avg_messages_per_user', return_value=12.3):
                    with patch.object(analytics, '_calculate_churn_rate', return_value=0.1):
                        metrics = analytics.get_user_metrics(days=30)
                        
                        assert metrics.total_users == 100
                        assert metrics.active_users_daily == 10
                        assert metrics.retention_rate_7d == 0.8
                        assert metrics.avg_session_duration == 15.5
    
    def test_segment_users(self, temp_db, cache_manager):
        """Test user segmentation."""
        # Mock database responses for different user segments
        temp_db.execute.return_value.fetchone.side_effect = [
            {'user_count': 10, 'avg_credits': 150, 'avg_conversations': 15},  # High value
            {'user_count': 50, 'avg_credits': 75, 'avg_conversations': 6},   # Regular
            {'user_count': 100, 'avg_credits': 25, 'avg_conversations': 1}   # Trial
        ]
        
        analytics = UserAnalytics(temp_db, cache_manager)
        segments = analytics.segment_users()
        
        assert len(segments) == 3
        assert segments[0].segment_name == "High Value Users"
        assert segments[1].segment_name == "Regular Users"
        assert segments[2].segment_name == "Trial Users"


class TestBusinessAnalytics:
    """Test business analytics functionality."""
    
    def test_initialization(self, temp_db, cache_manager):
        """Test business analytics initialization."""
        analytics = BusinessAnalytics(temp_db, cache_manager)
        assert analytics.database == temp_db
        assert analytics.cache_manager == cache_manager
    
    def test_get_revenue_metrics(self, temp_db, cache_manager):
        """Test revenue metrics calculation."""
        analytics = BusinessAnalytics(temp_db, cache_manager)
        
        with patch.object(analytics, '_calculate_total_revenue', return_value=1000.0):
            with patch.object(analytics, '_calculate_mrr', return_value=500.0):
                with patch.object(analytics, '_calculate_arpu', return_value=25.0):
                    with patch.object(analytics, '_calculate_clv', return_value=300.0):
                        with patch.object(analytics, '_calculate_conversion_rate', return_value=0.15):
                            with patch.object(analytics, '_calculate_churn_rate', return_value=0.05):
                                with patch.object(analytics, '_calculate_revenue_growth_rate', return_value=0.2):
                                    with patch.object(analytics, '_calculate_payment_success_rate', return_value=0.95):
                                        metrics = analytics.get_revenue_metrics(days=30)
                                        
                                        assert metrics.total_revenue == 1000.0
                                        assert metrics.monthly_recurring_revenue == 500.0
                                        assert metrics.conversion_rate == 0.15
                                        assert metrics.payment_success_rate == 0.95
    
    def test_analyze_costs(self, temp_db, cache_manager):
        """Test cost analysis."""
        # Mock database responses
        temp_db.execute.return_value.fetchone.side_effect = [
            [1000],  # message_count
            [100],   # voice_messages
            [50],    # active_users
            [200],   # conversation_count
            [800.0]  # revenue
        ]
        
        analytics = BusinessAnalytics(temp_db, cache_manager)
        cost_analysis = analytics.analyze_costs(days=30)
        
        assert cost_analysis.ai_api_costs >= 0
        assert cost_analysis.voice_processing_costs >= 0
        assert cost_analysis.infrastructure_costs >= 0
        assert cost_analysis.total_costs >= 0
    
    def test_generate_business_insights(self, temp_db, cache_manager):
        """Test business insights generation."""
        analytics = BusinessAnalytics(temp_db, cache_manager)
        
        with patch.object(analytics, 'get_revenue_metrics') as mock_revenue:
            with patch.object(analytics, 'analyze_costs') as mock_costs:
                mock_revenue.return_value = Mock(
                    revenue_growth_rate=0.25,
                    conversion_rate=0.08,
                    churn_rate=0.15,
                    payment_success_rate=0.85
                )
                mock_costs.return_value = Mock(profit_margin=-0.1)
                
                insights = analytics.generate_business_insights(days=30)
                
                assert len(insights) > 0
                assert any(insight.insight_type == "revenue_growth" for insight in insights)
                assert any(insight.insight_type == "negative_margin" for insight in insights)


class TestEventTracker:
    """Test event tracking functionality."""
    
    def test_initialization(self, temp_db, redis_client):
        """Test event tracker initialization."""
        tracker = EventTracker(temp_db, redis_client)
        assert tracker.database == temp_db
        assert tracker.redis_client == redis_client
    
    def test_track_event(self, temp_db, redis_client):
        """Test event tracking."""
        tracker = EventTracker(temp_db, redis_client)
        
        event_id = tracker.track_event(
            event_type=EventType.USER_REGISTRATION,
            user_id=123,
            properties={"method": "telegram"},
            context={"source": "organic"}
        )
        
        assert event_id.startswith("user_registration_")
        assert len(tracker.event_buffer) == 1
    
    def test_flush_events(self, temp_db, redis_client):
        """Test event buffer flushing."""
        tracker = EventTracker(temp_db, redis_client)
        
        # Add events to buffer
        tracker.track_event(EventType.USER_REGISTRATION, user_id=123)
        tracker.track_event(EventType.CONVERSATION_STARTED, user_id=123)
        
        # Flush events
        tracker.flush_events()
        
        assert len(tracker.event_buffer) == 0
        assert temp_db.executemany.called
    
    def test_get_event_summary(self, temp_db, redis_client):
        """Test event summary generation."""
        # Mock database response
        temp_db.execute.return_value.fetchall.side_effect = [
            [{'event_type': 'user_registration'}],
            [{'count': 10, 'unique_users': 8, 'first_occurrence': datetime.utcnow().isoformat(), 'last_occurrence': datetime.utcnow().isoformat()}]
        ]
        
        tracker = EventTracker(temp_db, redis_client)
        summaries = tracker.get_event_summary(days=7)
        
        assert len(summaries) == 1
        assert summaries[0].event_type == 'user_registration'
        assert summaries[0].count == 10


class TestMetricsCollector:
    """Test metrics collection functionality."""
    
    def test_initialization(self, temp_db, redis_client):
        """Test metrics collector initialization."""
        collector = MetricsCollector(temp_db, redis_client)
        assert collector.database == temp_db
        assert collector.redis_client == redis_client
    
    def test_record_metric(self, temp_db, redis_client):
        """Test metric recording."""
        collector = MetricsCollector(temp_db, redis_client)
        
        collector.record_metric("test.metric", 42.0, unit="count")
        
        assert "test.metric" in collector.metrics_buffer
        assert len(collector.metrics_buffer["test.metric"]) == 1
    
    def test_flush_metrics(self, temp_db, redis_client):
        """Test metrics flushing."""
        collector = MetricsCollector(temp_db, redis_client)
        
        # Record some metrics
        collector.record_metric("test.metric1", 10.0)
        collector.record_metric("test.metric2", 20.0)
        
        # Flush metrics
        collector.flush_metrics()
        
        assert all(len(buffer) == 0 for buffer in collector.metrics_buffer.values())
        assert temp_db.executemany.called
    
    def test_get_metric_summary(self, temp_db, redis_client):
        """Test metric summary generation."""
        # Mock database response
        temp_db.execute.return_value.fetchall.return_value = [
            {'value': 10.0, 'unit': 'count'},
            {'value': 20.0, 'unit': 'count'},
            {'value': 15.0, 'unit': 'count'}
        ]
        
        collector = MetricsCollector(temp_db, redis_client)
        summary = collector.get_metric_summary("test.metric", hours=24)
        
        assert summary is not None
        assert summary.name == "test.metric"
        assert summary.count == 3
        assert summary.avg_value == 15.0


class TestAnalyticsManager:
    """Test analytics manager functionality."""
    
    def test_initialization(self, temp_db, cache_manager, redis_client):
        """Test analytics manager initialization."""
        manager = AnalyticsManager(
            database=temp_db,
            cache_manager=cache_manager,
            redis_client=redis_client
        )
        
        assert manager.database == temp_db
        assert manager.cache_manager == cache_manager
        assert manager.redis_client == redis_client
        assert manager.conversation_analytics is not None
        assert manager.user_analytics is not None
        assert manager.business_analytics is not None
        assert manager.event_tracker is not None
        assert manager.metrics_collector is not None
    
    def test_get_comprehensive_dashboard(self, temp_db, cache_manager, redis_client):
        """Test comprehensive dashboard generation."""
        manager = AnalyticsManager(temp_db, cache_manager, redis_client)
        
        # Mock all analytics components
        with patch.object(manager.user_analytics, 'get_user_metrics') as mock_user:
            with patch.object(manager.conversation_analytics, 'analyze_conversation_patterns') as mock_conv:
                with patch.object(manager.business_analytics, 'get_revenue_metrics') as mock_revenue:
                    with patch.object(manager.business_analytics, 'analyze_costs') as mock_costs:
                        with patch.object(manager.metrics_collector, 'get_dashboard_metrics') as mock_metrics:
                            # Setup mock returns
                            mock_user.return_value = Mock(total_users=100, active_users_daily=20)
                            mock_conv.return_value = Mock(total_conversations=500, avg_conversation_length=10.5)
                            mock_revenue.return_value = Mock(total_revenue=1000.0, conversion_rate=0.15)
                            mock_costs.return_value = Mock(total_costs=800.0, profit_margin=0.2)
                            mock_metrics.return_value = {"system.cpu.percent": {"current": 45.0}}
                            
                            dashboard = manager.get_comprehensive_dashboard(days=30)
                            
                            assert "user_metrics" in dashboard
                            assert "conversation_metrics" in dashboard
                            assert "business_metrics" in dashboard
                            assert "system_metrics" in dashboard
                            assert dashboard["period_days"] == 30
    
    def test_track_user_action(self, temp_db, cache_manager, redis_client):
        """Test user action tracking."""
        manager = AnalyticsManager(temp_db, cache_manager, redis_client)
        
        event_id = manager.track_user_action(
            event_type=EventType.MESSAGE_SENT,
            user_id=123,
            properties={"message_type": "text"}
        )
        
        assert event_id.startswith("message_sent_")
    
    def test_record_performance_metric(self, temp_db, cache_manager, redis_client):
        """Test performance metric recording."""
        manager = AnalyticsManager(temp_db, cache_manager, redis_client)
        
        manager.record_performance_metric("response_time", 1.5, "seconds")
        
        assert "response_time" in manager.metrics_collector.metrics_buffer
    
    def test_get_analytics_summary(self, temp_db, cache_manager, redis_client):
        """Test analytics summary generation."""
        manager = AnalyticsManager(temp_db, cache_manager, redis_client)
        
        summary = manager.get_analytics_summary()
        
        assert summary["status"] == "active"
        assert "components" in summary
        assert summary["components"]["conversation_analytics"] is True
        assert summary["components"]["user_analytics"] is True
        assert summary["components"]["business_analytics"] is True
