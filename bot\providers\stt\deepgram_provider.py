"""
Deepgram STT provider for VoicePal.

This module provides a Deepgram-based STT provider.
"""

import logging
from typing import Dict, Any

from bot.providers.stt.base import BaseSTTProvider

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class DeepgramProvider(BaseSTTProvider):
    """Deepgram-based STT provider."""

    def __init__(self, api_key: str):
        """
        Initialize the Deepgram provider.

        Args:
            api_key: Deepgram API key
        """
        self.api_key = api_key
        self._supported_features = ["transcription", "language_detection"]

        # Import Deepgram SDK only when needed
        try:
            # Try to use the latest Deepgram SDK (v4.x)
            try:
                from deepgram import DeepgramClient, PrerecordedOptions, SpeakOptionsClient
                self.deepgram = DeepgramClient(api_key)
                self.sdk_version = "v4"
                self._supported_features.append("sentiment_analysis")
                logger.info("Deepgram provider initialized with SDK v4.x")
            except Exception as e:
                logger.warning(f"Failed to initialize with latest SDK: {e}")
                # Fall back to older SDK if needed
                from deepgram import DeepgramClient, PrerecordedOptions, SpeakOptions
                self.deepgram = Deepgram(api_key)
                self.sdk_version = "v2"
                self._supported_features.append("sentiment_analysis")
                logger.info("Deepgram provider initialized with SDK v2.x")
        except ImportError:
            logger.warning("Deepgram SDK not found, using mock implementation")
            self.deepgram = None
            self.sdk_version = "mock"

    def supports_feature(self, feature_name: str) -> bool:
        """
        Check if a feature is supported.

        Args:
            feature_name: Feature name

        Returns:
            bool: True if feature is supported, False otherwise
        """
        return feature_name in self._supported_features

    async def transcribe_audio(self, audio_file_path: str, **kwargs) -> Dict[str, Any]:
        """
        Transcribe audio to text.

        Args:
            audio_file_path: Path to audio file
            **kwargs: Additional provider-specific parameters

        Returns:
            Dict containing transcript, language, confidence, etc.
        """
        try:
            if not self.deepgram:
                # Mock implementation for testing
                return {
                    "transcript": "This is a mock transcription for testing.",
                    "language": "en",
                    "confidence": 0.95
                }

            # Read audio file
            with open(audio_file_path, "rb") as audio:
                source = {"buffer": audio.read(), "mimetype": "audio/ogg"}

            # Send to Deepgram based on SDK version
            if self.sdk_version == "v4":
                # For SDK v4.x
                try:
                    # Create config for transcription
                    config = {
                        "model": "nova-2",
                        "punctuate": True,
                        "language": "en",
                        "detect_language": True
                    }

                    # Use buffer approach for consistency
                    with open(audio_file_path, 'rb') as audio_file:
                        # Read the file content
                        file_content = audio_file.read()

                        # Import the correct classes for v4 SDK
                        from deepgram import BufferSource

                        # Create a buffer source from the file content
                        source = BufferSource(buffer=file_content, mimetype="audio/ogg")

                        # Use the buffer source for transcription
                        result = await self.deepgram.listen.prerecorded.v("1").transcribe(
                            source, config
                        )

                        # Extract transcript and metadata from v4 response
                        response = result.results
                        alternatives = response["channels"][0]["alternatives"]
                        transcript = alternatives[0]["transcript"]
                        confidence = alternatives[0].get("confidence", 0.95)
                        language = response.get("detected_language", "en")
                except Exception as e:
                    logger.error("Error with v4.x transcription: %s", e)
                    import traceback
                    logger.error(traceback.format_exc())
                    transcript = "Error transcribing audio."
                    confidence = 0.0
                    language = "en"
            else:
                # For SDK v2.x
                response = await self.deepgram.transcription.prerecorded(
                    source,
                    {
                        "punctuate": True,
                        "language": "en",
                        "detect_language": True,
                        "model": "nova-2"
                    }
                )

                # Extract transcript and metadata
                result = response["results"]["channels"][0]["alternatives"][0]
                transcript = result["transcript"]
                confidence = result["confidence"]
                language = response["results"]["channels"][0]["detected_language"]

            return {
                "transcript": transcript,
                "language": language,
                "confidence": confidence
            }
        except Exception as e:
            logger.error(f"Error transcribing audio: {e}")
            return {
                "transcript": "",
                "language": "en",
                "confidence": 0.0
            }

    async def transcribe_with_sentiment(self, audio_file_path: str) -> Dict[str, Any]:
        """
        Transcribe audio to text with sentiment analysis.

        Args:
            audio_file_path: Path to audio file

        Returns:
            Dict containing transcript, metadata, and sentiment
        """
        try:
            if not self.deepgram:
                # Mock implementation for testing
                return {
                    "transcript": "This is a mock transcription for testing.",
                    "language": "en",
                    "confidence": 0.95,
                    "sentiment": {
                        "sentiment": "positive",
                        "confidence": 0.8
                    }
                }

            # Read audio file
            with open(audio_file_path, "rb") as audio:
                source = {"buffer": audio.read(), "mimetype": "audio/ogg"}

            # Send to Deepgram based on SDK version
            if self.sdk_version == "v4":
                # For SDK v4.x
                try:
                    # Create config for transcription with sentiment
                    config = {
                        "model": "nova-2",
                        "punctuate": True,
                        "language": "en",
                        "detect_language": True,
                        "sentiment": True
                    }

                    # Use the file directly
                    with open(audio_file_path, 'rb') as audio_file:
                        result = await self.deepgram.listen.prerecorded.v("1").transcribe_file(
                            audio_file, config
                        )

                        # Extract transcript and metadata from v4 response
                        response = result.results
                        alternatives = response["channels"][0]["alternatives"]
                        transcript = alternatives[0]["transcript"]
                        confidence = alternatives[0].get("confidence", 0.95)
                        language = response.get("detected_language", "en")

                        # Extract sentiment if available
                        sentiment = "neutral"
                        sentiment_confidence = 0.5
                        if "sentiment" in alternatives[0]:
                            sentiment = alternatives[0]["sentiment"]["overall"]
                            sentiment_confidence = alternatives[0]["sentiment"].get("overall_confidence", 0.5)
                except Exception as e:
                    logger.error("Error with v4.x transcription: %s", e)
                    import traceback
                    logger.error(traceback.format_exc())
                    transcript = "Error transcribing audio."
                    confidence = 0.0
                    language = "en"
                    sentiment = "neutral"
                    sentiment_confidence = 0.0
            else:
                # For SDK v2.x
                response = await self.deepgram.transcription.prerecorded(
                    source,
                    {
                        "punctuate": True,
                        "language": "en",
                        "detect_language": True,
                        "model": "nova-2",
                        "sentiment": True
                    }
                )

                # Extract transcript and metadata
                result = response["results"]["channels"][0]["alternatives"][0]
                transcript = result["transcript"]
                confidence = result["confidence"]
                language = response["results"]["channels"][0]["detected_language"]

                # Extract sentiment if available
                sentiment = "neutral"
                sentiment_confidence = 0.5
                if "sentiment" in result:
                    sentiment = result["sentiment"]["overall"]
                    sentiment_confidence = result["sentiment"]["overall_confidence"]

            return {
                "transcript": transcript,
                "language": language,
                "confidence": confidence,
                "sentiment": {
                    "sentiment": sentiment,
                    "confidence": sentiment_confidence
                }
            }
        except Exception as e:
            logger.error(f"Error transcribing audio with sentiment: {e}")
            return {
                "transcript": "",
                "language": "en",
                "confidence": 0.0,
                "sentiment": {
                    "sentiment": "neutral",
                    "confidence": 0.0
                }
            }

    def get_available_languages(self) -> Dict[str, str]:
        """
        Get available languages.

        Returns:
            Dictionary of language codes and names
        """
        return {
            "en": "English",
            "es": "Spanish",
            "fr": "French",
            "de": "German",
            "it": "Italian",
            "pt": "Portuguese",
            "ru": "Russian",
            "ja": "Japanese",
            "ko": "Korean",
            "zh": "Chinese"
        }

    async def detect_language(self, audio_file_path: str) -> str:
        """
        Detect language from audio.

        Args:
            audio_file_path: Path to audio file

        Returns:
            str: Detected language code
        """
        try:
            if not self.deepgram:
                # Mock implementation for testing
                return "en"

            # Read audio file
            with open(audio_file_path, "rb") as audio:
                source = {"buffer": audio.read(), "mimetype": "audio/ogg"}

            # Send to Deepgram based on SDK version
            if self.sdk_version == "v4":
                # For SDK v4.x
                try:
                    # Create config for language detection
                    config = {
                        "model": "nova-2",
                        "detect_language": True
                    }

                    # Use buffer approach for consistency
                    with open(audio_file_path, 'rb') as audio_file:
                        # Read the file content
                        file_content = audio_file.read()

                        # Import the correct classes for v4 SDK
                        from deepgram import BufferSource

                        # Create a buffer source from the file content
                        source = BufferSource(buffer=file_content, mimetype="audio/ogg")

                        # Use the buffer source for transcription
                        result = await self.deepgram.listen.prerecorded.v("1").transcribe(
                            source, config
                        )

                        # Extract language from v4 response
                        response = result.results
                        language = response.get("detected_language", "en")
                except Exception as e:
                    logger.error("Error with v4.x language detection: %s", e)
                    import traceback
                    logger.error(traceback.format_exc())
                    language = "en"
            else:
                # For SDK v2.x
                response = await self.deepgram.transcription.prerecorded(
                    source,
                    {
                        "detect_language": True,
                        "model": "nova-2"
                    }
                )

                # Extract language
                language = response["results"]["channels"][0]["detected_language"]

            return language
        except Exception as e:
            logger.error(f"Error detecting language: {e}")
            return "en"
