# VoicePal Hierarchical Memory System

This document provides an overview of the hierarchical memory system implemented in VoicePal.

## Overview

The hierarchical memory system is designed to provide a more human-like conversation experience by maintaining different levels of memory:

1. **Short-term Memory**: Recent conversations (last 10 messages)
2. **Medium-term Memory**: Important recent conversations (last 50 important messages)
3. **Long-term Memory**: Very important or semantically relevant memories

The system uses a combination of traditional database storage and vector search to retrieve relevant memories based on the current conversation context.

## Architecture

The hierarchical memory system consists of the following components:

### Core Components

- **HierarchicalMemoryManager**: The main class that manages the different memory tiers and provides a unified interface for the bot.
- **RedisProvider**: Provides distributed caching and rate limiting using Upstash Redis.
- **QdrantProvider**: Provides vector search capabilities for semantic memory retrieval.
- **EmbeddingProvider**: Generates embeddings for text using sentence-transformers.

### Memory Tiers

1. **Short-term Memory**
   - Stores the most recent conversations
   - Used for immediate context in conversations
   - Limited to the last 10 messages by default

2. **Medium-term Memory**
   - Stores important conversations from the recent past
   - Used for maintaining context over longer periods
   - Limited to the last 50 important messages by default

3. **Long-term Memory**
   - Stores very important memories or semantically relevant memories
   - Used for recalling important information from the past
   - Combines importance-based retrieval and semantic search

## Configuration

The memory system can be configured through the `memory_config.json` file:

```json
{
  "memory": {
    "short_term_limit": 10,
    "medium_term_limit": 50,
    "long_term_threshold": 0.7,
    "cache_ttl_minutes": 30,
    "name_cache_ttl_minutes": 60,
    "enable_rate_limiting": true,
    "conversation_memory": 20,
    "summary_update_frequency": 24,
    "token_limit": 3000,
    "importance_threshold": 0.5,
    "redis": {
      "cache_ttl_minutes": 30,
      "rate_limit_max_requests": 10,
      "rate_limit_window_seconds": 10
    },
    "qdrant": {
      "collection_name": "voicepal_memories",
      "vector_size": 384,
      "distance": "cosine",
      "auto_init": true,
      "use_in_memory": true
    },
    "embedding": {
      "model_name": "all-MiniLM-L6-v2",
      "vector_size": 384,
      "batch_size": 32
    }
  }
}
```

### Configuration Options

#### General Settings

- `short_term_limit`: Maximum number of messages in short-term memory
- `medium_term_limit`: Maximum number of messages in medium-term memory
- `long_term_threshold`: Importance threshold for long-term memory (0-1)
- `cache_ttl_minutes`: Time-to-live for cached conversation context in minutes
- `name_cache_ttl_minutes`: Time-to-live for cached user names in minutes
- `enable_rate_limiting`: Whether to enable rate limiting for message processing
- `conversation_memory`: Number of conversations to include in context
- `summary_update_frequency`: How often to update user summaries (in hours)
- `token_limit`: Maximum number of tokens to include in context
- `importance_threshold`: Default importance threshold for memories

#### Redis Settings

- `cache_ttl_minutes`: Time-to-live for Redis cache in minutes
- `rate_limit_max_requests`: Maximum number of requests in the rate limit window
- `rate_limit_window_seconds`: Rate limit window in seconds

#### Qdrant Settings

- `collection_name`: Name of the Qdrant collection for storing memories
- `vector_size`: Size of the embedding vectors
- `distance`: Distance metric for vector search (cosine, euclidean, dot)
- `auto_init`: Whether to automatically initialize the collection
- `use_in_memory`: Whether to use in-memory storage (for development)

#### Embedding Settings

- `model_name`: Name of the sentence-transformers model for embeddings
- `vector_size`: Size of the embedding vectors
- `batch_size`: Batch size for generating embeddings

## Integration

The hierarchical memory system is integrated with the bot through the `memory_integration.py` module. This module provides functions to initialize the memory system and integrate it with the bot's handlers.

### Initialization

The memory system is initialized in the `main.py` file through the `_integrate_hierarchical_memory` method:

```python
async def _integrate_hierarchical_memory(self) -> tuple[bool, Optional[str]]:
    """
    Integrate hierarchical memory system with Redis and Qdrant.

    Returns:
        Tuple of (success, error_message)
    """
    try:
        # Get dependencies
        config_manager = self.init_manager.get_component("config_manager")
        memory_manager = self.init_manager.get_component("memory_manager")

        # Initialize hierarchical memory system if memory is enabled
        if config_manager.is_feature_enabled("memory") and memory_manager:
            # Check if hierarchical memory is specifically enabled
            memory_config = config_manager.get_feature_config("memory") or {}
            use_hierarchical = memory_config.get("use_hierarchical", True)
            
            if use_hierarchical:
                # Integrate hierarchical memory system
                from bot.features.memory_integration import integrate_hierarchical_memory
                success = await integrate_hierarchical_memory(self)
                
                if success:
                    # Update feature registry
                    if hasattr(self, 'feature_registry'):
                        self.feature_registry.register_feature(
                            "hierarchical_memory",
                            True,
                            "Hierarchical memory system with Redis and vector search"
                        )
                    
                    logger.info("Hierarchical memory system integrated successfully")
                else:
                    logger.warning("Failed to integrate hierarchical memory system")

        return True, None
    except Exception as e:
        logger.error(f"Error integrating hierarchical memory: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False, str(e)
```

## Usage

The hierarchical memory system is used by the bot to:

1. **Retrieve Conversation Context**: Get the context for a conversation based on the user's history.
2. **Store Conversations**: Store conversations with importance scoring.
3. **Search Relevant Memories**: Search for relevant memories based on the current conversation.
4. **Preserve Context for Short Messages**: Determine if context should be preserved for short messages.

### Example: Getting Conversation Context

```python
# Get conversation context for a user
user_context = bot_instance.memory_manager.get_conversation_context(user_id)

# Process message using dialog engine with context
response = await bot_instance.dialog_engine.process_message(
    user_id=user_id,
    message=message,
    language=language,
    is_voice=False,
    user_context=user_context,
    preserve_context=preserve_context
)
```

### Example: Storing a Conversation

```python
# Store conversation with importance scoring
await bot_instance.memory_manager.store_conversation(
    user_id=user_id,
    message=message,
    response=response.get("text", ""),
    is_voice=False
)
```

## Dependencies

The hierarchical memory system requires the following dependencies:

- `upstash-redis`: Redis client for Upstash
- `upstash-ratelimit`: Rate limiting for Upstash Redis
- `qdrant-client`: Vector database client
- `sentence-transformers`: For generating embeddings

These dependencies are included in the `requirements.txt` file.

## Future Improvements

Potential future improvements to the memory system include:

1. **Memory Consolidation**: Periodically consolidate memories to reduce redundancy.
2. **Memory Pruning**: Remove less important memories to save storage space.
3. **Memory Tagging**: Add tags to memories for better organization and retrieval.
4. **Memory Visualization**: Provide a visual representation of the user's memories.
5. **Cross-User Memory**: Allow sharing of memories between users (with privacy controls).
6. **Memory Backup**: Implement backup and restore functionality for memories.
7. **Memory Analytics**: Provide analytics on memory usage and retrieval patterns.

## Conclusion

The hierarchical memory system provides a more human-like conversation experience by maintaining different levels of memory. It combines traditional database storage with vector search to retrieve relevant memories based on the current conversation context.
