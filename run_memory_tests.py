#!/usr/bin/env python3
"""
Test runner for the hierarchical memory system tests.

This script runs all the tests for the hierarchical memory system and provides
a summary of the results.
"""

import sys
import os
import subprocess
import importlib.util
from pathlib import Path

def check_dependencies():
    """Check if required dependencies are available."""
    required_packages = ['pytest', 'pytest_asyncio']
    missing_packages = []
    
    for package in required_packages:
        try:
            spec = importlib.util.find_spec(package)
            if spec is None:
                missing_packages.append(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"Missing required packages: {', '.join(missing_packages)}")
        print("Please install them with: pip install pytest pytest-asyncio pytest-cov")
        return False
    
    return True

def find_test_files():
    """Find all test files for the hierarchical memory system."""
    test_dirs = [
        'tests/providers',
        'tests/utils', 
        'tests/features',
        'tests/integration',
        'tests/e2e'
    ]
    
    test_files = []
    for test_dir in test_dirs:
        test_path = Path(test_dir)
        if test_path.exists():
            for test_file in test_path.glob('test_*.py'):
                test_files.append(str(test_file))
    
    return test_files

def run_simple_test():
    """Run a simple test to verify the testing framework works."""
    print("Running simple test to verify pytest is working...")
    
    # Create a simple test
    simple_test = """
def test_simple():
    assert 1 + 1 == 2, "Basic math works"

def test_string():
    assert "hello" + " world" == "hello world", "String concatenation works"
"""
    
    with open('temp_simple_test.py', 'w') as f:
        f.write(simple_test)
    
    try:
        # Try to run the simple test
        result = subprocess.run([
            sys.executable, '-m', 'pytest', 'temp_simple_test.py', '-v'
        ], capture_output=True, text=True, timeout=30)
        
        print("Simple test output:")
        print(result.stdout)
        if result.stderr:
            print("Errors:")
            print(result.stderr)
        
        # Clean up
        os.remove('temp_simple_test.py')
        
        return result.returncode == 0
    except Exception as e:
        print(f"Error running simple test: {e}")
        # Clean up
        if os.path.exists('temp_simple_test.py'):
            os.remove('temp_simple_test.py')
        return False

def run_memory_tests():
    """Run all hierarchical memory system tests."""
    print("Checking dependencies...")
    if not check_dependencies():
        return False
    
    print("Running simple test first...")
    if not run_simple_test():
        print("Simple test failed. There may be issues with the pytest installation.")
        return False
    
    print("Finding test files...")
    test_files = find_test_files()
    
    if not test_files:
        print("No test files found. Creating test directories...")
        # Create test directories
        test_dirs = ['tests/providers', 'tests/utils', 'tests/features', 'tests/integration', 'tests/e2e']
        for test_dir in test_dirs:
            Path(test_dir).mkdir(parents=True, exist_ok=True)
        
        print("Test directories created. Please ensure test files are in the correct locations.")
        return False
    
    print(f"Found {len(test_files)} test files:")
    for test_file in test_files:
        print(f"  - {test_file}")
    
    print("\nRunning hierarchical memory system tests...")
    
    # Run all tests
    try:
        result = subprocess.run([
            sys.executable, '-m', 'pytest'
        ] + test_files + ['-v', '--tb=short'], 
        capture_output=True, text=True, timeout=300)
        
        print("Test Results:")
        print("=" * 50)
        print(result.stdout)
        
        if result.stderr:
            print("\nErrors/Warnings:")
            print("=" * 50)
            print(result.stderr)
        
        print(f"\nTest execution completed with return code: {result.returncode}")
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("Tests timed out after 5 minutes")
        return False
    except Exception as e:
        print(f"Error running tests: {e}")
        return False

def run_with_coverage():
    """Run tests with coverage reporting."""
    print("Running tests with coverage...")
    
    test_files = find_test_files()
    if not test_files:
        print("No test files found.")
        return False
    
    try:
        result = subprocess.run([
            sys.executable, '-m', 'pytest'
        ] + test_files + [
            '--cov=bot.features.hierarchical_memory_manager',
            '--cov=bot.providers.memory',
            '--cov=bot.utils.embedding_utils',
            '--cov-report=term-missing',
            '-v'
        ], capture_output=True, text=True, timeout=300)
        
        print("Coverage Report:")
        print("=" * 50)
        print(result.stdout)
        
        if result.stderr:
            print("\nErrors/Warnings:")
            print("=" * 50)
            print(result.stderr)
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"Error running coverage: {e}")
        return False

if __name__ == "__main__":
    print("Hierarchical Memory System Test Runner")
    print("=" * 50)
    
    # Check if we should run with coverage
    run_coverage = '--coverage' in sys.argv
    
    if run_coverage:
        success = run_with_coverage()
    else:
        success = run_memory_tests()
    
    if success:
        print("\n✅ All tests completed successfully!")
    else:
        print("\n❌ Some tests failed or there were issues running the tests.")
        print("\nTroubleshooting tips:")
        print("1. Make sure pytest is installed: pip install pytest pytest-asyncio pytest-cov")
        print("2. Make sure all test files are in the correct directories")
        print("3. Make sure the bot modules are importable")
        print("4. Check that all dependencies are available")
    
    sys.exit(0 if success else 1)
