"""
Check if the new features (Deepgram TTS and Stars payment) are properly integrated.
"""

import os
import sys
import logging
import importlib

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

def check_module_exists(module_name):
    """Check if a module exists and can be imported."""
    try:
        importlib.import_module(module_name)
        logger.info(f"✅ Module '{module_name}' exists and can be imported.")
        return True
    except ImportError as e:
        logger.error(f"❌ Module '{module_name}' cannot be imported: {e}")
        return False

def check_file_exists(file_path):
    """Check if a file exists."""
    if os.path.exists(file_path):
        logger.info(f"✅ File '{file_path}' exists.")
        return True
    else:
        logger.error(f"❌ File '{file_path}' does not exist.")
        return False

def check_deepgram_integration():
    """Check if Deepgram TTS is properly integrated."""
    logger.info("Checking Deepgram TTS integration...")
    
    # Check if the Deepgram TTS provider module exists
    deepgram_provider_exists = check_module_exists("bot.tts_providers.deepgram_tts_provider")
    
    # Check if the Deepgram TTS provider is registered in the TTS manager
    try:
        from bot.tts_manager import TTSManager
        tts_manager = TTSManager()
        providers = tts_manager.get_available_providers()
        if "deepgram" in providers:
            logger.info("✅ Deepgram TTS provider is registered in the TTS manager.")
        else:
            logger.error("❌ Deepgram TTS provider is not registered in the TTS manager.")
            logger.info(f"Available providers: {providers}")
    except Exception as e:
        logger.error(f"❌ Error checking TTS manager: {e}")
    
    return deepgram_provider_exists

def check_stars_payment_integration():
    """Check if Stars payment is properly integrated."""
    logger.info("Checking Stars payment integration...")
    
    # Check if the Stars payment module exists
    stars_payment_exists = check_module_exists("bot.payment.telegram_stars_payment")
    
    # Check if the Stars payment is registered in the payment system
    try:
        from bot.payment_system import PaymentSystem
        payment_system = PaymentSystem()
        if hasattr(payment_system, "use_telegram_stars") and payment_system.use_telegram_stars:
            logger.info("✅ Stars payment is enabled in the payment system.")
        else:
            logger.error("❌ Stars payment is not enabled in the payment system.")
    except Exception as e:
        logger.error(f"❌ Error checking payment system: {e}")
    
    return stars_payment_exists

def main():
    """Main function."""
    logger.info("Starting integration check...")
    
    # Check Python version
    logger.info(f"Python version: {sys.version}")
    
    # Check if the bot module exists
    bot_module_exists = check_module_exists("bot")
    if not bot_module_exists:
        logger.error("❌ Bot module does not exist. Make sure you're in the correct directory.")
        return 1
    
    # Check if the main bot file exists
    main_file_exists = check_file_exists("bot/main.py")
    if not main_file_exists:
        logger.error("❌ Main bot file does not exist. Make sure you're in the correct directory.")
        return 1
    
    # Check Deepgram TTS integration
    deepgram_integration_ok = check_deepgram_integration()
    
    # Check Stars payment integration
    stars_payment_integration_ok = check_stars_payment_integration()
    
    # Summary
    logger.info("\n--- Integration Check Summary ---")
    logger.info(f"Deepgram TTS integration: {'✅ OK' if deepgram_integration_ok else '❌ Failed'}")
    logger.info(f"Stars payment integration: {'✅ OK' if stars_payment_integration_ok else '❌ Failed'}")
    
    if deepgram_integration_ok and stars_payment_integration_ok:
        logger.info("✅ All integrations are OK.")
        return 0
    else:
        logger.error("❌ Some integrations failed. See above for details.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
