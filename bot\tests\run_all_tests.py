"""
<PERSON><PERSON>t to run all tests for the VoicePal bot.

This script runs:
1. Unit tests
2. Deepgram tests
3. User interaction tests
"""

import os
import sys
import asyncio
import logging
import importlib
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

async def run_all_tests():
    """Run all tests."""
    logger.info("Starting all tests...")
    
    # Import test modules
    test_fixes = importlib.import_module('bot.tests.test_fixes')
    test_deepgram = importlib.import_module('bot.tests.test_deepgram')
    test_user_interactions = importlib.import_module('bot.tests.test_user_interactions')
    
    # Run unit tests
    logger.info("Running unit tests...")
    test_fixes.run_tests()
    
    # Run Deepgram tests
    logger.info("Running Deepgram tests...")
    await test_deepgram.run_tests()
    
    # Run user interaction tests
    logger.info("Running user interaction tests...")
    await test_user_interactions.run_tests()
    
    logger.info("All tests completed")

def main():
    """Main function."""
    asyncio.run(run_all_tests())

if __name__ == '__main__':
    main()
