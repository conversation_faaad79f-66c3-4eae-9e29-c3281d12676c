"""
Integration module for VoicePal API with the main bot.

This module provides integration between the FastAPI application and the VoicePal bot.
"""

import logging
import asyncio
from typing import Optional
from threading import Thread

from api.app import create_app, run_api
from api.config import APIConfig

logger = logging.getLogger(__name__)

class APIIntegration:
    """Integration class for VoicePal API."""
    
    def __init__(self, bot_instance, config: Optional[APIConfig] = None):
        """
        Initialize API integration.
        
        Args:
            bot_instance: VoicePal bot instance
            config: API configuration
        """
        self.bot_instance = bot_instance
        self.config = config or APIConfig()
        self.app = None
        self.server_thread = None
        self.is_running = False
    
    def start_api_server(self) -> bool:
        """
        Start the API server.
        
        Returns:
            True if server started successfully, False otherwise
        """
        try:
            # Validate configuration
            errors = self.config.validate()
            if errors:
                logger.error(f"API configuration errors: {errors}")
                return False
            
            # Create FastAPI app
            self.app = create_app(self.bot_instance, self.config)
            
            # Start server in a separate thread
            self.server_thread = Thread(
                target=self._run_server,
                daemon=True,
                name="VoicePal-API-Server"
            )
            self.server_thread.start()
            
            self.is_running = True
            logger.info(f"API server started on {self.config.host}:{self.config.port}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start API server: {e}")
            return False
    
    def stop_api_server(self) -> bool:
        """
        Stop the API server.
        
        Returns:
            True if server stopped successfully, False otherwise
        """
        try:
            if self.is_running and self.server_thread:
                self.is_running = False
                # Note: uvicorn doesn't have a clean way to stop from another thread
                # In production, you'd want to use a proper process manager
                logger.info("API server stop requested")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to stop API server: {e}")
            return False
    
    def _run_server(self):
        """Run the API server."""
        try:
            run_api(
                host=self.config.host,
                port=self.config.port,
                reload=self.config.reload,
                log_level=self.config.log_level
            )
        except Exception as e:
            logger.error(f"API server error: {e}")
            self.is_running = False
    
    def get_api_status(self) -> dict:
        """
        Get API server status.
        
        Returns:
            API status information
        """
        return {
            "running": self.is_running,
            "host": self.config.host,
            "port": self.config.port,
            "endpoints": {
                "health": f"http://{self.config.host}:{self.config.port}/health",
                "metrics": f"http://{self.config.host}:{self.config.port}/metrics",
                "admin": f"http://{self.config.host}:{self.config.port}/admin",
                "webhooks": f"http://{self.config.host}:{self.config.port}/webhooks",
                "docs": f"http://{self.config.host}:{self.config.port}/docs" if self.config.enable_docs else None
            }
        }

def integrate_api_with_bot(bot_instance, api_config: Optional[APIConfig] = None) -> Optional[APIIntegration]:
    """
    Integrate API with the VoicePal bot.
    
    Args:
        bot_instance: VoicePal bot instance
        api_config: API configuration
        
    Returns:
        APIIntegration instance if successful, None otherwise
    """
    try:
        # Create API integration
        api_integration = APIIntegration(bot_instance, api_config)
        
        # Start API server
        if api_integration.start_api_server():
            # Store reference in bot instance
            bot_instance.api_integration = api_integration
            
            # Register with feature registry if available
            if hasattr(bot_instance, 'feature_registry'):
                bot_instance.feature_registry.register_feature(
                    "rest_api",
                    True,
                    "REST API for external integrations and monitoring"
                )
            
            logger.info("API successfully integrated with VoicePal bot")
            return api_integration
        else:
            logger.error("Failed to start API server")
            return None
            
    except Exception as e:
        logger.error(f"Failed to integrate API with bot: {e}")
        return None

async def start_api_async(bot_instance, api_config: Optional[APIConfig] = None) -> Optional[APIIntegration]:
    """
    Start API server asynchronously.
    
    Args:
        bot_instance: VoicePal bot instance
        api_config: API configuration
        
    Returns:
        APIIntegration instance if successful, None otherwise
    """
    try:
        # Run integration in executor to avoid blocking
        loop = asyncio.get_event_loop()
        api_integration = await loop.run_in_executor(
            None,
            integrate_api_with_bot,
            bot_instance,
            api_config
        )
        
        return api_integration
        
    except Exception as e:
        logger.error(f"Failed to start API asynchronously: {e}")
        return None

def create_api_config_from_bot(bot_instance) -> APIConfig:
    """
    Create API configuration from bot configuration.
    
    Args:
        bot_instance: VoicePal bot instance
        
    Returns:
        API configuration
    """
    try:
        config = APIConfig()
        
        # Get admin user IDs from bot config
        if hasattr(bot_instance, 'config_manager'):
            telegram_config = bot_instance.config_manager.get_telegram_config()
            config.admin_user_ids = telegram_config.get("admin_user_ids", [])
        
        return config
        
    except Exception as e:
        logger.error(f"Failed to create API config from bot: {e}")
        return APIConfig()

# Utility functions for bot integration
def add_api_commands_to_bot(bot_instance):
    """
    Add API-related commands to the bot.
    
    Args:
        bot_instance: VoicePal bot instance
    """
    try:
        from telegram.ext import CommandHandler
        
        async def api_status_command(update, context):
            """Handle /api_status command."""
            user_id = update.effective_user.id
            
            # Check if user is admin
            if hasattr(bot_instance, 'config_manager'):
                telegram_config = bot_instance.config_manager.get_telegram_config()
                admin_user_ids = telegram_config.get("admin_user_ids", [])
                
                if user_id not in admin_user_ids:
                    await update.message.reply_text("❌ This command is only available to administrators.")
                    return
            
            # Get API status
            if hasattr(bot_instance, 'api_integration'):
                status = bot_instance.api_integration.get_api_status()
                
                status_text = f"🔌 **API Status**\n\n"
                status_text += f"Status: {'🟢 Running' if status['running'] else '🔴 Stopped'}\n"
                status_text += f"Host: `{status['host']}`\n"
                status_text += f"Port: `{status['port']}`\n\n"
                status_text += "**Endpoints:**\n"
                
                for name, url in status['endpoints'].items():
                    if url:
                        status_text += f"• {name.title()}: `{url}`\n"
                
                await update.message.reply_text(status_text, parse_mode="Markdown")
            else:
                await update.message.reply_text("❌ API integration not available.")
        
        # Add command handler
        if hasattr(bot_instance, 'application'):
            bot_instance.application.add_handler(CommandHandler("api_status", api_status_command))
            logger.info("Added API status command to bot")
        
    except Exception as e:
        logger.error(f"Failed to add API commands to bot: {e}")

def get_api_metrics_for_bot(bot_instance) -> dict:
    """
    Get API metrics for bot monitoring.
    
    Args:
        bot_instance: VoicePal bot instance
        
    Returns:
        API metrics dictionary
    """
    try:
        if hasattr(bot_instance, 'api_integration') and bot_instance.api_integration.is_running:
            return {
                "api_enabled": True,
                "api_running": True,
                "api_host": bot_instance.api_integration.config.host,
                "api_port": bot_instance.api_integration.config.port
            }
        else:
            return {
                "api_enabled": False,
                "api_running": False
            }
            
    except Exception as e:
        logger.error(f"Failed to get API metrics: {e}")
        return {"api_enabled": False, "api_running": False, "error": str(e)}
