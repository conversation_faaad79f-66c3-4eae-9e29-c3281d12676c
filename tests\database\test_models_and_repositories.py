"""
Tests for the database models and repositories.

This module provides tests for the database models and repositories.
"""

import os
import unittest
import tempfile
from pathlib import Path
from datetime import datetime, timedelta

from bot.database.database_manager import DatabaseManager
from bot.database.core.exceptions import DatabaseError
from bot.database.models import (
    User,
    UserPreference,
    UserStat,
    Conversation,
    Message,
    MessageMetadata,
    Transaction,
    PaymentPackage,
    Subscription,
    Memory,
    MemoryTag,
    VoiceSetting,
    VoiceRecording
)

class TestModelsAndRepositories(unittest.TestCase):
    """Test case for database models and repositories."""
    
    def setUp(self):
        """Set up test environment."""
        # Create temporary directory for database
        self.temp_dir = tempfile.TemporaryDirectory()
        self.db_path = Path(self.temp_dir.name) / "test.db"
        
        # Initialize database
        self.db = DatabaseManager(self.db_path)
        
        # Create test user
        self.user_id = "test_user"
        self.user = User(
            user_id=self.user_id,
            username="test_username",
            first_name="Test",
            last_name="User"
        )
        self.db.users.create(self.user)
    
    def tearDown(self):
        """Clean up test environment."""
        # Close database connection
        self.db.close()
        
        # Remove temporary directory
        self.temp_dir.cleanup()
    
    def test_user_model_and_repository(self):
        """Test User model and repository."""
        # Test model methods
        user = self.db.users.find_by_id(self.user_id)
        self.assertEqual(user.get_full_name(), "Test User")
        
        # Test adding credits
        initial_credits = user.credits
        added_credits = 50
        user.add_credits(added_credits)
        self.db.users.update(user)
        
        user = self.db.users.find_by_id(self.user_id)
        self.assertEqual(user.credits, initial_credits + added_credits)
        
        # Test using credits
        used_credits = 20
        user.use_credits(used_credits)
        self.db.users.update(user)
        
        user = self.db.users.find_by_id(self.user_id)
        self.assertEqual(user.credits, initial_credits + added_credits - used_credits)
        
        # Test repository methods
        self.db.users.update_last_interaction(self.user_id)
        user = self.db.users.find_by_id(self.user_id)
        self.assertIsNotNone(user.last_interaction)
        
        # Test adding credits through repository
        added_credits = 30
        self.db.users.add_credits(self.user_id, added_credits)
        user = self.db.users.find_by_id(self.user_id)
        self.assertEqual(user.credits, initial_credits + added_credits - used_credits + added_credits)
        
        # Test using credits through repository
        used_credits = 10
        self.db.users.use_credits(self.user_id, used_credits)
        user = self.db.users.find_by_id(self.user_id)
        self.assertEqual(user.credits, initial_credits + added_credits - used_credits + added_credits - used_credits)
    
    def test_user_preference_model_and_repository(self):
        """Test UserPreference model and repository."""
        # Set preferences
        theme_key = "theme"
        theme_value = "dark"
        language_key = "language"
        language_value = "en"
        
        self.db.user_preferences.set_preference(self.user_id, theme_key, theme_value)
        self.db.user_preferences.set_preference(self.user_id, language_key, language_value)
        
        # Get preferences
        theme = self.db.user_preferences.get_preference(self.user_id, theme_key)
        language = self.db.user_preferences.get_preference(self.user_id, language_key)
        
        self.assertEqual(theme, theme_value)
        self.assertEqual(language, language_value)
        
        # Get all preferences
        preferences = self.db.user_preferences.find_by_user(self.user_id)
        self.assertEqual(len(preferences), 2)
        
        # Update preference
        new_theme_value = "light"
        self.db.user_preferences.set_preference(self.user_id, theme_key, new_theme_value)
        theme = self.db.user_preferences.get_preference(self.user_id, theme_key)
        self.assertEqual(theme, new_theme_value)
        
        # Delete preference
        self.db.user_preferences.delete_preference(self.user_id, theme_key)
        theme = self.db.user_preferences.get_preference(self.user_id, theme_key)
        self.assertIsNone(theme)
    
    def test_user_stat_model_and_repository(self):
        """Test UserStat model and repository."""
        # Increment stats
        messages_key = "messages_sent"
        messages_value = 5
        calls_key = "voice_calls"
        calls_value = 3
        
        self.db.user_stats.increment_stat(self.user_id, messages_key, messages_value)
        self.db.user_stats.increment_stat(self.user_id, calls_key, calls_value)
        
        # Get stats
        messages = self.db.user_stats.get_stat(self.user_id, messages_key)
        calls = self.db.user_stats.get_stat(self.user_id, calls_key)
        
        self.assertEqual(messages, messages_value)
        self.assertEqual(calls, calls_value)
        
        # Increment again
        self.db.user_stats.increment_stat(self.user_id, messages_key, 2)
        messages = self.db.user_stats.get_stat(self.user_id, messages_key)
        self.assertEqual(messages, messages_value + 2)
        
        # Reset stat
        self.db.user_stats.reset_stat(self.user_id, messages_key)
        messages = self.db.user_stats.get_stat(self.user_id, messages_key)
        self.assertEqual(messages, 0)
    
    def test_conversation_and_message_models_and_repositories(self):
        """Test Conversation and Message models and repositories."""
        # Create conversation
        conversation, created = self.db.conversations.get_or_create_active_conversation(self.user_id)
        self.assertTrue(created)
        
        # Add messages
        user_message = "Hello, bot!"
        bot_message = "Hello, human!"
        
        user_msg = self.db.messages.add_message(
            conversation.conversation_id,
            self.user_id,
            user_message,
            Message.ROLE_USER
        )
        
        bot_msg = self.db.messages.add_message(
            conversation.conversation_id,
            self.user_id,
            bot_message,
            Message.ROLE_ASSISTANT
        )
        
        # Add metadata
        sentiment_key = "sentiment"
        sentiment_value = "positive"
        
        self.db.message_metadata.set_metadata(bot_msg.message_id, sentiment_key, sentiment_value)
        
        # Get conversation history
        history = self.db.messages.get_conversation_history(conversation.conversation_id)
        self.assertEqual(len(history), 2)
        self.assertEqual(history[0]["role"], Message.ROLE_USER)
        self.assertEqual(history[0]["content"], user_message)
        self.assertEqual(history[1]["role"], Message.ROLE_ASSISTANT)
        self.assertEqual(history[1]["content"], bot_message)
        
        # Get metadata
        sentiment = self.db.message_metadata.get_metadata(bot_msg.message_id, sentiment_key)
        self.assertEqual(sentiment, sentiment_value)
        
        # Update conversation title
        new_title = "Test Conversation"
        self.db.conversations.update_title(conversation.conversation_id, new_title)
        
        conversation = self.db.conversations.find_by_id(conversation.conversation_id)
        self.assertEqual(conversation.title, new_title)
        
        # Archive conversation
        self.db.conversations.archive_conversation(conversation.conversation_id)
        
        conversation = self.db.conversations.find_by_id(conversation.conversation_id)
        self.assertFalse(conversation.is_active)
        
        # Create new conversation
        new_conversation, created = self.db.conversations.get_or_create_active_conversation(self.user_id)
        self.assertTrue(created)
        self.assertNotEqual(new_conversation.conversation_id, conversation.conversation_id)
    
    def test_payment_models_and_repositories(self):
        """Test payment models and repositories."""
        # Create payment package
        package = PaymentPackage(
            name="Basic",
            description="Basic package",
            credits=100,
            price=9.99
        )
        self.db.payment_packages.create(package)
        
        # Create transaction
        transaction = self.db.transactions.create_purchase_transaction(
            self.user_id,
            package.credits,
            "stripe"
        )
        
        # Complete transaction
        provider_transaction_id = "txn_123456"
        self.db.transactions.complete_transaction(transaction.transaction_id, provider_transaction_id)
        
        transaction = self.db.transactions.find_by_id(transaction.transaction_id)
        self.assertEqual(transaction.status, Transaction.STATUS_COMPLETED)
        self.assertEqual(transaction.provider_transaction_id, provider_transaction_id)
        
        # Create subscription
        subscription = self.db.subscriptions.create_subscription(
            self.user_id,
            package.package_id,
            "stripe",
            "sub_123456"
        )
        
        # Activate subscription
        start_date = datetime.now().isoformat()
        end_date = (datetime.now() + timedelta(days=30)).isoformat()
        
        self.db.subscriptions.activate_subscription(
            subscription.subscription_id,
            start_date,
            end_date
        )
        
        subscription = self.db.subscriptions.find_by_id(subscription.subscription_id)
        self.assertEqual(subscription.status, Subscription.STATUS_ACTIVE)
        self.assertEqual(subscription.start_date, start_date)
        self.assertEqual(subscription.end_date, end_date)
        
        # Check if user has active subscription
        has_subscription = self.db.subscriptions.has_active_subscription(self.user_id)
        self.assertTrue(has_subscription)
    
    def test_memory_models_and_repositories(self):
        """Test memory models and repositories."""
        # Create memory
        content = "User likes coffee"
        importance = 5
        tags = ["preferences", "food"]
        
        memory = self.db.memories.create_memory(
            self.user_id,
            content,
            importance,
            tags
        )
        
        # Get memory
        memory = self.db.memories.find_by_id(memory.memory_id)
        self.assertEqual(memory.content, content)
        self.assertEqual(memory.importance, importance)
        
        # Get tags
        memory_tags = self.db.memory_tags.get_memory_tags(memory.memory_id)
        self.assertEqual(len(memory_tags), len(tags))
        for tag in tags:
            self.assertIn(tag, memory_tags)
        
        # Update memory
        new_content = "User prefers tea over coffee"
        self.db.memories.update_content(memory.memory_id, new_content)
        
        memory = self.db.memories.find_by_id(memory.memory_id)
        self.assertEqual(memory.content, new_content)
        
        # Update importance
        new_importance = 8
        self.db.memories.update_importance(memory.memory_id, new_importance)
        
        memory = self.db.memories.find_by_id(memory.memory_id)
        self.assertEqual(memory.importance, new_importance)
        
        # Add tag
        new_tag = "drinks"
        self.db.memory_tags.add_tag(memory.memory_id, new_tag)
        
        memory_tags = self.db.memory_tags.get_memory_tags(memory.memory_id)
        self.assertEqual(len(memory_tags), len(tags) + 1)
        self.assertIn(new_tag, memory_tags)
        
        # Remove tag
        self.db.memory_tags.remove_tag(memory.memory_id, new_tag)
        
        memory_tags = self.db.memory_tags.get_memory_tags(memory.memory_id)
        self.assertEqual(len(memory_tags), len(tags))
        self.assertNotIn(new_tag, memory_tags)
        
        # Access memory
        self.db.memories.access_memory(memory.memory_id)
        
        memory = self.db.memories.find_by_id(memory.memory_id)
        self.assertIsNotNone(memory.last_accessed)
    
    def test_voice_models_and_repositories(self):
        """Test voice models and repositories."""
        # Create voice setting
        provider = "deepgram"
        voice_id = "aura"
        
        setting, created = self.db.voice_settings.get_or_create(
            self.user_id,
            provider,
            voice_id
        )
        
        self.assertTrue(created)
        self.assertEqual(setting.provider, provider)
        self.assertEqual(setting.voice_id, voice_id)
        
        # Update voice
        new_provider = "elevenlabs"
        new_voice_id = "bella"
        
        self.db.voice_settings.update_voice(
            self.user_id,
            new_provider,
            new_voice_id
        )
        
        setting = self.db.voice_settings.find_by_user(self.user_id)
        self.assertEqual(setting.provider, new_provider)
        self.assertEqual(setting.voice_id, new_voice_id)
        
        # Update settings
        new_pitch = 1.2
        new_rate = 0.9
        
        self.db.voice_settings.update_settings(
            self.user_id,
            pitch=new_pitch,
            rate=new_rate
        )
        
        setting = self.db.voice_settings.find_by_user(self.user_id)
        self.assertEqual(setting.pitch, new_pitch)
        self.assertEqual(setting.rate, new_rate)
        
        # Create recording
        file_path = "/tmp/recording.mp3"
        duration = 5.5
        
        recording = self.db.voice_recordings.create_recording(
            self.user_id,
            file_path,
            duration=duration
        )
        
        self.assertEqual(recording.user_id, self.user_id)
        self.assertEqual(recording.file_path, file_path)
        self.assertEqual(recording.duration, duration)
        
        # Get total duration
        total_duration = self.db.voice_recordings.get_total_duration(self.user_id)
        self.assertEqual(total_duration, duration)

if __name__ == "__main__":
    unittest.main()
