"""
Conversation analytics for VoicePal.

This module provides analytics for conversation patterns and insights.
"""

import logging
import statistics
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from collections import defaultdict, Counter

logger = logging.getLogger(__name__)

@dataclass
class ConversationMetrics:
    """Conversation metrics data structure."""
    total_conversations: int
    avg_conversation_length: float
    avg_messages_per_conversation: float
    avg_response_time: float
    most_active_hours: List[int]
    conversation_topics: Dict[str, int]
    sentiment_distribution: Dict[str, float]
    user_engagement_score: float

@dataclass
class ConversationInsight:
    """Conversation insight data structure."""
    insight_type: str
    title: str
    description: str
    value: Any
    confidence: float
    timestamp: datetime

class ConversationAnalytics:
    """Analytics for conversation patterns and insights."""
    
    def __init__(self, database, cache_manager=None):
        """
        Initialize conversation analytics.
        
        Args:
            database: Database instance
            cache_manager: Cache manager for performance
        """
        self.database = database
        self.cache_manager = cache_manager
        
        # Topic keywords for basic topic detection
        self.topic_keywords = {
            "greeting": ["hello", "hi", "hey", "good morning", "good evening"],
            "help": ["help", "assist", "support", "problem", "issue"],
            "weather": ["weather", "temperature", "rain", "sunny", "cloudy"],
            "music": ["music", "song", "play", "listen", "artist"],
            "food": ["food", "eat", "hungry", "restaurant", "recipe"],
            "work": ["work", "job", "office", "meeting", "project"],
            "family": ["family", "mother", "father", "child", "parent"],
            "health": ["health", "doctor", "medicine", "sick", "pain"],
            "travel": ["travel", "trip", "vacation", "flight", "hotel"],
            "technology": ["computer", "phone", "app", "software", "internet"]
        }
        
        logger.info("Conversation analytics initialized")
    
    def analyze_conversation_patterns(self, user_id: Optional[int] = None, days: int = 30) -> ConversationMetrics:
        """
        Analyze conversation patterns for a user or globally.
        
        Args:
            user_id: User ID to analyze (None for global analysis)
            days: Number of days to analyze
            
        Returns:
            Conversation metrics
        """
        try:
            # Check cache first
            cache_key = f"conv_patterns_{user_id}_{days}"
            if self.cache_manager:
                cached_result = self.cache_manager.get(cache_key)
                if cached_result:
                    return ConversationMetrics(**cached_result)
            
            # Calculate date range
            end_date = datetime.utcnow()
            start_date = end_date - timedelta(days=days)
            
            # Build query
            if user_id:
                conversations = self.database.execute("""
                    SELECT c.*, COUNT(m.message_id) as message_count
                    FROM conversations c
                    LEFT JOIN messages m ON c.conversation_id = m.conversation_id
                    WHERE c.user_id = ? AND c.created_at >= ?
                    GROUP BY c.conversation_id
                """, (user_id, start_date.isoformat())).fetchall()
            else:
                conversations = self.database.execute("""
                    SELECT c.*, COUNT(m.message_id) as message_count
                    FROM conversations c
                    LEFT JOIN messages m ON c.conversation_id = m.conversation_id
                    WHERE c.created_at >= ?
                    GROUP BY c.conversation_id
                """, (start_date.isoformat(),)).fetchall()
            
            if not conversations:
                return ConversationMetrics(
                    total_conversations=0,
                    avg_conversation_length=0.0,
                    avg_messages_per_conversation=0.0,
                    avg_response_time=0.0,
                    most_active_hours=[],
                    conversation_topics={},
                    sentiment_distribution={},
                    user_engagement_score=0.0
                )
            
            # Calculate metrics
            total_conversations = len(conversations)
            conversation_lengths = []
            message_counts = []
            response_times = []
            hourly_activity = defaultdict(int)
            
            for conv in conversations:
                # Conversation length (duration)
                if conv['ended_at']:
                    start_time = datetime.fromisoformat(conv['created_at'])
                    end_time = datetime.fromisoformat(conv['ended_at'])
                    length = (end_time - start_time).total_seconds() / 60  # minutes
                    conversation_lengths.append(length)
                
                # Message count
                message_counts.append(conv['message_count'])
                
                # Hourly activity
                created_at = datetime.fromisoformat(conv['created_at'])
                hourly_activity[created_at.hour] += 1
            
            # Calculate averages
            avg_conversation_length = statistics.mean(conversation_lengths) if conversation_lengths else 0.0
            avg_messages_per_conversation = statistics.mean(message_counts) if message_counts else 0.0
            avg_response_time = statistics.mean(response_times) if response_times else 0.0
            
            # Most active hours
            most_active_hours = sorted(hourly_activity.keys(), key=lambda h: hourly_activity[h], reverse=True)[:3]
            
            # Analyze topics and sentiment
            conversation_topics = self._analyze_conversation_topics(user_id, start_date, end_date)
            sentiment_distribution = self._analyze_sentiment_distribution(user_id, start_date, end_date)
            
            # Calculate engagement score
            user_engagement_score = self._calculate_engagement_score(
                avg_messages_per_conversation,
                avg_conversation_length,
                len(conversation_topics)
            )
            
            metrics = ConversationMetrics(
                total_conversations=total_conversations,
                avg_conversation_length=avg_conversation_length,
                avg_messages_per_conversation=avg_messages_per_conversation,
                avg_response_time=avg_response_time,
                most_active_hours=most_active_hours,
                conversation_topics=conversation_topics,
                sentiment_distribution=sentiment_distribution,
                user_engagement_score=user_engagement_score
            )
            
            # Cache result
            if self.cache_manager:
                self.cache_manager.set(cache_key, metrics.__dict__, ttl=3600)
            
            return metrics
            
        except Exception as e:
            logger.error(f"Failed to analyze conversation patterns: {e}")
            return ConversationMetrics(
                total_conversations=0,
                avg_conversation_length=0.0,
                avg_messages_per_conversation=0.0,
                avg_response_time=0.0,
                most_active_hours=[],
                conversation_topics={},
                sentiment_distribution={},
                user_engagement_score=0.0
            )
    
    def _analyze_conversation_topics(self, user_id: Optional[int], start_date: datetime, end_date: datetime) -> Dict[str, int]:
        """Analyze conversation topics using keyword matching."""
        try:
            # Get messages for analysis
            if user_id:
                messages = self.database.execute("""
                    SELECT m.content
                    FROM messages m
                    JOIN conversations c ON m.conversation_id = c.conversation_id
                    WHERE c.user_id = ? AND m.created_at >= ? AND m.created_at <= ?
                    AND m.role = 'user'
                """, (user_id, start_date.isoformat(), end_date.isoformat())).fetchall()
            else:
                messages = self.database.execute("""
                    SELECT m.content
                    FROM messages m
                    JOIN conversations c ON m.conversation_id = c.conversation_id
                    WHERE m.created_at >= ? AND m.created_at <= ?
                    AND m.role = 'user'
                """, (start_date.isoformat(), end_date.isoformat())).fetchall()
            
            topic_counts = defaultdict(int)
            
            for message in messages:
                content = message['content'].lower()
                
                # Check for topic keywords
                for topic, keywords in self.topic_keywords.items():
                    if any(keyword in content for keyword in keywords):
                        topic_counts[topic] += 1
            
            return dict(topic_counts)
            
        except Exception as e:
            logger.error(f"Failed to analyze conversation topics: {e}")
            return {}
    
    def _analyze_sentiment_distribution(self, user_id: Optional[int], start_date: datetime, end_date: datetime) -> Dict[str, float]:
        """Analyze sentiment distribution in conversations."""
        try:
            # Get sentiment data from messages
            if user_id:
                sentiments = self.database.execute("""
                    SELECT m.sentiment
                    FROM messages m
                    JOIN conversations c ON m.conversation_id = c.conversation_id
                    WHERE c.user_id = ? AND m.created_at >= ? AND m.created_at <= ?
                    AND m.sentiment IS NOT NULL
                """, (user_id, start_date.isoformat(), end_date.isoformat())).fetchall()
            else:
                sentiments = self.database.execute("""
                    SELECT m.sentiment
                    FROM messages m
                    JOIN conversations c ON m.conversation_id = c.conversation_id
                    WHERE m.created_at >= ? AND m.created_at <= ?
                    AND m.sentiment IS NOT NULL
                """, (start_date.isoformat(), end_date.isoformat())).fetchall()
            
            if not sentiments:
                return {"positive": 0.0, "neutral": 0.0, "negative": 0.0}
            
            sentiment_counts = Counter(s['sentiment'] for s in sentiments)
            total = len(sentiments)
            
            return {
                "positive": sentiment_counts.get("positive", 0) / total,
                "neutral": sentiment_counts.get("neutral", 0) / total,
                "negative": sentiment_counts.get("negative", 0) / total
            }
            
        except Exception as e:
            logger.error(f"Failed to analyze sentiment distribution: {e}")
            return {"positive": 0.0, "neutral": 0.0, "negative": 0.0}
    
    def _calculate_engagement_score(self, avg_messages: float, avg_length: float, topic_diversity: int) -> float:
        """Calculate user engagement score."""
        try:
            # Normalize metrics to 0-1 scale
            message_score = min(avg_messages / 10, 1.0)  # 10+ messages = max score
            length_score = min(avg_length / 30, 1.0)     # 30+ minutes = max score
            topic_score = min(topic_diversity / 5, 1.0)   # 5+ topics = max score
            
            # Weighted average
            engagement_score = (message_score * 0.4 + length_score * 0.4 + topic_score * 0.2)
            
            return round(engagement_score, 3)
            
        except Exception as e:
            logger.error(f"Failed to calculate engagement score: {e}")
            return 0.0
    
    def generate_conversation_insights(self, user_id: Optional[int] = None, days: int = 30) -> List[ConversationInsight]:
        """
        Generate actionable insights from conversation data.
        
        Args:
            user_id: User ID to analyze (None for global analysis)
            days: Number of days to analyze
            
        Returns:
            List of conversation insights
        """
        try:
            insights = []
            metrics = self.analyze_conversation_patterns(user_id, days)
            
            # Engagement insights
            if metrics.user_engagement_score > 0.8:
                insights.append(ConversationInsight(
                    insight_type="engagement",
                    title="High User Engagement",
                    description=f"User shows high engagement with score of {metrics.user_engagement_score:.2f}",
                    value=metrics.user_engagement_score,
                    confidence=0.9,
                    timestamp=datetime.utcnow()
                ))
            elif metrics.user_engagement_score < 0.3:
                insights.append(ConversationInsight(
                    insight_type="engagement",
                    title="Low User Engagement",
                    description=f"User shows low engagement with score of {metrics.user_engagement_score:.2f}",
                    value=metrics.user_engagement_score,
                    confidence=0.8,
                    timestamp=datetime.utcnow()
                ))
            
            # Activity pattern insights
            if metrics.most_active_hours:
                peak_hour = metrics.most_active_hours[0]
                insights.append(ConversationInsight(
                    insight_type="activity_pattern",
                    title="Peak Activity Hour",
                    description=f"Most active during hour {peak_hour}:00",
                    value=peak_hour,
                    confidence=0.7,
                    timestamp=datetime.utcnow()
                ))
            
            # Topic insights
            if metrics.conversation_topics:
                top_topic = max(metrics.conversation_topics.items(), key=lambda x: x[1])
                insights.append(ConversationInsight(
                    insight_type="topic_preference",
                    title="Preferred Topic",
                    description=f"Most discussed topic: {top_topic[0]} ({top_topic[1]} mentions)",
                    value=top_topic,
                    confidence=0.8,
                    timestamp=datetime.utcnow()
                ))
            
            # Sentiment insights
            if metrics.sentiment_distribution:
                dominant_sentiment = max(metrics.sentiment_distribution.items(), key=lambda x: x[1])
                if dominant_sentiment[1] > 0.6:
                    insights.append(ConversationInsight(
                        insight_type="sentiment",
                        title="Dominant Sentiment",
                        description=f"Predominantly {dominant_sentiment[0]} sentiment ({dominant_sentiment[1]:.1%})",
                        value=dominant_sentiment,
                        confidence=0.7,
                        timestamp=datetime.utcnow()
                    ))
            
            return insights
            
        except Exception as e:
            logger.error(f"Failed to generate conversation insights: {e}")
            return []
    
    def get_conversation_trends(self, days: int = 30) -> Dict[str, List[Tuple[str, float]]]:
        """
        Get conversation trends over time.
        
        Args:
            days: Number of days to analyze
            
        Returns:
            Dictionary of trends by metric
        """
        try:
            trends = {
                "daily_conversations": [],
                "daily_messages": [],
                "avg_sentiment": []
            }
            
            end_date = datetime.utcnow()
            
            for i in range(days):
                day_start = end_date - timedelta(days=i+1)
                day_end = end_date - timedelta(days=i)
                
                # Daily conversations
                conv_count = self.database.execute("""
                    SELECT COUNT(*) FROM conversations
                    WHERE created_at >= ? AND created_at < ?
                """, (day_start.isoformat(), day_end.isoformat())).fetchone()[0]
                
                trends["daily_conversations"].append((day_start.strftime("%Y-%m-%d"), conv_count))
                
                # Daily messages
                msg_count = self.database.execute("""
                    SELECT COUNT(*) FROM messages m
                    JOIN conversations c ON m.conversation_id = c.conversation_id
                    WHERE c.created_at >= ? AND c.created_at < ?
                """, (day_start.isoformat(), day_end.isoformat())).fetchone()[0]
                
                trends["daily_messages"].append((day_start.strftime("%Y-%m-%d"), msg_count))
                
                # Average sentiment (simplified)
                avg_sentiment = self.database.execute("""
                    SELECT AVG(CASE 
                        WHEN sentiment = 'positive' THEN 1
                        WHEN sentiment = 'neutral' THEN 0
                        WHEN sentiment = 'negative' THEN -1
                        ELSE 0
                    END) FROM messages m
                    JOIN conversations c ON m.conversation_id = c.conversation_id
                    WHERE c.created_at >= ? AND c.created_at < ?
                    AND sentiment IS NOT NULL
                """, (day_start.isoformat(), day_end.isoformat())).fetchone()[0] or 0
                
                trends["avg_sentiment"].append((day_start.strftime("%Y-%m-%d"), avg_sentiment))
            
            # Reverse to get chronological order
            for key in trends:
                trends[key].reverse()
            
            return trends
            
        except Exception as e:
            logger.error(f"Failed to get conversation trends: {e}")
            return {"daily_conversations": [], "daily_messages": [], "avg_sentiment": []}
