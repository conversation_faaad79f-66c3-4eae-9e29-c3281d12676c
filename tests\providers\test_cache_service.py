"""
Tests for cache service.

This module tests the CacheService class.
"""

import os
import pytest
import json
import time
from pathlib import Path

from bot.providers.core.cache_service import CacheService

def test_init(temp_cache_dir):
    """Test cache service initialization."""
    # Test with directory path
    cache = CacheService(cache_dir=temp_cache_dir)
    assert cache.cache_dir == Path(temp_cache_dir)
    assert cache.max_age == 3600
    assert os.path.exists(temp_cache_dir)
    
    # Test with Path object
    cache = CacheService(cache_dir=Path(temp_cache_dir), max_age=60)
    assert cache.cache_dir == Path(temp_cache_dir)
    assert cache.max_age == 60
    
    # Test with default directory
    cache = CacheService()
    assert cache.cache_dir == Path("cache")
    assert os.path.exists("cache")
    
    # Clean up default directory
    import shutil
    shutil.rmtree("cache")

def test_generate_key():
    """Test key generation."""
    cache = CacheService()
    
    # Test with string
    key1 = cache._generate_key("test", "data")
    assert key1.startswith("test_")
    assert len(key1) > 5
    
    # Test with dictionary
    key2 = cache._generate_key("test", {"key": "value"})
    assert key2.startswith("test_")
    assert len(key2) > 5
    
    # Test with other type
    key3 = cache._generate_key("test", 123)
    assert key3.startswith("test_")
    assert len(key3) > 5
    
    # Test deterministic
    key4 = cache._generate_key("test", "data")
    assert key4 == key1
    
    key5 = cache._generate_key("test", {"key": "value"})
    assert key5 == key2

def test_set_get(temp_cache_dir):
    """Test setting and getting cache items."""
    cache = CacheService(cache_dir=temp_cache_dir)
    
    # Test setting and getting string
    cache.set("test", "key1", "value1")
    value = cache.get("test", "key1")
    assert value == "value1"
    
    # Test setting and getting dictionary
    cache.set("test", "key2", {"nested": "value"})
    value = cache.get("test", "key2")
    assert value == {"nested": "value"}
    
    # Test setting and getting list
    cache.set("test", "key3", [1, 2, 3])
    value = cache.get("test", "key3")
    assert value == [1, 2, 3]
    
    # Test getting non-existent key
    value = cache.get("test", "non_existent")
    assert value is None
    
    # Test file cache
    key = cache._generate_key("test", "key1")
    cache_file = cache.cache_dir / f"{key}.json"
    assert os.path.exists(cache_file)
    
    with open(cache_file, "r") as f:
        file_value = json.load(f)
    
    assert file_value == "value1"
    
    # Test memory cache is used
    os.unlink(cache_file)
    value = cache.get("test", "key1")
    assert value == "value1"

def test_expiration(temp_cache_dir):
    """Test cache expiration."""
    cache = CacheService(cache_dir=temp_cache_dir, max_age=1)  # 1 second expiration
    
    # Set cache item
    cache.set("test", "key", "value")
    
    # Get immediately
    value = cache.get("test", "key")
    assert value == "value"
    
    # Wait for expiration
    time.sleep(1.1)
    
    # Get after expiration
    value = cache.get("test", "key")
    assert value is None

def test_clear(temp_cache_dir):
    """Test clearing cache."""
    cache = CacheService(cache_dir=temp_cache_dir, max_age=1)
    
    # Set cache items
    cache.set("prefix1", "key1", "value1")
    cache.set("prefix1", "key2", "value2")
    cache.set("prefix2", "key3", "value3")
    
    # Wait for expiration
    time.sleep(1.1)
    
    # Clear expired items
    cleared = cache.clear()
    assert cleared == 3
    
    # Verify items were cleared
    assert cache.get("prefix1", "key1") is None
    assert cache.get("prefix1", "key2") is None
    assert cache.get("prefix2", "key3") is None
    
    # Set new items
    cache.set("prefix1", "key1", "value1")
    cache.set("prefix1", "key2", "value2")
    cache.set("prefix2", "key3", "value3")
    
    # Clear specific prefix
    cleared = cache.clear(prefix="prefix1")
    assert cleared == 2
    
    # Verify only prefix1 items were cleared
    assert cache.get("prefix1", "key1") is None
    assert cache.get("prefix1", "key2") is None
    assert cache.get("prefix2", "key3") == "value3"
    
    # Clear with custom max_age
    cache.set("prefix1", "key1", "value1")
    cache.set("prefix1", "key2", "value2")
    
    # No items should be cleared with max_age=10
    cleared = cache.clear(max_age=10)
    assert cleared == 0
    
    # Items should still be accessible
    assert cache.get("prefix1", "key1") == "value1"
    assert cache.get("prefix1", "key2") == "value2"

def test_delete(temp_cache_dir):
    """Test deleting cache items."""
    cache = CacheService(cache_dir=temp_cache_dir)
    
    # Set cache item
    cache.set("test", "key", "value")
    
    # Delete item
    deleted = cache.delete("test", "key")
    assert deleted is True
    
    # Verify item was deleted
    value = cache.get("test", "key")
    assert value is None
    
    # Test deleting non-existent item
    deleted = cache.delete("test", "non_existent")
    assert deleted is False
    
    # Test deleting from file cache only
    cache.set("test", "key", "value")
    key = cache._generate_key("test", "key")
    
    # Remove from memory cache
    del cache.cache[key]
    del cache.timestamps[key]
    
    # Delete from file cache
    deleted = cache.delete("test", "key")
    assert deleted is True
    
    # Verify item was deleted
    value = cache.get("test", "key")
    assert value is None
