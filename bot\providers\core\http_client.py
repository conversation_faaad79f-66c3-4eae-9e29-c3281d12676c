"""
HTTP client service for VoicePal providers.

This module provides a shared HTTP client for making API requests with
consistent error handling, retries, and logging.
"""

import logging
import httpx
import asyncio
from typing import Dict, Any, Optional, Union, List
from urllib.parse import urljoin

from bot.providers.core.exceptions import (
    ProviderAPIError,
    ProviderTimeoutError,
    ProviderAuthenticationError,
    ProviderRateLimitError,
    ProviderServiceUnavailableError
)

# Set up logging
logger = logging.getLogger(__name__)

class HTTPClient:
    """Shared HTTP client for provider API calls."""
    
    def __init__(self, base_url: str = None, timeout: float = 30.0):
        """Initialize HTTP client.
        
        Args:
            base_url: Base URL for API calls
            timeout: Default timeout in seconds
        """
        self.base_url = base_url
        self.timeout = timeout
        self.default_headers = {
            "User-Agent": "VoicePal/1.0",
            "Accept": "application/json"
        }
    
    def _build_url(self, endpoint: str) -> str:
        """Build full URL from endpoint.
        
        Args:
            endpoint: API endpoint
            
        Returns:
            Full URL
        """
        if self.base_url:
            return urljoin(self.base_url, endpoint)
        return endpoint
    
    async def request(self, method: str, endpoint: str, 
                     headers: Dict[str, str] = None,
                     params: Dict[str, Any] = None,
                     json: Dict[str, Any] = None,
                     data: Any = None,
                     timeout: float = None,
                     retries: int = 3,
                     retry_delay: float = 1.0) -> Dict[str, Any]:
        """Make an HTTP request.
        
        Args:
            method: HTTP method (GET, POST, etc.)
            endpoint: API endpoint
            headers: Request headers
            params: Query parameters
            json: JSON body
            data: Form data or raw body
            timeout: Request timeout in seconds
            retries: Number of retry attempts
            retry_delay: Delay between retries in seconds
            
        Returns:
            Response data as dictionary
            
        Raises:
            ProviderAPIError: If the API call fails
            ProviderTimeoutError: If the API call times out
            ProviderAuthenticationError: If authentication fails
            ProviderRateLimitError: If rate limit is exceeded
        """
        url = self._build_url(endpoint)
        _timeout = timeout or self.timeout
        _headers = {**self.default_headers, **(headers or {})}
        
        for attempt in range(retries):
            try:
                async with httpx.AsyncClient(timeout=_timeout) as client:
                    response = await client.request(
                        method=method,
                        url=url,
                        headers=_headers,
                        params=params,
                        json=json,
                        data=data
                    )
                    
                    # Handle common error status codes
                    if response.status_code == 401:
                        raise ProviderAuthenticationError(
                            f"Authentication failed: {response.text}",
                            status_code=response.status_code,
                            response_text=response.text
                        )
                    elif response.status_code == 429:
                        if attempt < retries - 1:
                            retry_after = int(response.headers.get("Retry-After", retry_delay))
                            logger.warning(f"Rate limit exceeded. Retrying after {retry_after} seconds.")
                            await asyncio.sleep(retry_after)
                            continue
                        else:
                            raise ProviderRateLimitError(
                                f"Rate limit exceeded: {response.text}",
                                status_code=response.status_code,
                                response_text=response.text
                            )
                    elif response.status_code >= 500:
                        if attempt < retries - 1:
                            logger.warning(f"Server error: {response.status_code}. Retrying...")
                            await asyncio.sleep(retry_delay * (attempt + 1))  # Exponential backoff
                            continue
                        else:
                            raise ProviderServiceUnavailableError(
                                f"Service unavailable: {response.text}",
                                status_code=response.status_code,
                                response_text=response.text
                            )
                    elif response.status_code >= 400:
                        raise ProviderAPIError(
                            f"API error: {response.status_code} - {response.text}",
                            status_code=response.status_code,
                            response_text=response.text
                        )
                    
                    # Parse JSON response if possible
                    try:
                        return response.json()
                    except ValueError:
                        # Return raw text if not JSON
                        return {"text": response.text}
                        
            except httpx.TimeoutException as e:
                if attempt < retries - 1:
                    logger.warning(f"Request timed out. Retrying ({attempt + 1}/{retries})...")
                    await asyncio.sleep(retry_delay)
                    continue
                else:
                    logger.error(f"Request timed out after {retries} attempts: {e}")
                    raise ProviderTimeoutError(f"Request timed out: {e}") from e
            except (ProviderAuthenticationError, ProviderRateLimitError, ProviderAPIError, ProviderServiceUnavailableError):
                # Re-raise these exceptions without retry
                raise
            except Exception as e:
                if attempt < retries - 1:
                    logger.warning(f"Request failed. Retrying ({attempt + 1}/{retries}): {e}")
                    await asyncio.sleep(retry_delay)
                    continue
                else:
                    logger.error(f"Request failed after {retries} attempts: {e}")
                    raise ProviderAPIError(f"Request failed: {e}") from e
    
    async def get(self, endpoint: str, **kwargs) -> Dict[str, Any]:
        """Make a GET request.
        
        Args:
            endpoint: API endpoint
            **kwargs: Additional request parameters
            
        Returns:
            Response data
        """
        return await self.request("GET", endpoint, **kwargs)
    
    async def post(self, endpoint: str, **kwargs) -> Dict[str, Any]:
        """Make a POST request.
        
        Args:
            endpoint: API endpoint
            **kwargs: Additional request parameters
            
        Returns:
            Response data
        """
        return await self.request("POST", endpoint, **kwargs)
    
    async def put(self, endpoint: str, **kwargs) -> Dict[str, Any]:
        """Make a PUT request.
        
        Args:
            endpoint: API endpoint
            **kwargs: Additional request parameters
            
        Returns:
            Response data
        """
        return await self.request("PUT", endpoint, **kwargs)
    
    async def delete(self, endpoint: str, **kwargs) -> Dict[str, Any]:
        """Make a DELETE request.
        
        Args:
            endpoint: API endpoint
            **kwargs: Additional request parameters
            
        Returns:
            Response data
        """
        return await self.request("DELETE", endpoint, **kwargs)
