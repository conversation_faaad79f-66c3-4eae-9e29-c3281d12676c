{"providers": {"stt": {"type": "deepgram", "api_key": "your_deepgram_api_key_here"}, "ai": {"type": "google_ai", "api_key": "your_google_ai_api_key_here", "model_name": "gemini-2.0-flash"}, "tts": {"type": "deepgram", "api_key": "your_deepgram_api_key_here", "voice_id": "aura-2-thalia-en", "model_id": "aura-2"}}, "features": {"memory": {"enabled": true, "conversation_memory": 10, "summary_update_frequency": 24}, "mood_tracking": {"enabled": true, "analysis_frequency": 7}, "personalization": {"enabled": true, "default_personality": "friendly", "default_language": "en", "default_voice": "default"}}, "credit_system": {"enabled": true, "text_credit_cost": 1, "voice_credit_cost": 3, "free_trial_credits": 10}, "telegram": {"token": "your_telegram_token_here", "payment_provider_token": "your_payment_provider_token_here", "admin_user_ids": [123456789]}, "database": {"file": "voicepal.db"}}