"""
Test script for MoodEntry.

This script tests the MoodEntry class functionality.
"""

import unittest
from unittest.mock import <PERSON><PERSON>ock, patch

from telegram import InlineKeyboardMarkup, Update, CallbackQuery, Message, Chat, User
from telegram.ext import ContextTypes

from bot.features.mood_entry import MoodEntry


class TestMoodEntry(unittest.TestCase):
    """Test cases for the MoodEntry class."""
    
    def setUp(self):
        """Set up test environment."""
        # Create a mock database
        self.mock_db = MagicMock()
        
        # Create a mock mood tracker
        self.mock_mood_tracker = MagicMock()
        
        # Create a mock config
        self.mock_config = {}
        
        # Create a MoodEntry instance
        self.mood_entry = MoodEntry(
            database=self.mock_db,
            mood_tracker=self.mock_mood_tracker,
            config=self.mock_config
        )
        
        # Create mock update and context objects
        self.mock_update = MagicMock(spec=Update)
        self.mock_context = MagicMock(spec=ContextTypes.DEFAULT_TYPE)
        
        # Create mock user, chat, and message objects
        self.mock_user = MagicMock(spec=User)
        self.mock_user.id = 123
        self.mock_user.first_name = "Test"
        
        self.mock_chat = MagicMock(spec=Chat)
        self.mock_chat.id = 123
        
        self.mock_message = MagicMock(spec=Message)
        self.mock_message.chat = self.mock_chat
        self.mock_message.reply_text = MagicMock()
        
        self.mock_update.message = self.mock_message
        self.mock_update.effective_user = self.mock_user
        
        # Create mock callback query
        self.mock_callback_query = MagicMock(spec=CallbackQuery)
        self.mock_callback_query.from_user = self.mock_user
        self.mock_callback_query.message = self.mock_message
        self.mock_callback_query.answer = MagicMock()
        self.mock_callback_query.edit_message_text = MagicMock()
        
        self.mock_update.callback_query = self.mock_callback_query
    
    def test_get_mood_entry_keyboard(self):
        """Test get_mood_entry_keyboard method."""
        # Get mood entry keyboard
        keyboard = self.mood_entry.get_mood_entry_keyboard()
        
        # Check that it's an InlineKeyboardMarkup
        self.assertIsInstance(keyboard, InlineKeyboardMarkup)
        
        # Check that it has the expected number of rows (3 rows of 2 buttons each + cancel button)
        self.assertEqual(len(keyboard.inline_keyboard), 4)
        
        # Check that the last button is the cancel button
        self.assertEqual(keyboard.inline_keyboard[-1][0].text, "❌ Cancel")
        self.assertEqual(keyboard.inline_keyboard[-1][0].callback_data, "mood_entry_cancel")
        
        # Check that all mood buttons have the expected format
        for i in range(3):
            self.assertEqual(len(keyboard.inline_keyboard[i]), 2)
            for j in range(2):
                if i * 2 + j < 6:  # There are 6 mood options
                    self.assertTrue(keyboard.inline_keyboard[i][j].callback_data.startswith("mood_entry_"))
    
    async def test_start_mood_entry(self):
        """Test start_mood_entry method."""
        # Start mood entry
        await self.mood_entry.start_mood_entry(self.mock_update, self.mock_context)
        
        # Check that reply_text was called with a prompt and keyboard
        self.mock_message.reply_text.assert_called_once()
        args, kwargs = self.mock_message.reply_text.call_args
        self.assertIn(args[0], self.mood_entry.mood_prompts)
        self.assertIsInstance(kwargs["reply_markup"], InlineKeyboardMarkup)
    
    async def test_handle_mood_entry_callback_cancel(self):
        """Test handle_mood_entry_callback method with cancel."""
        # Set callback data to cancel
        self.mock_callback_query.data = "mood_entry_cancel"
        
        # Handle mood entry callback
        await self.mood_entry.handle_mood_entry_callback(self.mock_update, self.mock_context)
        
        # Check that answer was called
        self.mock_callback_query.answer.assert_called_once()
        
        # Check that edit_message_text was called with the expected message
        self.mock_callback_query.edit_message_text.assert_called_once_with("Mood entry cancelled.")
    
    async def test_handle_mood_entry_callback_happy(self):
        """Test handle_mood_entry_callback method with happy mood."""
        # Set callback data to happy
        self.mock_callback_query.data = "mood_entry_happy"
        
        # Handle mood entry callback
        await self.mood_entry.handle_mood_entry_callback(self.mock_update, self.mock_context)
        
        # Check that answer was called
        self.mock_callback_query.answer.assert_called_once()
        
        # Check that add_mood_entry was called with the expected mood data
        self.mock_mood_tracker.add_mood_entry.assert_called_once()
        args, kwargs = self.mock_mood_tracker.add_mood_entry.call_args
        self.assertEqual(args[0], 123)
        self.assertEqual(args[1]["sentiment"], "positive")
        self.assertEqual(args[1]["source"], "manual")
        
        # Check that edit_message_text was called with the expected message
        self.mock_callback_query.edit_message_text.assert_called_once()
        args, kwargs = self.mock_callback_query.edit_message_text.call_args
        self.assertIn("Thanks for sharing!", args[0])
        self.assertIn("Happy", args[0])
    
    async def test_handle_mood_entry_callback_sad(self):
        """Test handle_mood_entry_callback method with sad mood."""
        # Set callback data to sad
        self.mock_callback_query.data = "mood_entry_sad"
        
        # Handle mood entry callback
        await self.mood_entry.handle_mood_entry_callback(self.mock_update, self.mock_context)
        
        # Check that answer was called
        self.mock_callback_query.answer.assert_called_once()
        
        # Check that add_mood_entry was called with the expected mood data
        self.mock_mood_tracker.add_mood_entry.assert_called_once()
        args, kwargs = self.mock_mood_tracker.add_mood_entry.call_args
        self.assertEqual(args[0], 123)
        self.assertEqual(args[1]["sentiment"], "negative")
        self.assertEqual(args[1]["source"], "manual")
        
        # Check that edit_message_text was called with the expected message and follow-up keyboard
        self.mock_callback_query.edit_message_text.assert_called_once()
        args, kwargs = self.mock_callback_query.edit_message_text.call_args
        self.assertIn("Thanks for sharing!", args[0])
        self.assertIn("Sad", args[0])
        self.assertIn("Is there anything you'd like to talk about?", args[0])
        self.assertIsInstance(kwargs["reply_markup"], InlineKeyboardMarkup)
    
    async def test_handle_mood_entry_callback_invalid(self):
        """Test handle_mood_entry_callback method with invalid mood."""
        # Set callback data to invalid
        self.mock_callback_query.data = "mood_entry_invalid"
        
        # Handle mood entry callback
        await self.mood_entry.handle_mood_entry_callback(self.mock_update, self.mock_context)
        
        # Check that answer was called
        self.mock_callback_query.answer.assert_called_once()
        
        # Check that edit_message_text was called with the expected message
        self.mock_callback_query.edit_message_text.assert_called_once_with("Invalid mood selection.")
    
    async def test_handle_mood_followup_callback_chat(self):
        """Test handle_mood_followup_callback method with chat."""
        # Set callback data to chat
        self.mock_callback_query.data = "mood_followup_chat"
        
        # Handle mood followup callback
        await self.mood_entry.handle_mood_followup_callback(self.mock_update, self.mock_context)
        
        # Check that answer was called
        self.mock_callback_query.answer.assert_called_once()
        
        # Check that edit_message_text was called with the expected message
        self.mock_callback_query.edit_message_text.assert_called_once()
        args, kwargs = self.mock_callback_query.edit_message_text.call_args
        self.assertIn("I'm here to listen", args[0])
    
    async def test_handle_mood_followup_callback_no(self):
        """Test handle_mood_followup_callback method with no."""
        # Set callback data to no
        self.mock_callback_query.data = "mood_followup_no"
        
        # Handle mood followup callback
        await self.mood_entry.handle_mood_followup_callback(self.mock_update, self.mock_context)
        
        # Check that answer was called
        self.mock_callback_query.answer.assert_called_once()
        
        # Check that edit_message_text was called with the expected message and keyboard
        self.mock_callback_query.edit_message_text.assert_called_once()
        args, kwargs = self.mock_callback_query.edit_message_text.call_args
        self.assertIn("No problem!", args[0])
        self.assertIsInstance(kwargs["reply_markup"], InlineKeyboardMarkup)
    
    async def test_handle_mood_followup_callback_invalid(self):
        """Test handle_mood_followup_callback method with invalid."""
        # Set callback data to invalid
        self.mock_callback_query.data = "mood_followup_invalid"
        
        # Handle mood followup callback
        result = await self.mood_entry.handle_mood_followup_callback(self.mock_update, self.mock_context)
        
        # Check that answer was called
        self.mock_callback_query.answer.assert_called_once()
        
        # Check that the result is as expected
        self.assertEqual(result, ("Invalid selection.", None))


if __name__ == "__main__":
    unittest.main()
