"""
Custom exceptions for the VoicePal bot.

This module defines custom exceptions that are used throughout the VoicePal bot
to provide more specific error handling and better error messages.
"""

class VoicePalError(Exception):
    """Base exception for all VoicePal errors."""
    pass


class DatabaseError(VoicePalError):
    """Base exception for database-related errors."""
    pass


class DatabaseConnectionError(DatabaseError):
    """Exception raised when there's an error connecting to the database."""
    pass


class DatabaseQueryError(DatabaseError):
    """Exception raised when there's an error executing a database query."""
    pass


class DatabaseIntegrityError(DatabaseError):
    """Exception raised when there's a database integrity error."""
    pass


class AIProviderError(VoicePalError):
    """Base exception for AI provider-related errors."""
    pass


class AIProviderConnectionError(AIProviderError):
    """Exception raised when there's an error connecting to the AI provider."""
    pass


class AIProviderResponseError(AIProviderError):
    """Exception raised when there's an error in the AI provider's response."""
    pass


class AIProviderQuotaExceededError(AIProviderError):
    """Exception raised when the AI provider's quota is exceeded."""
    pass


class VoiceProcessingError(VoicePalError):
    """Base exception for voice processing-related errors."""
    pass


class STTError(VoiceProcessingError):
    """Exception raised when there's an error in speech-to-text processing."""
    pass


class TTSError(VoiceProcessingError):
    """Exception raised when there's an error in text-to-speech processing."""
    pass


class PaymentError(VoicePalError):
    """Base exception for payment-related errors."""
    pass


class PaymentProviderError(PaymentError):
    """Exception raised when there's an error with the payment provider."""
    pass


class PaymentValidationError(PaymentError):
    """Exception raised when there's an error validating a payment."""
    pass


class UserError(VoicePalError):
    """Base exception for user-related errors."""
    pass


class UserNotFoundError(UserError):
    """Exception raised when a user is not found."""
    pass


class InsufficientCreditsError(UserError):
    """Exception raised when a user has insufficient credits."""
    pass


class ConfigurationError(VoicePalError):
    """Exception raised when there's a configuration error."""
    pass


class WebhookError(VoicePalError):
    """Exception raised when there's an error with the webhook."""
    pass
