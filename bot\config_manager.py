"""
Configuration manager for VoicePal.

This module provides a configuration manager for the VoicePal bot.
"""

import os
import json
import logging
from typing import Dict, Any, List
from dotenv import load_dotenv

# Import centralized logging configuration
from bot.core.logging_config import get_logger

# Set up logging
logger = get_logger(__name__)

class ConfigManager:
    """Configuration manager for VoicePal."""

    def __init__(self, config_file: str = "config.json"):
        """
        Initialize the configuration manager.

        Args:
            config_file: Path to the configuration file
        """
        # Load environment variables
        load_dotenv()

        self.config_file = config_file
        self.config = self._load_config()

        # Initialize default configuration
        self._init_default_config()

        logger.info(f"Configuration manager initialized: {config_file}")

    def _load_config(self) -> Dict[str, Any]:
        """
        Load configuration from file.

        Returns:
            Dict containing configuration
        """
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, "r") as f:
                    return json.load(f)
            return {}
        except Exception as e:
            logger.error(f"Error loading configuration: {e}")
            return {}

    def _save_config(self) -> bool:
        """
        Save configuration to file.

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            with open(self.config_file, "w") as f:
                json.dump(self.config, f, indent=4)
            logger.info(f"Configuration saved to {self.config_file}")
            return True
        except Exception as e:
            logger.error(f"Error saving configuration: {e}")
            return False

    def _init_default_config(self) -> None:
        """Initialize default configuration."""
        # Default providers configuration
        if "providers" not in self.config:
            self.config["providers"] = {
                "stt": {
                    "type": os.getenv("STT_PROVIDER", "deepgram"),
                    "api_key": os.getenv("DEEPGRAM_API_KEY", "")
                },
                "ai": {
                    "type": os.getenv("AI_PROVIDER", "google_ai"),
                    "api_key": os.getenv("GOOGLE_AI_API_KEY", "") if os.getenv("AI_PROVIDER", "google_ai").lower() == "google_ai" else os.getenv("GROQ_API_KEY", ""),
                    "model_name": os.getenv("AI_MODEL_NAME", "gemini-2.0-flash") if os.getenv("AI_PROVIDER", "google_ai").lower() == "google_ai" else os.getenv("GROQ_MODEL_NAME", "mixtral-8x7b-32768")
                },
                "tts": {
                    "type": os.getenv("TTS_PROVIDER", "elevenlabs"),
                    "api_key": os.getenv("ELEVENLABS_API_KEY", ""),
                    "voice_id": os.getenv("TTS_VOICE_ID", "Bella"),
                    "model_id": os.getenv("TTS_MODEL_ID", "eleven_multilingual_v2")
                }
            }

        # Default features configuration
        if "features" not in self.config:
            self.config["features"] = {
                "memory": {
                    "enabled": os.getenv("ENABLE_ENHANCED_MEMORY", "true").lower() == "true",
                    "conversation_memory": int(os.getenv("CONVERSATION_MEMORY", "10")),
                    "summary_update_frequency": int(os.getenv("SUMMARY_UPDATE_FREQUENCY", "24"))  # hours
                },
                "mood_tracking": {
                    "enabled": True,
                    "analysis_frequency": int(os.getenv("MOOD_ANALYSIS_FREQUENCY", "7"))  # days
                },
                "personalization": {
                    "enabled": True,
                    "default_personality": os.getenv("DEFAULT_PERSONALITY", "friendly"),
                    "default_language": os.getenv("DEFAULT_LANGUAGE", "en"),
                    "default_voice": os.getenv("DEFAULT_VOICE", "default")
                },
                "sentiment": {
                    "enabled": os.getenv("ENABLE_SENTIMENT_ANALYSIS", "true").lower() == "true",
                    "response_adjustment": True,
                    "store_history": True
                },
                "security_monitoring": {
                    "enabled": os.getenv("ENABLE_SECURITY_MONITORING", "true").lower() == "true",
                    "alert_thresholds": {
                        "failed_login_attempts": int(os.getenv("SECURITY_FAILED_LOGIN_THRESHOLD", "5")),
                        "payment_failures": int(os.getenv("SECURITY_PAYMENT_FAILURES_THRESHOLD", "3")),
                        "api_key_access": int(os.getenv("SECURITY_API_KEY_ACCESS_THRESHOLD", "10")),
                        "credit_usage_rate": int(os.getenv("SECURITY_CREDIT_USAGE_THRESHOLD", "50")),
                        "multiple_accounts": int(os.getenv("SECURITY_MULTIPLE_ACCOUNTS_THRESHOLD", "2")),
                        "verification_failures": int(os.getenv("SECURITY_VERIFICATION_FAILURES_THRESHOLD", "3"))
                    },
                    "alert_cooldowns": {
                        "INFO": int(os.getenv("SECURITY_INFO_COOLDOWN", "3600")),
                        "LOW": int(os.getenv("SECURITY_LOW_COOLDOWN", "1800")),
                        "MEDIUM": int(os.getenv("SECURITY_MEDIUM_COOLDOWN", "900")),
                        "HIGH": int(os.getenv("SECURITY_HIGH_COOLDOWN", "300")),
                        "CRITICAL": int(os.getenv("SECURITY_CRITICAL_COOLDOWN", "60"))
                    },
                    "audit_retention_days": int(os.getenv("SECURITY_AUDIT_RETENTION_DAYS", "90")),
                    "file_logging_enabled": os.getenv("SECURITY_FILE_LOGGING_ENABLED", "false").lower() == "true",
                    "log_directory": os.getenv("SECURITY_LOG_DIRECTORY", "logs")
                },
                "payment_security": {
                    "enabled": os.getenv("ENABLE_PAYMENT_SECURITY", "true").lower() == "true",
                    "thresholds": {
                        "max_daily_payments": int(os.getenv("PAYMENT_MAX_DAILY_PAYMENTS", "10")),
                        "max_daily_amount": float(os.getenv("PAYMENT_MAX_DAILY_AMOUNT", "100")),
                        "max_payment_attempts": int(os.getenv("PAYMENT_MAX_ATTEMPTS", "5")),
                        "suspicious_time_window": int(os.getenv("PAYMENT_SUSPICIOUS_TIME_WINDOW", "5")),
                        "max_payment_velocity": int(os.getenv("PAYMENT_MAX_VELOCITY", "3")),
                        "max_different_payment_methods": int(os.getenv("PAYMENT_MAX_DIFFERENT_METHODS", "3")),
                        "min_account_age_days": int(os.getenv("PAYMENT_MIN_ACCOUNT_AGE_DAYS", "1")),
                        "max_credits_per_payment": int(os.getenv("PAYMENT_MAX_CREDITS", "1000")),
                        "max_payment_amount": float(os.getenv("PAYMENT_MAX_AMOUNT", "100")),
                        "max_payment_frequency": int(os.getenv("PAYMENT_MAX_FREQUENCY", "10"))
                    }
                }
            }

        # Default credit system configuration
        if "credit_system" not in self.config:
            self.config["credit_system"] = {
                "enabled": True,
                "text_credit_cost": int(os.getenv("TEXT_CREDIT_COST", "1")),
                "voice_credit_cost": int(os.getenv("VOICE_CREDIT_COST", "3")),
                "free_trial_credits": int(os.getenv("FREE_TRIAL_CREDITS", "100"))
            }

        # Default telegram configuration
        if "telegram" not in self.config:
            self.config["telegram"] = {
                "token": os.getenv("TELEGRAM_TOKEN", ""),
                "payment_provider_token": os.getenv("PAYMENT_PROVIDER_TOKEN", ""),
                "admin_user_ids": [
                    int(id.strip()) for id in os.getenv("ADMIN_USER_IDS", "").split(",")
                    if id.strip()
                ],
                "use_telegram_stars": os.getenv("USE_TELEGRAM_STARS", "true").lower() == "true"
            }

        # Default database configuration
        if "database" not in self.config:
            self.config["database"] = {
                "file": os.getenv("DATABASE_FILE", "voicepal.db")
            }

        # Save default configuration
        self._save_config()

    def get_provider_config(self, provider_type: str) -> Dict[str, Any]:
        """
        Get configuration for a provider type.

        Args:
            provider_type: Provider type (stt, ai, tts)

        Returns:
            Dict containing provider configuration
        """
        return self.config.get("providers", {}).get(provider_type, {})

    def get_feature_config(self, feature_name: str) -> Dict[str, Any]:
        """
        Get configuration for a feature.

        Args:
            feature_name: Feature name

        Returns:
            Dict containing feature configuration
        """
        return self.config.get("features", {}).get(feature_name, {})

    def is_feature_enabled(self, feature_name: str, default: bool = False) -> bool:
        """
        Check if a feature is enabled.

        Args:
            feature_name: Feature name
            default: Default value if feature config doesn't exist

        Returns:
            bool: True if feature is enabled, False otherwise
        """
        feature_config = self.get_feature_config(feature_name)
        return feature_config.get("enabled", default)

    def enable_feature(self, feature_name: str) -> bool:
        """
        Enable a feature.

        Args:
            feature_name: Feature name

        Returns:
            bool: True if successful, False otherwise
        """
        if "features" not in self.config:
            self.config["features"] = {}

        if feature_name not in self.config["features"]:
            self.config["features"][feature_name] = {}

        self.config["features"][feature_name]["enabled"] = True

        return self._save_config()

    def disable_feature(self, feature_name: str) -> bool:
        """
        Disable a feature.

        Args:
            feature_name: Feature name

        Returns:
            bool: True if successful, False otherwise
        """
        if "features" not in self.config:
            return False

        if feature_name not in self.config["features"]:
            return False

        self.config["features"][feature_name]["enabled"] = False

        return self._save_config()

    def update_feature_config(self, feature_name: str, config: Dict[str, Any]) -> bool:
        """
        Update feature configuration.

        Args:
            feature_name: Feature name
            config: Feature configuration

        Returns:
            bool: True if successful, False otherwise
        """
        if "features" not in self.config:
            self.config["features"] = {}

        if feature_name not in self.config["features"]:
            self.config["features"][feature_name] = {}

        # Update feature configuration
        for key, value in config.items():
            self.config["features"][feature_name][key] = value

        return self._save_config()

    def get_credit_system_config(self) -> Dict[str, Any]:
        """
        Get credit system configuration.

        Returns:
            Dict containing credit system configuration
        """
        return self.config.get("credit_system", {})

    def get_telegram_config(self) -> Dict[str, Any]:
        """
        Get Telegram configuration.

        Returns:
            Dict containing Telegram configuration
        """
        return self.config.get("telegram", {})

    def get_database_config(self) -> Dict[str, Any]:
        """
        Get database configuration.

        Returns:
            Dict containing database configuration
        """
        return self.config.get("database", {})

    def get_admin_user_ids(self) -> List[int]:
        """
        Get admin user IDs.

        Returns:
            List of admin user IDs
        """
        return self.config.get("telegram", {}).get("admin_user_ids", [])
