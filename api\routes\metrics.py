"""
Metrics routes for VoicePal API.

This module provides metrics endpoints for monitoring and observability.
"""

import time
import logging
from typing import Dict, Any
from datetime import datetime, timedelta

from fastapi import APIRouter, Response, HTTPException
from prometheus_client import (
    Counter, Histogram, Gauge, generate_latest, 
    CollectorRegistry, CONTENT_TYPE_LATEST
)

from api.app import get_bot_instance, get_config

logger = logging.getLogger(__name__)

router = APIRouter()

# Create a custom registry for VoicePal metrics
registry = CollectorRegistry()

# Define metrics
request_count = Counter(
    'voicepal_api_requests_total',
    'Total number of API requests',
    ['method', 'endpoint', 'status'],
    registry=registry
)

request_duration = Histogram(
    'voicepal_api_request_duration_seconds',
    'API request duration in seconds',
    ['method', 'endpoint'],
    registry=registry
)

active_users = Gauge(
    'voicepal_active_users',
    'Number of active users',
    registry=registry
)

total_users = Gauge(
    'voicepal_total_users',
    'Total number of users',
    registry=registry
)

total_conversations = Gauge(
    'voicepal_total_conversations',
    'Total number of conversations',
    registry=registry
)

total_messages = Gauge(
    'voicepal_total_messages',
    'Total number of messages',
    registry=registry
)

bot_uptime = Gauge(
    'voicepal_bot_uptime_seconds',
    'Bot uptime in seconds',
    registry=registry
)

memory_usage = Gauge(
    'voicepal_memory_usage_percent',
    'Memory usage percentage',
    registry=registry
)

cpu_usage = Gauge(
    'voicepal_cpu_usage_percent',
    'CPU usage percentage',
    registry=registry
)

# Track startup time
startup_time = time.time()

@router.get("/prometheus")
async def prometheus_metrics():
    """
    Get Prometheus metrics.
    
    Returns:
        Prometheus metrics in text format
    """
    try:
        # Update metrics before returning
        await _update_metrics()
        
        # Generate metrics
        metrics_data = generate_latest(registry)
        
        return Response(
            content=metrics_data,
            media_type=CONTENT_TYPE_LATEST
        )
        
    except Exception as e:
        logger.error(f"Failed to generate Prometheus metrics: {e}")
        raise HTTPException(status_code=500, detail="Failed to generate metrics")

@router.get("/")
async def get_metrics():
    """
    Get metrics in JSON format.
    
    Returns:
        Metrics data in JSON format
    """
    try:
        # Update metrics
        await _update_metrics()
        
        # Get bot instance
        bot = get_bot_instance()
        
        # Collect metrics
        metrics = {
            "timestamp": datetime.utcnow().isoformat(),
            "uptime": time.time() - startup_time,
            "bot": await _get_bot_metrics(bot),
            "system": await _get_system_metrics(),
            "database": await _get_database_metrics(bot),
            "api": await _get_api_metrics()
        }
        
        return metrics
        
    except Exception as e:
        logger.error(f"Failed to get metrics: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve metrics")

@router.get("/health")
async def metrics_health():
    """
    Health check for metrics endpoint.
    
    Returns:
        Health status
    """
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "metrics_enabled": True
    }

async def _update_metrics():
    """Update all metrics."""
    try:
        bot = get_bot_instance()
        if not bot:
            return
        
        # Update uptime
        bot_uptime.set(time.time() - startup_time)
        
        # Update system metrics
        try:
            import psutil
            memory_usage.set(psutil.virtual_memory().percent)
            cpu_usage.set(psutil.cpu_percent(interval=0.1))
        except ImportError:
            logger.warning("psutil not available for system metrics")
        
        # Update database metrics
        if hasattr(bot, 'database') and bot.database:
            try:
                # Get user count
                user_count = bot.database.execute("SELECT COUNT(*) FROM users").fetchone()[0]
                total_users.set(user_count)
                
                # Get active users (last 24 hours)
                yesterday = datetime.utcnow() - timedelta(days=1)
                active_count = bot.database.execute(
                    "SELECT COUNT(DISTINCT user_id) FROM conversations WHERE created_at > ?",
                    (yesterday.isoformat(),)
                ).fetchone()[0]
                active_users.set(active_count)
                
                # Get conversation count
                conv_count = bot.database.execute("SELECT COUNT(*) FROM conversations").fetchone()[0]
                total_conversations.set(conv_count)
                
                # Get message count
                msg_count = bot.database.execute("SELECT COUNT(*) FROM messages").fetchone()[0]
                total_messages.set(msg_count)
                
            except Exception as e:
                logger.warning(f"Failed to update database metrics: {e}")
        
    except Exception as e:
        logger.error(f"Failed to update metrics: {e}")

async def _get_bot_metrics(bot) -> Dict[str, Any]:
    """Get bot-specific metrics."""
    if not bot:
        return {"status": "not_available"}
    
    metrics = {
        "status": "running",
        "uptime": time.time() - startup_time,
        "features": {}
    }
    
    # Get feature status
    if hasattr(bot, 'feature_registry'):
        metrics["features"] = {
            "enabled_count": len(bot.feature_registry.enabled_features),
            "total_count": len(bot.feature_registry.features),
            "enabled_features": list(bot.feature_registry.enabled_features)
        }
    
    # Get provider status
    if hasattr(bot, 'ai_provider'):
        metrics["ai_provider"] = {
            "available": bot.ai_provider is not None,
            "type": getattr(bot.ai_provider, 'provider_type', 'unknown')
        }
    
    if hasattr(bot, 'voice_processor'):
        metrics["voice_processor"] = {
            "available": bot.voice_processor is not None,
            "tts_provider": getattr(bot.voice_processor, 'tts_provider_type', 'unknown')
        }
    
    return metrics

async def _get_system_metrics() -> Dict[str, Any]:
    """Get system metrics."""
    try:
        import psutil
        
        return {
            "cpu_percent": psutil.cpu_percent(interval=0.1),
            "memory_percent": psutil.virtual_memory().percent,
            "disk_percent": psutil.disk_usage('/').percent,
            "load_average": psutil.getloadavg() if hasattr(psutil, 'getloadavg') else None,
            "process_count": len(psutil.pids()),
            "network_connections": len(psutil.net_connections())
        }
    except ImportError:
        return {"error": "psutil not available"}
    except Exception as e:
        return {"error": str(e)}

async def _get_database_metrics(bot) -> Dict[str, Any]:
    """Get database metrics."""
    if not bot or not hasattr(bot, 'database'):
        return {"status": "not_available"}
    
    try:
        db = bot.database
        
        # Get table counts
        users_count = db.execute("SELECT COUNT(*) FROM users").fetchone()[0]
        conversations_count = db.execute("SELECT COUNT(*) FROM conversations").fetchone()[0]
        messages_count = db.execute("SELECT COUNT(*) FROM messages").fetchone()[0]
        
        # Get recent activity
        yesterday = datetime.utcnow() - timedelta(days=1)
        recent_conversations = db.execute(
            "SELECT COUNT(*) FROM conversations WHERE created_at > ?",
            (yesterday.isoformat(),)
        ).fetchone()[0]
        
        return {
            "status": "connected",
            "tables": {
                "users": users_count,
                "conversations": conversations_count,
                "messages": messages_count
            },
            "activity": {
                "conversations_24h": recent_conversations
            }
        }
        
    except Exception as e:
        return {"status": "error", "error": str(e)}

async def _get_api_metrics() -> Dict[str, Any]:
    """Get API metrics."""
    # This would typically come from the metrics collected by middleware
    # For now, return basic information
    return {
        "status": "running",
        "endpoints": {
            "health": "/health",
            "metrics": "/metrics",
            "admin": "/admin",
            "webhooks": "/webhooks"
        }
    }

# Utility functions for middleware to record metrics
def record_request(method: str, endpoint: str, status_code: int, duration: float):
    """Record API request metrics."""
    try:
        request_count.labels(method=method, endpoint=endpoint, status=str(status_code)).inc()
        request_duration.labels(method=method, endpoint=endpoint).observe(duration)
    except Exception as e:
        logger.error(f"Failed to record request metrics: {e}")

def get_metrics_registry():
    """Get the metrics registry."""
    return registry
