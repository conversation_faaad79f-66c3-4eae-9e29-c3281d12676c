"""
Database connection management for VoicePal.

This module handles database connection creation, management, and transaction support.
"""

import sqlite3
import logging
from typing import Optional
from pathlib import Path
from contextlib import contextmanager

from bot.database.core.exceptions import (
    DatabaseConnectionError,
    DatabaseQueryError,
    DatabaseTransactionError
)

# Set up logging
logger = logging.getLogger(__name__)

class DatabaseConnection:
    """Manages database connections and transactions."""
    
    def __init__(self, db_path: str):
        """Initialize database connection.
        
        Args:
            db_path: Path to SQLite database file
        
        Raises:
            DatabaseConnectionError: If connection fails
        """
        try:
            # Create directory if it doesn't exist
            db_dir = Path(db_path).parent
            if not db_dir.exists():
                db_dir.mkdir(parents=True, exist_ok=True)
                
            # Connect to database
            self.conn = sqlite3.connect(db_path)
            self.conn.row_factory = sqlite3.Row
            
            # Enable foreign keys
            self.conn.execute("PRAGMA foreign_keys = ON")
            
            logger.info(f"Connected to database: {db_path}")
        except Exception as e:
            logger.error(f"Failed to connect to database: {e}")
            raise DatabaseConnectionError(f"Failed to connect to database: {e}") from e
    
    def execute(self, query: str, params: tuple = None) -> sqlite3.Cursor:
        """Execute a query.
        
        Args:
            query: SQL query
            params: Query parameters
            
        Returns:
            sqlite3.Cursor: Query cursor
            
        Raises:
            DatabaseQueryError: If query execution fails
        """
        try:
            return self.conn.execute(query, params or ())
        except Exception as e:
            logger.error(f"Query execution failed: {e}")
            raise DatabaseQueryError(f"Query execution failed: {e}") from e
    
    def executemany(self, query: str, params_list: list) -> sqlite3.Cursor:
        """Execute a query with multiple parameter sets.
        
        Args:
            query: SQL query
            params_list: List of parameter tuples
            
        Returns:
            sqlite3.Cursor: Query cursor
            
        Raises:
            DatabaseQueryError: If query execution fails
        """
        try:
            return self.conn.executemany(query, params_list)
        except Exception as e:
            logger.error(f"Query execution failed: {e}")
            raise DatabaseQueryError(f"Query execution failed: {e}") from e
    
    def begin_transaction(self):
        """Begin a transaction."""
        self.conn.execute("BEGIN TRANSACTION")
    
    def commit(self):
        """Commit the current transaction."""
        self.conn.commit()
    
    def rollback(self):
        """Roll back the current transaction."""
        self.conn.rollback()
    
    @contextmanager
    def transaction(self):
        """Context manager for database transactions.
        
        Yields:
            None
            
        Raises:
            DatabaseTransactionError: If transaction fails
        """
        try:
            self.begin_transaction()
            yield
            self.commit()
        except Exception as e:
            self.rollback()
            logger.error(f"Transaction failed: {e}")
            raise DatabaseTransactionError(f"Transaction failed: {e}") from e
    
    def close(self):
        """Close the database connection."""
        if hasattr(self, 'conn') and self.conn:
            self.conn.close()
            logger.info("Database connection closed")
    
    def __enter__(self):
        """Enter context manager."""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Exit context manager."""
        self.close()
