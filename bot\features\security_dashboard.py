"""
Security dashboard for VoicePal.

This module provides a comprehensive security dashboard for administrators
to view security metrics, alerts, and user activity patterns.
"""

import logging
import json
import asyncio
from typing import Dict, Any, List, Optional, Tuple, Set, Union
from datetime import datetime, timedelta
from collections import defaultdict

from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes

from bot.core.security_monitor import (
    SEVERITY_INFO, SEVERITY_LOW, SEVERITY_MEDIUM, SEVERITY_HIGH, SEVERITY_CRITICAL,
    CATEGORY_AUTH, CATEGORY_ACCESS, CATEGORY_PAYMENT, CATEGORY_ABUSE, 
    CATEGORY_RATE_LIMIT, CATEGORY_API, CATEGORY_SYSTEM
)

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# Dashboard sections
SECTION_OVERVIEW = "overview"
SECTION_ALERTS = "alerts"
SECTION_EVENTS = "events"
SECTION_USERS = "users"
SECTION_PAYMENTS = "payments"
SECTION_METRICS = "metrics"

async def show_security_dashboard(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """
    Show the main security dashboard.
    
    Args:
        update: Update object
        context: Context object
    """
    bot_instance = context.bot_data.get("bot_instance")
    
    if not bot_instance or not hasattr(bot_instance, "security_monitor"):
        await update.message.reply_text("Security monitoring is not available.")
        return
    
    # Get active alerts count
    cursor = bot_instance.database.conn.cursor()
    cursor.execute("""
        SELECT COUNT(*) as count FROM security_alerts
        WHERE resolved = 0
    """)
    active_alerts_count = cursor.fetchone()["count"]
    
    # Get recent events count
    cursor.execute("""
        SELECT COUNT(*) as count FROM security_events
        WHERE timestamp > ?
    """, ((datetime.now() - timedelta(days=1)).isoformat(),))
    recent_events_count = cursor.fetchone()["count"]
    
    # Get suspicious users count
    cursor.execute("""
        SELECT COUNT(DISTINCT user_id) as count FROM security_events
        WHERE category = ? AND severity IN (?, ?, ?)
        AND timestamp > ?
    """, (
        CATEGORY_ABUSE, 
        SEVERITY_MEDIUM, SEVERITY_HIGH, SEVERITY_CRITICAL,
        (datetime.now() - timedelta(days=7)).isoformat()
    ))
    suspicious_users_count = cursor.fetchone()["count"]
    
    # Get payment issues count
    cursor.execute("""
        SELECT COUNT(*) as count FROM security_events
        WHERE category = ? AND severity IN (?, ?, ?)
        AND timestamp > ?
    """, (
        CATEGORY_PAYMENT, 
        SEVERITY_MEDIUM, SEVERITY_HIGH, SEVERITY_CRITICAL,
        (datetime.now() - timedelta(days=7)).isoformat()
    ))
    payment_issues_count = cursor.fetchone()["count"]
    
    # Format dashboard message
    message = (
        "🛡️ *Security Dashboard* 🛡️\n\n"
        "*System Status Overview:*\n"
    )
    
    # Determine overall security status
    if active_alerts_count > 0 and any(alert["severity"] == SEVERITY_CRITICAL for alert in bot_instance.security_monitor.get_active_alerts()):
        status_emoji = "🔴"
        status_text = "CRITICAL"
    elif active_alerts_count > 0 and any(alert["severity"] == SEVERITY_HIGH for alert in bot_instance.security_monitor.get_active_alerts()):
        status_emoji = "🟠"
        status_text = "WARNING"
    elif active_alerts_count > 0:
        status_emoji = "🟡"
        status_text = "CAUTION"
    else:
        status_emoji = "🟢"
        status_text = "NORMAL"
    
    message += f"{status_emoji} Overall Security Status: *{status_text}*\n\n"
    
    message += (
        f"🚨 Active Alerts: {active_alerts_count}\n"
        f"📊 Recent Events (24h): {recent_events_count}\n"
        f"👤 Suspicious Users (7d): {suspicious_users_count}\n"
        f"💳 Payment Issues (7d): {payment_issues_count}\n\n"
    )
    
    # Add recent critical alerts
    cursor.execute("""
        SELECT id, timestamp, alert_type, severity, description
        FROM security_alerts
        WHERE resolved = 0 AND severity IN (?, ?)
        ORDER BY timestamp DESC
        LIMIT 3
    """, (SEVERITY_HIGH, SEVERITY_CRITICAL))
    
    critical_alerts = cursor.fetchall()
    
    if critical_alerts:
        message += "*Critical Alerts:*\n"
        for alert in critical_alerts:
            alert_time = datetime.fromisoformat(alert["timestamp"]).strftime("%Y-%m-%d %H:%M")
            message += f"⚠️ {alert_time} - {alert['alert_type']}: {alert['description'][:50]}...\n"
        message += "\n"
    
    # Create dashboard keyboard
    keyboard = [
        [
            InlineKeyboardButton("🚨 Alerts", callback_data="security_section_alerts"),
            InlineKeyboardButton("📊 Events", callback_data="security_section_events")
        ],
        [
            InlineKeyboardButton("👤 User Activity", callback_data="security_section_users"),
            InlineKeyboardButton("💳 Payment Security", callback_data="security_section_payments")
        ],
        [
            InlineKeyboardButton("📈 Security Metrics", callback_data="security_section_metrics"),
            InlineKeyboardButton("🔄 Refresh", callback_data="security_section_overview")
        ]
    ]
    
    reply_markup = InlineKeyboardMarkup(keyboard)
    
    # Send or edit message
    if update.callback_query:
        await update.callback_query.edit_message_text(
            text=message,
            reply_markup=reply_markup,
            parse_mode="Markdown"
        )
    else:
        await update.message.reply_text(
            text=message,
            reply_markup=reply_markup,
            parse_mode="Markdown"
        )

async def show_alerts_section(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """
    Show the alerts section of the security dashboard.
    
    Args:
        update: Update object
        context: Context object
    """
    bot_instance = context.bot_data.get("bot_instance")
    
    if not bot_instance or not hasattr(bot_instance, "security_monitor"):
        await update.message.reply_text("Security monitoring is not available.")
        return
    
    # Get active alerts
    cursor = bot_instance.database.conn.cursor()
    cursor.execute("""
        SELECT id, timestamp, alert_type, severity, description, details
        FROM security_alerts
        WHERE resolved = 0
        ORDER BY 
            CASE severity
                WHEN 'CRITICAL' THEN 1
                WHEN 'HIGH' THEN 2
                WHEN 'MEDIUM' THEN 3
                WHEN 'LOW' THEN 4
                WHEN 'INFO' THEN 5
            END,
            timestamp DESC
        LIMIT 10
    """)
    
    active_alerts = cursor.fetchall()
    
    # Format alerts message
    message = (
        "🚨 *Security Alerts* 🚨\n\n"
    )
    
    if active_alerts:
        for alert in active_alerts:
            severity_emoji = {
                SEVERITY_INFO: "ℹ️",
                SEVERITY_LOW: "🔵",
                SEVERITY_MEDIUM: "🟡",
                SEVERITY_HIGH: "🔴",
                SEVERITY_CRITICAL: "⚠️"
            }.get(alert["severity"], "🔔")
            
            alert_time = datetime.fromisoformat(alert["timestamp"]).strftime("%Y-%m-%d %H:%M")
            
            message += (
                f"{severity_emoji} *{alert['alert_type']}*\n"
                f"  Time: {alert_time}\n"
                f"  Severity: {alert['severity']}\n"
                f"  Description: {alert['description']}\n"
            )
            
            # Add details if available
            if alert["details"]:
                try:
                    details = json.loads(alert["details"])
                    if isinstance(details, dict) and details:
                        message += "  Details:\n"
                        for key, value in details.items():
                            if value is not None:
                                message += f"    - {key}: {value}\n"
                except json.JSONDecodeError:
                    pass
            
            message += f"  [Resolve](https://t.me/{context.bot.username}?start=resolve_alert_{alert['id']})\n\n"
    else:
        message += "No active alerts. System status normal.\n\n"
    
    # Create keyboard
    keyboard = [
        [
            InlineKeyboardButton("View Resolved Alerts", callback_data="security_resolved_alerts"),
            InlineKeyboardButton("Filter by Severity", callback_data="security_filter_alerts_severity")
        ],
        [
            InlineKeyboardButton("🔙 Back to Dashboard", callback_data="security_section_overview")
        ]
    ]
    
    reply_markup = InlineKeyboardMarkup(keyboard)
    
    # Send or edit message
    if update.callback_query:
        await update.callback_query.edit_message_text(
            text=message,
            reply_markup=reply_markup,
            parse_mode="Markdown"
        )
    else:
        await update.message.reply_text(
            text=message,
            reply_markup=reply_markup,
            parse_mode="Markdown"
        )

async def show_events_section(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """
    Show the events section of the security dashboard.
    
    Args:
        update: Update object
        context: Context object
    """
    bot_instance = context.bot_data.get("bot_instance")
    
    if not bot_instance or not hasattr(bot_instance, "security_monitor"):
        await update.message.reply_text("Security monitoring is not available.")
        return
    
    # Get page from context or default to 0
    page = context.user_data.get("security_events_page", 0)
    page_size = 5
    
    # Get recent events
    cursor = bot_instance.database.conn.cursor()
    cursor.execute("""
        SELECT id, timestamp, event_type, category, severity, user_id, description
        FROM security_events
        ORDER BY timestamp DESC
        LIMIT ? OFFSET ?
    """, (page_size, page * page_size))
    
    events = cursor.fetchall()
    
    # Get total count for pagination
    cursor.execute("SELECT COUNT(*) as count FROM security_events")
    total_count = cursor.fetchone()["count"]
    total_pages = (total_count + page_size - 1) // page_size
    
    # Format events message
    message = (
        "📊 *Security Events* 📊\n\n"
    )
    
    if events:
        for event in events:
            severity_emoji = {
                SEVERITY_INFO: "ℹ️",
                SEVERITY_LOW: "🔵",
                SEVERITY_MEDIUM: "🟡",
                SEVERITY_HIGH: "🔴",
                SEVERITY_CRITICAL: "⚠️"
            }.get(event["severity"], "🔔")
            
            event_time = datetime.fromisoformat(event["timestamp"]).strftime("%Y-%m-%d %H:%M")
            
            message += (
                f"{severity_emoji} *{event['event_type']}*\n"
                f"  Time: {event_time}\n"
                f"  Category: {event['category']}\n"
            )
            
            if event["user_id"]:
                message += f"  User ID: {event['user_id']}\n"
            
            if event["description"]:
                message += f"  Description: {event['description']}\n"
            
            message += "\n"
    else:
        message += "No events found.\n\n"
    
    # Add pagination info
    if total_pages > 1:
        message += f"Page {page + 1} of {total_pages}\n\n"
    
    # Create keyboard with pagination
    keyboard = []
    
    # Add pagination buttons
    pagination_row = []
    if page > 0:
        pagination_row.append(
            InlineKeyboardButton("◀️ Previous", callback_data="security_events_prev")
        )
    
    if page < total_pages - 1:
        pagination_row.append(
            InlineKeyboardButton("Next ▶️", callback_data="security_events_next")
        )
    
    if pagination_row:
        keyboard.append(pagination_row)
    
    # Add filter buttons
    keyboard.append([
        InlineKeyboardButton("Filter by Category", callback_data="security_filter_events_category"),
        InlineKeyboardButton("Filter by Severity", callback_data="security_filter_events_severity")
    ])
    
    # Add back button
    keyboard.append([
        InlineKeyboardButton("🔙 Back to Dashboard", callback_data="security_section_overview")
    ])
    
    reply_markup = InlineKeyboardMarkup(keyboard)
    
    # Send or edit message
    if update.callback_query:
        await update.callback_query.edit_message_text(
            text=message,
            reply_markup=reply_markup,
            parse_mode="Markdown"
        )
    else:
        await update.message.reply_text(
            text=message,
            reply_markup=reply_markup,
            parse_mode="Markdown"
        )

async def show_users_section(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """
    Show the user activity section of the security dashboard.
    
    Args:
        update: Update object
        context: Context object
    """
    bot_instance = context.bot_data.get("bot_instance")
    
    if not bot_instance or not hasattr(bot_instance, "security_monitor"):
        await update.message.reply_text("Security monitoring is not available.")
        return
    
    # Get suspicious users
    cursor = bot_instance.database.conn.cursor()
    cursor.execute("""
        SELECT user_id, COUNT(*) as event_count
        FROM security_events
        WHERE category = ? AND severity IN (?, ?, ?)
        AND timestamp > ?
        GROUP BY user_id
        ORDER BY event_count DESC
        LIMIT 5
    """, (
        CATEGORY_ABUSE, 
        SEVERITY_MEDIUM, SEVERITY_HIGH, SEVERITY_CRITICAL,
        (datetime.now() - timedelta(days=7)).isoformat()
    ))
    
    suspicious_users = cursor.fetchall()
    
    # Get users with multiple accounts (same device ID)
    cursor.execute("""
        SELECT device_id, COUNT(DISTINCT user_id) as user_count
        FROM users
        WHERE device_id IS NOT NULL
        GROUP BY device_id
        HAVING COUNT(DISTINCT user_id) > 1
        ORDER BY user_count DESC
        LIMIT 5
    """)
    
    multiple_accounts = cursor.fetchall()
    
    # Get users with high credit usage
    cursor.execute("""
        SELECT user_id, SUM(ABS(amount)) as credit_usage
        FROM transactions
        WHERE timestamp > ? AND status = 'completed' AND amount < 0
        GROUP BY user_id
        ORDER BY credit_usage DESC
        LIMIT 5
    """, ((datetime.now() - timedelta(days=1)).isoformat(),))
    
    high_usage_users = cursor.fetchall()
    
    # Format user activity message
    message = (
        "👤 *User Activity Monitoring* 👤\n\n"
    )
    
    # Add suspicious users section
    message += "*Suspicious Activity:*\n"
    if suspicious_users:
        for user in suspicious_users:
            message += f"🔍 User {user['user_id']}: {user['event_count']} suspicious events\n"
    else:
        message += "No suspicious activity detected.\n"
    
    message += "\n"
    
    # Add multiple accounts section
    message += "*Multiple Accounts (Same Device):*\n"
    if multiple_accounts:
        for account in multiple_accounts:
            message += f"📱 Device {account['device_id'][:8]}...: {account['user_count']} accounts\n"
    else:
        message += "No multiple accounts detected.\n"
    
    message += "\n"
    
    # Add high usage users section
    message += "*High Credit Usage (24h):*\n"
    if high_usage_users:
        for user in high_usage_users:
            message += f"💰 User {user['user_id']}: {user['credit_usage']} credits\n"
    else:
        message += "No unusual credit usage detected.\n"
    
    # Create keyboard
    keyboard = [
        [
            InlineKeyboardButton("View User Details", callback_data="security_user_details"),
            InlineKeyboardButton("View IP Activity", callback_data="security_ip_activity")
        ],
        [
            InlineKeyboardButton("🔙 Back to Dashboard", callback_data="security_section_overview")
        ]
    ]
    
    reply_markup = InlineKeyboardMarkup(keyboard)
    
    # Send or edit message
    if update.callback_query:
        await update.callback_query.edit_message_text(
            text=message,
            reply_markup=reply_markup,
            parse_mode="Markdown"
        )
    else:
        await update.message.reply_text(
            text=message,
            reply_markup=reply_markup,
            parse_mode="Markdown"
        )

async def show_payments_section(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """
    Show the payment security section of the security dashboard.
    
    Args:
        update: Update object
        context: Context object
    """
    bot_instance = context.bot_data.get("bot_instance")
    
    if not bot_instance or not hasattr(bot_instance, "security_monitor"):
        await update.message.reply_text("Security monitoring is not available.")
        return
    
    # Get payment issues
    cursor = bot_instance.database.conn.cursor()
    cursor.execute("""
        SELECT id, timestamp, event_type, severity, user_id, description, details
        FROM security_events
        WHERE category = ? AND severity IN (?, ?, ?)
        AND timestamp > ?
        ORDER BY timestamp DESC
        LIMIT 5
    """, (
        CATEGORY_PAYMENT, 
        SEVERITY_MEDIUM, SEVERITY_HIGH, SEVERITY_CRITICAL,
        (datetime.now() - timedelta(days=7)).isoformat()
    ))
    
    payment_issues = cursor.fetchall()
    
    # Get failed payment transactions
    cursor.execute("""
        SELECT user_id, COUNT(*) as failure_count
        FROM transactions
        WHERE timestamp > ? AND status = 'failed' AND source = 'payment'
        GROUP BY user_id
        HAVING COUNT(*) >= 3
        ORDER BY failure_count DESC
        LIMIT 5
    """, ((datetime.now() - timedelta(days=7)).isoformat(),))
    
    failed_payments = cursor.fetchall()
    
    # Get payment statistics
    cursor.execute("""
        SELECT 
            COUNT(*) as total_count,
            SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_count,
            SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_count
        FROM transactions
        WHERE timestamp > ? AND source = 'payment'
    """, ((datetime.now() - timedelta(days=7)).isoformat(),))
    
    payment_stats = cursor.fetchone()
    
    # Format payment security message
    message = (
        "💳 *Payment Security* 💳\n\n"
    )
    
    # Add payment statistics
    if payment_stats and payment_stats["total_count"] > 0:
        total_count = payment_stats["total_count"]
        completed_count = payment_stats["completed_count"] or 0
        failed_count = payment_stats["failed_count"] or 0
        
        success_rate = (completed_count / total_count) * 100 if total_count > 0 else 0
        
        message += (
            f"*Payment Statistics (7d):*\n"
            f"Total Transactions: {total_count}\n"
            f"Completed: {completed_count}\n"
            f"Failed: {failed_count}\n"
            f"Success Rate: {success_rate:.1f}%\n\n"
        )
    
    # Add payment issues section
    message += "*Recent Payment Issues:*\n"
    if payment_issues:
        for issue in payment_issues:
            severity_emoji = {
                SEVERITY_INFO: "ℹ️",
                SEVERITY_LOW: "🔵",
                SEVERITY_MEDIUM: "🟡",
                SEVERITY_HIGH: "🔴",
                SEVERITY_CRITICAL: "⚠️"
            }.get(issue["severity"], "🔔")
            
            issue_time = datetime.fromisoformat(issue["timestamp"]).strftime("%Y-%m-%d %H:%M")
            
            message += (
                f"{severity_emoji} {issue_time} - {issue['event_type']}\n"
                f"  User: {issue['user_id']}\n"
            )
            
            if issue["description"]:
                message += f"  Description: {issue['description']}\n"
            
            message += "\n"
    else:
        message += "No payment issues detected.\n\n"
    
    # Add users with failed payments section
    message += "*Users with Failed Payments:*\n"
    if failed_payments:
        for user in failed_payments:
            message += f"❌ User {user['user_id']}: {user['failure_count']} failed payments\n"
    else:
        message += "No users with multiple failed payments.\n"
    
    # Create keyboard
    keyboard = [
        [
            InlineKeyboardButton("View Transaction History", callback_data="security_transaction_history"),
            InlineKeyboardButton("Payment Patterns", callback_data="security_payment_patterns")
        ],
        [
            InlineKeyboardButton("🔙 Back to Dashboard", callback_data="security_section_overview")
        ]
    ]
    
    reply_markup = InlineKeyboardMarkup(keyboard)
    
    # Send or edit message
    if update.callback_query:
        await update.callback_query.edit_message_text(
            text=message,
            reply_markup=reply_markup,
            parse_mode="Markdown"
        )
    else:
        await update.message.reply_text(
            text=message,
            reply_markup=reply_markup,
            parse_mode="Markdown"
        )

async def show_metrics_section(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """
    Show the security metrics section of the dashboard.
    
    Args:
        update: Update object
        context: Context object
    """
    bot_instance = context.bot_data.get("bot_instance")
    
    if not bot_instance or not hasattr(bot_instance, "security_monitor"):
        await update.message.reply_text("Security monitoring is not available.")
        return
    
    # Get event counts by category
    cursor = bot_instance.database.conn.cursor()
    cursor.execute("""
        SELECT category, COUNT(*) as count
        FROM security_events
        WHERE timestamp > ?
        GROUP BY category
        ORDER BY count DESC
    """, ((datetime.now() - timedelta(days=7)).isoformat(),))
    
    category_counts = cursor.fetchall()
    
    # Get event counts by severity
    cursor.execute("""
        SELECT severity, COUNT(*) as count
        FROM security_events
        WHERE timestamp > ?
        GROUP BY severity
        ORDER BY 
            CASE severity
                WHEN 'CRITICAL' THEN 1
                WHEN 'HIGH' THEN 2
                WHEN 'MEDIUM' THEN 3
                WHEN 'LOW' THEN 4
                WHEN 'INFO' THEN 5
            END
    """, ((datetime.now() - timedelta(days=7)).isoformat(),))
    
    severity_counts = cursor.fetchall()
    
    # Get daily event counts
    cursor.execute("""
        SELECT 
            strftime('%Y-%m-%d', timestamp) as day,
            COUNT(*) as count
        FROM security_events
        WHERE timestamp > ?
        GROUP BY day
        ORDER BY day DESC
        LIMIT 7
    """, ((datetime.now() - timedelta(days=7)).isoformat(),))
    
    daily_counts = cursor.fetchall()
    
    # Format metrics message
    message = (
        "📈 *Security Metrics* 📈\n\n"
    )
    
    # Add events by category
    message += "*Events by Category (7d):*\n"
    if category_counts:
        for category in category_counts:
            category_name = category["category"]
            count = category["count"]
            
            # Add emoji based on category
            category_emoji = {
                CATEGORY_AUTH: "🔐",
                CATEGORY_ACCESS: "🚪",
                CATEGORY_PAYMENT: "💳",
                CATEGORY_ABUSE: "⚠️",
                CATEGORY_RATE_LIMIT: "⏱️",
                CATEGORY_API: "🔌",
                CATEGORY_SYSTEM: "🖥️"
            }.get(category_name, "📊")
            
            message += f"{category_emoji} {category_name}: {count}\n"
    else:
        message += "No events recorded.\n"
    
    message += "\n"
    
    # Add events by severity
    message += "*Events by Severity (7d):*\n"
    if severity_counts:
        for severity in severity_counts:
            severity_name = severity["severity"]
            count = severity["count"]
            
            # Add emoji based on severity
            severity_emoji = {
                SEVERITY_INFO: "ℹ️",
                SEVERITY_LOW: "🔵",
                SEVERITY_MEDIUM: "🟡",
                SEVERITY_HIGH: "🔴",
                SEVERITY_CRITICAL: "⚠️"
            }.get(severity_name, "🔔")
            
            message += f"{severity_emoji} {severity_name}: {count}\n"
    else:
        message += "No events recorded.\n"
    
    message += "\n"
    
    # Add daily event counts
    message += "*Daily Event Counts:*\n"
    if daily_counts:
        for day_data in daily_counts:
            day = day_data["day"]
            count = day_data["count"]
            
            message += f"📅 {day}: {count} events\n"
    else:
        message += "No events recorded.\n"
    
    # Create keyboard
    keyboard = [
        [
            InlineKeyboardButton("Export Metrics", callback_data="security_export_metrics"),
            InlineKeyboardButton("View Trends", callback_data="security_view_trends")
        ],
        [
            InlineKeyboardButton("🔙 Back to Dashboard", callback_data="security_section_overview")
        ]
    ]
    
    reply_markup = InlineKeyboardMarkup(keyboard)
    
    # Send or edit message
    if update.callback_query:
        await update.callback_query.edit_message_text(
            text=message,
            reply_markup=reply_markup,
            parse_mode="Markdown"
        )
    else:
        await update.message.reply_text(
            text=message,
            reply_markup=reply_markup,
            parse_mode="Markdown"
        )

async def handle_security_dashboard_callback(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """
    Handle security dashboard callback queries.
    
    Args:
        update: Update object
        context: Context object
    """
    query = update.callback_query
    await query.answer()
    
    callback_data = query.data
    
    # Handle section navigation
    if callback_data == "security_section_overview":
        await show_security_dashboard(update, context)
    elif callback_data == "security_section_alerts":
        await show_alerts_section(update, context)
    elif callback_data == "security_section_events":
        await show_events_section(update, context)
    elif callback_data == "security_section_users":
        await show_users_section(update, context)
    elif callback_data == "security_section_payments":
        await show_payments_section(update, context)
    elif callback_data == "security_section_metrics":
        await show_metrics_section(update, context)
    
    # Handle events pagination
    elif callback_data == "security_events_prev":
        context.user_data["security_events_page"] = max(0, context.user_data.get("security_events_page", 0) - 1)
        await show_events_section(update, context)
    elif callback_data == "security_events_next":
        context.user_data["security_events_page"] = context.user_data.get("security_events_page", 0) + 1
        await show_events_section(update, context)
    
    # Handle other actions
    else:
        # For now, just return to the main dashboard
        await show_security_dashboard(update, context)
