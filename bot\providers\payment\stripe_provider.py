"""
Stripe payment provider for VoicePal.

This module provides a provider for Stripe payment services.
"""

import os
import logging
import asyncio
import json
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass, field

import stripe

from bot.providers.core.provider import PaymentProvider
from bot.providers.core.config import PaymentProviderConfig
from bot.providers.core.exceptions import (
    ProviderError,
    ProviderConfigError,
    ProviderAuthError,
    ProviderRateLimitError,
    ProviderTimeoutError,
    ProviderNotFoundError,
    ProviderValidationError,
    ProviderNotInitializedError
)

# Set up logging
logger = logging.getLogger(__name__)

@dataclass
class StripeConfig(PaymentProviderConfig):
    """Configuration for Stripe payment provider."""
    
    api_key: str = ""
    secret_key: str = ""
    webhook_secret: str = ""
    currency: str = "USD"
    payment_method_types: List[str] = field(default_factory=lambda: ["card"])
    success_url: str = ""
    cancel_url: str = ""
    timeout: int = 30
    
    def __post_init__(self):
        """Post-initialization."""
        self.provider_type = "payment"
        self.provider_name = "stripe"
    
    def validate(self) -> List[str]:
        """Validate configuration.
        
        Returns:
            List of validation error messages (empty if valid)
        """
        errors = super().validate()
        
        # Validate payment method types
        valid_payment_methods = ["card", "alipay", "ideal", "fpx", "bacs_debit", "bancontact", "giropay", "p24", "eps", "sofort", "sepa_debit", "grabpay", "afterpay_clearpay", "acss_debit", "wechat_pay", "boleto", "oxxo"]
        for payment_method in self.payment_method_types:
            if payment_method not in valid_payment_methods:
                errors.append(f"Invalid payment method: {payment_method}. Must be one of {valid_payment_methods}")
        
        # Validate currency
        valid_currencies = ["USD", "EUR", "GBP", "JPY", "CAD", "AUD", "CHF", "CNY", "SEK", "NZD", "MXN", "SGD", "HKD", "NOK", "KRW", "TRY", "RUB", "INR", "BRL", "ZAR"]
        if self.currency not in valid_currencies:
            errors.append(f"Invalid currency: {self.currency}. Must be one of {valid_currencies}")
        
        return errors

class StripeProvider(PaymentProvider[StripeConfig]):
    """Provider for Stripe payment services."""
    
    provider_type = "payment"
    provider_name = "stripe"
    provider_version = "1.0.0"
    provider_description = "Provider for Stripe payment services"
    config_class = StripeConfig
    
    def __init__(self, config: StripeConfig):
        """Initialize provider.
        
        Args:
            config: Provider configuration
        """
        super().__init__(config)
        self.products = {}
        self.prices = {}
    
    def validate_config(self) -> None:
        """Validate provider configuration.
        
        Raises:
            ProviderConfigError: If configuration is invalid
        """
        errors = self.config.validate()
        if errors:
            error_message = "; ".join(errors)
            raise ProviderConfigError(f"Invalid configuration: {error_message}")
    
    def initialize(self) -> None:
        """Initialize provider.
        
        Raises:
            ProviderInitializationError: If initialization fails
        """
        try:
            # Set API key
            stripe.api_key = self.config.secret_key
            
            # Set API version
            stripe.api_version = "2023-10-16"
            
            # Set timeout
            stripe.default_http_client = stripe.http_client.RequestsClient(timeout=self.config.timeout)
            
            self.initialized = True
            logger.info(f"Initialized {self.provider_name} provider")
        except Exception as e:
            logger.error(f"Failed to initialize {self.provider_name} provider: {e}")
            raise ProviderInitializationError(f"Failed to initialize {self.provider_name} provider: {e}") from e
    
    def shutdown(self) -> None:
        """Shutdown provider.
        
        Raises:
            ProviderShutdownError: If shutdown fails
        """
        self.products = {}
        self.prices = {}
        self.initialized = False
        logger.info(f"Shutdown {self.provider_name} provider")
    
    async def create_payment(self, amount: int, currency: str, description: str, **kwargs) -> Dict[str, Any]:
        """Create a payment.
        
        Args:
            amount: Payment amount in cents
            currency: Payment currency
            description: Payment description
            **kwargs: Additional arguments
            
        Returns:
            Payment information
            
        Raises:
            ProviderError: If payment creation fails
        """
        if not self.initialized:
            raise ProviderNotInitializedError(f"{self.provider_name} provider is not initialized")
        
        try:
            # Get customer ID if provided
            customer_id = kwargs.get("customer_id")
            
            # Get metadata if provided
            metadata = kwargs.get("metadata", {})
            
            # Get payment method types
            payment_method_types = kwargs.get("payment_method_types", self.config.payment_method_types)
            
            # Create payment intent
            payment_intent = await asyncio.to_thread(
                stripe.PaymentIntent.create,
                amount=amount,
                currency=currency or self.config.currency,
                description=description,
                customer=customer_id,
                payment_method_types=payment_method_types,
                metadata=metadata
            )
            
            return {
                "id": payment_intent.id,
                "amount": payment_intent.amount,
                "currency": payment_intent.currency,
                "description": payment_intent.description,
                "status": payment_intent.status,
                "client_secret": payment_intent.client_secret,
                "created": payment_intent.created,
                "metadata": payment_intent.metadata
            }
        except stripe.error.StripeError as e:
            logger.error(f"Failed to create payment: {e}")
            
            # Map Stripe exceptions to provider exceptions
            if isinstance(e, stripe.error.AuthenticationError):
                raise ProviderAuthError(f"Authentication failed: {e}") from e
            elif isinstance(e, stripe.error.RateLimitError):
                raise ProviderRateLimitError(f"Rate limit exceeded: {e}") from e
            elif isinstance(e, stripe.error.InvalidRequestError):
                raise ProviderValidationError(f"Invalid request: {e}") from e
            elif isinstance(e, stripe.error.APIConnectionError):
                raise ProviderTimeoutError(f"API connection error: {e}") from e
            elif isinstance(e, stripe.error.CardError):
                raise ProviderValidationError(f"Card error: {e}") from e
            else:
                raise ProviderError(f"Failed to create payment: {e}") from e
        except Exception as e:
            logger.error(f"Failed to create payment: {e}")
            raise ProviderError(f"Failed to create payment: {e}") from e
    
    async def get_payment_status(self, payment_id: str) -> Dict[str, Any]:
        """Get payment status.
        
        Args:
            payment_id: Payment ID
            
        Returns:
            Payment status
            
        Raises:
            ProviderError: If getting payment status fails
        """
        if not self.initialized:
            raise ProviderNotInitializedError(f"{self.provider_name} provider is not initialized")
        
        try:
            # Get payment intent
            payment_intent = await asyncio.to_thread(
                stripe.PaymentIntent.retrieve,
                payment_id
            )
            
            return {
                "id": payment_intent.id,
                "amount": payment_intent.amount,
                "currency": payment_intent.currency,
                "description": payment_intent.description,
                "status": payment_intent.status,
                "created": payment_intent.created,
                "metadata": payment_intent.metadata
            }
        except stripe.error.StripeError as e:
            logger.error(f"Failed to get payment status: {e}")
            
            # Map Stripe exceptions to provider exceptions
            if isinstance(e, stripe.error.AuthenticationError):
                raise ProviderAuthError(f"Authentication failed: {e}") from e
            elif isinstance(e, stripe.error.RateLimitError):
                raise ProviderRateLimitError(f"Rate limit exceeded: {e}") from e
            elif isinstance(e, stripe.error.InvalidRequestError):
                raise ProviderNotFoundError(f"Payment not found: {e}") from e
            elif isinstance(e, stripe.error.APIConnectionError):
                raise ProviderTimeoutError(f"API connection error: {e}") from e
            else:
                raise ProviderError(f"Failed to get payment status: {e}") from e
        except Exception as e:
            logger.error(f"Failed to get payment status: {e}")
            raise ProviderError(f"Failed to get payment status: {e}") from e
    
    async def create_subscription(self, customer_id: str, price_id: str, **kwargs) -> Dict[str, Any]:
        """Create a subscription.
        
        Args:
            customer_id: Customer ID
            price_id: Price ID
            **kwargs: Additional arguments
            
        Returns:
            Subscription information
            
        Raises:
            ProviderError: If subscription creation fails
        """
        if not self.initialized:
            raise ProviderNotInitializedError(f"{self.provider_name} provider is not initialized")
        
        try:
            # Get metadata if provided
            metadata = kwargs.get("metadata", {})
            
            # Create subscription
            subscription = await asyncio.to_thread(
                stripe.Subscription.create,
                customer=customer_id,
                items=[{"price": price_id}],
                metadata=metadata
            )
            
            return {
                "id": subscription.id,
                "customer": subscription.customer,
                "status": subscription.status,
                "current_period_start": subscription.current_period_start,
                "current_period_end": subscription.current_period_end,
                "created": subscription.created,
                "metadata": subscription.metadata
            }
        except stripe.error.StripeError as e:
            logger.error(f"Failed to create subscription: {e}")
            
            # Map Stripe exceptions to provider exceptions
            if isinstance(e, stripe.error.AuthenticationError):
                raise ProviderAuthError(f"Authentication failed: {e}") from e
            elif isinstance(e, stripe.error.RateLimitError):
                raise ProviderRateLimitError(f"Rate limit exceeded: {e}") from e
            elif isinstance(e, stripe.error.InvalidRequestError):
                raise ProviderValidationError(f"Invalid request: {e}") from e
            elif isinstance(e, stripe.error.APIConnectionError):
                raise ProviderTimeoutError(f"API connection error: {e}") from e
            else:
                raise ProviderError(f"Failed to create subscription: {e}") from e
        except Exception as e:
            logger.error(f"Failed to create subscription: {e}")
            raise ProviderError(f"Failed to create subscription: {e}") from e
    
    async def cancel_subscription(self, subscription_id: str) -> Dict[str, Any]:
        """Cancel a subscription.
        
        Args:
            subscription_id: Subscription ID
            
        Returns:
            Cancellation result
            
        Raises:
            ProviderError: If subscription cancellation fails
        """
        if not self.initialized:
            raise ProviderNotInitializedError(f"{self.provider_name} provider is not initialized")
        
        try:
            # Cancel subscription
            subscription = await asyncio.to_thread(
                stripe.Subscription.delete,
                subscription_id
            )
            
            return {
                "id": subscription.id,
                "customer": subscription.customer,
                "status": subscription.status,
                "canceled_at": subscription.canceled_at,
                "created": subscription.created,
                "metadata": subscription.metadata
            }
        except stripe.error.StripeError as e:
            logger.error(f"Failed to cancel subscription: {e}")
            
            # Map Stripe exceptions to provider exceptions
            if isinstance(e, stripe.error.AuthenticationError):
                raise ProviderAuthError(f"Authentication failed: {e}") from e
            elif isinstance(e, stripe.error.RateLimitError):
                raise ProviderRateLimitError(f"Rate limit exceeded: {e}") from e
            elif isinstance(e, stripe.error.InvalidRequestError):
                raise ProviderNotFoundError(f"Subscription not found: {e}") from e
            elif isinstance(e, stripe.error.APIConnectionError):
                raise ProviderTimeoutError(f"API connection error: {e}") from e
            else:
                raise ProviderError(f"Failed to cancel subscription: {e}") from e
        except Exception as e:
            logger.error(f"Failed to cancel subscription: {e}")
            raise ProviderError(f"Failed to cancel subscription: {e}") from e
