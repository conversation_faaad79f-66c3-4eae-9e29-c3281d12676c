"""
Test script for the AI conversation module.

This script tests the basic functionality of the AI conversation module.
"""

import unittest
from bot.ai_conversation import AIConversation

class TestAIConversation(unittest.TestCase):
    """Test cases for the AIConversation class."""
    
    def setUp(self):
        """Set up test environment."""
        # Create an AI conversation instance with default personality
        self.ai = AIConversation()
    
    def test_greeting(self):
        """Test getting a greeting."""
        greeting = self.ai.get_greeting()
        self.assertIsNotNone(greeting)
        self.assertIsInstance(greeting, str)
        self.assertTrue(len(greeting) > 0)
    
    def test_response(self):
        """Test getting a response to a message."""
        response = self.ai.get_response("Hello, how are you?")
        self.assertIsNotNone(response)
        self.assertIsInstance(response, str)
        self.assertTrue(len(response) > 0)
    
    def test_conversation_history(self):
        """Test conversation history tracking."""
        # Send a message
        self.ai.get_response("Hello, how are you?")
        
        # Check conversation history
        history = self.ai.get_conversation_history()
        self.assertEqual(len(history), 2)  # User message + AI response
        self.assertEqual(history[0]["role"], "user")
        self.assertEqual(history[0]["message"], "Hello, how are you?")
        self.assertEqual(history[1]["role"], "assistant")
    
    def test_clear_conversation_history(self):
        """Test clearing conversation history."""
        # Send a message
        self.ai.get_response("Hello, how are you?")
        
        # Clear history
        self.ai.clear_conversation_history()
        
        # Check that history is empty
        history = self.ai.get_conversation_history()
        self.assertEqual(len(history), 0)
    
    def test_personalities(self):
        """Test different personalities."""
        # Get available personalities
        personalities = self.ai.get_available_personalities()
        self.assertIsInstance(personalities, dict)
        self.assertGreater(len(personalities), 0)
        
        # Test each personality
        for personality in personalities:
            # Set personality
            self.ai.set_personality(personality)
            
            # Get greeting
            greeting = self.ai.get_greeting()
            self.assertIsNotNone(greeting)
            self.assertIsInstance(greeting, str)
            self.assertTrue(len(greeting) > 0)
            
            # Get response
            response = self.ai.get_response("Hello, how are you?")
            self.assertIsNotNone(response)
            self.assertIsInstance(response, str)
            self.assertTrue(len(response) > 0)
    
    def test_topic_detection(self):
        """Test topic detection."""
        # Test greeting detection
        response = self.ai.get_response("Hello there!")
        self.assertIsNotNone(response)
        
        # Test feeling good detection
        response = self.ai.get_response("I'm feeling great today!")
        self.assertIsNotNone(response)
        
        # Test feeling bad detection
        response = self.ai.get_response("I'm feeling sad today.")
        self.assertIsNotNone(response)
        
        # Test weather detection
        response = self.ai.get_response("The weather is nice today.")
        self.assertIsNotNone(response)
        
        # Test hobby detection
        response = self.ai.get_response("I enjoy playing the piano as a hobby.")
        self.assertIsNotNone(response)
        
        # Test work detection
        response = self.ai.get_response("My work has been stressful lately.")
        self.assertIsNotNone(response)
        
        # Test family detection
        response = self.ai.get_response("I visited my family last weekend.")
        self.assertIsNotNone(response)
        
        # Test thanks detection
        response = self.ai.get_response("Thank you for listening.")
        self.assertIsNotNone(response)
        
        # Test goodbye detection
        response = self.ai.get_response("Goodbye, talk to you later!")
        self.assertIsNotNone(response)
        
        # Test fallback
        response = self.ai.get_response("The square root of 144 is 12.")
        self.assertIsNotNone(response)

if __name__ == "__main__":
    unittest.main()
