"""
Payment UI handlers for VoicePal.

This module provides handlers for payment UI in VoicePal.
"""

import logging
from typing import Dict, List, Optional, Any, Tuple, Union
from datetime import datetime

from telegram import (
    Update,
    InlineKeyboardButton,
    InlineKeyboardMarkup
)
from telegram.ext import ContextTypes

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

async def payment_menu_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """
    Handle the /payment command to show payment options.

    Args:
        update: Telegram update
        context: Callback context
    """
    # Get bot instance from context
    bot_instance = context.application.bot_data.get("bot_instance")
    if not bot_instance:
        await update.message.reply_text("Bot instance not found. Please try again later.")
        return

    # Get payment system
    payment_system = bot_instance.payment_system
    if not payment_system:
        await update.message.reply_text("Payment system not available. Please try again later.")
        return

    # Get user ID
    user_id = update.effective_user.id

    # Get user credits
    user_credits = bot_instance.database.get_user_credits(user_id)

    # Create message text
    message_text = (
        f"💰 Credits & Subscriptions\n\n"
        f"Current Credits: {user_credits}\n\n"
        f"📦 One-time Packages:\n"
    )

    # Add credit packages
    credit_packages = payment_system.get_credit_packages()
    for package_id, package in credit_packages.items():
        price_display = f"{package['price']/100:.2f}"
        currency_symbol = "$" if package['currency'] == "USD" else "€"
        message_text += f"• {package['title']} ({package['credits']} credits) - {currency_symbol}{price_display}\n"

    # Add subscription packages
    subscription_packages = payment_system.get_subscription_packages()
    if subscription_packages:
        message_text += f"\n🔄 Subscription Plans:\n"
        for package_id, package in subscription_packages.items():
            price_display = f"{package['price']/100:.2f}"
            interval = package["interval"]
            currency_symbol = "$" if package['currency'] == "USD" else "€"
            message_text += f"• {package['title']} ({package['credits']} credits/{interval}) - {currency_symbol}{price_display}/{interval}\n"

    # Get active subscriptions
    try:
        active_subscriptions = bot_instance.database.get_user_subscriptions(user_id, active_only=True)
        if active_subscriptions:
            message_text += f"\n✅ Your Active Subscriptions:\n"
            for subscription in active_subscriptions:
                package_id = subscription["package_id"]
                package = subscription_packages.get(package_id)
                if package:
                    next_billing = subscription["next_billing_date"].split("T")[0]  # Get just the date part
                    message_text += f"• {package['title']} - Next billing: {next_billing}\n"
    except Exception as e:
        logger.error(f"Error getting active subscriptions: {e}")

    # Create keyboard
    keyboard = []

    # Add credit package buttons
    credit_buttons = []
    for package_id, package in credit_packages.items():
        price_display = f"{package['price']/100:.2f}"
        currency_symbol = "$" if package['currency'] == "USD" else "€"
        button_text = f"Buy {package['credits']} credits - {currency_symbol}{price_display}"
        credit_buttons.append(
            InlineKeyboardButton(
                text=button_text,
                callback_data=f"buy_credits:{package_id}"
            )
        )

    # Add credit buttons in rows of 1
    for button in credit_buttons:
        keyboard.append([button])

    # Add subscription package buttons
    subscription_buttons = []
    for package_id, package in subscription_packages.items():
        price_display = f"{package['price']/100:.2f}"
        interval = package["interval"]
        currency_symbol = "$" if package['currency'] == "USD" else "€"
        button_text = f"Subscribe {package['credits']} credits/{interval} - {currency_symbol}{price_display}"
        subscription_buttons.append(
            InlineKeyboardButton(
                text=button_text,
                callback_data=f"subscribe:{package_id}"
            )
        )

    # Add subscription buttons in rows of 1
    for button in subscription_buttons:
        keyboard.append([button])

    # Add subscription management button if user has active subscriptions
    if active_subscriptions:
        keyboard.append([
            InlineKeyboardButton(
                text="Manage Subscriptions",
                callback_data="manage_subscriptions"
            )
        ])

    # Add back button
    keyboard.append([
        InlineKeyboardButton(
            text="Back to Main Menu",
            callback_data="main_menu"
        )
    ])

    # Create reply markup
    reply_markup = InlineKeyboardMarkup(keyboard)

    # Send message
    await update.message.reply_text(
        message_text,
        reply_markup=reply_markup
    )

async def handle_payment_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> Optional[Tuple[str, InlineKeyboardMarkup]]:
    """
    Handle payment-related callback queries.

    Args:
        update: Telegram update
        context: Callback context

    Returns:
        Optional[Tuple[str, InlineKeyboardMarkup]]: Response text and keyboard markup if handled, None otherwise
    """
    # Get callback data
    callback_data = update.callback_query.data

    # Check if this is a payment-related callback
    if not (callback_data.startswith("buy_credits:") or
            callback_data.startswith("subscribe:") or
            callback_data == "manage_subscriptions"):
        return None

    # Get bot instance from context
    bot_instance = context.application.bot_data.get("bot_instance")
    if not bot_instance:
        await update.callback_query.answer("Bot instance not found. Please try again later.")
        return "Bot instance not found. Please try again later.", None

    # Get payment system
    payment_system = bot_instance.payment_system
    if not payment_system:
        await update.callback_query.answer("Payment system not available. Please try again later.")
        return "Payment system not available. Please try again later.", None

    # Handle buy credits callback
    if callback_data.startswith("buy_credits:"):
        package_id = callback_data.split(":")[1]
        await payment_system.start_payment(update, context, package_id)
        await update.callback_query.answer("Starting payment process...")
        return None

    # Handle subscribe callback
    elif callback_data.startswith("subscribe:"):
        package_id = callback_data.split(":")[1]
        await payment_system.start_subscription(update, context, package_id)
        await update.callback_query.answer("Starting subscription process...")
        return None

    # Handle manage subscriptions callback
    elif callback_data == "manage_subscriptions":
        return await handle_manage_subscriptions(update, context, bot_instance)

    return None

async def handle_manage_subscriptions(update: Update, context: ContextTypes.DEFAULT_TYPE, bot_instance) -> Tuple[str, InlineKeyboardMarkup]:
    """
    Handle manage subscriptions callback.

    Args:
        update: Telegram update
        context: Callback context
        bot_instance: VoicePalBot instance

    Returns:
        Tuple[str, InlineKeyboardMarkup]: Response text and keyboard markup
    """
    # Get user ID
    user_id = update.callback_query.from_user.id

    # Get payment system
    payment_system = bot_instance.payment_system

    # Get subscription packages
    subscription_packages = payment_system.get_subscription_packages()

    # Get active subscriptions
    try:
        active_subscriptions = bot_instance.database.get_user_subscriptions(user_id, active_only=True)
    except Exception as e:
        logger.error(f"Error getting active subscriptions: {e}")
        active_subscriptions = []

    # Create message text
    if not active_subscriptions:
        message_text = "You don't have any active subscriptions."
        keyboard = [[
            InlineKeyboardButton(
                text="Back to Payment Menu",
                callback_data="payment_menu"
            )
        ]]
        return message_text, InlineKeyboardMarkup(keyboard)

    message_text = "Your Active Subscriptions:\n\n"

    # Create keyboard
    keyboard = []

    # Add subscription details and cancel buttons
    for subscription in active_subscriptions:
        package_id = subscription["package_id"]
        package = subscription_packages.get(package_id)
        if package:
            subscription_id = subscription["id"]
            next_billing = subscription["next_billing_date"].split("T")[0]  # Get just the date part

            message_text += (
                f"📌 {package['title']}\n"
                f"Credits per {package['interval']}: {package['credits']}\n"
                f"Price: ${package['price']/100:.2f}/{package['interval']}\n"
                f"Next billing date: {next_billing}\n\n"
            )

            keyboard.append([
                InlineKeyboardButton(
                    text=f"Cancel {package['title']}",
                    callback_data=f"cancel_subscription:{subscription_id}"
                )
            ])

    # Add back button
    keyboard.append([
        InlineKeyboardButton(
            text="Back to Payment Menu",
            callback_data="payment_menu"
        )
    ])

    return message_text, InlineKeyboardMarkup(keyboard)

async def cancel_subscription_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> Optional[Tuple[str, InlineKeyboardMarkup]]:
    """
    Handle cancel subscription callback.

    Args:
        update: Telegram update
        context: Callback context

    Returns:
        Optional[Tuple[str, InlineKeyboardMarkup]]: Response text and keyboard markup if handled, None otherwise
    """
    # Get callback data
    callback_data = update.callback_query.data

    # Check if this is a cancel subscription callback
    if not callback_data.startswith("cancel_subscription:"):
        return None

    # Get bot instance from context
    bot_instance = context.application.bot_data.get("bot_instance")
    if not bot_instance:
        await update.callback_query.answer("Bot instance not found. Please try again later.")
        return "Bot instance not found. Please try again later.", None

    # Get subscription ID
    subscription_id = int(callback_data.split(":")[1])

    # Cancel subscription
    try:
        success = bot_instance.database.cancel_subscription(subscription_id)
        if success:
            await update.callback_query.answer("Subscription canceled successfully.")
            message_text = "Your subscription has been canceled. You will continue to receive credits until the end of your current billing period."
        else:
            await update.callback_query.answer("Failed to cancel subscription. Please try again later.")
            message_text = "Failed to cancel subscription. Please try again later."
    except Exception as e:
        logger.error(f"Error canceling subscription: {e}")
        await update.callback_query.answer("Error canceling subscription. Please try again later.")
        message_text = "Error canceling subscription. Please try again later."

    # Create keyboard
    keyboard = [[
        InlineKeyboardButton(
            text="Back to Payment Menu",
            callback_data="payment_menu"
        )
    ]]

    return message_text, InlineKeyboardMarkup(keyboard)
