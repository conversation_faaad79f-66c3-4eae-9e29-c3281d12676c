"""
Voice repositories for VoicePal.

This module provides the voice-related repositories for the VoicePal database.
"""

import logging
from typing import Dict, List, Any, Optional, Union, Tuple
from datetime import datetime

from bot.database.core.connection import DatabaseConnection
from bot.database.core.exceptions import (
    DatabaseError,
    DatabaseNotFoundError,
    DatabaseDuplicateError
)
from bot.database.repositories.repository import Repository
from bot.database.models.voice import VoiceSetting, VoiceRecording

# Set up logging
logger = logging.getLogger(__name__)

class VoiceSettingRepository(Repository[VoiceSetting]):
    """Repository for VoiceSetting model."""
    
    _model_class = VoiceSetting
    
    def find_by_user(self, user_id: str) -> Optional[VoiceSetting]:
        """Find voice setting by user ID.
        
        Args:
            user_id: User ID
            
        Returns:
            Voice setting or None if not found
            
        Raises:
            DatabaseError: If query fails
        """
        return self.find_one({"user_id": user_id})
    
    def get_or_create(self, user_id: str, provider: str, voice_id: str) -> <PERSON>ple[VoiceSetting, bool]:
        """Get or create voice setting.
        
        Args:
            user_id: User ID
            provider: Voice provider
            voice_id: Voice ID
            
        Returns:
            Tuple of (voice setting, created) where created is True if a new setting was created
            
        Raises:
            DatabaseError: If query fails
        """
        try:
            setting = self.find_by_user(user_id)
            
            if setting:
                return setting, False
            
            # Create new setting
            setting = VoiceSetting(
                user_id=user_id,
                provider=provider,
                voice_id=voice_id
            )
            
            self.create(setting)
            return setting, True
        except Exception as e:
            logger.error(f"Failed to get or create voice setting: {e}")
            raise DatabaseError(f"Failed to get or create voice setting: {e}") from e
    
    def update_voice(self, user_id: str, provider: str, voice_id: str) -> Optional[VoiceSetting]:
        """Update voice provider and ID.
        
        Args:
            user_id: User ID
            provider: Voice provider
            voice_id: Voice ID
            
        Returns:
            Updated voice setting or None if not found
            
        Raises:
            DatabaseError: If update fails
        """
        try:
            setting = self.find_by_user(user_id)
            
            if not setting:
                # Create new setting
                setting = VoiceSetting(
                    user_id=user_id,
                    provider=provider,
                    voice_id=voice_id
                )
                return self.create(setting)
            
            # Update existing setting
            setting.update_voice(provider, voice_id)
            return self.update(setting)
        except Exception as e:
            logger.error(f"Failed to update voice: {e}")
            raise DatabaseError(f"Failed to update voice: {e}") from e
    
    def update_settings(self, user_id: str, pitch: Optional[float] = None,
                       rate: Optional[float] = None, volume: Optional[float] = None) -> Optional[VoiceSetting]:
        """Update voice settings.
        
        Args:
            user_id: User ID
            pitch: Voice pitch
            rate: Voice rate
            volume: Voice volume
            
        Returns:
            Updated voice setting or None if not found
            
        Raises:
            DatabaseError: If update fails
        """
        try:
            setting = self.find_by_user(user_id)
            
            if not setting:
                return None
            
            setting.update_settings(pitch, rate, volume)
            return self.update(setting)
        except Exception as e:
            logger.error(f"Failed to update voice settings: {e}")
            raise DatabaseError(f"Failed to update voice settings: {e}") from e

class VoiceRecordingRepository(Repository[VoiceRecording]):
    """Repository for VoiceRecording model."""
    
    _model_class = VoiceRecording
    
    def find_by_user(self, user_id: str, limit: Optional[int] = None, offset: Optional[int] = None) -> List[VoiceRecording]:
        """Find voice recordings by user ID.
        
        Args:
            user_id: User ID
            limit: Maximum number of recordings to return
            offset: Offset for pagination
            
        Returns:
            List of voice recordings
            
        Raises:
            DatabaseError: If query fails
        """
        return self.find_all(
            where={"user_id": user_id},
            order_by="created_at DESC",
            limit=limit,
            offset=offset
        )
    
    def find_by_message(self, message_id: str) -> Optional[VoiceRecording]:
        """Find voice recording by message ID.
        
        Args:
            message_id: Message ID
            
        Returns:
            Voice recording or None if not found
            
        Raises:
            DatabaseError: If query fails
        """
        return self.find_one({"message_id": message_id})
    
    def create_recording(self, user_id: str, file_path: str, message_id: Optional[str] = None,
                        duration: Optional[float] = None) -> VoiceRecording:
        """Create a voice recording.
        
        Args:
            user_id: User ID
            file_path: File path
            message_id: Message ID
            duration: Recording duration in seconds
            
        Returns:
            Created voice recording
            
        Raises:
            DatabaseError: If creation fails
        """
        try:
            recording = VoiceRecording(
                user_id=user_id,
                file_path=file_path,
                message_id=message_id,
                duration=duration
            )
            
            return self.create(recording)
        except Exception as e:
            logger.error(f"Failed to create voice recording: {e}")
            raise DatabaseError(f"Failed to create voice recording: {e}") from e
    
    def update_duration(self, recording_id: str, duration: float) -> Optional[VoiceRecording]:
        """Update recording duration.
        
        Args:
            recording_id: Recording ID
            duration: Recording duration in seconds
            
        Returns:
            Updated voice recording or None if not found
            
        Raises:
            DatabaseError: If update fails
        """
        try:
            recording = self.find_by_id(recording_id)
            
            if not recording:
                return None
            
            recording.update_duration(duration)
            return self.update(recording)
        except Exception as e:
            logger.error(f"Failed to update recording duration: {e}")
            raise DatabaseError(f"Failed to update recording duration: {e}") from e
    
    def get_total_duration(self, user_id: str) -> float:
        """Get total duration of user's recordings.
        
        Args:
            user_id: User ID
            
        Returns:
            Total duration in seconds
            
        Raises:
            DatabaseError: If query fails
        """
        try:
            query = f"""
                SELECT SUM(duration) as total_duration
                FROM {self._model_class.get_table_name()}
                WHERE user_id = ? AND duration IS NOT NULL
            """
            
            cursor = self.connection.execute(query, (user_id,))
            row = cursor.fetchone()
            
            return row["total_duration"] or 0.0
        except Exception as e:
            logger.error(f"Failed to get total recording duration: {e}")
            raise DatabaseError(f"Failed to get total recording duration: {e}") from e
