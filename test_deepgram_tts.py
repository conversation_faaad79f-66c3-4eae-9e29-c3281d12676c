#!/usr/bin/env python
"""
Test script for Deepgram TTS provider.

This script tests the Deepgram TTS provider with a real API key to verify
the improvements to voice quality and initialization.

Usage:
    python test_deepgram_tts.py [--no-play] [--save-dir DIR]

Options:
    --no-play    Don't play audio files (just generate them)
    --save-dir   Directory to save generated audio files (default: test_output)
"""

import os
import sys
import logging
import asyncio
import argparse
import subprocess
import shutil
from pathlib import Path
from datetime import datetime

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath("."))

# Import the Deepgram TTS provider
from bot.providers.tts.deepgram_provider import DeepgramTTSProvider

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

async def test_deepgram_tts(play_audio=True, save_dir="test_output"):
    """Test the Deepgram TTS provider with various scenarios."""
    # Get Deepgram API key from environment variable
    api_key = os.getenv("DEEPGRAM_API_KEY")
    if not api_key:
        logger.error("DEEPGRAM_API_KEY environment variable not set")
        return False

    # Create output directory
    output_dir = Path(save_dir)
    output_dir.mkdir(exist_ok=True)

    # Create a timestamp for this test run
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # Create Deepgram TTS provider
    provider = DeepgramTTSProvider(api_key=api_key)
    logger.info(f"Created Deepgram TTS provider with API key: {api_key[:5]}...{api_key[-5:]}")

    # Test with different voices
    voices = [
        "aura-2-thalia-en",  # Female voice
        "aura-2-stella-en",  # Female voice
        "aura-2-cael-en",    # Male voice
        "aura-2-leo-en",     # Male voice
    ]

    # Test texts of different lengths and complexity
    test_texts = [
        # Short responses (testing our short message handling)
        "Yes.",
        "No, I don't think so.",
        "Maybe later.",
        "That sounds great!",

        # Medium responses
        "Hello! I'm VoicePal, your AI companion. How are you feeling today?",
        "I'm designed to sound natural and human-like. Do I sound realistic to you?",

        # Longer responses
        "This is a longer response that tests how the voice handles paragraphs of text. "
        "Natural pauses and intonation are important for realistic speech synthesis. "
        "The improvements to the TTS provider should make this sound more human-like.",

        # Responses with emotion
        "I'm so excited to talk with you today! Let's have a great conversation!",
        "I'm sorry to hear that you're feeling down. Is there anything I can do to help?",
        "That's absolutely amazing news! Congratulations on your achievement!"
    ]

    # Test with different voices
    logger.info("=== Testing different voices ===")
    for voice in voices:
        logger.info(f"Testing voice: {voice}")
        provider.voice_id = voice

        # Use a medium-length text for voice comparison
        text = test_texts[4]  # "Hello! I'm VoicePal..."

        output_path = provider.generate_speech(text, voice=voice)
        if output_path:
            # Copy to output directory with descriptive name
            voice_name = voice.split('-')[-2]
            dest_path = output_dir / f"voice_{voice_name}_{timestamp}.mp3"
            shutil.copy(output_path, dest_path)

            logger.info(f"Speech generated successfully: {dest_path}")

            # Play the audio if requested
            if play_audio:
                play_audio_file(dest_path)
        else:
            logger.error(f"Failed to generate speech with voice {voice}")
            return False

    # Test with different text lengths
    logger.info("\n=== Testing different text lengths ===")
    provider.voice_id = "aura-2-thalia-en"  # Use a consistent voice

    for i, text in enumerate(test_texts):
        logger.info(f"Testing text {i+1}: {text[:30]}...")

        output_path = provider.generate_speech(text)
        if output_path:
            # Copy to output directory with descriptive name
            dest_path = output_dir / f"text_length_{i+1}_{timestamp}.mp3"
            shutil.copy(output_path, dest_path)

            logger.info(f"Speech generated successfully: {dest_path}")

            # Play the audio if requested
            if play_audio:
                play_audio_file(dest_path)
        else:
            logger.error(f"Failed to generate speech for text {i+1}")
            return False

    # Test with different personalities
    logger.info("\n=== Testing different personalities ===")
    personalities = ["friendly", "calm", "witty", "professional"]

    for personality in personalities:
        logger.info(f"Testing personality: {personality}")
        output_path = provider.generate_speech(
            f"This is a {personality} response from VoicePal.",
            personality=personality
        )

        if output_path:
            # Copy to output directory with descriptive name
            dest_path = output_dir / f"personality_{personality}_{timestamp}.mp3"
            shutil.copy(output_path, dest_path)

            logger.info(f"Speech generated successfully: {dest_path}")

            # Play the audio if requested
            if play_audio:
                play_audio_file(dest_path)
        else:
            logger.error(f"Failed to generate speech with personality {personality}")
            return False

    # Test streaming (if supported)
    logger.info("\n=== Testing streaming audio ===")
    streaming_text = "This is a test of streaming audio with Deepgram TTS. " \
                    "Streaming should provide a more responsive experience for users."

    try:
        success = await provider.stream_speech(streaming_text)
        if not success:
            logger.warning("Streaming not successful - this might be expected if ffplay is not available")
    except Exception as e:
        logger.warning(f"Streaming test failed: {e} - this might be expected if the feature is not fully supported")

    logger.info("\nAll tests completed successfully!")
    return True

def play_audio_file(file_path):
    """Play an audio file using the appropriate method for the platform."""
    try:
        if sys.platform == "win32":
            # Windows
            os.system(f'start {file_path}')
        else:
            # Linux/Mac - try ffplay first, then fall back to platform-specific players
            try:
                subprocess.run(
                    ["ffplay", "-autoexit", "-nodisp", str(file_path)],
                    stdout=subprocess.DEVNULL,
                    stderr=subprocess.DEVNULL
                )
            except FileNotFoundError:
                if sys.platform == "darwin":  # macOS
                    subprocess.run(["afplay", str(file_path)])
                else:  # Linux
                    subprocess.run(["aplay", str(file_path)])

        logger.info("Playing audio... (wait for playback)")
        # Sleep briefly to allow audio to start playing
        import time
        time.sleep(2)
    except Exception as e:
        logger.error(f"Error playing audio: {e}")

def main():
    """Parse arguments and run the test."""
    parser = argparse.ArgumentParser(description="Test Deepgram TTS provider")
    parser.add_argument("--no-play", action="store_true", help="Don't play audio files")
    parser.add_argument("--save-dir", default="test_output", help="Directory to save generated audio files")

    args = parser.parse_args()

    asyncio.run(test_deepgram_tts(play_audio=not args.no_play, save_dir=args.save_dir))

if __name__ == "__main__":
    main()
