"""
Test script for context retention in VoicePal.

This script tests the context retention capabilities of the VoicePal bot
by simulating a conversation and checking if the bot maintains context
between messages.
"""

import asyncio
import logging
import os
import sys
from datetime import datetime

# Add the parent directory to the path so we can import the bot modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from bot.config_manager import ConfigManager
from bot.database.core import Database
from bot.providers.ai.google_ai_provider import GoogleAIProvider
from bot.features.memory_system import initialize_memory_system
from bot.core.user_manager import UserManager

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class ContextRetentionTester:
    """Test context retention in VoicePal."""

    def __init__(self):
        """Initialize the tester."""
        # Load configuration
        self.config_manager = ConfigManager()

        # Initialize database
        self.database = Database(":memory:")  # Use in-memory database for testing

        # Initialize AI provider
        google_ai_key = os.getenv("GOOGLE_AI_API_KEY")
        if not google_ai_key:
            logger.error("GOOGLE_AI_API_KEY environment variable not set")
            sys.exit(1)

        self.ai_provider = GoogleAIProvider(api_key=google_ai_key)

        # Initialize user manager
        self.user_manager = UserManager(self.database)

        # Initialize memory system
        memory_components = initialize_memory_system(
            database=self.database,
            ai_provider=self.ai_provider,
            user_manager=self.user_manager,
            config=self.config_manager.get_feature_config("memory")
        )

        self.memory_manager = memory_components["memory_manager"]
        self.dialog_engine = memory_components["dialog_engine"]

        # Create a test user
        self.test_user_id = 12345
        self.database.add_user(
            user_id=self.test_user_id,
            username="test_user",
            first_name="Test",
            last_name="User"
        )

        # Add initial credits
        self.database.add_credits(self.test_user_id, 100)

        # Set up user preferences
        self.database.set_user_personality(self.test_user_id, "friendly")

    async def run_test(self):
        """Run the context retention test."""
        logger.info("Starting context retention test")

        # Test conversation with context-dependent messages
        messages = [
            "Hi, my name is Alex.",
            "I'm interested in learning about machine learning.",
            "What's a good resource to start with?",
            "That sounds interesting. What about neural networks specifically?",
            "Can you remember what my name is?",
            "What were we talking about earlier?",
            "Thanks for the information. I'll check those resources out."
        ]

        # Process each message and check for context retention
        for i, message in enumerate(messages):
            logger.info(f"Processing message {i+1}: {message}")

            # Process the message
            response = await self.dialog_engine.process_message(
                user_id=self.test_user_id,
                message=message,
                language="en",
                is_voice=False
            )

            # Log the response
            logger.info(f"Response: {response['text']}")

            # Add a small delay to simulate real conversation
            await asyncio.sleep(1)

        # Check if the bot remembered the user's name
        name_message = "What's my name again?"
        logger.info(f"Testing name recall: {name_message}")
        name_response = await self.dialog_engine.process_message(
            user_id=self.test_user_id,
            message=name_message,
            language="en",
            is_voice=False
        )
        logger.info(f"Name recall response: {name_response['text']}")

        # Check if the name "Alex" is in the response
        if "Alex" in name_response["text"]:
            logger.info("SUCCESS: Bot remembered the user's name")
        else:
            logger.warning("FAILURE: Bot did not remember the user's name")

        # Test topic recall
        topic_message = "What topic were we discussing earlier?"
        logger.info(f"Testing topic recall: {topic_message}")
        topic_response = await self.dialog_engine.process_message(
            user_id=self.test_user_id,
            message=topic_message,
            language="en",
            is_voice=False
        )
        logger.info(f"Topic recall response: {topic_response['text']}")

        # Check if "machine learning" or "neural networks" is in the response
        if "machine learning" in topic_response["text"].lower() or "neural networks" in topic_response["text"].lower():
            logger.info("SUCCESS: Bot remembered the conversation topic")
        else:
            logger.warning("FAILURE: Bot did not remember the conversation topic")

        # Print conversation history from database
        conversations = self.database.get_conversations(self.test_user_id)
        logger.info(f"Conversation history from database ({len(conversations)} entries):")
        for i, conv in enumerate(conversations):
            logger.info(f"  {i+1}. User: {conv['message']}")
            logger.info(f"     Bot: {conv['response']}")

        logger.info("Context retention test completed")

async def main():
    """Run the test."""
    tester = ContextRetentionTester()
    await tester.run_test()

if __name__ == "__main__":
    asyncio.run(main())
