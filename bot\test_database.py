"""
Test script for the database module.

This script tests the basic functionality of the database module.
"""

import os
import unittest
from database import Database

class TestDatabase(unittest.TestCase):
    """Test cases for the Database class."""
    
    def setUp(self):
        """Set up test environment."""
        # Use a test database file
        self.test_db_file = "test_voicepal.db"
        # Remove test database if it exists
        if os.path.exists(self.test_db_file):
            os.remove(self.test_db_file)
        # Create a new database instance
        self.db = Database(self.test_db_file)
    
    def tearDown(self):
        """Clean up after tests."""
        self.db.close()
        # Remove test database
        if os.path.exists(self.test_db_file):
            os.remove(self.test_db_file)
    
    def test_add_user(self):
        """Test adding a new user."""
        # Add a new user
        is_new = self.db.add_user(
            user_id=123456789,
            username="testuser",
            first_name="Test",
            last_name="User"
        )
        # Should be a new user
        self.assertTrue(is_new)
        
        # Add the same user again
        is_new = self.db.add_user(
            user_id=123456789,
            username="testuser_updated",
            first_name="Test",
            last_name="User"
        )
        # Should not be a new user
        self.assertFalse(is_new)
        
        # Get the user
        user = self.db.get_user(123456789)
        # Check user data
        self.assertEqual(user["user_id"], 123456789)
        self.assertEqual(user["username"], "testuser_updated")
        self.assertEqual(user["first_name"], "Test")
        self.assertEqual(user["last_name"], "User")
        self.assertEqual(user["credits"], 0)
        self.assertEqual(user["personality"], "friendly")
    
    def test_credits(self):
        """Test credit operations."""
        # Add a user
        self.db.add_user(user_id=123456789)
        
        # Initial credits should be 0
        self.assertEqual(self.db.get_user_credits(123456789), 0)
        
        # Add credits
        new_balance = self.db.add_credits(123456789, 100)
        self.assertEqual(new_balance, 100)
        self.assertEqual(self.db.get_user_credits(123456789), 100)
        
        # Use credits successfully
        success = self.db.use_credits(123456789, 50)
        self.assertTrue(success)
        self.assertEqual(self.db.get_user_credits(123456789), 50)
        
        # Try to use more credits than available
        success = self.db.use_credits(123456789, 100)
        self.assertFalse(success)
        self.assertEqual(self.db.get_user_credits(123456789), 50)
    
    def test_personality(self):
        """Test personality operations."""
        # Add a user
        self.db.add_user(user_id=123456789)
        
        # Default personality should be 'friendly'
        self.assertEqual(self.db.get_user_personality(123456789), "friendly")
        
        # Set personality
        success = self.db.set_user_personality(123456789, "witty")
        self.assertTrue(success)
        self.assertEqual(self.db.get_user_personality(123456789), "witty")
    
    def test_transactions(self):
        """Test transaction operations."""
        # Add a user
        self.db.add_user(user_id=123456789)
        
        # Add a transaction
        transaction_id = self.db.add_transaction(
            user_id=123456789,
            amount=9.99,
            credits=100,
            transaction_id="test_transaction_123",
            status="completed"
        )
        self.assertIsNotNone(transaction_id)
        
        # Get transactions
        transactions = self.db.get_transactions(123456789)
        self.assertEqual(len(transactions), 1)
        self.assertEqual(transactions[0]["user_id"], 123456789)
        self.assertEqual(transactions[0]["amount"], 9.99)
        self.assertEqual(transactions[0]["credits"], 100)
        self.assertEqual(transactions[0]["transaction_id"], "test_transaction_123")
        self.assertEqual(transactions[0]["status"], "completed")
    
    def test_conversations(self):
        """Test conversation operations."""
        # Add a user
        self.db.add_user(user_id=123456789)
        
        # Add a conversation
        conversation_id = self.db.add_conversation(
            user_id=123456789,
            message="Hello, bot!",
            response="Hello, human!",
            is_voice=False,
            credits_used=1
        )
        self.assertIsNotNone(conversation_id)
        
        # Get conversations
        conversations = self.db.get_conversations(123456789)
        self.assertEqual(len(conversations), 1)
        self.assertEqual(conversations[0]["user_id"], 123456789)
        self.assertEqual(conversations[0]["message"], "Hello, bot!")
        self.assertEqual(conversations[0]["response"], "Hello, human!")
        self.assertEqual(conversations[0]["is_voice"], 0)  # SQLite stores booleans as 0/1
        self.assertEqual(conversations[0]["credits_used"], 1)

if __name__ == "__main__":
    unittest.main()
