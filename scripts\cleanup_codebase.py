"""
<PERSON><PERSON><PERSON> to clean up the codebase by removing duplicate implementations and outdated components.

This script identifies and removes duplicate implementations, updates imports,
and cleans up outdated components to improve maintainability.
"""

import os
import sys
import re
import shutil
import logging
from typing import Dict, List, Set, Tuple, Any
from pathlib import Path
import importlib
import inspect

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# Define duplicate implementations to be removed
DUPLICATE_IMPLEMENTATIONS = {
    # Original implementation: Enhanced implementation
    "bot/features/memory_manager.py": "bot/features/enhanced_memory_manager.py",
    "bot/core/dialog_engine.py": "bot/core/enhanced_dialog_engine.py",
}

# Define outdated components to be removed
OUTDATED_COMPONENTS = [
    "bot/providers/tts/dia_provider.py",  # Outdated TTS provider
    "bot/main.py.bak",  # Backup file
]

# Define import replacements
IMPORT_REPLACEMENTS = {
    "from bot.features.enhanced_memory_manager import EnhancedMemoryManager as MemoryManager": "from bot.features.enhanced_memory_manager import EnhancedMemoryManager as MemoryManager",
    "from bot.core.enhanced_dialog_engine import EnhancedDialogEngine as DialogEngine": "from bot.core.enhanced_dialog_engine import EnhancedDialogEngine as DialogEngine",
}

def backup_file(file_path: str) -> str:
    """
    Create a backup of a file.
    
    Args:
        file_path: Path to the file
        
    Returns:
        Path to the backup file
    """
    backup_path = f"{file_path}.bak"
    try:
        shutil.copy2(file_path, backup_path)
        logger.info(f"Created backup: {backup_path}")
        return backup_path
    except Exception as e:
        logger.error(f"Error creating backup of {file_path}: {e}")
        return ""

def find_files_with_imports(directory: str, import_patterns: List[str]) -> Dict[str, List[str]]:
    """
    Find files that contain specific import patterns.
    
    Args:
        directory: Directory to search
        import_patterns: List of import patterns to search for
        
    Returns:
        Dict mapping file paths to lists of found import patterns
    """
    result = {}
    
    for root, _, files in os.walk(directory):
        for file in files:
            if not file.endswith(".py"):
                continue
                
            file_path = os.path.join(root, file)
            
            try:
                with open(file_path, "r", encoding="utf-8") as f:
                    content = f.read()
                
                found_patterns = []
                for pattern in import_patterns:
                    if pattern in content:
                        found_patterns.append(pattern)
                
                if found_patterns:
                    result[file_path] = found_patterns
            except Exception as e:
                logger.error(f"Error reading {file_path}: {e}")
    
    return result

def update_imports(file_path: str, replacements: Dict[str, str]) -> bool:
    """
    Update imports in a file.
    
    Args:
        file_path: Path to the file
        replacements: Dict mapping old imports to new imports
        
    Returns:
        bool: True if file was updated, False otherwise
    """
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()
        
        updated_content = content
        for old_import, new_import in replacements.items():
            updated_content = updated_content.replace(old_import, new_import)
        
        if updated_content != content:
            # Create backup
            backup_file(file_path)
            
            # Write updated content
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(updated_content)
            
            logger.info(f"Updated imports in {file_path}")
            return True
        
        return False
    except Exception as e:
        logger.error(f"Error updating imports in {file_path}: {e}")
        return False

def remove_file(file_path: str) -> bool:
    """
    Remove a file after creating a backup.
    
    Args:
        file_path: Path to the file
        
    Returns:
        bool: True if file was removed, False otherwise
    """
    try:
        if not os.path.exists(file_path):
            logger.warning(f"File does not exist: {file_path}")
            return False
        
        # Create backup
        backup_file(file_path)
        
        # Remove file
        os.remove(file_path)
        logger.info(f"Removed file: {file_path}")
        return True
    except Exception as e:
        logger.error(f"Error removing {file_path}: {e}")
        return False

def cleanup_codebase() -> Tuple[int, int, int]:
    """
    Clean up the codebase by removing duplicate implementations and outdated components.
    
    Returns:
        Tuple containing counts of files updated, files removed, and errors
    """
    files_updated = 0
    files_removed = 0
    errors = 0
    
    try:
        # Find files with imports that need to be updated
        import_patterns = list(IMPORT_REPLACEMENTS.keys())
        files_with_imports = find_files_with_imports("bot", import_patterns)
        
        logger.info(f"Found {len(files_with_imports)} files with imports that need to be updated")
        
        # Update imports
        for file_path, imports in files_with_imports.items():
            # Create a dict with only the replacements needed for this file
            replacements = {old_import: IMPORT_REPLACEMENTS[old_import] 
                           for old_import in imports if old_import in IMPORT_REPLACEMENTS}
            
            if update_imports(file_path, replacements):
                files_updated += 1
        
        # Remove duplicate implementations
        for original, enhanced in DUPLICATE_IMPLEMENTATIONS.items():
            if os.path.exists(original) and os.path.exists(enhanced):
                if remove_file(original):
                    files_removed += 1
                else:
                    errors += 1
        
        # Remove outdated components
        for component in OUTDATED_COMPONENTS:
            if os.path.exists(component):
                if remove_file(component):
                    files_removed += 1
                else:
                    errors += 1
        
        return files_updated, files_removed, errors
    except Exception as e:
        logger.error(f"Error cleaning up codebase: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return files_updated, files_removed, errors + 1

def main():
    """Main function."""
    logger.info("Starting codebase cleanup")
    
    files_updated, files_removed, errors = cleanup_codebase()
    
    logger.info(f"Codebase cleanup completed:")
    logger.info(f"  - Files updated: {files_updated}")
    logger.info(f"  - Files removed: {files_removed}")
    logger.info(f"  - Errors: {errors}")
    
    if errors > 0:
        logger.warning("Cleanup completed with errors. Check the log for details.")
        return 1
    
    logger.info("Cleanup completed successfully")
    return 0

if __name__ == "__main__":
    sys.exit(main())
