"""
Database extension for enhanced memory management.

This module extends the database with methods needed for the enhanced memory manager.
"""

import logging
from typing import Dict, Any, List, Optional, Union
from datetime import datetime

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

def extend_database_for_memory(database):
    """
    Extend database with methods for enhanced memory management.

    Args:
        database: Database instance
    """
    try:
        conn = database.conn
        cursor = database.cursor

        # Create conversation_importance table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS conversation_importance (
            conversation_id INTEGER PRIMARY KEY,
            importance_score REAL DEFAULT 0.5,
            FOREIGN KEY (conversation_id) REFERENCES conversations (id)
        )
        ''')

        # Create conversation_topics table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS conversation_topics (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            conversation_id INTEGER,
            topic TEXT,
            FOREIG<PERSON> KEY (conversation_id) REFERENCES conversations (id)
        )
        ''')

        conn.commit()
        logger.info("Database schema extended for enhanced memory management")
    except Exception as e:
        logger.error(f"Error extending database schema for memory: {e}")
        if conn:
            conn.rollback()
        raise

    # Add method to get conversation count
    def get_conversation_count(self, user_id: int) -> int:
        """
        Get total number of conversations for a user.

        Args:
            user_id: User ID

        Returns:
            int: Number of conversations
        """
        try:
            self.cursor.execute(
                """SELECT COUNT(*) as count
                   FROM conversations
                   WHERE user_id = ?""",
                (user_id,)
            )
            result = self.cursor.fetchone()
            return result['count'] if result else 0
        except Exception as e:
            logger.error(f"Error getting conversation count for user {user_id}: {e}")
            return 0

    # Add method to get conversation count since a specific time
    def get_conversation_count_since(self, user_id: int, since: datetime) -> int:
        """
        Get number of conversations for a user since a specific time.

        Args:
            user_id: User ID
            since: Datetime to count from

        Returns:
            int: Number of conversations
        """
        try:
            since_str = since.strftime('%Y-%m-%d %H:%M:%S')
            self.cursor.execute(
                """SELECT COUNT(*) as count
                   FROM conversations
                   WHERE user_id = ? AND created_at > ?""",
                (user_id, since_str)
            )
            result = self.cursor.fetchone()
            return result['count'] if result else 0
        except Exception as e:
            logger.error(f"Error getting conversation count since {since} for user {user_id}: {e}")
            return 0

    # Add method to get older conversations
    def get_older_conversations(self, user_id: int, skip: int, limit: int) -> List[Dict[str, Any]]:
        """
        Get older conversations, skipping the most recent ones.

        Args:
            user_id: User ID
            skip: Number of recent conversations to skip
            limit: Maximum number of conversations to return

        Returns:
            List of conversation dictionaries
        """
        try:
            self.cursor.execute(
                """SELECT id, user_id, message, response, is_voice, credits_used, created_at
                   FROM conversations
                   WHERE user_id = ?
                   ORDER BY created_at DESC
                   LIMIT ? OFFSET ?""",
                (user_id, limit, skip)
            )
            conversations = self.cursor.fetchall()
            return [dict(c) for c in conversations]
        except Exception as e:
            logger.error(f"Error getting older conversations for user {user_id}: {e}")
            return []

    # Add method to set conversation importance
    def set_conversation_importance(self, conversation_id: int, importance_score: float) -> bool:
        """
        Set importance score for a conversation.

        Args:
            conversation_id: Conversation ID
            importance_score: Importance score (0.0 to 1.0)

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            self.cursor.execute(
                """INSERT OR REPLACE INTO conversation_importance
                   (conversation_id, importance_score)
                   VALUES (?, ?)""",
                (conversation_id, importance_score)
            )
            self.conn.commit()
            logger.info(f"Set importance score {importance_score} for conversation {conversation_id}")
            return True
        except Exception as e:
            logger.error(f"Error setting importance for conversation {conversation_id}: {e}")
            self.conn.rollback()
            return False

    # Add method to get important conversations
    def get_important_conversations(self, user_id: int, threshold: float = 0.7, limit: int = 5) -> List[Dict[str, Any]]:
        """
        Get important conversations for a user.

        Args:
            user_id: User ID
            threshold: Importance threshold (0.0 to 1.0)
            limit: Maximum number of conversations to return

        Returns:
            List of important conversation dictionaries
        """
        try:
            self.cursor.execute(
                """SELECT c.id, c.user_id, c.message, c.response, c.is_voice, c.credits_used, c.created_at
                   FROM conversations c
                   JOIN conversation_importance ci ON c.id = ci.conversation_id
                   WHERE c.user_id = ? AND ci.importance_score >= ?
                   ORDER BY ci.importance_score DESC
                   LIMIT ?""",
                (user_id, threshold, limit)
            )
            conversations = self.cursor.fetchall()
            return [dict(c) for c in conversations]
        except Exception as e:
            logger.error(f"Error getting important conversations for user {user_id}: {e}")
            return []

    # Add method to add conversation topic
    def add_conversation_topic(self, conversation_id: int, topic: str) -> bool:
        """
        Add a topic to a conversation.

        Args:
            conversation_id: Conversation ID
            topic: Topic

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            self.cursor.execute(
                """INSERT INTO conversation_topics
                   (conversation_id, topic)
                   VALUES (?, ?)""",
                (conversation_id, topic)
            )
            self.conn.commit()
            logger.info(f"Added topic '{topic}' to conversation {conversation_id}")
            return True
        except Exception as e:
            logger.error(f"Error adding topic to conversation {conversation_id}: {e}")
            self.conn.rollback()
            return False

    # Add method to get conversations by topic
    def get_conversations_by_topic(self, user_id: int, topic: str, limit: int = 5) -> List[Dict[str, Any]]:
        """
        Get conversations for a user by topic.

        Args:
            user_id: User ID
            topic: Topic
            limit: Maximum number of conversations to return

        Returns:
            List of conversation dictionaries
        """
        try:
            self.cursor.execute(
                """SELECT c.id, c.user_id, c.message, c.response, c.is_voice, c.credits_used, c.created_at
                   FROM conversations c
                   JOIN conversation_topics ct ON c.id = ct.conversation_id
                   WHERE c.user_id = ? AND ct.topic = ?
                   ORDER BY c.created_at DESC
                   LIMIT ?""",
                (user_id, topic, limit)
            )
            conversations = self.cursor.fetchall()
            return [dict(c) for c in conversations]
        except Exception as e:
            logger.error(f"Error getting conversations by topic for user {user_id}: {e}")
            return []

    # Add method to get user summary
    def get_user_summary(self, user_id: int) -> Optional[str]:
        """
        Get user summary.

        Args:
            user_id: User ID

        Returns:
            str: User summary or None if not available
        """
        try:
            self.cursor.execute(
                """SELECT summary
                   FROM user_summaries
                   WHERE user_id = ?""",
                (user_id,)
            )
            result = self.cursor.fetchone()
            return result['summary'] if result else None
        except Exception as e:
            logger.error(f"Error getting summary for user {user_id}: {e}")
            return None

    # Add method to update user summary
    def update_user_summary(self, user_id: int, summary: str) -> bool:
        """
        Update user summary.

        Args:
            user_id: User ID
            summary: Summary text

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            self.cursor.execute(
                """INSERT OR REPLACE INTO user_summaries
                   (user_id, summary, updated_at)
                   VALUES (?, ?, CURRENT_TIMESTAMP)""",
                (user_id, summary)
            )
            self.conn.commit()
            logger.info(f"Updated summary for user {user_id}")
            return True
        except Exception as e:
            logger.error(f"Error updating summary for user {user_id}: {e}")
            self.conn.rollback()
            return False

    # Add method to get user summary update time
    def get_user_summary_update_time(self, user_id: int) -> Optional[datetime]:
        """
        Get user summary update time.

        Args:
            user_id: User ID

        Returns:
            datetime: Update time or None if not available
        """
        try:
            self.cursor.execute(
                """SELECT updated_at
                   FROM user_summaries
                   WHERE user_id = ?""",
                (user_id,)
            )
            result = self.cursor.fetchone()
            if result and result['updated_at']:
                try:
                    # Try to parse the timestamp
                    return datetime.strptime(result['updated_at'], '%Y-%m-%d %H:%M:%S')
                except ValueError:
                    logger.error(f"Error parsing timestamp for user {user_id}")
                    return None
            return None
        except Exception as e:
            logger.error(f"Error getting summary update time for user {user_id}: {e}")
            return None

    # Add methods to Database class
    setattr(database.__class__, 'get_conversation_count', get_conversation_count)
    setattr(database.__class__, 'get_conversation_count_since', get_conversation_count_since)
    setattr(database.__class__, 'get_older_conversations', get_older_conversations)
    setattr(database.__class__, 'set_conversation_importance', set_conversation_importance)
    setattr(database.__class__, 'get_important_conversations', get_important_conversations)
    setattr(database.__class__, 'add_conversation_topic', add_conversation_topic)
    setattr(database.__class__, 'get_conversations_by_topic', get_conversations_by_topic)
    setattr(database.__class__, 'get_user_summary', get_user_summary)
    setattr(database.__class__, 'update_user_summary', update_user_summary)
    setattr(database.__class__, 'get_user_summary_update_time', get_user_summary_update_time)

    logger.info("Database extended with methods for enhanced memory management")
