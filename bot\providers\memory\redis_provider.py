"""
Redis provider for VoicePal memory system.

This module provides a Redis client wrapper for caching and rate limiting.
"""

import os
import json
import logging
from typing import Any, Dict, List, Optional, Union
from datetime import datetime, timedelta

try:
    from upstash_redis import Redis
    from upstash_redis.asyncio import Redis as AsyncRedis
    from upstash_ratelimit import Ratelimit, FixedWindow
    UPSTASH_AVAILABLE = True
except ImportError:
    UPSTASH_AVAILABLE = False

# Set up logging
logger = logging.getLogger(__name__)

class RedisProvider:
    """Redis provider for VoicePal memory system."""

    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize the Redis provider.

        Args:
            config: Configuration dictionary
        """
        self.config = config or {}
        
        # Initialize Redis client
        self.redis = self._initialize_redis()
        self.async_redis = self._initialize_async_redis()
        
        # Initialize rate limiter
        self.rate_limiter = self._initialize_rate_limiter()
        
        # Cache configuration
        self.cache_ttl_seconds = self.config.get("cache_ttl_minutes", 30) * 60
        self.name_cache_ttl_seconds = self.config.get("name_cache_ttl_minutes", 60) * 60
        
    def _initialize_redis(self) -> Optional[Any]:
        """Initialize Redis client."""
        if not UPSTASH_AVAILABLE:
            logger.warning("upstash-redis not installed, Redis functionality will be limited")
            return None
            
        try:
            # Try to initialize from environment variables
            redis = Redis.from_env()
            logger.info("Redis client initialized from environment variables")
            return redis
        except Exception as e:
            logger.warning(f"Failed to initialize Redis from environment: {e}")
            
            # Try to initialize from config
            url = self.config.get("redis_url")
            token = self.config.get("redis_token")
            
            if url and token:
                try:
                    redis = Redis(url=url, token=token)
                    logger.info("Redis client initialized from config")
                    return redis
                except Exception as e:
                    logger.error(f"Failed to initialize Redis from config: {e}")
            
            # Return None if initialization fails
            logger.warning("Redis client not initialized, using mock implementation")
            return None
    
    def _initialize_async_redis(self) -> Optional[Any]:
        """Initialize async Redis client."""
        if not UPSTASH_AVAILABLE:
            return None
            
        try:
            # Try to initialize from environment variables
            redis = AsyncRedis.from_env()
            logger.info("Async Redis client initialized from environment variables")
            return redis
        except Exception as e:
            logger.warning(f"Failed to initialize async Redis from environment: {e}")
            
            # Try to initialize from config
            url = self.config.get("redis_url")
            token = self.config.get("redis_token")
            
            if url and token:
                try:
                    redis = AsyncRedis(url=url, token=token)
                    logger.info("Async Redis client initialized from config")
                    return redis
                except Exception as e:
                    logger.error(f"Failed to initialize async Redis from config: {e}")
            
            # Return None if initialization fails
            logger.warning("Async Redis client not initialized, using mock implementation")
            return None
    
    def _initialize_rate_limiter(self) -> Optional[Any]:
        """Initialize rate limiter."""
        if not UPSTASH_AVAILABLE or not self.redis:
            return None
        
        try:
            # Create a rate limiter that allows 10 requests per 10 seconds by default
            max_requests = self.config.get("rate_limit_max_requests", 10)
            window_seconds = self.config.get("rate_limit_window_seconds", 10)
            
            rate_limiter = Ratelimit(
                redis=self.redis,
                limiter=FixedWindow(max_requests=max_requests, window=window_seconds),
                prefix="@voicepal/ratelimit"
            )
            
            logger.info(f"Rate limiter initialized with {max_requests} requests per {window_seconds} seconds")
            return rate_limiter
        except Exception as e:
            logger.error(f"Failed to initialize rate limiter: {e}")
            return None
    
    def is_available(self) -> bool:
        """Check if Redis is available."""
        if not self.redis:
            return False
        
        try:
            # Ping Redis to check availability
            self.redis.ping()
            return True
        except Exception as e:
            logger.error(f"Redis is not available: {e}")
            return False
    
    def get(self, key: str) -> Any:
        """
        Get a value from Redis.
        
        Args:
            key: Redis key
            
        Returns:
            Value or None if not found
        """
        if not self.redis:
            return None
        
        try:
            value = self.redis.get(key)
            
            # Try to parse JSON if the value is a string
            if isinstance(value, str):
                try:
                    return json.loads(value)
                except json.JSONDecodeError:
                    return value
            
            return value
        except Exception as e:
            logger.error(f"Error getting value from Redis: {e}")
            return None
    
    async def get_async(self, key: str) -> Any:
        """
        Get a value from Redis asynchronously.
        
        Args:
            key: Redis key
            
        Returns:
            Value or None if not found
        """
        if not self.async_redis:
            return None
        
        try:
            value = await self.async_redis.get(key)
            
            # Try to parse JSON if the value is a string
            if isinstance(value, str):
                try:
                    return json.loads(value)
                except json.JSONDecodeError:
                    return value
            
            return value
        except Exception as e:
            logger.error(f"Error getting value from Redis asynchronously: {e}")
            return None
    
    def set(self, key: str, value: Any, ttl_seconds: Optional[int] = None) -> bool:
        """
        Set a value in Redis.
        
        Args:
            key: Redis key
            value: Value to set
            ttl_seconds: Time to live in seconds
            
        Returns:
            True if successful, False otherwise
        """
        if not self.redis:
            return False
        
        try:
            # Convert dict or list to JSON string
            if isinstance(value, (dict, list)):
                value = json.dumps(value)
            
            # Set with TTL if provided
            if ttl_seconds is not None:
                return self.redis.setex(key, ttl_seconds, value)
            else:
                return self.redis.set(key, value)
        except Exception as e:
            logger.error(f"Error setting value in Redis: {e}")
            return False
    
    async def set_async(self, key: str, value: Any, ttl_seconds: Optional[int] = None) -> bool:
        """
        Set a value in Redis asynchronously.
        
        Args:
            key: Redis key
            value: Value to set
            ttl_seconds: Time to live in seconds
            
        Returns:
            True if successful, False otherwise
        """
        if not self.async_redis:
            return False
        
        try:
            # Convert dict or list to JSON string
            if isinstance(value, (dict, list)):
                value = json.dumps(value)
            
            # Set with TTL if provided
            if ttl_seconds is not None:
                return await self.async_redis.setex(key, ttl_seconds, value)
            else:
                return await self.async_redis.set(key, value)
        except Exception as e:
            logger.error(f"Error setting value in Redis asynchronously: {e}")
            return False
    
    def delete(self, key: str) -> bool:
        """
        Delete a key from Redis.
        
        Args:
            key: Redis key
            
        Returns:
            True if successful, False otherwise
        """
        if not self.redis:
            return False
        
        try:
            return self.redis.delete(key) > 0
        except Exception as e:
            logger.error(f"Error deleting key from Redis: {e}")
            return False
    
    async def delete_async(self, key: str) -> bool:
        """
        Delete a key from Redis asynchronously.
        
        Args:
            key: Redis key
            
        Returns:
            True if successful, False otherwise
        """
        if not self.async_redis:
            return False
        
        try:
            return await self.async_redis.delete(key) > 0
        except Exception as e:
            logger.error(f"Error deleting key from Redis asynchronously: {e}")
            return False
    
    def check_rate_limit(self, identifier: str, tokens: int = 1) -> bool:
        """
        Check if the rate limit is exceeded.
        
        Args:
            identifier: Rate limit identifier (e.g., user ID)
            tokens: Number of tokens to consume
            
        Returns:
            True if the rate limit is not exceeded, False otherwise
        """
        if not self.rate_limiter:
            # If rate limiter is not available, allow all requests
            return True
        
        try:
            # Check rate limit
            result = self.rate_limiter.limit(identifier, {"rate": tokens})
            return result.allowed
        except Exception as e:
            logger.error(f"Error checking rate limit: {e}")
            # If there's an error, allow the request
            return True
    
    async def check_rate_limit_async(self, identifier: str, tokens: int = 1) -> bool:
        """
        Check if the rate limit is exceeded asynchronously.
        
        Args:
            identifier: Rate limit identifier (e.g., user ID)
            tokens: Number of tokens to consume
            
        Returns:
            True if the rate limit is not exceeded, False otherwise
        """
        if not self.rate_limiter:
            # If rate limiter is not available, allow all requests
            return True
        
        try:
            # Check rate limit
            result = await self.rate_limiter.limit(identifier, {"rate": tokens})
            return result.allowed
        except Exception as e:
            logger.error(f"Error checking rate limit asynchronously: {e}")
            # If there's an error, allow the request
            return True
    
    def get_cache_key(self, prefix: str, user_id: Union[int, str]) -> str:
        """
        Generate a cache key.
        
        Args:
            prefix: Key prefix
            user_id: User ID
            
        Returns:
            Cache key
        """
        return f"{prefix}:{user_id}"
    
    def get_conversation_cache(self, user_id: Union[int, str]) -> Dict[str, Any]:
        """
        Get conversation cache for a user.
        
        Args:
            user_id: User ID
            
        Returns:
            Conversation cache or empty dict if not found
        """
        cache_key = self.get_cache_key("conversation", user_id)
        cache = self.get(cache_key)
        return cache if cache else {}
    
    def set_conversation_cache(self, user_id: Union[int, str], cache: Dict[str, Any]) -> bool:
        """
        Set conversation cache for a user.
        
        Args:
            user_id: User ID
            cache: Conversation cache
            
        Returns:
            True if successful, False otherwise
        """
        cache_key = self.get_cache_key("conversation", user_id)
        return self.set(cache_key, cache, self.cache_ttl_seconds)
    
    def clear_conversation_cache(self, user_id: Union[int, str]) -> bool:
        """
        Clear conversation cache for a user.
        
        Args:
            user_id: User ID
            
        Returns:
            True if successful, False otherwise
        """
        cache_key = self.get_cache_key("conversation", user_id)
        return self.delete(cache_key)
