"""
User manager for VoicePal.

This module handles user profiles, preferences, and state.
"""

import logging
from typing import Dict, Any, Optional, List
from datetime import datetime

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class UserManager:
    """Manages user profiles, preferences, and state."""

    def __init__(self, database):
        """
        Initialize the user manager.

        Args:
            database: Database instance
        """
        self.database = database

    def get_user_profile(self, user_id: int) -> Dict[str, Any]:
        """
        Get user profile including preferences and state.

        Args:
            user_id: User ID

        Returns:
            Dict containing user data, preferences, and conversation history
        """
        try:
            # Get basic user data
            user_data = self.database.get_user(user_id)

            # Get user preferences
            preferences = self.get_user_preferences(user_id)

            # Get conversation history
            conversation_history = self.database.get_conversations(user_id, limit=10)

            # Get user summary if available
            summary = self.get_user_summary(user_id)

            return {
                "user_data": user_data,
                "preferences": preferences,
                "conversation_history": conversation_history,
                "summary": summary
            }
        except Exception as e:
            logger.error(f"Error getting user profile for user {user_id}: {e}")
            return {
                "user_data": {},
                "preferences": {},
                "conversation_history": [],
                "summary": None
            }

    def get_user_preferences(self, user_id: int) -> Dict[str, Any]:
        """
        Get user preferences.

        Args:
            user_id: User ID

        Returns:
            Dict containing user preferences
        """
        try:
            # Get user preferences from database
            preferences = self.database.get_user_preferences(user_id)

            # If no preferences found, return default preferences
            if not preferences:
                # Get default preferences from config if available
                default_preferences = {}
                if hasattr(self, 'config_manager') and self.config_manager:
                    personalization_config = self.config_manager.get_feature_config("personalization") or {}
                    default_preferences = personalization_config.get("default_preferences", {})

                # Merge with hardcoded defaults for essential preferences
                default_preferences = {
                    "personality": self.database.get_user_personality(user_id) or "friendly",
                    "language": "en",
                    "voice": "default",
                    "response_length": "medium",
                    "formality": "casual",
                    **default_preferences
                }

                return default_preferences

            return preferences
        except Exception as e:
            logger.error(f"Error getting user preferences for user {user_id}: {e}")
            return {
                "personality": "friendly",
                "language": "en",
                "voice": "default",
                "response_length": "medium",
                "formality": "casual"
            }

    def update_user_preference(self, user_id: int, preference_key: str, preference_value: Any) -> bool:
        """
        Update a user preference.

        Args:
            user_id: User ID
            preference_key: Preference key
            preference_value: Preference value

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Update user preference in database
            self.database.update_user_preference(user_id, preference_key, preference_value)
            logger.info(f"Updated preference '{preference_key}' for user {user_id}")
            return True
        except Exception as e:
            logger.error(f"Error updating preference '{preference_key}' for user {user_id}: {e}")
            return False

    def get_user_summary(self, user_id: int) -> Optional[str]:
        """
        Get user summary.

        Args:
            user_id: User ID

        Returns:
            str: User summary or None if not available
        """
        try:
            # Get user summary from database
            summary = self.database.get_user_summary(user_id)
            return summary
        except Exception as e:
            logger.error(f"Error getting user summary for user {user_id}: {e}")
            return None

    def update_user_summary(self, user_id: int, summary: str) -> bool:
        """
        Update user summary.

        Args:
            user_id: User ID
            summary: User summary

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Update user summary in database
            self.database.update_user_summary(user_id, summary)
            logger.info(f"Updated summary for user {user_id}")
            return True
        except Exception as e:
            logger.error(f"Error updating summary for user {user_id}: {e}")
            return False

    def add_credits(self, user_id: int, amount: int, source: str = "system") -> Optional[int]:
        """
        Add credits to a user's account.

        Args:
            user_id: User ID
            amount: Amount of credits to add
            source: Source of the credits (purchase, gift, system, etc.)

        Returns:
            int: New credit balance if successful, None otherwise
        """
        try:
            # Add credits to user account
            new_balance = self.database.add_credits(user_id, amount, source)
            logger.info(f"Added {amount} credits to user {user_id} (source: {source})")
            return new_balance
        except Exception as e:
            logger.error(f"Error adding credits to user {user_id}: {e}")
            return None

    def add_conversation_entry(self, user_id: int, message: str, response: str,
                              is_voice: bool = False, credits_used: int = 1) -> Optional[int]:
        """
        Add a conversation entry.

        Args:
            user_id: User ID
            message: User message
            response: Bot response
            is_voice: Whether the conversation was voice-based
            credits_used: Number of credits used

        Returns:
            int: Conversation ID if successful, None otherwise
        """
        try:
            # Add conversation entry to database
            conversation_id = self.database.add_conversation(
                user_id=user_id,
                message=message,
                response=response,
                is_voice=is_voice,
                credits_used=credits_used
            )
            logger.info(f"Added conversation entry {conversation_id} for user {user_id}")
            return conversation_id
        except Exception as e:
            logger.error(f"Error adding conversation entry for user {user_id}: {e}")
            return None
