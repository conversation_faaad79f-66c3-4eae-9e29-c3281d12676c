"""
Check for syntax errors in Python files.
"""

import os
import sys
import py_compile
import traceback

def check_file(file_path):
    """Check a single file for syntax errors."""
    try:
        py_compile.compile(file_path, doraise=True)
        print(f"✓ {file_path}")
        return True
    except py_compile.PyCompileError as e:
        print(f"✗ {file_path}")
        print(f"  Error: {e}")
        return False
    except Exception as e:
        print(f"✗ {file_path}")
        print(f"  Unexpected error: {e}")
        return False

def check_directory(directory):
    """Check all Python files in a directory and its subdirectories."""
    all_valid = True
    for root, _, files in os.walk(directory):
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                if not check_file(file_path):
                    all_valid = False
    return all_valid

if __name__ == "__main__":
    print("Checking for syntax errors in Python files...")
    
    # Check bot directory
    if os.path.exists('bot'):
        print("\nChecking bot directory:")
        bot_valid = check_directory('bot')
    else:
        print("Bot directory not found!")
        bot_valid = False
    
    # Check scripts directory
    if os.path.exists('scripts'):
        print("\nChecking scripts directory:")
        scripts_valid = check_directory('scripts')
    else:
        print("Scripts directory not found!")
        scripts_valid = False
    
    # Check root Python files
    print("\nChecking root Python files:")
    root_valid = True
    for file in os.listdir('.'):
        if file.endswith('.py'):
            if not check_file(file):
                root_valid = False
    
    # Summary
    print("\nSummary:")
    print(f"Bot directory: {'✓' if bot_valid else '✗'}")
    print(f"Scripts directory: {'✓' if scripts_valid else '✗'}")
    print(f"Root Python files: {'✓' if root_valid else '✗'}")
    
    if bot_valid and scripts_valid and root_valid:
        print("\nAll Python files are syntactically valid!")
        sys.exit(0)
    else:
        print("\nSome Python files have syntax errors!")
        sys.exit(1)
