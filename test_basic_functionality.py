#!/usr/bin/env python3
"""
Basic functionality test for VoicePal core components.
"""

import sys
import os
import tempfile
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_database_functionality():
    """Test basic database functionality."""
    print("Testing database functionality...")
    
    try:
        from bot.database.database_manager import DatabaseManager
        
        # Create temporary database
        fd, db_path = tempfile.mkstemp(suffix='.db')
        os.close(fd)
        
        try:
            # Initialize database
            db_manager = DatabaseManager(db_path)
            db_manager.initialize_database()
            
            # Test user creation
            user_id = 12345
            db_manager.create_user(
                user_id=user_id,
                username="test_user",
                first_name="Test",
                last_name="User"
            )
            
            # Test user retrieval
            user = db_manager.get_user(user_id)
            assert user is not None
            assert user['username'] == "test_user"
            
            # Test credit operations
            db_manager.update_user_credits(user_id, 100)
            user = db_manager.get_user(user_id)
            assert user['credits'] == 100
            
            # Test conversation creation
            conversation_id = db_manager.create_conversation(user_id)
            assert conversation_id is not None
            
            # Test message addition
            db_manager.add_message(
                conversation_id=conversation_id,
                role="user",
                content="Hello, world!",
                message_type="text"
            )
            
            # Test conversation retrieval
            conversations = db_manager.get_user_conversations(user_id)
            assert len(conversations) == 1
            
            print("✅ Database functionality test passed!")
            return True
            
        finally:
            try:
                os.unlink(db_path)
            except OSError:
                pass
                
    except Exception as e:
        print(f"❌ Database functionality test failed: {e}")
        return False

def test_analytics_imports():
    """Test analytics module imports."""
    print("Testing analytics imports...")
    
    try:
        from bot.analytics.conversation_analytics import ConversationAnalytics
        from bot.analytics.user_analytics import UserAnalytics
        from bot.analytics.business_analytics import BusinessAnalytics
        from bot.analytics.event_tracker import EventTracker
        from bot.analytics.metrics_collector import MetricsCollector
        
        print("✅ Analytics imports test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Analytics imports test failed: {e}")
        return False

def test_performance_imports():
    """Test performance module imports."""
    print("Testing performance imports...")
    
    try:
        from bot.performance.database_pool import DatabaseConnectionPool
        from bot.performance.cache_manager import CacheManager
        from bot.performance.async_processor import AsyncProcessor
        
        print("✅ Performance imports test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Performance imports test failed: {e}")
        return False

def test_security_imports():
    """Test security module imports."""
    print("Testing security imports...")
    
    try:
        from bot.security.encryption import EncryptionManager
        from bot.security.rate_limiter import RateLimiter
        from bot.security.audit_logger import AuditLogger
        
        print("✅ Security imports test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Security imports test failed: {e}")
        return False

def test_api_imports():
    """Test API module imports."""
    print("Testing API imports...")
    
    try:
        from bot.api.main import create_app
        from bot.api.endpoints.health import router as health_router
        from bot.api.endpoints.users import router as users_router
        
        print("✅ API imports test passed!")
        return True
        
    except Exception as e:
        print(f"❌ API imports test failed: {e}")
        return False

def test_cache_functionality():
    """Test cache functionality."""
    print("Testing cache functionality...")
    
    try:
        from bot.performance.cache_manager import CacheManager
        from unittest.mock import Mock
        
        # Create mock Redis client
        redis_client = Mock()
        redis_client.get.return_value = None
        redis_client.set.return_value = True
        redis_client.delete.return_value = True
        
        # Test cache manager
        cache = CacheManager(redis_client=redis_client, enable_local_cache=True)
        
        # Test set/get operations
        cache.set("test_key", "test_value", ttl=3600)
        value = cache.get("test_key")
        
        # With local cache enabled, should return the value
        assert value == "test_value"
        
        # Test cache stats
        stats = cache.get_cache_stats()
        assert "local_cache_size" in stats
        
        print("✅ Cache functionality test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Cache functionality test failed: {e}")
        return False

def test_encryption_functionality():
    """Test encryption functionality."""
    print("Testing encryption functionality...")
    
    try:
        from bot.security.encryption import EncryptionManager
        
        # Test encryption manager
        encryption = EncryptionManager()
        
        # Test data encryption/decryption
        original_data = "This is sensitive data"
        encrypted_data = encryption.encrypt_data(original_data)
        decrypted_data = encryption.decrypt_data(encrypted_data)
        
        assert decrypted_data == original_data
        assert encrypted_data != original_data
        
        # Test field encryption
        field_data = {"password": "secret123", "email": "<EMAIL>"}
        encrypted_fields = encryption.encrypt_sensitive_fields(field_data, ["password"])
        decrypted_fields = encryption.decrypt_sensitive_fields(encrypted_fields, ["password"])
        
        assert decrypted_fields["password"] == "secret123"
        assert decrypted_fields["email"] == "<EMAIL>"
        assert encrypted_fields["password"] != "secret123"
        
        print("✅ Encryption functionality test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Encryption functionality test failed: {e}")
        return False

def main():
    """Run all basic functionality tests."""
    print("🚀 Running VoicePal Basic Functionality Tests")
    print("=" * 60)
    
    tests = [
        test_database_functionality,
        test_analytics_imports,
        test_performance_imports,
        test_security_imports,
        test_api_imports,
        test_cache_functionality,
        test_encryption_functionality
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            failed += 1
        print()
    
    print("=" * 60)
    print(f"Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All basic functionality tests passed!")
        return True
    else:
        print("💥 Some tests failed!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
