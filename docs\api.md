# VoicePal API Documentation

This document provides comprehensive documentation for the VoicePal REST API.

## Base URL

```
https://your-domain.com/api/v1
```

## Authentication

The API uses JWT-based authentication for protected endpoints.

### Getting an API Token

```bash
POST /auth/token
Content-Type: application/json

{
  "username": "your_username",
  "password": "your_password"
}
```

### Using the Token

Include the token in the Authorization header:

```bash
Authorization: Bearer your_jwt_token
```

## Health & Status Endpoints

### Health Check

Check if the API is running.

```bash
GET /health
```

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00Z",
  "uptime": 3600,
  "version": "2.0.0"
}
```

### System Status

Get detailed system status information.

```bash
GET /status
```

**Response:**
```json
{
  "system": {
    "cpu_percent": 45.0,
    "memory_percent": 60.0,
    "disk_percent": 30.0
  },
  "database": {
    "status": "connected",
    "pool_size": 10,
    "active_connections": 3
  },
  "redis": {
    "status": "connected",
    "memory_usage": "50MB"
  },
  "features": {
    "analytics": true,
    "ab_testing": true,
    "notifications": true,
    "feature_flags": true
  }
}
```

## User Management

### List Users

Get a list of all users with pagination.

```bash
GET /api/v1/users?page=1&limit=50&sort=created_at&order=desc
```

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 50, max: 100)
- `sort` (optional): Sort field (default: created_at)
- `order` (optional): Sort order (asc/desc, default: desc)
- `search` (optional): Search term for username/name

**Response:**
```json
{
  "users": [
    {
      "user_id": 123,
      "username": "john_doe",
      "first_name": "John",
      "last_name": "Doe",
      "credits": 100,
      "created_at": "2024-01-01T00:00:00Z",
      "last_active": "2024-01-15T12:30:00Z",
      "total_conversations": 25,
      "total_messages": 150
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 50,
    "total": 1000,
    "pages": 20
  }
}
```

### Get User

Get detailed information about a specific user.

```bash
GET /api/v1/users/{user_id}
```

**Response:**
```json
{
  "user_id": 123,
  "username": "john_doe",
  "first_name": "John",
  "last_name": "Doe",
  "credits": 100,
  "created_at": "2024-01-01T00:00:00Z",
  "last_active": "2024-01-15T12:30:00Z",
  "preferences": {
    "ai_provider": "google_ai",
    "tts_provider": "deepgram",
    "voice_id": "aura-asteria-en"
  },
  "statistics": {
    "total_conversations": 25,
    "total_messages": 150,
    "avg_conversation_length": 6.0,
    "total_voice_minutes": 45.5
  }
}
```

### Update User

Update user information.

```bash
PUT /api/v1/users/{user_id}
Content-Type: application/json

{
  "credits": 150,
  "first_name": "John Updated",
  "preferences": {
    "ai_provider": "groq",
    "tts_provider": "elevenlabs"
  }
}
```

### Delete User

Delete a user and all associated data.

```bash
DELETE /api/v1/users/{user_id}
```

**Response:**
```json
{
  "message": "User deleted successfully",
  "deleted_data": {
    "conversations": 25,
    "messages": 150,
    "transactions": 5
  }
}
```

## Analytics

### Analytics Dashboard

Get comprehensive analytics dashboard data.

```bash
GET /api/v1/analytics/dashboard?days=30
```

**Query Parameters:**
- `days` (optional): Number of days to analyze (default: 30)

**Response:**
```json
{
  "timestamp": "2024-01-15T12:00:00Z",
  "period_days": 30,
  "user_metrics": {
    "total_users": 1000,
    "active_users_daily": 150,
    "active_users_weekly": 400,
    "active_users_monthly": 800,
    "new_users_daily": 25,
    "retention_rate_7d": 0.75,
    "churn_rate": 0.05
  },
  "conversation_metrics": {
    "total_conversations": 5000,
    "avg_conversation_length": 8.5,
    "avg_messages_per_conversation": 12.3,
    "user_engagement_score": 0.82
  },
  "business_metrics": {
    "total_revenue": 15000.0,
    "monthly_recurring_revenue": 5000.0,
    "conversion_rate": 0.15,
    "customer_lifetime_value": 125.0
  },
  "insights": [
    {
      "type": "engagement",
      "title": "High User Engagement",
      "description": "User engagement score is 82%, above target of 75%",
      "confidence": 0.95
    }
  ]
}
```

### User Analytics

Get detailed user analytics.

```bash
GET /api/v1/analytics/users?days=30
```

### Conversation Analytics

Get conversation pattern analytics.

```bash
GET /api/v1/analytics/conversations?days=30&user_id=123
```

### Revenue Analytics

Get business and revenue analytics.

```bash
GET /api/v1/analytics/revenue?days=30
```

## A/B Testing

### Create A/B Test

Create a new A/B test.

```bash
POST /api/v1/ab-tests
Content-Type: application/json

{
  "name": "New Feature Test",
  "description": "Testing new conversation feature",
  "test_type": "feature_flag",
  "variants": [
    {
      "variant_id": "control",
      "name": "Control",
      "description": "Current experience",
      "traffic_allocation": 0.5,
      "configuration": {},
      "is_control": true
    },
    {
      "variant_id": "treatment",
      "name": "Treatment",
      "description": "New feature enabled",
      "traffic_allocation": 0.5,
      "configuration": {
        "feature_enabled": true,
        "feature_config": {
          "enhanced_responses": true
        }
      },
      "is_control": false
    }
  ],
  "start_date": "2024-01-20T00:00:00Z",
  "end_date": "2024-02-20T00:00:00Z",
  "target_audience": {
    "min_conversations": 5,
    "registration_days_ago": 7
  },
  "success_metrics": ["conversion", "engagement", "retention"]
}
```

### List A/B Tests

Get list of A/B tests.

```bash
GET /api/v1/ab-tests?status=active
```

### Get A/B Test Results

Get results for a specific A/B test.

```bash
GET /api/v1/ab-tests/{test_id}/results
```

**Response:**
```json
{
  "test_id": "test_123",
  "status": "active",
  "results": [
    {
      "variant_id": "control",
      "participants": 500,
      "conversions": 75,
      "conversion_rate": 0.15,
      "statistical_significance": 0.95
    },
    {
      "variant_id": "treatment",
      "participants": 485,
      "conversions": 97,
      "conversion_rate": 0.20,
      "statistical_significance": 0.98
    }
  ],
  "winner": "treatment",
  "confidence": 0.98
}
```

## Feature Flags

### Create Feature Flag

Create a new feature flag.

```bash
POST /api/v1/feature-flags
Content-Type: application/json

{
  "name": "Enhanced Voice Processing",
  "description": "Enable enhanced voice processing features",
  "flag_type": "boolean",
  "default_value": true,
  "rollout_strategy": "percentage",
  "rollout_config": {
    "percentage": 25,
    "fallback_value": false
  }
}
```

### List Feature Flags

Get list of feature flags.

```bash
GET /api/v1/feature-flags?enabled_only=true
```

### Update Feature Flag

Update an existing feature flag.

```bash
PUT /api/v1/feature-flags/{flag_id}
Content-Type: application/json

{
  "enabled": true,
  "rollout_config": {
    "percentage": 50
  }
}
```

### Evaluate Feature Flag

Evaluate a feature flag for a specific user.

```bash
POST /api/v1/feature-flags/{flag_id}/evaluate
Content-Type: application/json

{
  "user_id": 123,
  "user_attributes": {
    "subscription_tier": "premium",
    "registration_date": "2024-01-01"
  }
}
```

## Notifications

### Send Notification

Send an immediate notification.

```bash
POST /api/v1/notifications/send
Content-Type: application/json

{
  "user_id": 123,
  "channel": "telegram",
  "notification_type": "reminder",
  "content": "Don't forget to continue your conversation!",
  "subject": "VoicePal Reminder"
}
```

### Schedule Notification

Schedule a notification for later delivery.

```bash
POST /api/v1/notifications/schedule
Content-Type: application/json

{
  "user_id": 123,
  "template_id": "welcome_template",
  "channel": "telegram",
  "scheduled_at": "2024-01-20T10:00:00Z",
  "variables": {
    "user_name": "John",
    "credits": 100
  }
}
```

### Create Notification Template

Create a reusable notification template.

```bash
POST /api/v1/notifications/templates
Content-Type: application/json

{
  "name": "Welcome Message",
  "notification_type": "welcome",
  "channel": "telegram",
  "subject": "Welcome to VoicePal!",
  "content": "Hi {user_name}! Welcome to VoicePal. You have {credits} credits to start chatting!",
  "variables": ["user_name", "credits"]
}
```

## WebSocket Events

Connect to real-time events via WebSocket.

```javascript
const ws = new WebSocket('wss://your-domain.com/ws');

// Subscribe to analytics events
ws.send(JSON.stringify({
  type: 'subscribe',
  channel: 'analytics'
}));

// Listen for events
ws.onmessage = function(event) {
  const data = JSON.parse(event.data);
  console.log('Event:', data.type, data.payload);
};
```

### Available Channels

- `analytics` - Real-time analytics updates
- `ab_tests` - A/B test status changes
- `notifications` - Notification delivery status
- `system` - System health and status updates

## Error Handling

The API uses standard HTTP status codes and returns detailed error information.

### Error Response Format

```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid request data",
    "details": {
      "field": "email",
      "issue": "Invalid email format"
    },
    "timestamp": "2024-01-15T12:00:00Z",
    "request_id": "req_123456"
  }
}
```

### Common Status Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Validation Error
- `429` - Rate Limited
- `500` - Internal Server Error

## Rate Limiting

The API implements rate limiting to ensure fair usage.

### Rate Limit Headers

```
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: **********
```

### Default Limits

- **General API**: 1000 requests per hour
- **Analytics**: 100 requests per hour
- **Bulk operations**: 50 requests per hour

## SDKs and Examples

### Python SDK

```python
from voicepal_sdk import VoicePalClient

client = VoicePalClient(
    api_key="your_api_key",
    base_url="https://your-domain.com/api/v1"
)

# Get user analytics
analytics = client.analytics.get_dashboard(days=30)
print(f"Total users: {analytics.user_metrics.total_users}")

# Create A/B test
test = client.ab_tests.create(
    name="New Feature Test",
    variants=[...],
    start_date="2024-01-20T00:00:00Z"
)
```

### JavaScript SDK

```javascript
import { VoicePalClient } from 'voicepal-sdk';

const client = new VoicePalClient({
  apiKey: 'your_api_key',
  baseUrl: 'https://your-domain.com/api/v1'
});

// Get user data
const user = await client.users.get(123);
console.log(`User: ${user.first_name}, Credits: ${user.credits}`);

// Send notification
await client.notifications.send({
  userId: 123,
  channel: 'telegram',
  content: 'Hello from VoicePal!'
});
```
