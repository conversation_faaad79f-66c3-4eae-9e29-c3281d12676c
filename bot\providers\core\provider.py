"""
Base provider interface for VoicePal.

This module provides the base provider interface that defines common methods
and properties for all providers.
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List, Union, TypeVar, Generic, Type

from bot.providers.core.exceptions import (
    ProviderError,
    ProviderConfigError,
    ProviderAuthError,
    ProviderRateLimitError,
    ProviderTimeoutError,
    ProviderNotFoundError,
    ProviderValidationError
)

# Set up logging
logger = logging.getLogger(__name__)

# Type variable for provider configuration
T = TypeVar('T')

class Provider(Generic[T], ABC):
    """Base provider interface.
    
    This abstract class defines the common interface for all providers.
    """
    
    # Provider type (to be overridden by subclasses)
    provider_type: str = "base"
    
    # Provider name (to be overridden by subclasses)
    provider_name: str = "base"
    
    # Provider version (to be overridden by subclasses)
    provider_version: str = "1.0.0"
    
    # Provider description (to be overridden by subclasses)
    provider_description: str = "Base provider"
    
    # Provider configuration class (to be overridden by subclasses)
    config_class: Type[T] = dict
    
    def __init__(self, config: T):
        """Initialize provider.
        
        Args:
            config: Provider configuration
        """
        self.config = config
        self.initialized = False
        self.validate_config()
    
    @abstractmethod
    def validate_config(self) -> None:
        """Validate provider configuration.
        
        Raises:
            ProviderConfigError: If configuration is invalid
        """
        pass
    
    @abstractmethod
    def initialize(self) -> None:
        """Initialize provider.
        
        This method should be called after creating the provider instance
        to initialize any resources needed by the provider.
        
        Raises:
            ProviderError: If initialization fails
        """
        pass
    
    @abstractmethod
    def shutdown(self) -> None:
        """Shutdown provider.
        
        This method should be called when the provider is no longer needed
        to release any resources used by the provider.
        
        Raises:
            ProviderError: If shutdown fails
        """
        pass
    
    def get_info(self) -> Dict[str, Any]:
        """Get provider information.
        
        Returns:
            Provider information
        """
        return {
            "type": self.provider_type,
            "name": self.provider_name,
            "version": self.provider_version,
            "description": self.provider_description,
            "initialized": self.initialized
        }
    
    def __str__(self) -> str:
        """String representation of provider.
        
        Returns:
            String representation
        """
        return f"{self.provider_type}:{self.provider_name} (v{self.provider_version})"
    
    def __repr__(self) -> str:
        """Detailed string representation of provider.
        
        Returns:
            Detailed string representation
        """
        return f"{self.__class__.__name__}(type={self.provider_type}, name={self.provider_name}, version={self.provider_version}, initialized={self.initialized})"

class AIProvider(Provider[T], ABC):
    """Base AI provider interface.
    
    This abstract class defines the common interface for all AI providers.
    """
    
    provider_type = "ai"
    
    @abstractmethod
    async def generate_text(self, prompt: str, **kwargs) -> str:
        """Generate text from prompt.
        
        Args:
            prompt: Text prompt
            **kwargs: Additional arguments
            
        Returns:
            Generated text
            
        Raises:
            ProviderError: If text generation fails
        """
        pass
    
    @abstractmethod
    async def generate_chat_response(self, messages: List[Dict[str, str]], **kwargs) -> Dict[str, Any]:
        """Generate chat response from messages.
        
        Args:
            messages: List of messages in the format {"role": "user|assistant|system", "content": "message"}
            **kwargs: Additional arguments
            
        Returns:
            Generated response
            
        Raises:
            ProviderError: If chat response generation fails
        """
        pass
    
    @abstractmethod
    async def embed_text(self, text: str, **kwargs) -> List[float]:
        """Generate embeddings for text.
        
        Args:
            text: Text to embed
            **kwargs: Additional arguments
            
        Returns:
            Text embeddings
            
        Raises:
            ProviderError: If embedding generation fails
        """
        pass

class TTSProvider(Provider[T], ABC):
    """Base TTS (Text-to-Speech) provider interface.
    
    This abstract class defines the common interface for all TTS providers.
    """
    
    provider_type = "tts"
    
    @abstractmethod
    async def text_to_speech(self, text: str, voice_id: str, **kwargs) -> bytes:
        """Convert text to speech.
        
        Args:
            text: Text to convert to speech
            voice_id: Voice ID
            **kwargs: Additional arguments
            
        Returns:
            Audio data
            
        Raises:
            ProviderError: If text-to-speech conversion fails
        """
        pass
    
    @abstractmethod
    def get_available_voices(self) -> List[Dict[str, Any]]:
        """Get available voices.
        
        Returns:
            List of available voices
            
        Raises:
            ProviderError: If getting available voices fails
        """
        pass

class STTProvider(Provider[T], ABC):
    """Base STT (Speech-to-Text) provider interface.
    
    This abstract class defines the common interface for all STT providers.
    """
    
    provider_type = "stt"
    
    @abstractmethod
    async def speech_to_text(self, audio_data: bytes, **kwargs) -> Dict[str, Any]:
        """Convert speech to text.
        
        Args:
            audio_data: Audio data
            **kwargs: Additional arguments
            
        Returns:
            Transcription result
            
        Raises:
            ProviderError: If speech-to-text conversion fails
        """
        pass
    
    @abstractmethod
    async def analyze_sentiment(self, text: str, **kwargs) -> Dict[str, Any]:
        """Analyze sentiment of text.
        
        Args:
            text: Text to analyze
            **kwargs: Additional arguments
            
        Returns:
            Sentiment analysis result
            
        Raises:
            ProviderError: If sentiment analysis fails
        """
        pass

class PaymentProvider(Provider[T], ABC):
    """Base payment provider interface.
    
    This abstract class defines the common interface for all payment providers.
    """
    
    provider_type = "payment"
    
    @abstractmethod
    async def create_payment(self, amount: int, currency: str, description: str, **kwargs) -> Dict[str, Any]:
        """Create a payment.
        
        Args:
            amount: Payment amount
            currency: Payment currency
            description: Payment description
            **kwargs: Additional arguments
            
        Returns:
            Payment information
            
        Raises:
            ProviderError: If payment creation fails
        """
        pass
    
    @abstractmethod
    async def get_payment_status(self, payment_id: str) -> Dict[str, Any]:
        """Get payment status.
        
        Args:
            payment_id: Payment ID
            
        Returns:
            Payment status
            
        Raises:
            ProviderError: If getting payment status fails
        """
        pass
    
    @abstractmethod
    async def create_subscription(self, customer_id: str, price_id: str, **kwargs) -> Dict[str, Any]:
        """Create a subscription.
        
        Args:
            customer_id: Customer ID
            price_id: Price ID
            **kwargs: Additional arguments
            
        Returns:
            Subscription information
            
        Raises:
            ProviderError: If subscription creation fails
        """
        pass
    
    @abstractmethod
    async def cancel_subscription(self, subscription_id: str) -> Dict[str, Any]:
        """Cancel a subscription.
        
        Args:
            subscription_id: Subscription ID
            
        Returns:
            Cancellation result
            
        Raises:
            ProviderError: If subscription cancellation fails
        """
        pass
