"""
Hierarchical memory manager for VoicePal.

This module provides a hierarchical memory system with short-term, medium-term, and long-term memory.
"""

import logging
import json
from typing import Dict, List, Any, Optional, Union, Tuple
from datetime import datetime, timedelta

from bot.providers.memory.redis_provider import <PERSON>is<PERSON>rovider
from bot.providers.memory.qdrant_provider import <PERSON>dra<PERSON><PERSON><PERSON>ider
from bot.utils.embedding_utils import Embedding<PERSON>rovider

# Set up logging
logger = logging.getLogger(__name__)

class HierarchicalMemoryManager:
    """Hierarchical memory manager for VoicePal."""

    def __init__(
        self, 
        database, 
        ai_provider, 
        config: Dict[str, Any] = None,
        user_manager = None
    ):
        """
        Initialize the hierarchical memory manager.

        Args:
            database: Database instance
            ai_provider: AI provider instance
            config: Configuration dictionary
            user_manager: Optional UserManager instance for user profile access
        """
        self.database = database
        self.ai_provider = ai_provider
        self.config = config or {}
        self.user_manager = user_manager
        
        # Initialize providers
        self.redis_provider = RedisProvider(self.config.get("redis", {}))
        self.qdrant_provider = QdrantProvider(self.config.get("qdrant", {}))
        self.embedding_provider = EmbeddingProvider(self.config.get("embedding", {}))
        
        # Memory configuration
        self.short_term_limit = self.config.get("short_term_limit", 10)
        self.medium_term_limit = self.config.get("medium_term_limit", 50)
        self.long_term_importance_threshold = self.config.get("long_term_threshold", 0.7)
        
        # Cache configuration
        self.conversation_cache = {}
        self.cache_ttl_minutes = self.config.get("cache_ttl_minutes", 30)
        self.name_cache_ttl_minutes = self.config.get("name_cache_ttl_minutes", 60)
        
        # Initialize memory system
        self._initialize_memory_system()
    
    def _initialize_memory_system(self):
        """Initialize memory system."""
        # Extend database with memory-related methods if needed
        if hasattr(self.database, 'extend_database_for_memory'):
            self.database.extend_database_for_memory()
        
        logger.info("Hierarchical memory system initialized")
    
    def get_conversation_context(self, user_id: int) -> Dict[str, Any]:
        """
        Get enhanced context for a conversation with hierarchical memory.

        Args:
            user_id: User ID

        Returns:
            Dict containing user data, conversations, preferences, summary, and more
        """
        try:
            # Check if Redis is available and has cached context
            if self.redis_provider.is_available():
                cache_key = f"context:{user_id}"
                cached_context = self.redis_provider.get(cache_key)
                
                if cached_context:
                    logger.debug(f"Using Redis cached context for user {user_id}")
                    return cached_context
            
            # Check if we have a local cache that's still valid
            if user_id in self.conversation_cache:
                cache_entry = self.conversation_cache[user_id]
                cache_time = cache_entry.get("timestamp", datetime.min)
                cache_age = (datetime.now() - cache_time).total_seconds() / 60
                
                # Use cache if it's still valid
                if cache_age <= self.cache_ttl_minutes:
                    logger.debug(f"Using local cached context for user {user_id} (age: {cache_age:.1f} minutes)")
                    return cache_entry.get("context", {})
            
            # Get user data
            user_data = self.database.get_user(user_id) or {}
            
            # Get user preferences
            preferences = self.database.get_user_preferences(user_id) or {}
            
            # Get user summary
            summary = self.database.get_user_summary(user_id)
            
            # Get user interests
            interests = self._get_user_interests(user_id)
            
            # Get mood history
            mood_history = self._get_cached_mood_history(user_id)
            
            # Get hierarchical memory
            memory_context = self._get_hierarchical_memory_context(user_id)
            
            # Build the context
            context = {
                "user_data": user_data,
                "preferences": preferences,
                "summary": summary,
                "interests": interests,
                "mood_history": mood_history,
                **memory_context
            }
            
            # Cache the context
            self._cache_conversation_context(user_id, context)
            
            return context
        except Exception as e:
            logger.error(f"Error getting conversation context for user {user_id}: {e}")
            
            # Try to return a partial context if possible
            try:
                # Get basic user data as a minimum
                user_data = self.database.get_user(user_id) or {}
                
                # Try to get cached conversations if available
                cached_context = self.conversation_cache.get(user_id, {}).get("context", {})
                
                # Return a minimal context
                return {
                    "user_data": user_data,
                    "short_term_memory": cached_context.get("short_term_memory", []),
                    "medium_term_memory": cached_context.get("medium_term_memory", []),
                    "long_term_memory": cached_context.get("long_term_memory", []),
                    "preferences": {},
                    "summary": None,
                    "interests": [],
                    "mood_history": []
                }
            except Exception:
                # If all else fails, return an empty context
                return {
                    "user_data": {},
                    "short_term_memory": [],
                    "medium_term_memory": [],
                    "long_term_memory": [],
                    "preferences": {},
                    "summary": None,
                    "interests": [],
                    "mood_history": []
                }
    
    def _cache_conversation_context(self, user_id: int, context: Dict[str, Any]):
        """
        Cache conversation context.
        
        Args:
            user_id: User ID
            context: Conversation context
        """
        # Cache locally
        self.conversation_cache[user_id] = {
            "context": context,
            "timestamp": datetime.now()
        }
        
        # Cache in Redis if available
        if self.redis_provider.is_available():
            cache_key = f"context:{user_id}"
            self.redis_provider.set(cache_key, context, self.cache_ttl_minutes * 60)
    
    def clear_conversation_cache(self, user_id: int):
        """
        Clear conversation cache for a user.
        
        Args:
            user_id: User ID
        """
        # Clear local cache
        if user_id in self.conversation_cache:
            del self.conversation_cache[user_id]
        
        # Clear Redis cache if available
        if self.redis_provider.is_available():
            cache_key = f"context:{user_id}"
            self.redis_provider.delete(cache_key)
    
    def _get_hierarchical_memory_context(self, user_id: int) -> Dict[str, Any]:
        """
        Get hierarchical memory context.
        
        Args:
            user_id: User ID
            
        Returns:
            Dict containing short-term, medium-term, and long-term memory
        """
        # Get short-term memory (recent conversations)
        short_term_memory = self._get_short_term_memory(user_id)
        
        # Get medium-term memory (important recent conversations)
        medium_term_memory = self._get_medium_term_memory(user_id)
        
        # Get long-term memory (very important or semantically relevant)
        long_term_memory = self._get_long_term_memory(user_id)
        
        return {
            "short_term_memory": short_term_memory,
            "medium_term_memory": medium_term_memory,
            "long_term_memory": long_term_memory,
            "total_short_term": len(short_term_memory),
            "total_medium_term": len(medium_term_memory),
            "total_long_term": len(long_term_memory)
        }
    
    def _get_short_term_memory(self, user_id: int) -> List[Dict[str, Any]]:
        """
        Get short-term memory (recent conversations).
        
        Args:
            user_id: User ID
            
        Returns:
            List of recent conversations
        """
        try:
            # Get most recent conversations
            conversations = self.database.get_conversations(
                user_id=user_id,
                limit=self.short_term_limit
            )
            
            # Format conversations
            return self._format_conversations(conversations)
        except Exception as e:
            logger.error(f"Error getting short-term memory for user {user_id}: {e}")
            return []
    
    def _get_medium_term_memory(self, user_id: int) -> List[Dict[str, Any]]:
        """
        Get medium-term memory (important recent conversations).
        
        Args:
            user_id: User ID
            
        Returns:
            List of important recent conversations
        """
        try:
            # Get important conversations from the medium-term window
            important_convs = self.database.get_important_conversations(
                user_id=user_id,
                limit=self.medium_term_limit,
                threshold=0.5  # Medium importance threshold
            )
            
            # Format conversations
            return self._format_conversations(important_convs)
        except Exception as e:
            logger.error(f"Error getting medium-term memory for user {user_id}: {e}")
            return []
    
    def _get_long_term_memory(self, user_id: int, query: str = None) -> List[Dict[str, Any]]:
        """
        Get long-term memory (very important or semantically relevant).
        
        Args:
            user_id: User ID
            query: Optional query for semantic search
            
        Returns:
            List of long-term memories
        """
        try:
            memories = []
            
            # Get very important conversations from database
            important_memories = self.database.get_important_conversations(
                user_id=user_id,
                threshold=self.long_term_importance_threshold,
                limit=10
            )
            
            # Format and add to memories
            memories.extend(self._format_conversations(important_memories))
            
            # If query provided and vector search available, get relevant memories
            if query and self.qdrant_provider.is_available() and self.embedding_provider.is_available():
                # Generate query embedding
                query_embedding = self.embedding_provider.generate_embedding(query)
                
                # Search for similar memories
                similar_memories = self.qdrant_provider.search_memories(
                    user_id=user_id,
                    vector=query_embedding,
                    limit=5,
                    score_threshold=0.7
                )
                
                # Add to memories (avoiding duplicates)
                existing_ids = {memory.get("id") for memory in memories if "id" in memory}
                for memory in similar_memories:
                    if memory.get("id") not in existing_ids:
                        memories.append(memory)
                        existing_ids.add(memory.get("id"))
            
            return memories
        except Exception as e:
            logger.error(f"Error getting long-term memory for user {user_id}: {e}")
            return []
    
    def _format_conversations(self, conversations: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Format conversations for context.
        
        Args:
            conversations: List of conversations
            
        Returns:
            Formatted conversations
        """
        formatted = []
        
        for conv in conversations:
            if not conv:
                continue
            
            formatted_conv = {
                "id": conv.get("id"),
                "message": conv.get("message", ""),
                "response": conv.get("response", ""),
                "is_voice": conv.get("is_voice", False),
                "created_at": conv.get("created_at")
            }
            
            formatted.append(formatted_conv)
        
        return formatted
    
    def _get_user_interests(self, user_id: int) -> List[str]:
        """
        Get user interests.
        
        Args:
            user_id: User ID
            
        Returns:
            List of user interests
        """
        try:
            if hasattr(self.database, 'get_user_interests'):
                return self.database.get_user_interests(user_id)
            return []
        except Exception as e:
            logger.error(f"Error getting user interests for user {user_id}: {e}")
            return []
    
    def _get_cached_mood_history(self, user_id: int) -> List[Dict[str, Any]]:
        """
        Get mood history with caching.
        
        Args:
            user_id: User ID
            
        Returns:
            List of mood entries
        """
        try:
            if hasattr(self.database, 'get_mood_history'):
                return self.database.get_mood_history(user_id)
            return []
        except Exception as e:
            logger.error(f"Error getting mood history for user {user_id}: {e}")
            
            # Try to get from cache if available
            cached_context = self.conversation_cache.get(user_id, {}).get("context", {})
            return cached_context.get("mood_history", [])
    
    def get_user_name_from_context(self, user_id: int) -> Optional[str]:
        """
        Get user's name from context with caching.
        
        Args:
            user_id: User ID
            
        Returns:
            User's name or None
        """
        try:
            # Check Redis cache first
            if self.redis_provider.is_available():
                cache_key = f"user_name:{user_id}"
                cached_name = self.redis_provider.get(cache_key)
                
                if cached_name:
                    return cached_name
            
            # Get user data
            user_data = self.database.get_user(user_id) or {}
            
            # Try to get preferred name first
            name = user_data.get("preferred_name")
            
            # If no preferred name, try first name
            if not name:
                name = user_data.get("first_name")
            
            # Cache the name
            if name and self.redis_provider.is_available():
                cache_key = f"user_name:{user_id}"
                self.redis_provider.set(cache_key, name, self.name_cache_ttl_minutes * 60)
            
            return name
        except Exception as e:
            logger.error(f"Error getting user name for user {user_id}: {e}")
            return None
    
    async def store_conversation(
        self, 
        user_id: int, 
        message: str, 
        response: str, 
        is_voice: bool = False,
        importance_score: float = None
    ) -> Optional[int]:
        """
        Store a conversation with vector embedding.
        
        Args:
            user_id: User ID
            message: User message
            response: Bot response
            is_voice: Whether the message was voice-based
            importance_score: Optional importance score
            
        Returns:
            Conversation ID if successful, None otherwise
        """
        try:
            # Store in database
            conversation_id = self.database.add_conversation(
                user_id=user_id,
                message=message,
                response=response,
                is_voice=is_voice
            )
            
            if not conversation_id:
                logger.error(f"Failed to store conversation for user {user_id}")
                return None
            
            # Calculate importance score if not provided
            if importance_score is None:
                importance_score = self._calculate_importance_score(message, response)
            
            # Store importance score
            if hasattr(self.database, 'set_conversation_importance'):
                self.database.set_conversation_importance(
                    conversation_id=conversation_id,
                    importance_score=importance_score
                )
            
            # Store in vector database if available
            if self.qdrant_provider.is_available() and self.embedding_provider.is_available():
                # Generate embedding for combined message and response
                combined_text = f"User: {message}\nAssistant: {response}"
                embedding = self.embedding_provider.generate_embedding(combined_text)
                
                # Store in Qdrant
                self.qdrant_provider.store_memory(
                    user_id=user_id,
                    vector=embedding,
                    text=combined_text,
                    metadata={
                        "conversation_id": conversation_id,
                        "message": message,
                        "response": response,
                        "is_voice": is_voice,
                        "importance_score": importance_score,
                        "created_at": datetime.now().isoformat()
                    },
                    memory_id=str(conversation_id)
                )
            
            # Clear conversation cache to ensure fresh data next time
            self.clear_conversation_cache(user_id)
            
            return conversation_id
        except Exception as e:
            logger.error(f"Error storing conversation for user {user_id}: {e}")
            return None
    
    def _calculate_importance_score(self, message: str, response: str) -> float:
        """
        Calculate importance score for a conversation.
        
        Args:
            message: User message
            response: Bot response
            
        Returns:
            Importance score (0-1)
        """
        try:
            # Simple heuristic based on length and content
            combined_text = f"{message} {response}".lower()
            
            # Base score based on length
            length_score = min(len(combined_text) / 500, 0.5)
            
            # Check for important keywords
            important_keywords = [
                "remember", "important", "don't forget", "name is", "my name", 
                "i am", "i'm", "i like", "i love", "i hate", "i prefer",
                "address", "phone", "email", "contact", "birthday", "age"
            ]
            
            keyword_score = 0
            for keyword in important_keywords:
                if keyword in combined_text:
                    keyword_score += 0.1
            
            keyword_score = min(keyword_score, 0.5)
            
            # Combine scores
            importance_score = length_score + keyword_score
            
            # Ensure score is between 0 and 1
            return min(max(importance_score, 0), 1)
        except Exception as e:
            logger.error(f"Error calculating importance score: {e}")
            return 0.5  # Default medium importance
    
    async def search_relevant_memories(
        self, 
        user_id: int, 
        query: str, 
        limit: int = 5
    ) -> List[Dict[str, Any]]:
        """
        Search for relevant memories using semantic search.
        
        Args:
            user_id: User ID
            query: Search query
            limit: Maximum number of results
            
        Returns:
            List of relevant memories
        """
        if not self.qdrant_provider.is_available() or not self.embedding_provider.is_available():
            logger.warning("Vector search not available, falling back to database search")
            return self._fallback_memory_search(user_id, query, limit)
        
        try:
            # Generate query embedding
            query_embedding = self.embedding_provider.generate_embedding(query)
            
            # Search for similar memories
            memories = self.qdrant_provider.search_memories(
                user_id=user_id,
                vector=query_embedding,
                limit=limit,
                score_threshold=0.6
            )
            
            return memories
        except Exception as e:
            logger.error(f"Error searching relevant memories for user {user_id}: {e}")
            return self._fallback_memory_search(user_id, query, limit)
    
    def _fallback_memory_search(
        self, 
        user_id: int, 
        query: str, 
        limit: int = 5
    ) -> List[Dict[str, Any]]:
        """
        Fallback memory search using database.
        
        Args:
            user_id: User ID
            query: Search query
            limit: Maximum number of results
            
        Returns:
            List of relevant memories
        """
        try:
            # Get recent conversations
            conversations = self.database.get_conversations(
                user_id=user_id,
                limit=20  # Get more than needed for filtering
            )
            
            # Simple keyword matching
            query_words = set(query.lower().split())
            scored_conversations = []
            
            for conv in conversations:
                if not conv:
                    continue
                
                message = conv.get("message", "").lower()
                response = conv.get("response", "").lower()
                combined = f"{message} {response}"
                
                # Count matching words
                matching_words = sum(1 for word in query_words if word in combined)
                score = matching_words / max(len(query_words), 1)
                
                if score > 0:
                    scored_conversations.append((conv, score))
            
            # Sort by score and take top results
            scored_conversations.sort(key=lambda x: x[1], reverse=True)
            top_conversations = [conv for conv, _ in scored_conversations[:limit]]
            
            # Format conversations
            return self._format_conversations(top_conversations)
        except Exception as e:
            logger.error(f"Error in fallback memory search for user {user_id}: {e}")
            return []
    
    def should_preserve_context_for_short_message(self, message: str) -> bool:
        """
        Check if context should be preserved for a short message.
        
        Args:
            message: User message
            
        Returns:
            True if context should be preserved, False otherwise
        """
        # Preserve context for short messages or messages with follow-up indicators
        if len(message) < 20:
            return True
        
        follow_up_indicators = [
            "?", "why", "how", "what", "when", "where", "who", "which",
            "can you", "could you", "would you", "tell me", "explain"
        ]
        
        message_lower = message.lower()
        for indicator in follow_up_indicators:
            if indicator in message_lower:
                return True
        
        return False
