"""
Rate limiting middleware for VoicePal API.

This module provides rate limiting middleware for the VoicePal API.
"""

import time
import logging
from typing import Dict, Optional
from collections import defaultdict, deque
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.requests import Request
from starlette.responses import Response, JSONResponse
from starlette.status import HTTP_429_TOO_MANY_REQUESTS

from api.config import APIConfig

logger = logging.getLogger(__name__)

class RateLimitMiddleware(BaseHTTPMiddleware):
    """Rate limiting middleware for API endpoints."""
    
    def __init__(self, app, config: APIConfig):
        """
        Initialize rate limiting middleware.
        
        Args:
            app: ASGI application
            config: API configuration
        """
        super().__init__(app)
        self.config = config
        
        # In-memory storage for rate limiting
        # In production, this should use Redis or similar
        self.request_counts: Dict[str, deque] = defaultdict(deque)
        
        # Endpoints exempt from rate limiting
        self.exempt_endpoints = {
            "/health",
            "/health/",
            "/health/status",
            "/health/ready"
        }
    
    async def dispatch(self, request: Request, call_next) -> Response:
        """
        Process the request and apply rate limiting.
        
        Args:
            request: HTTP request
            call_next: Next middleware/handler
            
        Returns:
            HTTP response
        """
        # Skip rate limiting if disabled
        if not self.config.rate_limit_enabled:
            return await call_next(request)
        
        # Skip rate limiting for exempt endpoints
        if self._is_exempt_endpoint(request.url.path):
            return await call_next(request)
        
        # Get client identifier
        client_id = self._get_client_id(request)
        
        # Check rate limit
        if not self._check_rate_limit(client_id):
            # Get retry after time
            retry_after = self._get_retry_after(client_id)
            
            return JSONResponse(
                status_code=HTTP_429_TOO_MANY_REQUESTS,
                content={
                    "error": "Rate limit exceeded",
                    "message": f"Too many requests. Try again in {retry_after} seconds.",
                    "retry_after": retry_after
                },
                headers={"Retry-After": str(retry_after)}
            )
        
        # Record the request
        self._record_request(client_id)
        
        # Add rate limit headers to response
        response = await call_next(request)
        self._add_rate_limit_headers(response, client_id)
        
        return response
    
    def _is_exempt_endpoint(self, path: str) -> bool:
        """
        Check if an endpoint is exempt from rate limiting.
        
        Args:
            path: Request path
            
        Returns:
            True if endpoint is exempt, False otherwise
        """
        return path in self.exempt_endpoints or path.startswith("/health")
    
    def _get_client_id(self, request: Request) -> str:
        """
        Get client identifier for rate limiting.
        
        Args:
            request: HTTP request
            
        Returns:
            Client identifier
        """
        # Use API key if available
        api_key = self._extract_api_key(request)
        if api_key:
            return f"api_key:{api_key[:8]}..."  # Use first 8 chars for privacy
        
        # Use IP address as fallback
        client_ip = self._get_client_ip(request)
        return f"ip:{client_ip}"
    
    def _extract_api_key(self, request: Request) -> Optional[str]:
        """
        Extract API key from request.
        
        Args:
            request: HTTP request
            
        Returns:
            API key if found, None otherwise
        """
        # Check Authorization header
        auth_header = request.headers.get("Authorization")
        if auth_header and auth_header.startswith("Bearer "):
            return auth_header[7:]
        
        # Check X-API-Key header
        return request.headers.get("X-API-Key")
    
    def _get_client_ip(self, request: Request) -> str:
        """
        Get client IP address.
        
        Args:
            request: HTTP request
            
        Returns:
            Client IP address
        """
        # Check for forwarded headers (for reverse proxies)
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # Fallback to direct client IP
        return request.client.host if request.client else "unknown"
    
    def _check_rate_limit(self, client_id: str) -> bool:
        """
        Check if client has exceeded rate limit.
        
        Args:
            client_id: Client identifier
            
        Returns:
            True if within rate limit, False otherwise
        """
        now = time.time()
        window_start = now - self.config.rate_limit_window
        
        # Get request times for this client
        request_times = self.request_counts[client_id]
        
        # Remove old requests outside the window
        while request_times and request_times[0] < window_start:
            request_times.popleft()
        
        # Check if under the limit
        return len(request_times) < self.config.rate_limit_requests
    
    def _record_request(self, client_id: str) -> None:
        """
        Record a request for rate limiting.
        
        Args:
            client_id: Client identifier
        """
        now = time.time()
        self.request_counts[client_id].append(now)
    
    def _get_retry_after(self, client_id: str) -> int:
        """
        Get retry after time in seconds.
        
        Args:
            client_id: Client identifier
            
        Returns:
            Retry after time in seconds
        """
        request_times = self.request_counts[client_id]
        if not request_times:
            return self.config.rate_limit_window
        
        # Time until the oldest request in the window expires
        oldest_request = request_times[0]
        window_end = oldest_request + self.config.rate_limit_window
        retry_after = max(1, int(window_end - time.time()))
        
        return retry_after
    
    def _add_rate_limit_headers(self, response: Response, client_id: str) -> None:
        """
        Add rate limit headers to response.
        
        Args:
            response: HTTP response
            client_id: Client identifier
        """
        request_times = self.request_counts[client_id]
        remaining = max(0, self.config.rate_limit_requests - len(request_times))
        
        response.headers["X-RateLimit-Limit"] = str(self.config.rate_limit_requests)
        response.headers["X-RateLimit-Remaining"] = str(remaining)
        response.headers["X-RateLimit-Window"] = str(self.config.rate_limit_window)
