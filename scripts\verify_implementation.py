"""
Verify implementation script for VoicePal.

This script verifies that the implementation is working correctly.
"""

import os
import logging
import asyncio
import importlib
from typing import List, Dict, Any, Set

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

def check_module_imports(module_name: str) -> Dict[str, Any]:
    """
    Check if a module can be imported and return its attributes.

    Args:
        module_name: Name of the module to import

    Returns:
        Dict containing import status and module attributes
    """
    try:
        module = importlib.import_module(module_name)
        return {
            "status": "success",
            "module": module,
            "attributes": dir(module)
        }
    except ImportError as e:
        return {
            "status": "error",
            "error": str(e)
        }

def check_class_attributes(module: Any, class_name: str) -> Dict[str, Any]:
    """
    Check if a class has the required attributes.

    Args:
        module: Module containing the class
        class_name: Name of the class to check

    Returns:
        Dict containing class attributes
    """
    try:
        cls = getattr(module, class_name)
        return {
            "status": "success",
            "attributes": dir(cls),
            "methods": [attr for attr in dir(cls) if callable(getattr(cls, attr)) and not attr.startswith("__")]
        }
    except AttributeError as e:
        return {
            "status": "error",
            "error": str(e)
        }

async def verify_implementation():
    """Verify that the implementation is working correctly."""
    # Check core modules
    core_modules = [
        "bot.config_manager",
        "bot.database",
        "bot.voice_processing",
        "bot.ai_conversation",
        "bot.elevenlabs_tts_provider",
        "bot.providers.provider_factory",
        "bot.core.enhanced_dialog_engine",
        "bot.core.feature_registry",
        "bot.core.user_manager",
        "bot.features.enhanced_memory_manager",
        "bot.features.mood_tracker",
        "bot.features.personalization_manager"
    ]

    logger.info("Checking core modules...")
    for module_name in core_modules:
        result = check_module_imports(module_name)
        if result["status"] == "success":
            logger.info(f"✅ {module_name}: Successfully imported")
        else:
            logger.error(f"❌ {module_name}: Import failed - {result['error']}")

    # Check provider interfaces
    provider_interfaces = [
        "bot.providers.stt_provider_interface.STTProviderInterface",
        "bot.providers.ai_provider_interface.AIProviderInterface",
        "bot.providers.tts_provider_interface.TTSProviderInterface"
    ]

    logger.info("\nChecking provider interfaces...")
    for interface in provider_interfaces:
        module_name, class_name = interface.rsplit(".", 1)
        module_result = check_module_imports(module_name)

        if module_result["status"] == "success":
            class_result = check_class_attributes(module_result["module"], class_name)

            if class_result["status"] == "success":
                logger.info(f"✅ {interface}: Successfully imported")
                logger.info(f"   Methods: {', '.join(class_result['methods'])}")
            else:
                logger.error(f"❌ {interface}: Class check failed - {class_result['error']}")
        else:
            logger.error(f"❌ {interface}: Module import failed - {module_result['error']}")

    # Check provider implementations
    provider_implementations = [
        "bot.providers.stt.deepgram_provider.DeepgramProvider",
        "bot.ai_conversation.GoogleAIProvider",
        "bot.elevenlabs_tts_provider.ElevenLabsProvider"
    ]

    logger.info("\nChecking provider implementations...")
    for implementation in provider_implementations:
        module_name, class_name = implementation.rsplit(".", 1)
        module_result = check_module_imports(module_name)

        if module_result["status"] == "success":
            class_result = check_class_attributes(module_result["module"], class_name)

            if class_result["status"] == "success":
                logger.info(f"✅ {implementation}: Successfully imported")
                logger.info(f"   Methods: {', '.join(class_result['methods'])}")
            else:
                logger.error(f"❌ {implementation}: Class check failed - {class_result['error']}")
        else:
            logger.error(f"❌ {implementation}: Module import failed - {module_result['error']}")

    # Check main module
    logger.info("\nChecking main module...")
    main_result = check_module_imports("bot.main")

    if main_result["status"] == "success":
        logger.info("✅ bot.main: Successfully imported")

        # Check VoicePalBot class
        bot_result = check_class_attributes(main_result["module"], "VoicePalBot")

        if bot_result["status"] == "success":
            logger.info("✅ bot.main.VoicePalBot: Successfully imported")
            logger.info(f"   Methods: {', '.join(bot_result['methods'])}")
        else:
            logger.error(f"❌ bot.main.VoicePalBot: Class check failed - {bot_result['error']}")
    else:
        logger.error(f"❌ bot.main: Import failed - {main_result['error']}")

    logger.info("\nVerification completed")

async def main():
    """Main function."""
    try:
        await verify_implementation()
    except Exception as e:
        logger.error(f"Error during verification: {e}")

if __name__ == "__main__":
    asyncio.run(main())
