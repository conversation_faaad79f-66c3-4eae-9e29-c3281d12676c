"""
Test script to verify all fixes made to the VoicePal bot.

This script tests:
1. Deepgram transcription functionality
2. Menu navigation
3. Audio processing
4. Response quality
"""

import os
import sys
import asyncio
import logging
import unittest
from unittest.mock import MagicMock, patch
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# Import bot modules
from bot.providers.voice.processor import VoiceProcessor
from bot.providers.tts.deepgram_provider import DeepgramTTSProvider
from bot.features.mood_entry import MoodEntry
from bot.core.navigation_router import NavigationRouter
from bot.providers.ai.google_ai_provider import GoogleAIProvider
from bot.core.enhanced_dialog_engine import EnhancedDialogEngine
from bot.features.enhanced_memory_manager import EnhancedMemoryManager

class TestFixes(unittest.TestCase):
    """Test class for verifying fixes."""

    def setUp(self):
        """Set up test environment."""
        # Mock environment variables
        self.env_patcher = patch.dict('os.environ', {
            'DEEPGRAM_API_KEY': 'test_key',
            'GOOGLE_AI_API_KEY': 'test_key',
            'ELEVENLABS_API_KEY': 'test_key'
        })
        self.env_patcher.start()
        
        # Create mock objects
        self.mock_database = MagicMock()
        self.mock_user_manager = MagicMock()
        self.mock_config_manager = MagicMock()
        
        # Configure mocks
        self.mock_config_manager.get_provider_config.return_value = {
            'api_key': 'test_key',
            'voice_id': 'aura-thalia-en'
        }
        self.mock_config_manager.is_feature_enabled.return_value = True
        
    def tearDown(self):
        """Clean up after tests."""
        self.env_patcher.stop()

    def test_deepgram_tts_provider_initialization(self):
        """Test Deepgram TTS provider initialization."""
        provider = DeepgramTTSProvider(api_key='test_key', voice_id='aura-thalia-en')
        self.assertIsNotNone(provider)
        self.assertEqual(provider.voice_id, 'aura-thalia-en')

    def test_voice_processor_initialization(self):
        """Test voice processor initialization."""
        processor = VoiceProcessor(
            stt_provider_type='deepgram',
            stt_provider_options={'api_key': 'test_key'},
            tts_provider_type='deepgram',
            tts_provider_options={'api_key': 'test_key', 'voice_id': 'aura-thalia-en'}
        )
        self.assertIsNotNone(processor)
        self.assertEqual(processor.tts_provider_type, 'deepgram')

    def test_mood_entry_initialization(self):
        """Test mood entry initialization."""
        mood_entry = MoodEntry(self.mock_database)
        self.assertIsNotNone(mood_entry)
        
        # Test mood options
        self.assertIn('happy', mood_entry.mood_options)
        self.assertIn('sad', mood_entry.mood_options)
        
        # Test get_mood_entry_keyboard
        keyboard = mood_entry.get_mood_entry_keyboard()
        self.assertIsNotNone(keyboard)

    def test_navigation_router_initialization(self):
        """Test navigation router initialization."""
        router = NavigationRouter(MagicMock())
        self.assertIsNotNone(router)
        
        # Test callback handlers
        self.assertTrue(hasattr(router, '_handle_main_menu'))
        self.assertTrue(hasattr(router, '_handle_show_mood_diary'))
        self.assertTrue(hasattr(router, '_handle_add_mood_entry'))

    def test_google_ai_provider_initialization(self):
        """Test Google AI provider initialization."""
        provider = GoogleAIProvider(api_key='test_key')
        self.assertIsNotNone(provider)
        
        # Test personality setting
        provider.set_personality('friendly')
        self.assertEqual(provider.personality, 'friendly')
        
        # Test system prompt generation
        system_prompt = provider._get_system_prompt('friendly')
        self.assertIn('VoicePal', system_prompt)
        self.assertIn('friendly', system_prompt.lower())

    def test_enhanced_dialog_engine_initialization(self):
        """Test enhanced dialog engine initialization."""
        ai_provider = MagicMock()
        memory_manager = MagicMock()
        
        engine = EnhancedDialogEngine(ai_provider, memory_manager, self.mock_user_manager)
        self.assertIsNotNone(engine)
        self.assertEqual(engine.ai_provider, ai_provider)
        self.assertEqual(engine.memory_manager, memory_manager)

    def test_enhanced_memory_manager_short_message_handling(self):
        """Test enhanced memory manager short message handling."""
        memory_manager = EnhancedMemoryManager(
            database=self.mock_database,
            ai_provider=MagicMock(),
            config={'short_message_threshold': 10}
        )
        
        # Test short message detection
        self.assertTrue(memory_manager.should_preserve_context_for_short_message('Hi'))
        self.assertTrue(memory_manager.should_preserve_context_for_short_message('How are you?'))
        
        # Test empty message
        self.assertTrue(memory_manager.should_preserve_context_for_short_message(''))

async def run_async_tests():
    """Run async tests."""
    # Test async methods here
    logger.info("Running async tests...")
    
    # Test Google AI provider response generation
    try:
        provider = GoogleAIProvider(api_key='test_key')
        response = await provider.generate_stateless_response(
            message="Hello, how are you?",
            language="en"
        )
        logger.info(f"Generated response: {response}")
    except Exception as e:
        logger.error(f"Error testing Google AI provider: {e}")
    
    logger.info("Async tests completed")

def run_tests():
    """Run all tests."""
    logger.info("Starting tests...")
    
    # Run unittest tests
    unittest.main(argv=['first-arg-is-ignored'], exit=False)
    
    # Run async tests
    asyncio.run(run_async_tests())
    
    logger.info("All tests completed")

if __name__ == '__main__':
    run_tests()
