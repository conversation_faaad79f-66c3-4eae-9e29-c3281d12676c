"""
AI Provider Interface for VoicePal.

This module defines the interface for AI providers.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional

class AIProvider(ABC):
    """Interface for AI providers."""

    @abstractmethod
    async def generate_response(self, prompt: str, context: Optional[List[Dict[str, str]]] = None, 
                               system_prompt: Optional[str] = None, 
                               temperature: float = 0.7, 
                               max_tokens: int = 1000) -> str:
        """
        Generate a response from the AI.

        Args:
            prompt: User prompt
            context: Conversation context (list of message dicts with 'role' and 'content')
            system_prompt: System prompt to guide the AI
            temperature: Temperature for response generation
            max_tokens: Maximum number of tokens to generate

        Returns:
            Generated response text
        """
        pass

    @abstractmethod
    async def analyze_sentiment(self, text: str) -> Dict[str, Any]:
        """
        Analyze sentiment of text.

        Args:
            text: Text to analyze

        Returns:
            Dictionary with sentiment analysis results
        """
        pass

    @abstractmethod
    async def summarize_text(self, text: str, max_length: int = 100) -> str:
        """
        Summarize text.

        Args:
            text: Text to summarize
            max_length: Maximum length of summary

        Returns:
            Summarized text
        """
        pass

    @abstractmethod
    async def extract_keywords(self, text: str, max_keywords: int = 5) -> List[str]:
        """
        Extract keywords from text.

        Args:
            text: Text to extract keywords from
            max_keywords: Maximum number of keywords to extract

        Returns:
            List of keywords
        """
        pass

    @abstractmethod
    async def classify_text(self, text: str, categories: List[str]) -> Dict[str, float]:
        """
        Classify text into categories.

        Args:
            text: Text to classify
            categories: List of categories

        Returns:
            Dictionary mapping categories to confidence scores
        """
        pass

    @abstractmethod
    def get_provider_name(self) -> str:
        """
        Get provider name.

        Returns:
            Provider name
        """
        pass
