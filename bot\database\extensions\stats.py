"""
Database extension for statistics.

This module extends the database with methods for retrieving statistics.
"""

import logging
from typing import Dict, Any
from datetime import datetime, timedelta

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

def extend_database_for_stats(database) -> None:
    """
    Extend the database with statistics methods.
    
    Args:
        database: Database instance
    """
    # Add method to get statistics
    def get_stats(self) -> Dict[str, Any]:
        """
        Get system statistics.
        
        Returns:
            Dict containing statistics
        """
        try:
            stats = {}
            
            # Get total users
            self.cursor.execute("SELECT COUNT(*) as count FROM users")
            result = self.cursor.fetchone()
            stats["total_users"] = result["count"] if result else 0
            
            # Get active users in the last 7 days
            seven_days_ago = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d %H:%M:%S')
            self.cursor.execute(
                """SELECT COUNT(DISTINCT user_id) as count
                   FROM conversations
                   WHERE created_at >= ?""",
                (seven_days_ago,)
            )
            result = self.cursor.fetchone()
            stats["active_users"] = result["count"] if result else 0
            
            # Get total conversations
            self.cursor.execute("SELECT COUNT(*) as count FROM conversations")
            result = self.cursor.fetchone()
            stats["total_conversations"] = result["count"] if result else 0
            
            # Get voice conversations
            self.cursor.execute(
                """SELECT COUNT(*) as count
                   FROM conversations
                   WHERE is_voice = 1"""
            )
            result = self.cursor.fetchone()
            stats["voice_conversations"] = result["count"] if result else 0
            
            # Get total credits used
            self.cursor.execute(
                """SELECT SUM(credits_used) as total
                   FROM conversations"""
            )
            result = self.cursor.fetchone()
            stats["total_credits_used"] = result["total"] if result and result["total"] else 0
            
            # Get total revenue
            try:
                self.cursor.execute(
                    """SELECT SUM(amount) as total
                       FROM transactions
                       WHERE status = 'completed'"""
                )
                result = self.cursor.fetchone()
                stats["total_revenue"] = result["total"] / 100.0 if result and result["total"] else 0
            except Exception:
                # Transactions table might not exist
                stats["total_revenue"] = 0
            
            return stats
        except Exception as e:
            logger.error(f"Error getting statistics: {e}")
            return {
                "total_users": 0,
                "active_users": 0,
                "total_conversations": 0,
                "voice_conversations": 0,
                "total_credits_used": 0,
                "total_revenue": 0
            }
    
    # Add method to Database class
    setattr(database.__class__, 'get_stats', get_stats)
    
    logger.info("Database extended with methods for statistics")
