"""
Test script to verify welcome manager initialization.
"""

import os
import sys
import logging
import asyncio
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.DEBUG
)
logger = logging.getLogger(__name__)

async def test_welcome_manager():
    """Test initializing the welcome manager."""
    logger.info("Testing welcome manager initialization...")
    
    try:
        # Import the necessary classes
        from bot.core.initialization_manager import InitializationManager
        from bot.features.welcome_manager import WelcomeManager
        from bot.database import Database
        from bot.config_manager import ConfigManager
        
        # Create a database instance
        database = Database("voicepal.db")
        
        # Create a config manager instance
        config_manager = ConfigManager()
        
        # Get credit system config
        credit_system_config = config_manager.get_credit_system_config()
        
        # Create a welcome manager instance
        welcome_manager = WelcomeManager(
            database=database,
            mood_tracker=None,
            config=credit_system_config
        )
        
        # Test generating a welcome message
        welcome_message = welcome_manager.generate_welcome_message(user_id=1, is_new_user=True)
        logger.info(f"Welcome message: {welcome_message}")
        
        logger.info("Welcome manager created successfully")
        return True
    except Exception as e:
        logger.error(f"Error testing welcome manager: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

async def main():
    """Main function."""
    # Load environment variables
    load_dotenv()
    
    success = await test_welcome_manager()
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
