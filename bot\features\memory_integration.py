"""
Memory integration for VoicePal.

This module provides functions to integrate the memory systems into the main bot.
It supports both the enhanced memory system and the hierarchical memory system.
"""

import logging
import traceback
from typing import Dict, Any, Optional

from bot.features.memory_system import initialize_memory_system, get_memory_config

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

def integrate_enhanced_memory(bot_instance) -> bool:
    """
    Integrate enhanced memory system into the bot.

    Args:
        bot_instance: VoicePalBot instance

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Get memory configuration
        memory_config = get_memory_config(bot_instance.config_manager)

        # Initialize memory system
        memory_system = initialize_memory_system(
            database=bot_instance.database,
            ai_provider=bot_instance.ai_provider,
            user_manager=bot_instance.user_manager,
            config=memory_config
        )

        # Replace existing memory manager and dialog engine with enhanced versions
        # Store the enhanced memory manager as the primary memory manager
        bot_instance.memory_manager = memory_system["memory_manager"]
        # Keep a reference to the enhanced memory manager for compatibility
        bot_instance.enhanced_memory_manager = bot_instance.memory_manager

        # Replace the dialog engine with the enhanced version
        bot_instance.dialog_engine = memory_system["dialog_engine"]
        # Keep a reference to the enhanced dialog engine for compatibility
        bot_instance.enhanced_dialog_engine = bot_instance.dialog_engine

        # Update feature registry to indicate enhanced memory is enabled
        if hasattr(bot_instance, 'feature_registry'):
            bot_instance.feature_registry.register_feature(
                "enhanced_memory",
                True,
                "Enhanced memory system with improved conversation context"
            )

        # Update bot handlers to use enhanced memory
        update_bot_handlers_for_memory(bot_instance)

        logger.info("Enhanced memory system integrated successfully")
        return True
    except Exception as e:
        logger.error(f"Error integrating enhanced memory system: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def integrate_hierarchical_memory(bot_instance) -> bool:
    """
    Integrate hierarchical memory system into the bot.

    Args:
        bot_instance: VoicePalBot instance

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        logger.info("Integrating hierarchical memory system...")

        # Get configuration
        memory_config = bot_instance.config_manager.get_feature_config("memory") or {}

        # Import hierarchical memory manager
        from bot.features.hierarchical_memory_manager import HierarchicalMemoryManager
        from bot.providers.memory.redis_provider import RedisProvider
        from bot.providers.memory.qdrant_provider import QdrantProvider
        from bot.utils.embedding_utils import EmbeddingProvider

        # Initialize providers
        redis_provider = RedisProvider(memory_config.get("redis", {}))
        qdrant_provider = QdrantProvider(memory_config.get("qdrant", {}))
        embedding_provider = EmbeddingProvider(memory_config.get("embedding", {}))

        # Initialize hierarchical memory manager
        hierarchical_memory_manager = HierarchicalMemoryManager(
            database=bot_instance.database,
            ai_provider=bot_instance.ai_provider,
            config=memory_config,
            user_manager=bot_instance.user_manager
        )

        # Replace existing memory manager with hierarchical version
        bot_instance.memory_manager = hierarchical_memory_manager
        # Keep a reference to the hierarchical memory manager for compatibility
        bot_instance.hierarchical_memory_manager = hierarchical_memory_manager

        # Store providers in bot instance
        bot_instance.redis_provider = redis_provider
        bot_instance.qdrant_provider = qdrant_provider
        bot_instance.embedding_provider = embedding_provider

        # Update feature registry to indicate hierarchical memory is enabled
        if hasattr(bot_instance, 'feature_registry'):
            bot_instance.feature_registry.register_feature(
                "hierarchical_memory",
                True,
                "Hierarchical memory system with Redis and vector search"
            )

        # Update bot handlers to use hierarchical memory
        update_bot_handlers_for_hierarchical_memory(bot_instance)

        logger.info("Hierarchical memory system integrated successfully")
        return True
    except Exception as e:
        logger.error(f"Error integrating hierarchical memory system: {e}")
        logger.error(traceback.format_exc())
        return False

def update_bot_handlers_for_hierarchical_memory(bot_instance) -> bool:
    """
    Update bot handlers to use hierarchical memory system.

    Args:
        bot_instance: VoicePalBot instance

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Store original methods for fallback
        _original_handle_text = bot_instance.handle_text
        _original_handle_voice = getattr(bot_instance, 'handle_voice', None)

        # Create enhanced text handler
        async def enhanced_handle_text(update, context):
            """Enhanced text handler with hierarchical memory."""
            try:
                # Get user ID and message
                user_id = update.effective_user.id
                message = update.message.text

                # Check if Redis rate limiting is available and enabled
                if (hasattr(bot_instance, 'redis_provider') and
                    bot_instance.redis_provider.is_available() and
                    bot_instance.config_manager.get_feature_config("memory", {}).get("enable_rate_limiting", True)):

                    # Check rate limit
                    rate_limit_key = f"user:{user_id}:message"
                    is_allowed = bot_instance.redis_provider.check_rate_limit(rate_limit_key)

                    if not is_allowed:
                        logger.warning(f"Rate limit exceeded for user {user_id}")
                        await update.message.reply_text(
                            "You're sending messages too quickly. Please wait a moment before trying again."
                        )
                        return

                # Process message using hierarchical memory
                user_context = bot_instance.memory_manager.get_conversation_context(user_id)

                # Get language from user preferences
                language = None
                if user_context and "preferences" in user_context:
                    language = user_context["preferences"].get("language")

                # Check if we should preserve context for short messages
                preserve_context = bot_instance.memory_manager.should_preserve_context_for_short_message(message)

                # Process message using dialog engine
                response = await bot_instance.dialog_engine.process_message(
                    user_id=user_id,
                    message=message,
                    language=language,
                    is_voice=False,
                    user_context=user_context,
                    preserve_context=preserve_context
                )

                # Store conversation with importance scoring
                await bot_instance.memory_manager.store_conversation(
                    user_id=user_id,
                    message=message,
                    response=response.get("text", ""),
                    is_voice=False
                )

                # Send response
                await update.message.reply_text(response.get("text", ""))

                # Check if we should update the user summary
                if hasattr(bot_instance.dialog_engine, '_should_update_summary'):
                    if bot_instance.dialog_engine._should_update_summary(user_id, user_context):
                        await bot_instance.dialog_engine._update_user_summary(user_id)
                        logger.info(f"Updated summary for user {user_id}")
            except Exception as e:
                logger.error(f"Error in enhanced_handle_text: {e}")
                logger.error(traceback.format_exc())

                # Fall back to original method
                return await _original_handle_text(update, context)

        # Replace the handle_text method
        bot_instance.handle_text = enhanced_handle_text

        # Create enhanced voice handler if original exists
        if _original_handle_voice:
            async def enhanced_handle_voice(update, context):
                """Enhanced voice handler with hierarchical memory."""
                try:
                    # Get user ID
                    user_id = update.effective_user.id

                    # Check if Redis rate limiting is available and enabled
                    if (hasattr(bot_instance, 'redis_provider') and
                        bot_instance.redis_provider.is_available() and
                        bot_instance.config_manager.get_feature_config("memory", {}).get("enable_rate_limiting", True)):

                        # Check rate limit (voice messages consume more tokens)
                        rate_limit_key = f"user:{user_id}:voice"
                        is_allowed = bot_instance.redis_provider.check_rate_limit(rate_limit_key, tokens=3)

                        if not is_allowed:
                            logger.warning(f"Rate limit exceeded for voice message from user {user_id}")
                            await update.message.reply_text(
                                "You're sending voice messages too quickly. Please wait a moment before trying again."
                            )
                            return

                    # Process voice message using original method
                    result = await _original_handle_voice(update, context)

                    # If voice processing was successful and we have the text and response
                    if (hasattr(bot_instance, 'last_voice_text') and
                        hasattr(bot_instance, 'last_voice_response')):

                        # Store conversation with importance scoring
                        await bot_instance.memory_manager.store_conversation(
                            user_id=user_id,
                            message=bot_instance.last_voice_text.get(user_id, ""),
                            response=bot_instance.last_voice_response.get(user_id, ""),
                            is_voice=True
                        )

                    return result
                except Exception as e:
                    logger.error(f"Error in enhanced_handle_voice: {e}")
                    logger.error(traceback.format_exc())

                    # Fall back to original method
                    return await _original_handle_voice(update, context)

            # Replace the handle_voice method
            bot_instance.handle_voice = enhanced_handle_voice

        # Store original methods for reference
        bot_instance._original_handle_text = _original_handle_text
        if _original_handle_voice:
            bot_instance._original_handle_voice = _original_handle_voice

        logger.info("Bot handlers updated for hierarchical memory system")
        return True
    except Exception as e:
        logger.error(f"Error updating bot handlers for hierarchical memory: {e}")
        logger.error(traceback.format_exc())
        return False

def update_bot_handlers_for_memory(bot_instance) -> bool:
    """
    Update bot handlers to use enhanced memory system.

    Args:
        bot_instance: VoicePalBot instance

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Store original methods for fallback
        _original_handle_personalized_response = bot_instance._handle_personalized_response
        _original_handle_mood_analysis = bot_instance._handle_mood_analysis

        # Create enhanced personalized response handler
        async def enhanced_handle_personalized_response(user_id, message, **kwargs):
            """Enhanced personalized response handler."""
            try:
                # Get user context from memory manager
                user_context = bot_instance.memory_manager.get_conversation_context(user_id)

                # Add sentiment data to user context if available
                sentiment = kwargs.get("sentiment")
                if sentiment:
                    if not user_context:
                        user_context = {}
                    user_context["sentiment"] = sentiment

                # Add response adjustment to user context if available
                response_adjustment = kwargs.get("response_adjustment")
                if response_adjustment:
                    if not user_context:
                        user_context = {}
                    user_context["response_adjustment"] = response_adjustment

                # Process message using dialog engine
                response = await bot_instance.dialog_engine.process_message(
                    user_id=user_id,
                    message=message,
                    language=kwargs.get("language"),
                    is_voice=kwargs.get("is_voice", False)
                )

                # Format response with sentiment if available
                response_text = response.get("text", "")
                if hasattr(bot_instance, "sentiment_analyzer") and sentiment:
                    from bot.features.sentiment_integration import format_response_with_sentiment
                    response_text = format_response_with_sentiment(
                        response_text,
                        {"sentiment": sentiment, "response_adjustment": response_adjustment},
                        bot_instance.database
                    )

                return response_text
            except Exception as e:
                logger.error(f"Error in enhanced_handle_personalized_response: {e}")
                import traceback
                logger.error(traceback.format_exc())

                # Fall back to original method
                return await _original_handle_personalized_response(user_id, message, **kwargs)

        # Create enhanced mood analysis handler
        async def enhanced_handle_mood_analysis(user_id, audio_file_path=None, text=None):
            """Enhanced mood analysis handler."""
            try:
                # Use dialog engine to analyze mood
                if hasattr(bot_instance.dialog_engine, "analyze_mood"):
                    return await bot_instance.dialog_engine.analyze_mood(
                        user_id=user_id,
                        text=text,
                        audio_file_path=audio_file_path
                    )
                else:
                    # Fall back to original method
                    return await _original_handle_mood_analysis(user_id, audio_file_path, text)
            except Exception as e:
                logger.error(f"Error in enhanced_handle_mood_analysis: {e}")
                import traceback
                logger.error(traceback.format_exc())

                # Fall back to original method
                return await _original_handle_mood_analysis(user_id, audio_file_path, text)

        # Replace the methods
        bot_instance._handle_personalized_response = enhanced_handle_personalized_response
        bot_instance._handle_mood_analysis = enhanced_handle_mood_analysis

        # Store original methods for fallback
        bot_instance._original_handle_personalized_response = _original_handle_personalized_response
        bot_instance._original_handle_mood_analysis = _original_handle_mood_analysis

        logger.info("Bot handlers updated for enhanced memory system")
        return True
    except Exception as e:
        logger.error(f"Error updating bot handlers for enhanced memory: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False
