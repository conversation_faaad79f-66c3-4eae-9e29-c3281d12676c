"""
Data protection and GDPR compliance module for VoicePal.

This module provides data protection and GDPR compliance functionality.
"""

import json
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class DataExportRequest:
    """Data export request."""
    user_id: int
    request_date: datetime
    status: str  # pending, processing, completed, failed
    export_file: Optional[str] = None
    completion_date: Optional[datetime] = None

@dataclass
class DataDeletionRequest:
    """Data deletion request."""
    user_id: int
    request_date: datetime
    status: str  # pending, processing, completed, failed
    deletion_type: str  # partial, complete
    completion_date: Optional[datetime] = None
    retention_period: Optional[int] = None  # days

class DataProtection:
    """Data protection manager for VoicePal."""
    
    def __init__(self, database, encryption=None, audit_logger=None):
        """
        Initialize data protection manager.
        
        Args:
            database: Database instance
            encryption: Encryption instance
            audit_logger: Audit logger instance
        """
        self.database = database
        self.encryption = encryption
        self.audit_logger = audit_logger
        
        # Data retention policies (in days)
        self.retention_policies = {
            "messages": 365,  # 1 year
            "conversations": 365,  # 1 year
            "voice_recordings": 90,  # 3 months
            "audit_logs": 2555,  # 7 years (compliance requirement)
            "payment_records": 2555,  # 7 years (legal requirement)
            "user_preferences": 1095,  # 3 years
            "temporary_files": 7,  # 1 week
        }
    
    def anonymize_user_data(self, user_id: int) -> bool:
        """
        Anonymize user data while preserving analytics.
        
        Args:
            user_id: User ID to anonymize
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Generate anonymous ID
            anonymous_id = f"anon_{user_id}_{int(datetime.utcnow().timestamp())}"
            
            # Anonymize user record
            self.database.execute("""
                UPDATE users SET
                    username = ?,
                    first_name = 'Anonymous',
                    last_name = 'User',
                    email = NULL,
                    phone_number = NULL,
                    anonymized = 1,
                    anonymized_at = ?
                WHERE user_id = ?
            """, (anonymous_id, datetime.utcnow().isoformat(), user_id))
            
            # Anonymize messages (keep structure for analytics)
            self.database.execute("""
                UPDATE messages SET
                    content = '[ANONYMIZED]',
                    metadata = '{"anonymized": true}'
                WHERE conversation_id IN (
                    SELECT conversation_id FROM conversations WHERE user_id = ?
                )
            """, (user_id,))
            
            # Log anonymization
            if self.audit_logger:
                self.audit_logger.log_user_action(
                    event_type="data_anonymization",
                    user_id=user_id,
                    description=f"User data anonymized with ID: {anonymous_id}",
                    severity="medium"
                )
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to anonymize user data: {e}")
            return False
    
    def delete_user_data(self, user_id: int, deletion_type: str = "complete") -> bool:
        """
        Delete user data according to GDPR requirements.
        
        Args:
            user_id: User ID to delete data for
            deletion_type: Type of deletion (partial, complete)
            
        Returns:
            True if successful, False otherwise
        """
        try:
            if deletion_type == "complete":
                return self._complete_data_deletion(user_id)
            elif deletion_type == "partial":
                return self._partial_data_deletion(user_id)
            else:
                logger.error(f"Unknown deletion type: {deletion_type}")
                return False
                
        except Exception as e:
            logger.error(f"Failed to delete user data: {e}")
            return False
    
    def _complete_data_deletion(self, user_id: int) -> bool:
        """Complete data deletion."""
        try:
            # Delete in reverse dependency order
            tables_to_delete = [
                "messages",
                "conversations", 
                "voice_recordings",
                "user_preferences",
                "user_stats",
                "transactions",
                "memories",
                "users"
            ]
            
            for table in tables_to_delete:
                if table == "messages":
                    # Delete messages from user's conversations
                    self.database.execute("""
                        DELETE FROM messages WHERE conversation_id IN (
                            SELECT conversation_id FROM conversations WHERE user_id = ?
                        )
                    """, (user_id,))
                else:
                    # Delete directly by user_id
                    self.database.execute(f"DELETE FROM {table} WHERE user_id = ?", (user_id,))
            
            # Log deletion
            if self.audit_logger:
                self.audit_logger.log_user_action(
                    event_type="data_deletion",
                    user_id=user_id,
                    description="Complete user data deletion performed",
                    severity="high"
                )
            
            return True
            
        except Exception as e:
            logger.error(f"Complete data deletion failed: {e}")
            return False
    
    def _partial_data_deletion(self, user_id: int) -> bool:
        """Partial data deletion (keep essential records)."""
        try:
            # Delete personal data but keep anonymized analytics
            self.database.execute("""
                UPDATE users SET
                    username = NULL,
                    first_name = NULL,
                    last_name = NULL,
                    email = NULL,
                    phone_number = NULL
                WHERE user_id = ?
            """, (user_id,))
            
            # Delete message content but keep metadata for analytics
            self.database.execute("""
                UPDATE messages SET
                    content = '[DELETED]',
                    voice_file_path = NULL
                WHERE conversation_id IN (
                    SELECT conversation_id FROM conversations WHERE user_id = ?
                )
            """, (user_id,))
            
            # Delete voice recordings
            self.database.execute("""
                DELETE FROM voice_recordings WHERE user_id = ?
            """, (user_id,))
            
            # Log partial deletion
            if self.audit_logger:
                self.audit_logger.log_user_action(
                    event_type="data_deletion",
                    user_id=user_id,
                    description="Partial user data deletion performed",
                    severity="medium"
                )
            
            return True
            
        except Exception as e:
            logger.error(f"Partial data deletion failed: {e}")
            return False
    
    def export_user_data(self, user_id: int) -> Optional[Dict[str, Any]]:
        """
        Export all user data for GDPR compliance.
        
        Args:
            user_id: User ID to export data for
            
        Returns:
            Dictionary containing all user data or None if failed
        """
        try:
            export_data = {
                "export_date": datetime.utcnow().isoformat(),
                "user_id": user_id,
                "data": {}
            }
            
            # Export user profile
            user = self.database.execute(
                "SELECT * FROM users WHERE user_id = ?", (user_id,)
            ).fetchone()
            
            if user:
                export_data["data"]["profile"] = dict(user)
            
            # Export conversations
            conversations = self.database.execute(
                "SELECT * FROM conversations WHERE user_id = ?", (user_id,)
            ).fetchall()
            export_data["data"]["conversations"] = [dict(conv) for conv in conversations]
            
            # Export messages
            messages = self.database.execute("""
                SELECT m.* FROM messages m
                JOIN conversations c ON m.conversation_id = c.conversation_id
                WHERE c.user_id = ?
            """, (user_id,)).fetchall()
            export_data["data"]["messages"] = [dict(msg) for msg in messages]
            
            # Export preferences
            preferences = self.database.execute(
                "SELECT * FROM user_preferences WHERE user_id = ?", (user_id,)
            ).fetchall()
            export_data["data"]["preferences"] = [dict(pref) for pref in preferences]
            
            # Export transactions
            transactions = self.database.execute(
                "SELECT * FROM transactions WHERE user_id = ?", (user_id,)
            ).fetchall()
            export_data["data"]["transactions"] = [dict(trans) for trans in transactions]
            
            # Export memories
            memories = self.database.execute(
                "SELECT * FROM memories WHERE user_id = ?", (user_id,)
            ).fetchall()
            export_data["data"]["memories"] = [dict(mem) for mem in memories]
            
            # Log export
            if self.audit_logger:
                self.audit_logger.log_user_action(
                    event_type="data_export",
                    user_id=user_id,
                    description="User data export performed",
                    severity="medium"
                )
            
            return export_data
            
        except Exception as e:
            logger.error(f"Failed to export user data: {e}")
            return None
    
    def cleanup_expired_data(self) -> Dict[str, int]:
        """
        Clean up expired data according to retention policies.
        
        Returns:
            Dictionary with cleanup statistics
        """
        cleanup_stats = {}
        
        try:
            for table, retention_days in self.retention_policies.items():
                cutoff_date = datetime.utcnow() - timedelta(days=retention_days)
                
                if table == "messages":
                    # Delete old messages
                    result = self.database.execute("""
                        DELETE FROM messages WHERE created_at < ?
                    """, (cutoff_date.isoformat(),))
                    cleanup_stats[table] = result.rowcount
                
                elif table == "voice_recordings":
                    # Delete old voice recordings
                    result = self.database.execute("""
                        DELETE FROM voice_recordings WHERE created_at < ?
                    """, (cutoff_date.isoformat(),))
                    cleanup_stats[table] = result.rowcount
                
                elif table == "audit_logs":
                    # Delete old audit logs (except critical ones)
                    result = self.database.execute("""
                        DELETE FROM audit_logs 
                        WHERE created_at < ? AND severity != 'critical'
                    """, (cutoff_date.isoformat(),))
                    cleanup_stats[table] = result.rowcount
            
            # Log cleanup
            if self.audit_logger:
                self.audit_logger.log_security_event(
                    event_type="data_cleanup",
                    description="Automated data cleanup performed",
                    details=cleanup_stats,
                    severity="low"
                )
            
            return cleanup_stats
            
        except Exception as e:
            logger.error(f"Data cleanup failed: {e}")
            return {}

class GDPR:
    """GDPR compliance utilities."""
    
    @staticmethod
    def generate_privacy_notice() -> str:
        """Generate privacy notice text."""
        return """
**VoicePal Privacy Notice**

We collect and process your personal data in accordance with GDPR. This includes:

**Data We Collect:**
- Telegram user information (username, name)
- Messages and voice recordings
- Usage statistics and preferences
- Payment information

**Legal Basis:**
- Legitimate interest for service provision
- Consent for optional features
- Contract performance for paid services

**Your Rights:**
- Access your data (/export_data)
- Rectify incorrect data
- Delete your data (/delete_data)
- Data portability
- Object to processing

**Data Retention:**
- Messages: 1 year
- Voice recordings: 3 months
- Payment records: 7 years (legal requirement)

Contact us for any privacy concerns.
        """.strip()
    
    @staticmethod
    def generate_consent_text() -> str:
        """Generate consent request text."""
        return """
By using VoicePal, you consent to:

✅ Processing of your messages for AI responses
✅ Temporary storage of voice recordings for processing
✅ Collection of usage statistics for service improvement
✅ Storage of preferences and conversation history

You can withdraw consent at any time using /delete_data.
        """.strip()
