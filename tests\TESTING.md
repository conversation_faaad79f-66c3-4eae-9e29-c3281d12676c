# VoicePal Testing Guide

This document provides a comprehensive guide to testing the VoicePal Telegram bot, including information about the test framework, test cases, and how to run tests.

## Test Framework

VoicePal uses the Python `unittest` framework for testing. The tests are organized into test modules, each focusing on a specific component or feature of the bot.

### Test Structure

- **Unit Tests**: Test individual components in isolation
- **Integration Tests**: Test interactions between components
- **End-to-End Tests**: Test the complete bot flow

### Test Modules

#### Original Test Modules

- `test_bot.py`: Tests for the main bot functionality
- `test_deepgram.py`: Tests for the Deepgram integration
- `test_elevenlabs.py`: Tests for the ElevenLabs TTS integration
- `test_full_flow.py`: End-to-end tests for the complete bot flow
- `test_google_ai_dia.py`: Tests for the Google AI and Dia integration
- `test_telegram.py`: Tests for the Telegram bot integration
- `test_tts_providers.py`: Tests for various TTS providers
- `test_voice_bot.py`: Tests for voice processing functionality
- `test_voice_personality.py`: Tests for voice personality features
- `test_keyboard_manager.py`: Tests for the keyboard manager
- `test_welcome_manager.py`: Tests for the welcome manager
- `test_mood_tracker.py`: Tests for the mood tracker
- `test_mood_entry.py`: Tests for the mood entry functionality

#### Updated Test Modules (2023)

- `test_database_core.py`: Tests for the database module, focusing on credit system
- `test_telegram_stars_payment_updated.py`: Tests for the updated Telegram Stars payment system
- `test_deepgram_tts_provider.py`: Tests for the improved Deepgram TTS provider
- `test_credit_system.py`: Specialized tests for the credit system functionality
- `test_deepgram_cache.py`: Specialized tests for the Deepgram TTS caching system

#### New Test Modules (2024)

- `test_enhanced_memory_system.py`: Tests for the enhanced memory system
- `test_ui_button_functionality.py`: Tests for UI and button functionality
- `test_tts_provider_selection.py`: Tests for TTS provider selection

## Running Tests

### Running All Tests

To run all tests, use the `run_tests.py` script:

```bash
python tests/run_tests.py
```

Or use the unittest discover command:

```bash
python -m unittest discover tests
```

### Running Specific Test Groups

To run only the updated tests (database, payment, TTS), use the `run_updated_tests.py` script:

```bash
python tests/run_updated_tests.py
```

To run only the new tests (memory, UI, TTS selection), use the `run_new_tests.py` script:

```bash
python tests/run_new_tests.py
```

### Running Individual Tests

To run a specific test module:

```bash
python -m unittest tests.test_enhanced_memory_system
```

Or run the test file directly:

```bash
python tests/test_enhanced_memory_system.py
```

## Test Environment Setup

### Dependencies

Make sure you have all the required dependencies installed:

```bash
pip install -r requirements.txt
pip install -r requirements-dev.txt  # Development dependencies
```

### Environment Variables

Some tests require API keys and other environment variables. Create a `.env.test` file with the following variables:

```
DEEPGRAM_API_KEY=your_deepgram_api_key
ELEVENLABS_API_KEY=your_elevenlabs_api_key
GOOGLE_AI_API_KEY=your_google_ai_api_key
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
```

Then load these variables before running tests:

```bash
source .env.test  # On Linux/Mac
# OR
set -a; source .env.test; set +a  # On Windows with Git Bash
```

### Mock Data

Some tests use mock data to simulate user interactions. The mock data is defined in the test files themselves.

## Test Cases

### Enhanced Memory System Tests

The enhanced memory system tests (`test_enhanced_memory_system.py`) verify that:

1. The memory manager is properly initialized
2. Conversation context is correctly retrieved
3. Conversation importance is properly analyzed
4. User summaries are generated and updated
5. The memory system integrates correctly with the dialog engine

### UI and Button Functionality Tests

The UI and button functionality tests (`test_ui_button_functionality.py`) verify that:

1. The keyboard manager is properly initialized
2. Main menu keyboard is correctly generated
3. Persistent menu button is correctly generated
4. Button state manager correctly saves and retrieves button states
5. Callbacks are properly recorded and processed
6. Error recovery suggestions are correctly generated
7. Callback queries are properly handled

### TTS Provider Selection Tests

The TTS provider selection tests (`test_tts_provider_selection.py`) verify that:

1. The voice processor is properly initialized
2. TTS providers can be switched dynamically
3. Voice selection works correctly
4. Fallback mechanisms work when a provider fails
5. Voice settings callbacks are properly handled

## Writing New Tests

When adding new tests, please follow these guidelines:

1. Create a new file named `test_<component>.py`
2. Use the `unittest` framework
3. Include docstrings for test classes and methods
4. Mock external dependencies when appropriate
5. Use `async` tests for asynchronous methods
6. Add the new test to the appropriate test runner script

## Continuous Integration

The tests are automatically run in the CI/CD pipeline when changes are pushed to the repository. The pipeline will fail if any tests fail.

## Test Coverage

To generate a test coverage report, run:

```bash
coverage run -m unittest discover tests
coverage report
coverage html  # Generate HTML report
```

The HTML report will be generated in the `htmlcov` directory.

## Troubleshooting

### Common Issues

1. **ImportError**: Make sure the project root directory is in the Python path
2. **Missing API Keys**: Set the required environment variables
3. **Database Errors**: Make sure the database is properly initialized
4. **Async Test Errors**: Make sure async tests are run with `asyncio.run_until_complete`

### Getting Help

If you encounter any issues with the tests, please contact the development team or open an issue on the repository.
