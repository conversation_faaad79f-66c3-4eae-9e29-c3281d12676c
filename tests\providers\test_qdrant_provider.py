"""
Unit tests for the Qdrant provider module.
"""

import pytest
import numpy as np
from unittest.mock import Magic<PERSON><PERSON>, patch, call

from bot.providers.memory.qdrant_provider import QdrantProvider


@pytest.fixture
def mock_qdrant_client():
    """Create a mock Qdrant client."""
    mock_client = MagicMock()
    
    # Mock collection_exists method
    mock_client.collection_exists.return_value = True
    
    # Mock get_collection method
    mock_client.get_collection.return_value = MagicMock(
        vectors_count=100,
        status="green"
    )
    
    # Mock create_collection method
    mock_client.create_collection.return_value = None
    
    # Mock upsert method
    mock_client.upsert.return_value = MagicMock(
        operation_id=123,
        status="completed"
    )
    
    # Mock search method
    mock_search_result = [
        MagicMock(
            id="memory_1",
            score=0.95,
            payload={
                "user_id": 123,
                "text": "This is a test memory",
                "metadata": {
                    "conversation_id": 1,
                    "importance_score": 0.8
                }
            }
        ),
        MagicMock(
            id="memory_2",
            score=0.85,
            payload={
                "user_id": 123,
                "text": "This is another test memory",
                "metadata": {
                    "conversation_id": 2,
                    "importance_score": 0.7
                }
            }
        )
    ]
    mock_client.search.return_value = mock_search_result
    
    # Mock delete method
    mock_client.delete.return_value = MagicMock(
        operation_id=456,
        status="completed"
    )
    
    return mock_client


@pytest.fixture
def qdrant_provider(mock_qdrant_client):
    """Create a QdrantProvider with mocked dependencies."""
    with patch('bot.providers.memory.qdrant_provider.QdrantClient', return_value=mock_qdrant_client):
        provider = QdrantProvider({
            "collection_name": "test_memories",
            "vector_size": 384,
            "distance": "cosine",
            "auto_init": True,
            "use_in_memory": True
        })
        provider._client = mock_qdrant_client
        yield provider


@pytest.fixture
def qdrant_provider_no_config():
    """Create a QdrantProvider with no configuration."""
    with patch('bot.providers.memory.qdrant_provider.QdrantClient', side_effect=Exception("Connection error")):
        provider = QdrantProvider({})
        yield provider


class TestQdrantProvider:
    """Test cases for the QdrantProvider class."""

    def test_initialization_with_config(self, qdrant_provider):
        """Test initialization with configuration."""
        assert qdrant_provider.is_available() is True
        assert qdrant_provider._collection_name == "test_memories"
        assert qdrant_provider._vector_size == 384
        assert qdrant_provider._distance == "cosine"
        assert qdrant_provider._auto_init is True
        assert qdrant_provider._use_in_memory is True

    def test_initialization_without_config(self, qdrant_provider_no_config):
        """Test initialization without configuration."""
        assert qdrant_provider_no_config.is_available() is False
        assert qdrant_provider_no_config._collection_name == "voicepal_memories"  # Default value
        assert qdrant_provider_no_config._vector_size == 384  # Default value
        assert qdrant_provider_no_config._distance == "cosine"  # Default value
        assert qdrant_provider_no_config._auto_init is True  # Default value
        assert qdrant_provider_no_config._use_in_memory is True  # Default value

    def test_is_available(self, qdrant_provider, qdrant_provider_no_config):
        """Test is_available method."""
        assert qdrant_provider.is_available() is True
        assert qdrant_provider_no_config.is_available() is False

    def test_initialize_collection(self, qdrant_provider, mock_qdrant_client):
        """Test initialize_collection method."""
        # Test when collection already exists
        mock_qdrant_client.collection_exists.return_value = True
        result = qdrant_provider.initialize_collection()
        assert result is True
        mock_qdrant_client.collection_exists.assert_called_with("test_memories")
        
        # Test when collection doesn't exist
        mock_qdrant_client.collection_exists.return_value = False
        result = qdrant_provider.initialize_collection()
        assert result is True
        mock_qdrant_client.create_collection.assert_called_once()

    def test_initialize_collection_with_error(self, qdrant_provider, mock_qdrant_client):
        """Test initialize_collection method with error."""
        mock_qdrant_client.collection_exists.side_effect = Exception("Qdrant error")
        result = qdrant_provider.initialize_collection()
        assert result is False

    def test_store_memory(self, qdrant_provider, mock_qdrant_client):
        """Test store_memory method."""
        # Create a test vector
        test_vector = np.random.rand(384).tolist()
        
        # Test with memory_id
        result = qdrant_provider.store_memory(
            user_id=123,
            vector=test_vector,
            text="This is a test memory",
            metadata={"conversation_id": 1, "importance_score": 0.8},
            memory_id="test_memory_1"
        )
        assert result is True
        mock_qdrant_client.upsert.assert_called_once()
        
        # Test without memory_id (should generate a UUID)
        mock_qdrant_client.upsert.reset_mock()
        result = qdrant_provider.store_memory(
            user_id=123,
            vector=test_vector,
            text="This is another test memory",
            metadata={"conversation_id": 2, "importance_score": 0.7}
        )
        assert result is True
        mock_qdrant_client.upsert.assert_called_once()

    def test_store_memory_with_error(self, qdrant_provider, mock_qdrant_client):
        """Test store_memory method with error."""
        test_vector = np.random.rand(384).tolist()
        mock_qdrant_client.upsert.side_effect = Exception("Qdrant error")
        result = qdrant_provider.store_memory(
            user_id=123,
            vector=test_vector,
            text="This is a test memory",
            metadata={"conversation_id": 1, "importance_score": 0.8},
            memory_id="test_memory_1"
        )
        assert result is False

    def test_search_memories(self, qdrant_provider, mock_qdrant_client):
        """Test search_memories method."""
        # Create a test vector
        test_vector = np.random.rand(384).tolist()
        
        # Test search
        results = qdrant_provider.search_memories(
            user_id=123,
            vector=test_vector,
            limit=2,
            score_threshold=0.8
        )
        assert len(results) == 2
        assert results[0]["id"] == "memory_1"
        assert results[0]["score"] == 0.95
        assert results[0]["text"] == "This is a test memory"
        assert results[0]["metadata"]["conversation_id"] == 1
        assert results[0]["metadata"]["importance_score"] == 0.8
        
        mock_qdrant_client.search.assert_called_once()

    def test_search_memories_with_error(self, qdrant_provider, mock_qdrant_client):
        """Test search_memories method with error."""
        test_vector = np.random.rand(384).tolist()
        mock_qdrant_client.search.side_effect = Exception("Qdrant error")
        results = qdrant_provider.search_memories(
            user_id=123,
            vector=test_vector,
            limit=2,
            score_threshold=0.8
        )
        assert results == []

    def test_delete_memory(self, qdrant_provider, mock_qdrant_client):
        """Test delete_memory method."""
        result = qdrant_provider.delete_memory("test_memory_1")
        assert result is True
        mock_qdrant_client.delete.assert_called_once()

    def test_delete_memory_with_error(self, qdrant_provider, mock_qdrant_client):
        """Test delete_memory method with error."""
        mock_qdrant_client.delete.side_effect = Exception("Qdrant error")
        result = qdrant_provider.delete_memory("test_memory_1")
        assert result is False

    def test_delete_user_memories(self, qdrant_provider, mock_qdrant_client):
        """Test delete_user_memories method."""
        result = qdrant_provider.delete_user_memories(123)
        assert result is True
        mock_qdrant_client.delete.assert_called_once()

    def test_delete_user_memories_with_error(self, qdrant_provider, mock_qdrant_client):
        """Test delete_user_memories method with error."""
        mock_qdrant_client.delete.side_effect = Exception("Qdrant error")
        result = qdrant_provider.delete_user_memories(123)
        assert result is False

    def test_get_collection_stats(self, qdrant_provider, mock_qdrant_client):
        """Test get_collection_stats method."""
        stats = qdrant_provider.get_collection_stats()
        assert stats["vectors_count"] == 100
        assert stats["status"] == "green"
        mock_qdrant_client.get_collection.assert_called_once()

    def test_get_collection_stats_with_error(self, qdrant_provider, mock_qdrant_client):
        """Test get_collection_stats method with error."""
        mock_qdrant_client.get_collection.side_effect = Exception("Qdrant error")
        stats = qdrant_provider.get_collection_stats()
        assert stats == {"vectors_count": 0, "status": "unknown"}

    def test_fallback_when_qdrant_unavailable(self, qdrant_provider_no_config):
        """Test fallback behavior when Qdrant is unavailable."""
        # All operations should gracefully handle Qdrant being unavailable
        assert qdrant_provider_no_config.initialize_collection() is False
        
        test_vector = np.random.rand(384).tolist()
        assert qdrant_provider_no_config.store_memory(
            user_id=123,
            vector=test_vector,
            text="This is a test memory",
            metadata={"conversation_id": 1, "importance_score": 0.8},
            memory_id="test_memory_1"
        ) is False
        
        assert qdrant_provider_no_config.search_memories(
            user_id=123,
            vector=test_vector,
            limit=2,
            score_threshold=0.8
        ) == []
        
        assert qdrant_provider_no_config.delete_memory("test_memory_1") is False
        assert qdrant_provider_no_config.delete_user_memories(123) is False
        
        stats = qdrant_provider_no_config.get_collection_stats()
        assert stats == {"vectors_count": 0, "status": "unknown"}
