"""
Mock payment provider for testing.

This module provides a mock payment provider for testing the payment system
without making actual payment requests.
"""

import logging
import uuid
from typing import Dict, Any, Optional, List
from datetime import datetime

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class MockPayment:
    """Mock payment provider for testing."""

    # Define credit packages
    CREDIT_PACKAGES = {
        "small": {
            "id": "small",
            "title": "100 Credits",
            "description": "100 credits for conversations",
            "credits": 100,
            "price": 499,  # in cents ($4.99)
            "currency": "USD"
        },
        "medium": {
            "id": "medium",
            "title": "300 Credits",
            "description": "300 credits for conversations",
            "credits": 300,
            "price": 999,  # in cents ($9.99)
            "currency": "USD"
        },
        "large": {
            "id": "large",
            "title": "1000 Credits",
            "description": "1000 credits for conversations",
            "credits": 1000,
            "price": 1999,  # in cents ($19.99)
            "currency": "USD"
        }
    }

    def __init__(self, database):
        """
        Initialize the mock payment provider.

        Args:
            database: Database instance
        """
        self.database = database
        logger.info("Mock payment provider initialized")

    def get_credit_packages(self) -> Dict[str, Dict[str, Any]]:
        """
        Get available credit packages.

        Returns:
            Dict of credit packages
        """
        return self.CREDIT_PACKAGES

    def create_invoice(self, user_id: int, package_id: str) -> Dict[str, Any]:
        """
        Create a payment invoice.

        Args:
            user_id: Telegram user ID
            package_id: Credit package ID

        Returns:
            Dict containing invoice information
        """
        try:
            if package_id not in self.CREDIT_PACKAGES:
                logger.error(f"Invalid package_id: {package_id}")
                return {"success": False, "error": "Invalid package ID"}

            package = self.CREDIT_PACKAGES[package_id]

            # Generate a unique invoice payload
            invoice_payload = f"mock-{package_id}-{uuid.uuid4()}"

            # Store invoice in database
            try:
                self.database.add_invoice(
                    user_id=user_id,
                    package_id=package_id,
                    amount=package["price"],
                    currency=package["currency"],
                    payload=invoice_payload,
                    status="pending"
                )
            except Exception as e:
                logger.error(f"Error storing invoice in database: {e}")
                return {"success": False, "error": f"Database error: {str(e)}"}

            logger.info(f"Created mock invoice for user {user_id}, package: {package_id}")

            return {
                "success": True,
                "title": package["title"],
                "description": package["description"],
                "payload": invoice_payload,
                "provider_token": "mock_provider_token",
                "currency": package["currency"],
                "prices": [{"label": package["title"], "amount": package["price"]}],
                "max_tip_amount": 0,
                "suggested_tip_amounts": [],
                "start_parameter": f"buy-{package_id}",
                "provider_data": None,
                "photo_url": None,
                "photo_size": None,
                "photo_width": None,
                "photo_height": None,
                "need_name": False,
                "need_phone_number": False,
                "need_email": False,
                "need_shipping_address": False,
                "send_phone_number_to_provider": False,
                "send_email_to_provider": False,
                "is_flexible": False
            }
        except Exception as e:
            logger.error(f"Error creating invoice: {e}")
            return {"success": False, "error": str(e)}

    def process_payment(self, user_id: int, payment: Any) -> int:
        """
        Process a successful payment.

        Args:
            user_id: Telegram user ID
            payment: Payment information

        Returns:
            Number of credits added
        """
        try:
            # Extract package_id from payload
            payload = payment.invoice_payload
            package_id = payload.split('-')[1]

            if package_id not in self.CREDIT_PACKAGES:
                logger.error(f"Invalid package_id in payment: {package_id}")
                return 0

            package = self.CREDIT_PACKAGES[package_id]

            # Add credits to user account
            new_credits = self.database.add_credits(user_id, package["credits"], source="purchase")

            # Record transaction
            self.database.add_transaction(
                user_id=user_id,
                amount=payment.total_amount,
                credits=package["credits"],
                transaction_id=f"mock-{uuid.uuid4()}",
                status="completed"
            )

            # Update invoice status
            self.database.update_invoice_status(
                payload=payload,
                status="completed"
            )

            logger.info(f"Processed mock payment for user {user_id}, package: {package_id}, credits: {package['credits']}")

            return package["credits"]
        except Exception as e:
            logger.error(f"Error processing payment: {e}")
            return 0
