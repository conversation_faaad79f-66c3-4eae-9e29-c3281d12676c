"""
Integration tests for the hierarchical memory system.

These tests verify that the components of the hierarchical memory system
work together correctly.
"""

import pytest
import os
import json
import tempfile
import sqlite3
from datetime import datetime, timedelta
from unittest.mock import MagicMock, patch, AsyncMock

from bot.database.database import Database
from bot.providers.ai.ai_provider import AIProvider
from bot.features.user_manager import UserManager
from bot.features.hierarchical_memory_manager import HierarchicalMemoryManager
from bot.providers.memory.redis_provider import RedisProvider
from bot.providers.memory.qdrant_provider import QdrantProvider
from bot.utils.embedding_utils import EmbeddingProvider
from bot.features.memory_integration import integrate_hierarchical_memory


@pytest.fixture
def temp_db_file():
    """Create a temporary database file."""
    fd, path = tempfile.mkstemp(suffix='.db')
    os.close(fd)
    yield path
    os.unlink(path)


@pytest.fixture
def database(temp_db_file):
    """Create a test database with schema."""
    db = Database(temp_db_file)
    
    # Create test user
    db.add_user(
        user_id=123,
        username="test_user",
        first_name="Test",
        last_name="User"
    )
    
    # Add user preferences
    db.update_user_preference(123, "language", "en")
    db.update_user_preference(123, "tts_provider", "deepgram")
    db.update_user_preference(123, "voice_id", "aura-thalia-en")
    
    # Add some conversations
    for i in range(5):
        db.add_conversation(
            user_id=123,
            message=f"Test message {i}",
            response=f"Test response {i}",
            is_voice=False
        )
    
    yield db
    db.close()


@pytest.fixture
def mock_ai_provider():
    """Create a mock AI provider."""
    mock_ai = MagicMock(spec=AIProvider)
    mock_ai.process_message = AsyncMock(return_value={
        "text": "This is a test response",
        "tokens_used": 10
    })
    return mock_ai


@pytest.fixture
def user_manager(database):
    """Create a user manager."""
    return UserManager(database)


@pytest.fixture
def memory_config():
    """Create memory configuration."""
    return {
        "short_term_limit": 10,
        "medium_term_limit": 50,
        "long_term_threshold": 0.7,
        "cache_ttl_minutes": 30,
        "name_cache_ttl_minutes": 60,
        "redis": {
            "url": None,  # Use in-memory Redis for testing
            "cache_ttl_minutes": 30,
            "rate_limit_max_requests": 10,
            "rate_limit_window_seconds": 10
        },
        "qdrant": {
            "collection_name": "test_memories",
            "vector_size": 384,
            "distance": "cosine",
            "auto_init": True,
            "use_in_memory": True
        },
        "embedding": {
            "model_name": "all-MiniLM-L6-v2",
            "vector_size": 384,
            "batch_size": 32
        }
    }


@pytest.fixture
def mock_redis_provider():
    """Create a mock Redis provider."""
    mock_redis = MagicMock(spec=RedisProvider)
    mock_redis.is_available.return_value = True
    mock_redis.get.return_value = None  # Default to cache miss
    mock_redis.set.return_value = True
    mock_redis.delete.return_value = True
    mock_redis.check_rate_limit.return_value = True
    return mock_redis


@pytest.fixture
def mock_qdrant_provider():
    """Create a mock Qdrant provider."""
    mock_qdrant = MagicMock(spec=QdrantProvider)
    mock_qdrant.is_available.return_value = True
    mock_qdrant.initialize_collection.return_value = True
    mock_qdrant.store_memory.return_value = True
    mock_qdrant.search_memories.return_value = [
        {
            "id": "memory_1",
            "score": 0.95,
            "text": "User: What are your hobbies?\nAssistant: I enjoy chatting with people like you!",
            "metadata": {
                "conversation_id": 5,
                "message": "What are your hobbies?",
                "response": "I enjoy chatting with people like you!",
                "is_voice": False,
                "importance_score": 0.7,
                "created_at": (datetime.now() - timedelta(days=2)).isoformat()
            }
        }
    ]
    return mock_qdrant


@pytest.fixture
def mock_embedding_provider():
    """Create a mock embedding provider."""
    mock_embedding = MagicMock(spec=EmbeddingProvider)
    mock_embedding.is_available.return_value = True
    mock_embedding.generate_embedding.return_value = [0.1] * 384
    return mock_embedding


@pytest.fixture
def hierarchical_memory_manager(database, mock_ai_provider, user_manager, memory_config,
                               mock_redis_provider, mock_qdrant_provider, mock_embedding_provider):
    """Create a hierarchical memory manager with real database and mocked providers."""
    with patch('bot.providers.memory.redis_provider.RedisProvider', return_value=mock_redis_provider), \
         patch('bot.providers.memory.qdrant_provider.QdrantProvider', return_value=mock_qdrant_provider), \
         patch('bot.utils.embedding_utils.EmbeddingProvider', return_value=mock_embedding_provider):
        
        manager = HierarchicalMemoryManager(
            database=database,
            ai_provider=mock_ai_provider,
            config=memory_config,
            user_manager=user_manager
        )
        
        # Set mocked providers directly
        manager.redis_provider = mock_redis_provider
        manager.qdrant_provider = mock_qdrant_provider
        manager.embedding_provider = mock_embedding_provider
        
        yield manager


@pytest.fixture
def mock_bot():
    """Create a mock bot instance."""
    mock_bot = MagicMock()
    mock_bot.database = None  # Will be set in tests
    mock_bot.ai_provider = None  # Will be set in tests
    mock_bot.user_manager = None  # Will be set in tests
    mock_bot.config_manager = MagicMock()
    mock_bot.config_manager.get_feature_config.return_value = {
        "short_term_limit": 10,
        "medium_term_limit": 50,
        "long_term_threshold": 0.7,
        "cache_ttl_minutes": 30,
        "name_cache_ttl_minutes": 60
    }
    mock_bot.config_manager.is_feature_enabled.return_value = True
    
    # Mock feature registry
    mock_bot.feature_registry = MagicMock()
    mock_bot.feature_registry.register_feature = MagicMock()
    
    # Mock dialog engine
    mock_bot.dialog_engine = MagicMock()
    mock_bot.dialog_engine.process_message = AsyncMock(return_value={
        "text": "This is a test response",
        "tokens_used": 10
    })
    
    # Mock handlers
    mock_bot.handle_text = AsyncMock()
    mock_bot.handle_voice = AsyncMock()
    
    return mock_bot


class TestMemoryIntegration:
    """Integration tests for the hierarchical memory system."""

    def test_database_extension(self, database, hierarchical_memory_manager):
        """Test that the database is extended with memory-related methods."""
        # Verify that the database has been extended with memory-related methods
        assert hasattr(database, 'get_important_conversations')
        assert hasattr(database, 'set_conversation_importance')
        
        # Test the extended methods
        # Add a conversation
        conversation_id = database.add_conversation(
            user_id=123,
            message="This is an important message",
            response="I'll remember this",
            is_voice=False
        )
        
        # Set importance
        result = database.set_conversation_importance(
            conversation_id=conversation_id,
            importance_score=0.8
        )
        assert result is True
        
        # Get important conversations
        important_convs = database.get_important_conversations(
            user_id=123,
            threshold=0.7,
            limit=10
        )
        
        # Verify that the important conversation is returned
        assert len(important_convs) > 0
        assert any(conv['id'] == conversation_id for conv in important_convs)

    @pytest.mark.asyncio
    async def test_conversation_storage_and_retrieval(self, hierarchical_memory_manager):
        """Test storing and retrieving conversations."""
        # Store a conversation
        conversation_id = await hierarchical_memory_manager.store_conversation(
            user_id=123,
            message="What's your name?",
            response="I'm VoicePal, your AI assistant!",
            is_voice=False
        )
        
        # Verify that the conversation was stored
        assert conversation_id is not None
        
        # Get conversation context
        context = hierarchical_memory_manager.get_conversation_context(123)
        
        # Verify that the conversation is in the context
        assert "short_term_memory" in context
        assert len(context["short_term_memory"]) > 0
        
        # Verify that the conversation has the correct content
        found = False
        for conv in context["short_term_memory"]:
            if conv.get("id") == conversation_id:
                assert conv["message"] == "What's your name?"
                assert conv["response"] == "I'm VoicePal, your AI assistant!"
                assert conv["is_voice"] is False
                found = True
                break
        
        assert found, "Stored conversation not found in context"

    @pytest.mark.asyncio
    async def test_semantic_search(self, hierarchical_memory_manager, mock_qdrant_provider):
        """Test semantic search for relevant memories."""
        # Store a conversation
        await hierarchical_memory_manager.store_conversation(
            user_id=123,
            message="I like playing piano",
            response="That's wonderful! Playing piano is a great hobby.",
            is_voice=False,
            importance_score=0.8
        )
        
        # Search for relevant memories
        memories = await hierarchical_memory_manager.search_relevant_memories(
            user_id=123,
            query="What are my hobbies?",
            limit=5
        )
        
        # Verify that the search was performed
        assert mock_qdrant_provider.search_memories.called
        
        # Verify that memories were returned
        assert len(memories) > 0

    @pytest.mark.asyncio
    async def test_memory_integration_with_bot(self, database, mock_ai_provider, user_manager, 
                                              mock_redis_provider, mock_qdrant_provider, 
                                              mock_embedding_provider, mock_bot):
        """Test integrating the hierarchical memory system with the bot."""
        # Set up the mock bot
        mock_bot.database = database
        mock_bot.ai_provider = mock_ai_provider
        mock_bot.user_manager = user_manager
        
        # Mock the providers
        with patch('bot.providers.memory.redis_provider.RedisProvider', return_value=mock_redis_provider), \
             patch('bot.providers.memory.qdrant_provider.QdrantProvider', return_value=mock_qdrant_provider), \
             patch('bot.utils.embedding_utils.EmbeddingProvider', return_value=mock_embedding_provider):
            
            # Integrate the hierarchical memory system
            success = await integrate_hierarchical_memory(mock_bot)
            
            # Verify that integration was successful
            assert success is True
            
            # Verify that the memory manager was set
            assert hasattr(mock_bot, 'memory_manager')
            assert hasattr(mock_bot, 'hierarchical_memory_manager')
            assert mock_bot.memory_manager is mock_bot.hierarchical_memory_manager
            
            # Verify that the providers were set
            assert hasattr(mock_bot, 'redis_provider')
            assert hasattr(mock_bot, 'qdrant_provider')
            assert hasattr(mock_bot, 'embedding_provider')
            
            # Verify that the feature registry was updated
            mock_bot.feature_registry.register_feature.assert_called_with(
                "hierarchical_memory",
                True,
                "Hierarchical memory system with Redis and vector search"
            )
            
            # Verify that the handlers were updated
            assert mock_bot.handle_text is not None
            assert mock_bot.handle_text.__wrapped__ is not mock_bot._original_handle_text
            
            # Test the enhanced handle_text method
            update = MagicMock()
            update.effective_user.id = 123
            update.message.text = "Hello, how are you?"
            context = MagicMock()
            
            # Call the enhanced handle_text method
            await mock_bot.handle_text(update, context)
            
            # Verify that the dialog engine was called with the right parameters
            mock_bot.dialog_engine.process_message.assert_called_once()
            
            # Verify that the memory manager's store_conversation method was called
            assert hasattr(mock_bot.memory_manager, 'store_conversation')
            if hasattr(mock_bot.memory_manager, 'store_conversation'):
                # This is a bit tricky to verify since we're using AsyncMock
                # We'll just check that the method exists and was called
                assert mock_bot.memory_manager.store_conversation.called
