"""
Test script for the enhanced memory system.

This script tests the enhanced memory system functionality, including:
1. Memory manager initialization
2. Conversation context retrieval
3. Importance analysis
4. User summary generation
5. Integration with dialog engine
"""

import unittest
import os
import sys
import logging
import asyncio
from unittest.mock import MagicMock, patch, AsyncMock
from datetime import datetime, timedelta

# Add parent directory to path to import bot modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from bot.database import Database
from bot.features.enhanced_memory_manager import EnhancedMemoryManager
from bot.core.user_manager import UserManager
from bot.core.enhanced_dialog_engine import EnhancedDialogEngine

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class TestEnhancedMemorySystem(unittest.TestCase):
    """Test case for the enhanced memory system."""

    def setUp(self):
        """Set up test environment."""
        # Create in-memory database
        self.database = Database(":memory:")

        # Create user manager
        self.user_manager = UserManager(self.database)

        # Create mock AI provider
        self.ai_provider = MagicMock()
        self.ai_provider.generate_text = AsyncMock(return_value="This is a mock response")
        self.ai_provider.analyze_text = AsyncMock(return_value={"sentiment": "positive", "topics": ["test"]})

        # Create enhanced memory manager
        self.enhanced_memory_manager = EnhancedMemoryManager(
            database=self.database,
            ai_provider=self.ai_provider
        )

        # Create enhanced dialog engine
        self.enhanced_dialog_engine = EnhancedDialogEngine(
            ai_provider=self.ai_provider,
            user_manager=self.user_manager,
            memory_manager=self.enhanced_memory_manager
        )

        # Add test user
        self.test_user_id = 123456789
        self.database.add_user(
            user_id=self.test_user_id,
            username="test_user",
            first_name="Test",
            last_name="User"
        )

        # Add test conversations
        self.test_conversations = [
            ("Hello, how are you?", "I'm doing well, thank you for asking!"),
            ("What's your name?", "My name is VoicePal, your AI companion."),
            ("I'm feeling sad today.", "I'm sorry to hear that. Would you like to talk about it?")
        ]

        for message, response in self.test_conversations:
            conversation_id = self.user_manager.add_conversation_entry(
                user_id=self.test_user_id,
                message=message,
                response=response,
                is_voice=False,
                credits_used=1
            )

    def test_memory_manager_initialization(self):
        """Test memory manager initialization."""
        # Check that enhanced memory manager is initialized
        self.assertIsNotNone(self.enhanced_memory_manager)

        # Check that enhanced memory manager has the correct attributes
        self.assertEqual(self.enhanced_memory_manager.database, self.database)
        self.assertEqual(self.enhanced_memory_manager.ai_provider, self.ai_provider)

    def test_get_conversation_context(self):
        """Test get_conversation_context method."""
        # Get conversation context
        context = self.enhanced_memory_manager.get_conversation_context(self.test_user_id)

        # Check that context contains expected keys
        self.assertIn("user_data", context)
        self.assertIn("conversations", context)
        self.assertIn("preferences", context)

        # Check that user data is correct
        self.assertEqual(context["user_data"]["user_id"], self.test_user_id)
        self.assertEqual(context["user_data"]["username"], "test_user")
        self.assertEqual(context["user_data"]["first_name"], "Test")
        self.assertEqual(context["user_data"]["last_name"], "User")

        # Check that conversations are included
        self.assertEqual(len(context["conversations"]), len(self.test_conversations))

        # Check that conversations have the correct format
        for conversation in context["conversations"]:
            self.assertIn("message", conversation)
            self.assertIn("response", conversation)

    async def test_analyze_conversation_importance(self):
        """Test _analyze_conversation_importance method."""
        # Get the first conversation
        conversation_id = 1

        # Analyze conversation importance
        importance_score = await self.enhanced_memory_manager._analyze_conversation_importance(
            user_id=self.test_user_id,
            conversation_id=conversation_id,
            message=self.test_conversations[0][0],
            response=self.test_conversations[0][1]
        )

        # Check that importance score is a float between 0 and 1
        self.assertIsInstance(importance_score, float)
        self.assertGreaterEqual(importance_score, 0.0)
        self.assertLessEqual(importance_score, 1.0)

    async def test_update_user_summary(self):
        """Test update_user_summary method."""
        # Update user summary
        await self.enhanced_memory_manager.update_user_summary(self.test_user_id)

        # Get user summary
        summary = self.database.get_user_summary(self.test_user_id)

        # Check that summary exists
        self.assertIsNotNone(summary)
        self.assertIn("summary", summary)
        self.assertIn("interests", summary)
        self.assertIn("updated_at", summary)

    async def test_dialog_engine_integration(self):
        """Test integration with dialog engine."""
        # Process message with enhanced dialog engine
        response = await self.enhanced_dialog_engine.process_message(
            user_id=self.test_user_id,
            message="Do you remember my name?",
            language="en",
            is_voice=False
        )

        # Check that response is generated
        self.assertIsNotNone(response)
        self.assertIn("text", response)

        # Check that AI provider was called with context
        self.ai_provider.generate_text.assert_called_once()
        args, kwargs = self.ai_provider.generate_text.call_args
        self.assertIn("context", kwargs)

        # Check that context contains user data
        context = kwargs["context"]
        self.assertIn("user_data", context)
        self.assertEqual(context["user_data"]["user_id"], self.test_user_id)

def run_async_test(test_func):
    """Run an async test function."""
    loop = asyncio.get_event_loop()
    loop.run_until_complete(test_func())

if __name__ == "__main__":
    # Create test suite
    suite = unittest.TestSuite()

    # Add test cases
    test_case = TestEnhancedMemorySystem()
    suite.addTest(test_case)

    # Add async tests
    for method_name in dir(test_case):
        if method_name.startswith("test_") and method_name != "test_memory_manager_initialization" and method_name != "test_get_conversation_context":
            method = getattr(test_case, method_name)
            if asyncio.iscoroutinefunction(method):
                run_async_test(method)

    # Run tests
    unittest.TextTestRunner(verbosity=2).run(suite)
