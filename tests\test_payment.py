"""
Test script for the payment system.

This script tests the payment system by simulating payment flows
and verifying that the database is updated correctly.
"""

import os
import sys
import logging
import unittest
import asyncio
from unittest.mock import MagicMock, AsyncMock, patch
from datetime import datetime

# Add parent directory to path to import bot modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import bot modules
from bot.database.core import Database
from bot.payment.telegram_stars_payment import TelegramStarsPayment
from bot.payment.mock_payment import MockPayment
from bot.payment import PaymentSystem

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class MockDatabase:
    """Mock database for testing."""

    def __init__(self):
        self.users = {}
        self.transactions = []

    def get_user_credits(self, user_id):
        """Get user credits."""
        return self.users.get(user_id, {}).get('credits', 0)

    def add_credits(self, user_id, credits):
        """Add credits to user."""
        if user_id not in self.users:
            self.users[user_id] = {'credits': 0}
        self.users[user_id]['credits'] += credits
        return self.users[user_id]['credits']

    def add_transaction(self, user_id, amount, credits, transaction_id, status):
        """Add transaction."""
        transaction = {
            'user_id': user_id,
            'amount': amount,
            'credits': credits,
            'transaction_id': transaction_id,
            'status': status
        }
        self.transactions.append(transaction)
        return len(self.transactions)

class TestPaymentSystem(unittest.TestCase):
    """Test cases for the PaymentSystem class."""

    def setUp(self):
        """Set up test environment."""
        self.database = MockDatabase()
        self.payment_system = PaymentSystem(self.database, "test_payment_provider_token")

    def test_get_credit_packages(self):
        """Test getting credit packages."""
        packages = self.payment_system.get_credit_packages()
        self.assertIsNotNone(packages)
        self.assertIsInstance(packages, dict)
        self.assertGreater(len(packages), 0)

        # Check if packages have required fields
        for package_id, package in packages.items():
            self.assertIn('id', package)
            self.assertIn('title', package)
            self.assertIn('description', package)
            self.assertIn('credits', package)
            self.assertIn('price', package)
            self.assertIn('currency', package)

    def test_get_credit_package(self):
        """Test getting a specific credit package."""
        # Get existing package
        package = self.payment_system.get_credit_package('small')
        self.assertIsNotNone(package)
        self.assertEqual(package['id'], 'small')

        # Get non-existing package
        package = self.payment_system.get_credit_package('non_existing')
        self.assertIsNone(package)

    def test_create_credit_packages_keyboard(self):
        """Test creating credit packages keyboard."""
        keyboard = self.payment_system.create_credit_packages_keyboard()
        self.assertIsNotNone(keyboard)

        # Check if keyboard has buttons
        self.assertGreater(len(keyboard.inline_keyboard), 0)

        # Check if buttons have callback data
        for row in keyboard.inline_keyboard:
            for button in row:
                self.assertTrue(button.callback_data.startswith('buy_credits_'))

    @patch('telegram.Bot.send_invoice')
    async def test_start_payment(self, mock_send_invoice):
        """Test starting payment process."""
        # Mock update and context
        update = MagicMock()
        update.callback_query.from_user.id = 123456789
        context = MagicMock()
        context.bot = MagicMock()
        context.bot.send_invoice = AsyncMock()

        # Test with valid package
        await self.payment_system.start_payment(update, context, 'small')
        context.bot.send_invoice.assert_called_once()

        # Reset mock
        context.bot.send_invoice.reset_mock()

        # Test with invalid package
        await self.payment_system.start_payment(update, context, 'non_existing')
        update.callback_query.message.reply_text.assert_called_once()
        context.bot.send_invoice.assert_not_called()

    @patch('telegram.PreCheckoutQuery.answer')
    async def test_precheckout_callback(self, mock_answer):
        """Test pre-checkout callback."""
        # Mock update and context
        update = MagicMock()
        update.pre_checkout_query.invoice_payload = 'credits-small-12345'
        update.pre_checkout_query.from_user.id = 123456789
        update.pre_checkout_query.answer = AsyncMock()
        context = MagicMock()

        # Test with valid package
        await self.payment_system.precheckout_callback(update, context)
        update.pre_checkout_query.answer.assert_called_once_with(ok=True)

        # Reset mock
        update.pre_checkout_query.answer.reset_mock()

        # Test with invalid package
        update.pre_checkout_query.invoice_payload = 'credits-non_existing-12345'
        await self.payment_system.precheckout_callback(update, context)
        update.pre_checkout_query.answer.assert_called_once_with(
            ok=False,
            error_message="Selected credit package is no longer available."
        )

    async def test_successful_payment_callback(self):
        """Test successful payment callback."""
        # Mock update and context
        update = MagicMock()
        update.message.successful_payment.invoice_payload = 'credits-small-12345'
        update.message.successful_payment.total_amount = 499  # $4.99 in cents
        update.message.successful_payment.telegram_payment_charge_id = 'test_charge_id'
        update.effective_user.id = 123456789
        update.message.reply_text = AsyncMock()
        context = MagicMock()

        # Test successful payment
        await self.payment_system.successful_payment_callback(update, context)

        # Check if credits were added
        self.assertEqual(self.database.get_user_credits(123456789), 100)

        # Check if transaction was recorded
        self.assertEqual(len(self.database.transactions), 1)
        self.assertEqual(self.database.transactions[0]['user_id'], 123456789)
        self.assertEqual(self.database.transactions[0]['credits'], 100)
        self.assertEqual(self.database.transactions[0]['amount'], 4.99)
        self.assertEqual(self.database.transactions[0]['status'], 'completed')

        # Check if message was sent
        update.message.reply_text.assert_called_once()

class MockSuccessfulPayment:
    """Mock SuccessfulPayment object for testing."""

    def __init__(self, invoice_payload, total_amount, telegram_payment_charge_id):
        self.invoice_payload = invoice_payload
        self.total_amount = total_amount
        self.telegram_payment_charge_id = telegram_payment_charge_id

class MockUpdate:
    """Mock Update object for testing."""

    def __init__(self, user_id, username="test_user", first_name="Test", last_name="User"):
        self.effective_user = MagicMock()
        self.effective_user.id = user_id
        self.effective_user.username = username
        self.effective_user.first_name = first_name
        self.effective_user.last_name = last_name

        self.message = MagicMock()
        self.callback_query = MagicMock()
        self.callback_query.from_user = self.effective_user
        self.callback_query.message = self.message

    def set_successful_payment(self, payment):
        """Set successful payment for the update."""
        self.message.successful_payment = payment

class MockContext:
    """Mock Context object for testing."""

    def __init__(self):
        self.bot = MagicMock()

    async def mock_send_invoice(self, **kwargs):
        """Mock send_invoice method."""
        logger.info("Mock send_invoice called with: %s", kwargs)
        return True

    def setup_mock_methods(self):
        """Set up mock methods for the context."""
        self.bot.send_invoice = AsyncMock(side_effect=self.mock_send_invoice)

class TestPaymentProviders(unittest.TestCase):
    """Test cases for the specific payment providers."""

    def setUp(self):
        """Set up test environment."""
        # Create in-memory database for testing
        self.database = Database(":memory:")
        self.database.setup()

        # Create payment providers
        self.telegram_payment = TelegramStarsPayment(self.database)
        self.mock_payment = MockPayment(self.database)

        # Create test user
        self.user_id = 123456789
        self.database.add_user(
            user_id=self.user_id,
            username="test_user",
            first_name="Test",
            last_name="User"
        )

        # Set initial credits
        self.database.add_credits(self.user_id, 100, source="initial")

        # Create mock objects
        self.update = MockUpdate(self.user_id)
        self.context = MockContext()
        self.context.setup_mock_methods()

    def tearDown(self):
        """Clean up after tests."""
        self.database.conn.close()

    def test_get_credit_packages(self):
        """Test getting credit packages."""
        # Test Telegram Stars payment
        packages = self.telegram_payment.get_credit_packages()
        self.assertIsNotNone(packages)
        self.assertIn("small", packages)
        self.assertIn("medium", packages)
        self.assertIn("large", packages)

        # Test mock payment
        packages = self.mock_payment.get_credit_packages()
        self.assertIsNotNone(packages)
        self.assertIn("small", packages)
        self.assertIn("medium", packages)
        self.assertIn("large", packages)

    def test_process_payment(self):
        """Test processing a payment."""
        # Create a mock payment
        payment = MockSuccessfulPayment(
            invoice_payload="credits-small-12345",
            total_amount=100,
            telegram_payment_charge_id="test_charge_id"
        )

        # Process payment with Telegram Stars payment
        credits_added = self.telegram_payment.process_payment(self.user_id, payment)
        self.assertEqual(credits_added, 100)

        # Check that credits were added
        user = self.database.get_user(self.user_id)
        self.assertEqual(user["credits"], 200)

        # Check that transaction was recorded
        transactions = self.database.get_user_transactions(self.user_id)
        self.assertEqual(len(transactions), 1)
        self.assertEqual(transactions[0]["amount"], 100)
        self.assertEqual(transactions[0]["credits"], 100)
        self.assertEqual(transactions[0]["status"], "completed")

    def test_invalid_payload(self):
        """Test processing a payment with invalid payload."""
        # Create a mock payment with invalid payload
        payment = MockSuccessfulPayment(
            invoice_payload="invalid-payload",
            total_amount=100,
            telegram_payment_charge_id="test_charge_id"
        )

        # Process payment with Telegram Stars payment
        credits_added = self.telegram_payment.process_payment(self.user_id, payment)
        self.assertEqual(credits_added, 0)

        # Check that credits were not added
        user = self.database.get_user(self.user_id)
        self.assertEqual(user["credits"], 100)

        # Check that no transaction was recorded
        transactions = self.database.get_user_transactions(self.user_id)
        self.assertEqual(len(transactions), 0)

    def test_invalid_package(self):
        """Test processing a payment with invalid package."""
        # Create a mock payment with invalid package
        payment = MockSuccessfulPayment(
            invoice_payload="credits-invalid-12345",
            total_amount=100,
            telegram_payment_charge_id="test_charge_id"
        )

        # Process payment with Telegram Stars payment
        credits_added = self.telegram_payment.process_payment(self.user_id, payment)
        self.assertEqual(credits_added, 0)

        # Check that credits were not added
        user = self.database.get_user(self.user_id)
        self.assertEqual(user["credits"], 100)

        # Check that no transaction was recorded
        transactions = self.database.get_user_transactions(self.user_id)
        self.assertEqual(len(transactions), 0)

    async def test_create_invoice(self):
        """Test creating an invoice."""
        # Create an invoice with Telegram Stars payment
        result = await self.telegram_payment.create_invoice(
            chat_id=self.user_id,
            title="Test Invoice",
            description="Test Description",
            payload="credits-small-12345",
            price_cents=100,
            context=self.context
        )

        self.assertTrue(result)
        self.context.bot.send_invoice.assert_called_once()

def run_tests():
    """Run the payment system tests."""
    unittest.main()

if __name__ == "__main__":
    run_tests()
