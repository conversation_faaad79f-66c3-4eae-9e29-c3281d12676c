"""
Button state manager for VoicePal.

This module manages the state of buttons for the Telegram bot.
It ensures that button states are preserved between sessions and
provides error recovery for button callbacks.
"""

import logging
import json
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime, timedelta

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class ButtonStateManager:
    """Manages button states for the Telegram bot."""

    def __init__(self, database):
        """
        Initialize the button state manager.

        Args:
            database: Database instance
        """
        self.database = database
        self.state_cache = {}  # Cache for button states
        self.callback_history = {}  # History of processed callbacks
        self.error_recovery = {}  # Error recovery information

    def save_button_state(self, user_id: int, menu_id: str, state: Dict[str, Any]) -> bool:
        """
        Save button state for a user.

        Args:
            user_id: User ID
            menu_id: Menu identifier
            state: Button state dictionary

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Convert state to JSON string
            state_json = json.dumps(state)

            # Save to database
            self.database.update_user_preference(
                user_id=user_id,
                key=f"button_state_{menu_id}",
                value=state_json
            )

            # Update cache
            if user_id not in self.state_cache:
                self.state_cache[user_id] = {}
            self.state_cache[user_id][menu_id] = state

            return True
        except Exception as e:
            logger.error(f"Error saving button state: {e}")
            return False

    def get_button_state(self, user_id: int, menu_id: str) -> Optional[Dict[str, Any]]:
        """
        Get button state for a user.

        Args:
            user_id: User ID
            menu_id: Menu identifier

        Returns:
            Optional[Dict[str, Any]]: Button state dictionary or None if not found
        """
        try:
            # Check cache first
            if user_id in self.state_cache and menu_id in self.state_cache[user_id]:
                return self.state_cache[user_id][menu_id]

            # Get from database
            state_json = self.database.get_user_preference(
                user_id=user_id,
                key=f"button_state_{menu_id}"
            )

            if not state_json:
                return None

            # Parse JSON
            state = json.loads(state_json)

            # Update cache
            if user_id not in self.state_cache:
                self.state_cache[user_id] = {}
            self.state_cache[user_id][menu_id] = state

            return state
        except Exception as e:
            logger.error(f"Error getting button state: {e}")
            return None

    def clear_button_state(self, user_id: int, menu_id: str) -> bool:
        """
        Clear button state for a user.

        Args:
            user_id: User ID
            menu_id: Menu identifier

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Remove from database
            self.database.update_user_preference(
                user_id=user_id,
                key=f"button_state_{menu_id}",
                value=None
            )

            # Remove from cache
            if user_id in self.state_cache and menu_id in self.state_cache[user_id]:
                del self.state_cache[user_id][menu_id]

            return True
        except Exception as e:
            logger.error(f"Error clearing button state: {e}")
            return False

    def record_callback(self, user_id: int, callback_data: str) -> None:
        """
        Record a processed callback.

        Args:
            user_id: User ID
            callback_data: Callback data
        """
        if user_id not in self.callback_history:
            self.callback_history[user_id] = []

        # Add to history with timestamp
        self.callback_history[user_id].append({
            'callback_data': callback_data,
            'timestamp': datetime.now()
        })

        # Limit history size
        if len(self.callback_history[user_id]) > 20:
            self.callback_history[user_id] = self.callback_history[user_id][-20:]

    def get_last_callback(self, user_id: int) -> Optional[str]:
        """
        Get the last processed callback for a user.

        Args:
            user_id: User ID

        Returns:
            Optional[str]: Last callback data or None if not found
        """
        if user_id not in self.callback_history or not self.callback_history[user_id]:
            return None

        return self.callback_history[user_id][-1]['callback_data']

    def register_error(self, user_id: int, callback_data: str, error: Exception) -> None:
        """
        Register an error for a callback.

        Args:
            user_id: User ID
            callback_data: Callback data
            error: Exception that occurred
        """
        if user_id not in self.error_recovery:
            self.error_recovery[user_id] = {}

        self.error_recovery[user_id][callback_data] = {
            'error': str(error),
            'timestamp': datetime.now(),
            'count': self.error_recovery.get(user_id, {}).get(callback_data, {}).get('count', 0) + 1
        }

    def get_recovery_suggestion(self, user_id: int, callback_data: str) -> Optional[str]:
        """
        Get a recovery suggestion for a failed callback.

        Args:
            user_id: User ID
            callback_data: Callback data

        Returns:
            Optional[str]: Recovery suggestion or None if not available
        """
        if user_id not in self.error_recovery or callback_data not in self.error_recovery[user_id]:
            # If no specific error for this callback, suggest going back to main menu
            return "back_to_main"

        error_info = self.error_recovery[user_id][callback_data]

        # Check if error is recent (within last hour)
        if datetime.now() - error_info['timestamp'] > timedelta(hours=1):
            # If error is old, clear it and suggest going back to main menu
            if user_id in self.error_recovery and callback_data in self.error_recovery[user_id]:
                del self.error_recovery[user_id][callback_data]
            return "back_to_main"

        # Provide different suggestions based on error count
        if error_info['count'] == 1:
            return "back_to_main"  # Suggest going back to main menu
        elif error_info['count'] == 2:
            return "show_help"  # Suggest showing help
        else:
            # For persistent errors, always suggest going back to main menu
            return "back_to_main"

    def save_menu_state(self, user_id: int, menu_id: str, current_menu: str) -> bool:
        """
        Save the current menu state for a user.

        Args:
            user_id: User ID
            menu_id: Menu identifier
            current_menu: Current menu callback data

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Save as button state
            return self.save_button_state(user_id, menu_id, {"current_menu": current_menu})
        except Exception as e:
            logger.error(f"Error saving menu state: {e}")
            return False

    def get_menu_state(self, user_id: int, menu_id: str) -> Optional[str]:
        """
        Get the current menu state for a user.

        Args:
            user_id: User ID
            menu_id: Menu identifier

        Returns:
            Optional[str]: Current menu callback data or None
        """
        try:
            # Get from button state
            state = self.get_button_state(user_id, menu_id)
            if state and "current_menu" in state:
                return state["current_menu"]
            return None
        except Exception as e:
            logger.error(f"Error getting menu state: {e}")
            return None

    def clear_all_errors(self, user_id: int) -> bool:
        """
        Clear all errors for a user.

        Args:
            user_id: User ID

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if user_id in self.error_recovery:
                del self.error_recovery[user_id]
            return True
        except Exception as e:
            logger.error(f"Error clearing errors: {e}")
            return False

    def get_error_recovery_suggestion(self, user_id: int, callback_data: str) -> Optional[str]:
        """
        Get an error recovery suggestion for a user.

        This is an alias for get_recovery_suggestion for better naming consistency.

        Args:
            user_id: User ID
            callback_data: Callback data that caused the error

        Returns:
            Optional[str]: Error recovery suggestion or None
        """
        return self.get_recovery_suggestion(user_id, callback_data)

    def clear_old_data(self) -> None:
        """Clear old data from caches."""
        now = datetime.now()

        # Clear old callback history
        for user_id in list(self.callback_history.keys()):
            self.callback_history[user_id] = [
                cb for cb in self.callback_history[user_id]
                if now - cb['timestamp'] < timedelta(days=1)
            ]
            if not self.callback_history[user_id]:
                del self.callback_history[user_id]

        # Clear old error recovery data
        for user_id in list(self.error_recovery.keys()):
            for callback_data in list(self.error_recovery[user_id].keys()):
                if now - self.error_recovery[user_id][callback_data]['timestamp'] > timedelta(days=1):
                    del self.error_recovery[user_id][callback_data]
            if not self.error_recovery[user_id]:
                del self.error_recovery[user_id]
