"""
Base AI provider interface for VoicePal.

This module defines the base AI provider interface that all AI providers must implement.
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, Optional, Any

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class BaseAIProvider(ABC):
    """Base class for AI providers."""

    @abstractmethod
    async def generate_response(self, message: str, language: Optional[str] = None,
                               user_context: Optional[Dict[str, Any]] = None, **kwargs) -> Dict[str, Any]:
        """
        Generate a response to a user message.

        Args:
            message: User message
            language: Language code
            user_context: User context information (history, preferences, etc.)
            **kwargs: Additional provider-specific parameters

        Returns:
            Dict containing response text, language, etc.
        """
        pass

    @abstractmethod
    def set_personality(self, personality: str) -> None:
        """
        Set the AI personality.

        Args:
            personality: Personality type (friendly, witty, calm, motivational, thoughtful)
        """
        pass

    @abstractmethod
    def supports_feature(self, feature_name: str) -> bool:
        """
        Check if provider supports a specific feature.

        Args:
            feature_name: Name of the feature to check

        Returns:
            bool: True if the feature is supported, False otherwise
        """
        pass

    @abstractmethod
    def summarize_conversation(self, conversation_history: list[Dict[str, Any]]) -> str:
        """
        Summarize a conversation history.

        Args:
            conversation_history: List of conversation exchanges

        Returns:
            str: Summary of the conversation
        """
        pass

    @abstractmethod
    def analyze_sentiment(self, text: str) -> Dict[str, Any]:
        """
        Analyze the sentiment of a text.

        Args:
            text: Text to analyze

        Returns:
            Dict containing sentiment information
        """
        pass

    @abstractmethod
    def analyze_mood_patterns(self, mood_history: list[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Analyze mood patterns from history.

        Args:
            mood_history: List of mood entries

        Returns:
            Dict containing mood pattern analysis
        """
        pass

    @abstractmethod
    def generate_reflection_prompt(self, journal_entries: list[Dict[str, Any]]) -> str:
        """
        Generate a reflection prompt based on journal entries.

        Args:
            journal_entries: List of journal entries

        Returns:
            str: Reflection prompt
        """
        pass
