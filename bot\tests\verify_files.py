"""
<PERSON><PERSON><PERSON> to verify the files we've modified.
"""

import os
import sys
import logging
from pathlib import Path

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

def verify_files():
    """Verify the files we've modified."""
    logger.info("Verifying files...")
    
    # Base directory
    base_dir = Path(r"C:\Users\<USER>\Documents\augment-projects\MoneyMule\bot")
    
    # List of files to verify
    files = [
        base_dir / "providers" / "voice" / "processor.py",
        base_dir / "providers" / "tts" / "deepgram_provider.py",
        base_dir / "features" / "mood_entry.py",
        base_dir / "core" / "navigation_router.py",
        base_dir / "providers" / "ai" / "google_ai_provider.py",
        base_dir / "core" / "enhanced_dialog_engine.py",
        base_dir / "features" / "enhanced_memory_manager.py"
    ]
    
    # Verify each file
    for file_path in files:
        try:
            logger.info(f"Checking {file_path}...")
            
            if file_path.exists():
                # Check file size
                size = file_path.stat().st_size
                logger.info(f"File exists, size: {size} bytes")
                
                # Check if file contains expected content
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                    # Check for specific strings based on our modifications
                    checks = {
                        "processor.py": ["aura-thalia-en", "Initialized Deepgram client"],
                        "deepgram_provider.py": ["clean_voice_id", "Using cleaned voice ID"],
                        "mood_entry.py": ["handle_mood_entry_callback", "handle_mood_followup_callback"],
                        "navigation_router.py": ["_handle_add_mood_entry", "mood_entry is not available"],
                        "google_ai_provider.py": ["Remember to use their name", "Reference specific details"],
                        "enhanced_dialog_engine.py": ["Add user data to context", "Short message detected"],
                        "enhanced_memory_manager.py": ["should_preserve_context_for_short_message", "Always preserve context for short messages"]
                    }
                    
                    file_name = file_path.name
                    if file_name in checks:
                        for check_string in checks[file_name]:
                            if check_string in content:
                                logger.info(f"Found expected content: '{check_string}'")
                            else:
                                logger.warning(f"Did not find expected content: '{check_string}'")
            else:
                logger.error(f"File does not exist: {file_path}")
        except Exception as e:
            logger.error(f"Error checking {file_path}: {e}")
    
    logger.info("File verification completed")

if __name__ == "__main__":
    verify_files()
