"""
Test script for VoicePal bot voice processing.

This script tests the voice processing functionality of the VoicePal bot.
It simulates a voice message by creating a test audio file, transcribing it,
generating an AI response, and converting it back to speech.

Usage:
    python test_voice_bot.py
"""

import os
import logging
import tempfile
import asyncio
from pathlib import Path
from gtts import gTTS

from bot.providers.voice.processor import VoiceProcessor
from bot.ai_conversation import AIConversation
from bot.config import Config

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# Test text
TEST_TEXT = "Hello VoicePal, how are you today? I'm feeling a bit lonely and would like to chat."

async def create_test_audio(text):
    """Create a test audio file with the given text."""
    # Create a temporary file
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".mp3")
    temp_file_path = temp_file.name
    temp_file.close()

    # Generate speech
    tts = gTTS(text=text, lang='en', slow=False)
    tts.save(temp_file_path)

    return temp_file_path

async def test_voice_processing():
    """Test the voice processing functionality."""
    logger.info("Testing voice processing functionality...")

    # Initialize components
    voice_processor = VoiceProcessor(
        deepgram_api_key=Config.DEEPGRAM_API_KEY,
        default_language=Config.DEFAULT_LANGUAGE
    )

    ai_conversation = AIConversation(
        personality=Config.DEFAULT_PERSONALITY
    )

    try:
        # Create test audio
        logger.info(f"Creating test audio with text: '{TEST_TEXT}'")
        audio_path = await create_test_audio(TEST_TEXT)

        # Transcribe audio
        logger.info("Transcribing audio...")
        transcript = await voice_processor.transcribe_audio(audio_path)

        if not transcript:
            logger.error("Failed to transcribe audio")
            return

        logger.info(f"Transcription: '{transcript}'")

        # Get AI response
        logger.info("Generating AI response...")
        ai_response = ai_conversation.get_response(transcript)
        logger.info(f"AI response: '{ai_response}'")

        # Generate voice response
        logger.info("Generating voice response...")
        voice_response_path = voice_processor.generate_voice_response(ai_response)

        if not voice_response_path:
            logger.error("Failed to generate voice response")
            return

        # Copy to a permanent location
        output_dir = Path("test_output")
        output_dir.mkdir(exist_ok=True)

        permanent_path = output_dir / "voice_response.mp3"

        # Copy file
        with open(voice_response_path, "rb") as src, open(permanent_path, "wb") as dst:
            dst.write(src.read())

        # Clean up temporary files
        voice_processor.cleanup_temp_file(voice_response_path)
        os.unlink(audio_path)

        logger.info(f"Voice response saved to: {permanent_path}")
        logger.info("Voice processing test completed successfully!")

    except Exception as e:
        logger.error(f"Error testing voice processing: {e}")
    finally:
        # Close voice processor
        await voice_processor.close()

async def main():
    """Run all tests."""
    logger.info("Testing VoicePal bot voice processing...")

    # Create output directory
    output_dir = Path("test_output")
    output_dir.mkdir(exist_ok=True)

    # Test voice processing
    await test_voice_processing()

    logger.info("All tests completed!")

if __name__ == "__main__":
    asyncio.run(main())
