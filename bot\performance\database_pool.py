"""
Database connection pooling for VoicePal.

This module provides database connection pooling for improved performance.
"""

import sqlite3
import threading
import logging
import time
from typing import Optional, Dict, Any, List
from contextlib import contextmanager
from queue import Queue, Empty
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class PoolStats:
    """Database pool statistics."""
    total_connections: int
    active_connections: int
    idle_connections: int
    total_requests: int
    failed_requests: int
    average_wait_time: float
    peak_connections: int

class DatabaseConnection:
    """Wrapper for database connection with metadata."""
    
    def __init__(self, connection: sqlite3.Connection, pool_id: int):
        """
        Initialize database connection wrapper.
        
        Args:
            connection: SQLite connection
            pool_id: Unique pool ID for this connection
        """
        self.connection = connection
        self.pool_id = pool_id
        self.created_at = time.time()
        self.last_used = time.time()
        self.usage_count = 0
        self.is_active = False
        
        # Configure connection
        self.connection.row_factory = sqlite3.Row
        self.connection.execute("PRAGMA journal_mode=WAL")
        self.connection.execute("PRAGMA synchronous=NORMAL")
        self.connection.execute("PRAGMA cache_size=10000")
        self.connection.execute("PRAGMA temp_store=MEMORY")
    
    def execute(self, query: str, params=None):
        """Execute query on this connection."""
        self.last_used = time.time()
        self.usage_count += 1
        return self.connection.execute(query, params or ())
    
    def executemany(self, query: str, params_list):
        """Execute query multiple times on this connection."""
        self.last_used = time.time()
        self.usage_count += 1
        return self.connection.executemany(query, params_list)
    
    def commit(self):
        """Commit transaction."""
        return self.connection.commit()
    
    def rollback(self):
        """Rollback transaction."""
        return self.connection.rollback()
    
    def close(self):
        """Close the connection."""
        try:
            self.connection.close()
        except Exception as e:
            logger.warning(f"Error closing connection {self.pool_id}: {e}")

class DatabaseConnectionPool:
    """Database connection pool for SQLite."""
    
    def __init__(
        self,
        database_path: str,
        min_connections: int = 2,
        max_connections: int = 10,
        max_idle_time: int = 300,  # 5 minutes
        connection_timeout: int = 30
    ):
        """
        Initialize database connection pool.
        
        Args:
            database_path: Path to SQLite database
            min_connections: Minimum number of connections to maintain
            max_connections: Maximum number of connections allowed
            max_idle_time: Maximum idle time before closing connection (seconds)
            connection_timeout: Timeout for getting connection from pool (seconds)
        """
        self.database_path = database_path
        self.min_connections = min_connections
        self.max_connections = max_connections
        self.max_idle_time = max_idle_time
        self.connection_timeout = connection_timeout
        
        # Connection pool
        self.pool = Queue(maxsize=max_connections)
        self.active_connections: Dict[int, DatabaseConnection] = {}
        self.connection_counter = 0
        
        # Statistics
        self.stats = PoolStats(
            total_connections=0,
            active_connections=0,
            idle_connections=0,
            total_requests=0,
            failed_requests=0,
            average_wait_time=0.0,
            peak_connections=0
        )
        
        # Thread safety
        self.lock = threading.RLock()
        
        # Initialize pool
        self._initialize_pool()
        
        # Start cleanup thread
        self.cleanup_thread = threading.Thread(target=self._cleanup_idle_connections, daemon=True)
        self.cleanup_thread.start()
        
        logger.info(f"Database connection pool initialized: {min_connections}-{max_connections} connections")
    
    def _initialize_pool(self):
        """Initialize the connection pool with minimum connections."""
        with self.lock:
            for _ in range(self.min_connections):
                try:
                    conn = self._create_connection()
                    self.pool.put(conn, block=False)
                    self.stats.total_connections += 1
                except Exception as e:
                    logger.error(f"Failed to create initial connection: {e}")
    
    def _create_connection(self) -> DatabaseConnection:
        """Create a new database connection."""
        self.connection_counter += 1
        connection = sqlite3.connect(
            self.database_path,
            timeout=30,
            check_same_thread=False
        )
        
        return DatabaseConnection(connection, self.connection_counter)
    
    @contextmanager
    def get_connection(self):
        """
        Get a connection from the pool.
        
        Yields:
            DatabaseConnection: Connection from the pool
            
        Raises:
            Exception: If unable to get connection within timeout
        """
        start_time = time.time()
        connection = None
        
        try:
            # Update statistics
            with self.lock:
                self.stats.total_requests += 1
            
            # Try to get connection from pool
            try:
                connection = self.pool.get(timeout=self.connection_timeout)
            except Empty:
                # Pool is empty, try to create new connection if under limit
                with self.lock:
                    if len(self.active_connections) < self.max_connections:
                        connection = self._create_connection()
                        self.stats.total_connections += 1
                    else:
                        # Wait for connection to become available
                        connection = self.pool.get(timeout=self.connection_timeout)
            
            if not connection:
                raise Exception("Unable to get database connection")
            
            # Mark connection as active
            with self.lock:
                connection.is_active = True
                self.active_connections[connection.pool_id] = connection
                self.stats.active_connections = len(self.active_connections)
                self.stats.idle_connections = self.pool.qsize()
                self.stats.peak_connections = max(self.stats.peak_connections, self.stats.active_connections)
            
            # Update wait time statistics
            wait_time = time.time() - start_time
            self.stats.average_wait_time = (
                (self.stats.average_wait_time * (self.stats.total_requests - 1) + wait_time) /
                self.stats.total_requests
            )
            
            yield connection
            
        except Exception as e:
            logger.error(f"Failed to get database connection: {e}")
            with self.lock:
                self.stats.failed_requests += 1
            raise
        finally:
            # Return connection to pool
            if connection:
                self._return_connection(connection)
    
    def _return_connection(self, connection: DatabaseConnection):
        """Return connection to the pool."""
        try:
            with self.lock:
                connection.is_active = False
                if connection.pool_id in self.active_connections:
                    del self.active_connections[connection.pool_id]
                
                # Check if connection is still valid
                if self._is_connection_valid(connection):
                    self.pool.put(connection, block=False)
                else:
                    # Connection is invalid, close it and create a new one if needed
                    connection.close()
                    self.stats.total_connections -= 1
                    
                    # Create replacement if below minimum
                    if self.stats.total_connections < self.min_connections:
                        try:
                            new_conn = self._create_connection()
                            self.pool.put(new_conn, block=False)
                            self.stats.total_connections += 1
                        except Exception as e:
                            logger.error(f"Failed to create replacement connection: {e}")
                
                self.stats.active_connections = len(self.active_connections)
                self.stats.idle_connections = self.pool.qsize()
                
        except Exception as e:
            logger.error(f"Failed to return connection to pool: {e}")
    
    def _is_connection_valid(self, connection: DatabaseConnection) -> bool:
        """Check if connection is still valid."""
        try:
            connection.execute("SELECT 1").fetchone()
            return True
        except Exception:
            return False
    
    def _cleanup_idle_connections(self):
        """Clean up idle connections that exceed max_idle_time."""
        while True:
            try:
                time.sleep(60)  # Check every minute
                
                current_time = time.time()
                connections_to_close = []
                
                # Get all connections from pool to check idle time
                temp_connections = []
                while not self.pool.empty():
                    try:
                        conn = self.pool.get_nowait()
                        if current_time - conn.last_used > self.max_idle_time:
                            connections_to_close.append(conn)
                        else:
                            temp_connections.append(conn)
                    except Empty:
                        break
                
                # Return valid connections to pool
                for conn in temp_connections:
                    try:
                        self.pool.put_nowait(conn)
                    except:
                        pass
                
                # Close idle connections (but maintain minimum)
                with self.lock:
                    connections_to_keep = max(0, len(connections_to_close) - 
                                            (self.stats.total_connections - self.min_connections))
                    
                    for i, conn in enumerate(connections_to_close):
                        if i < len(connections_to_close) - connections_to_keep:
                            conn.close()
                            self.stats.total_connections -= 1
                        else:
                            try:
                                self.pool.put_nowait(conn)
                            except:
                                pass
                
                logger.debug(f"Cleaned up {len(connections_to_close) - connections_to_keep} idle connections")
                
            except Exception as e:
                logger.error(f"Error in connection cleanup: {e}")
    
    def get_stats(self) -> PoolStats:
        """Get pool statistics."""
        with self.lock:
            self.stats.active_connections = len(self.active_connections)
            self.stats.idle_connections = self.pool.qsize()
            return self.stats
    
    def close_all(self):
        """Close all connections in the pool."""
        with self.lock:
            # Close active connections
            for conn in list(self.active_connections.values()):
                conn.close()
            self.active_connections.clear()
            
            # Close idle connections
            while not self.pool.empty():
                try:
                    conn = self.pool.get_nowait()
                    conn.close()
                except Empty:
                    break
            
            self.stats.total_connections = 0
            self.stats.active_connections = 0
            self.stats.idle_connections = 0
        
        logger.info("All database connections closed")

# Utility functions for database optimization
def optimize_database_settings(connection: sqlite3.Connection):
    """Apply optimization settings to a database connection."""
    optimizations = [
        "PRAGMA journal_mode=WAL",
        "PRAGMA synchronous=NORMAL", 
        "PRAGMA cache_size=10000",
        "PRAGMA temp_store=MEMORY",
        "PRAGMA mmap_size=268435456",  # 256MB
        "PRAGMA page_size=4096",
        "PRAGMA auto_vacuum=INCREMENTAL"
    ]
    
    for pragma in optimizations:
        try:
            connection.execute(pragma)
        except Exception as e:
            logger.warning(f"Failed to apply optimization {pragma}: {e}")

def create_database_indexes(connection: sqlite3.Connection):
    """Create performance indexes on database tables."""
    indexes = [
        "CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at)",
        "CREATE INDEX IF NOT EXISTS idx_conversations_user_id ON conversations(user_id)",
        "CREATE INDEX IF NOT EXISTS idx_conversations_created_at ON conversations(created_at)",
        "CREATE INDEX IF NOT EXISTS idx_messages_conversation_id ON messages(conversation_id)",
        "CREATE INDEX IF NOT EXISTS idx_messages_created_at ON messages(created_at)",
        "CREATE INDEX IF NOT EXISTS idx_transactions_user_id ON transactions(user_id)",
        "CREATE INDEX IF NOT EXISTS idx_transactions_created_at ON transactions(created_at)",
        "CREATE INDEX IF NOT EXISTS idx_memories_user_id ON memories(user_id)",
        "CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id)",
        "CREATE INDEX IF NOT EXISTS idx_audit_logs_timestamp ON audit_logs(timestamp)"
    ]
    
    for index_sql in indexes:
        try:
            connection.execute(index_sql)
        except Exception as e:
            logger.warning(f"Failed to create index: {e}")
    
    connection.commit()
    logger.info("Database indexes created/verified")
