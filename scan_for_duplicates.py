"""
<PERSON><PERSON><PERSON> to scan the project for duplicate files and redundant modules.

This script:
1. Scans the entire project directory
2. Identifies potential duplicate files based on name similarity
3. Identifies files with similar content
4. Looks for redundant modules or overlapping functionality
5. Generates a report of potential issues
"""

import os
import sys
import hashlib
import re
from pathlib import Path
from collections import defaultdict
import difflib

# Define directories to ignore
IGNORE_DIRS = [
    '.git',
    'venv',
    '__pycache__',
    '.idea',
    '.vscode',
    'node_modules',
]

# Define file extensions to analyze
CODE_EXTENSIONS = [
    '.py',
    '.md',
    '.json',
    '.yml',
    '.yaml',
    '.txt',
    '.html',
    '.css',
    '.js',
    '.bat',
    '.sh',
]

# Define patterns for identifying similar files
SIMILAR_PATTERNS = [
    (r'(.+)\.bak$', r'\1'),  # backup files
    (r'(.+)\.old$', r'\1'),  # old files
    (r'(.+)\.new$', r'\1'),  # new files
    (r'(.+)_old\.(.+)$', r'\1.\2'),  # old files with extension
    (r'(.+)_new\.(.+)$', r'\1.\2'),  # new files with extension
    (r'(.+)_backup\.(.+)$', r'\1.\2'),  # backup files with extension
    (r'(.+)_copy\.(.+)$', r'\1.\2'),  # copy files with extension
    (r'(.+)_v\d+\.(.+)$', r'\1.\2'),  # versioned files
]

def get_file_hash(file_path):
    """Calculate MD5 hash of a file."""
    hash_md5 = hashlib.md5()
    try:
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    except Exception as e:
        print(f"Error hashing file {file_path}: {e}")
        return None

def get_file_content(file_path):
    """Get file content as string."""
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            return f.read()
    except Exception as e:
        print(f"Error reading file {file_path}: {e}")
        return ""

def find_similar_files(files_by_name, similarity_threshold=0.7):
    """Find files with similar names."""
    similar_files = []
    
    # Compare all file names
    file_names = list(files_by_name.keys())
    for i, name1 in enumerate(file_names):
        for name2 in file_names[i+1:]:
            similarity = difflib.SequenceMatcher(None, name1, name2).ratio()
            if similarity > similarity_threshold:
                similar_files.append((name1, name2, similarity))
    
    return similar_files

def find_similar_content(files_by_hash):
    """Find files with identical content."""
    duplicate_content = []
    
    for file_hash, paths in files_by_hash.items():
        if len(paths) > 1:
            duplicate_content.append((file_hash, paths))
    
    return duplicate_content

def find_potential_duplicates(base_dir):
    """Find potential duplicate files in the project."""
    files_by_name = defaultdict(list)
    files_by_hash = defaultdict(list)
    pattern_matches = defaultdict(list)
    
    # Walk through the directory
    for root, dirs, files in os.walk(base_dir):
        # Skip ignored directories
        dirs[:] = [d for d in dirs if d not in IGNORE_DIRS]
        
        for file in files:
            file_path = os.path.join(root, file)
            rel_path = os.path.relpath(file_path, base_dir)
            
            # Skip files with extensions not in our list
            _, ext = os.path.splitext(file)
            if ext.lower() not in CODE_EXTENSIONS:
                continue
            
            # Add to files by name
            files_by_name[file].append(rel_path)
            
            # Calculate hash and add to files by hash
            file_hash = get_file_hash(file_path)
            if file_hash:
                files_by_hash[file_hash].append(rel_path)
            
            # Check for pattern matches
            for pattern, replacement in SIMILAR_PATTERNS:
                match = re.match(pattern, file)
                if match:
                    original = re.sub(pattern, replacement, file)
                    pattern_matches[(original, file)].append(rel_path)
    
    return files_by_name, files_by_hash, pattern_matches

def analyze_module_overlap(base_dir):
    """Analyze potential module overlap based on imports and functionality."""
    module_imports = defaultdict(set)
    module_exports = defaultdict(set)
    
    # Walk through Python files
    for root, dirs, files in os.walk(base_dir):
        # Skip ignored directories
        dirs[:] = [d for d in dirs if d not in IGNORE_DIRS]
        
        for file in files:
            if not file.endswith('.py'):
                continue
                
            file_path = os.path.join(root, file)
            rel_path = os.path.relpath(file_path, base_dir)
            
            # Extract imports and exports
            content = get_file_content(file_path)
            
            # Extract imports
            import_pattern = r'^(?:from\s+(\S+)\s+import|import\s+([^,\s]+))'
            for line in content.split('\n'):
                match = re.match(import_pattern, line)
                if match:
                    imported_module = match.group(1) or match.group(2)
                    module_imports[rel_path].add(imported_module)
            
            # Extract class and function definitions as exports
            class_pattern = r'^class\s+([^\(:]+)'
            func_pattern = r'^def\s+([^\(:]+)'
            
            for line in content.split('\n'):
                class_match = re.match(class_pattern, line)
                if class_match:
                    module_exports[rel_path].add(f"class:{class_match.group(1)}")
                
                func_match = re.match(func_pattern, line)
                if func_match:
                    module_exports[rel_path].add(f"func:{func_match.group(1)}")
    
    # Find potential overlaps
    overlaps = []
    modules = list(module_exports.keys())
    
    for i, module1 in enumerate(modules):
        for module2 in modules[i+1:]:
            # Skip comparing files in the same directory
            if os.path.dirname(module1) == os.path.dirname(module2):
                continue
                
            # Check for export overlap
            common_exports = module_exports[module1].intersection(module_exports[module2])
            if common_exports:
                overlaps.append((module1, module2, common_exports))
    
    return overlaps

def main():
    """Main function."""
    base_dir = os.path.dirname(os.path.abspath(__file__))
    print(f"Scanning directory: {base_dir}")
    
    # Find potential duplicates
    files_by_name, files_by_hash, pattern_matches = find_potential_duplicates(base_dir)
    
    # Find files with similar names
    similar_files = find_similar_files(files_by_name)
    
    # Find files with identical content
    duplicate_content = find_similar_content(files_by_hash)
    
    # Analyze module overlap
    module_overlaps = analyze_module_overlap(base_dir)
    
    # Generate report
    print("\n=== DUPLICATE FILE ANALYSIS REPORT ===\n")
    
    print("1. Files with identical names:")
    for name, paths in files_by_name.items():
        if len(paths) > 1:
            print(f"  - {name}:")
            for path in paths:
                print(f"    * {path}")
    
    print("\n2. Files with similar names:")
    for name1, name2, similarity in similar_files:
        print(f"  - {name1} and {name2} (similarity: {similarity:.2f})")
        print(f"    * {', '.join(files_by_name[name1])}")
        print(f"    * {', '.join(files_by_name[name2])}")
    
    print("\n3. Files with identical content:")
    for file_hash, paths in duplicate_content:
        print(f"  - Hash: {file_hash}")
        for path in paths:
            print(f"    * {path}")
    
    print("\n4. Pattern-matched potential duplicates:")
    for (original, variant), paths in pattern_matches.items():
        print(f"  - Original: {original}, Variant: {variant}")
        for path in paths:
            print(f"    * {path}")
    
    print("\n5. Potential module overlaps:")
    for module1, module2, common_exports in module_overlaps:
        print(f"  - {module1} and {module2} share:")
        for export in common_exports:
            print(f"    * {export}")
    
    print("\n=== END OF REPORT ===")

if __name__ == "__main__":
    main()
