"""
Security audit logging for VoicePal.

This module provides comprehensive security audit logging capabilities,
with proper categorization, severity levels, and export functionality.
"""

import logging
import json
import csv
import os
import time
from typing import Dict, Any, List, Optional, Tuple, Set, Union
from datetime import datetime, timedelta
from collections import defaultdict
import sqlite3
import traceback

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# Security event severity levels
SEVERITY_INFO = "INFO"
SEVERITY_LOW = "LOW"
SEVERITY_MEDIUM = "MEDIUM"
SEVERITY_HIGH = "HIGH"
SEVERITY_CRITICAL = "CRITICAL"

# Security event categories
CATEGORY_AUTH = "AUTHENTICATION"
CATEGORY_ACCESS = "ACCESS_CONTROL"
CATEGORY_PAYMENT = "PAYMENT"
CATEGORY_ABUSE = "ABUSE"
CATEGORY_RATE_LIMIT = "RATE_LIMIT"
CATEGORY_API = "API"
CATEGORY_SYSTEM = "SYSTEM"
CATEGORY_DATA = "DATA"
CATEGORY_ADMIN = "ADMIN"
CATEGORY_USER = "USER"

class SecurityAudit:
    """
    Security audit logging for VoicePal.
    
    This class provides comprehensive security audit logging capabilities,
    with proper categorization, severity levels, and export functionality.
    """
    
    def __init__(self, database, config_manager):
        """
        Initialize the security audit logger.
        
        Args:
            database: Database instance
            config_manager: Configuration manager instance
        """
        self.database = database
        self.config_manager = config_manager
        
        # Get security audit configuration
        self.config = self.config_manager.get_feature_config("security_monitoring") or {}
        
        # Get audit log retention period (in days)
        self.retention_period = self.config.get("audit_retention_days", 90)
        
        # Initialize database tables
        self._setup_database()
        
        # Set up file logging if enabled
        self.file_logging_enabled = self.config.get("file_logging_enabled", False)
        self.log_directory = self.config.get("log_directory", "logs")
        
        if self.file_logging_enabled:
            os.makedirs(self.log_directory, exist_ok=True)
            
            # Set up file handler for security audit logs
            self.file_handler = logging.FileHandler(
                os.path.join(self.log_directory, "security_audit.log")
            )
            self.file_handler.setLevel(logging.INFO)
            self.file_handler.setFormatter(
                logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            )
            
            # Create a separate logger for security audit logs
            self.audit_logger = logging.getLogger("security_audit")
            self.audit_logger.setLevel(logging.INFO)
            self.audit_logger.addHandler(self.file_handler)
            
            # Make sure audit logger doesn't propagate to root logger
            self.audit_logger.propagate = False
        
        logger.info("Security audit logger initialized")
    
    def _setup_database(self) -> None:
        """Create security audit tables if they don't exist."""
        try:
            cursor = self.database.conn.cursor()
            
            # Create security_audit_logs table
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS security_audit_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                event_type TEXT NOT NULL,
                category TEXT NOT NULL,
                severity TEXT NOT NULL,
                user_id INTEGER,
                ip_address TEXT,
                device_id TEXT,
                description TEXT,
                details TEXT,
                source TEXT,
                success BOOLEAN DEFAULT 1
            )
            ''')
            
            # Create index on timestamp for faster queries
            cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_audit_logs_timestamp 
            ON security_audit_logs (timestamp)
            ''')
            
            # Create index on user_id for faster queries
            cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id 
            ON security_audit_logs (user_id)
            ''')
            
            # Create index on category for faster queries
            cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_audit_logs_category 
            ON security_audit_logs (category)
            ''')
            
            self.database.conn.commit()
            logger.info("Security audit tables created")
        except sqlite3.Error as e:
            logger.error(f"Error setting up security audit tables: {e}")
            raise
    
    def log_event(self, event_type: str, category: str, severity: str,
                 user_id: Optional[int] = None, ip_address: Optional[str] = None,
                 device_id: Optional[str] = None, description: Optional[str] = None,
                 details: Optional[Dict[str, Any]] = None, source: Optional[str] = None,
                 success: bool = True) -> int:
        """
        Log a security audit event.
        
        Args:
            event_type: Type of security event
            category: Category of security event
            severity: Severity level of event
            user_id: User ID associated with event
            ip_address: IP address associated with event
            device_id: Device ID associated with event
            description: Human-readable description of event
            details: Additional details as a dictionary
            source: Source of the event (e.g., module name)
            success: Whether the action was successful
            
        Returns:
            int: ID of the logged event
        """
        try:
            cursor = self.database.conn.cursor()
            
            # Convert details dictionary to JSON string
            details_json = json.dumps(details) if details else None
            
            # Get current timestamp
            timestamp = datetime.now().isoformat()
            
            # Insert event into database
            cursor.execute(
                """INSERT INTO security_audit_logs 
                   (timestamp, event_type, category, severity, user_id, ip_address, 
                    device_id, description, details, source, success) 
                   VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)""",
                (timestamp, event_type, category, severity, user_id, 
                 ip_address, device_id, description, details_json, source, success)
            )
            self.database.conn.commit()
            event_id = cursor.lastrowid
            
            # Log to file if enabled
            if self.file_logging_enabled:
                log_message = f"[{severity}] [{category}] {event_type}: {description}"
                if details:
                    log_message += f" - Details: {json.dumps(details)}"
                
                if user_id:
                    log_message += f" - User: {user_id}"
                
                if ip_address:
                    log_message += f" - IP: {ip_address}"
                
                if source:
                    log_message += f" - Source: {source}"
                
                if not success:
                    log_message += " - Failed"
                
                self.audit_logger.info(log_message)
            
            # Log to console for high severity events
            if severity in [SEVERITY_HIGH, SEVERITY_CRITICAL]:
                logger.warning(f"Security audit: {event_type} - {description}")
            
            return event_id
        except Exception as e:
            logger.error(f"Error logging security audit event: {e}")
            logger.error(traceback.format_exc())
            return -1
    
    def get_audit_logs(self, start_date: Optional[datetime] = None, 
                      end_date: Optional[datetime] = None,
                      user_id: Optional[int] = None,
                      category: Optional[str] = None,
                      severity: Optional[str] = None,
                      event_type: Optional[str] = None,
                      limit: int = 100,
                      offset: int = 0) -> List[Dict[str, Any]]:
        """
        Get security audit logs with optional filtering.
        
        Args:
            start_date: Start date for filtering
            end_date: End date for filtering
            user_id: Filter by user ID
            category: Filter by category
            severity: Filter by severity
            event_type: Filter by event type
            limit: Maximum number of logs to return
            offset: Offset for pagination
            
        Returns:
            List of audit log dictionaries
        """
        try:
            cursor = self.database.conn.cursor()
            
            # Build query
            query = "SELECT * FROM security_audit_logs WHERE 1=1"
            params = []
            
            if start_date:
                query += " AND timestamp >= ?"
                params.append(start_date.isoformat())
            
            if end_date:
                query += " AND timestamp <= ?"
                params.append(end_date.isoformat())
            
            if user_id:
                query += " AND user_id = ?"
                params.append(user_id)
            
            if category:
                query += " AND category = ?"
                params.append(category)
            
            if severity:
                query += " AND severity = ?"
                params.append(severity)
            
            if event_type:
                query += " AND event_type = ?"
                params.append(event_type)
            
            # Add order by and limit
            query += " ORDER BY timestamp DESC LIMIT ? OFFSET ?"
            params.append(limit)
            params.append(offset)
            
            # Execute query
            cursor.execute(query, params)
            logs = cursor.fetchall()
            
            # Convert to list of dictionaries
            result = []
            for log in logs:
                log_dict = dict(log)
                
                # Parse details JSON
                if log_dict.get("details"):
                    try:
                        log_dict["details"] = json.loads(log_dict["details"])
                    except json.JSONDecodeError:
                        pass
                
                result.append(log_dict)
            
            return result
        except Exception as e:
            logger.error(f"Error getting security audit logs: {e}")
            logger.error(traceback.format_exc())
            return []
    
    def get_audit_log_count(self, start_date: Optional[datetime] = None, 
                           end_date: Optional[datetime] = None,
                           user_id: Optional[int] = None,
                           category: Optional[str] = None,
                           severity: Optional[str] = None,
                           event_type: Optional[str] = None) -> int:
        """
        Get count of security audit logs with optional filtering.
        
        Args:
            start_date: Start date for filtering
            end_date: End date for filtering
            user_id: Filter by user ID
            category: Filter by category
            severity: Filter by severity
            event_type: Filter by event type
            
        Returns:
            int: Count of matching audit logs
        """
        try:
            cursor = self.database.conn.cursor()
            
            # Build query
            query = "SELECT COUNT(*) as count FROM security_audit_logs WHERE 1=1"
            params = []
            
            if start_date:
                query += " AND timestamp >= ?"
                params.append(start_date.isoformat())
            
            if end_date:
                query += " AND timestamp <= ?"
                params.append(end_date.isoformat())
            
            if user_id:
                query += " AND user_id = ?"
                params.append(user_id)
            
            if category:
                query += " AND category = ?"
                params.append(category)
            
            if severity:
                query += " AND severity = ?"
                params.append(severity)
            
            if event_type:
                query += " AND event_type = ?"
                params.append(event_type)
            
            # Execute query
            cursor.execute(query, params)
            result = cursor.fetchone()
            
            return result["count"] if result else 0
        except Exception as e:
            logger.error(f"Error getting security audit log count: {e}")
            logger.error(traceback.format_exc())
            return 0
    
    def export_audit_logs(self, file_path: str, format: str = "csv",
                         start_date: Optional[datetime] = None, 
                         end_date: Optional[datetime] = None,
                         user_id: Optional[int] = None,
                         category: Optional[str] = None,
                         severity: Optional[str] = None,
                         event_type: Optional[str] = None) -> bool:
        """
        Export security audit logs to a file.
        
        Args:
            file_path: Path to export file
            format: Export format (csv or json)
            start_date: Start date for filtering
            end_date: End date for filtering
            user_id: Filter by user ID
            category: Filter by category
            severity: Filter by severity
            event_type: Filter by event type
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Get logs with filtering
            logs = self.get_audit_logs(
                start_date=start_date,
                end_date=end_date,
                user_id=user_id,
                category=category,
                severity=severity,
                event_type=event_type,
                limit=10000  # Export up to 10,000 logs
            )
            
            if not logs:
                logger.warning("No logs to export")
                return False
            
            # Export based on format
            if format.lower() == "csv":
                with open(file_path, "w", newline="", encoding="utf-8") as f:
                    # Get field names from first log
                    fieldnames = logs[0].keys()
                    
                    writer = csv.DictWriter(f, fieldnames=fieldnames)
                    writer.writeheader()
                    
                    for log in logs:
                        # Convert details to string if it's a dictionary
                        if isinstance(log.get("details"), dict):
                            log["details"] = json.dumps(log["details"])
                        
                        writer.writerow(log)
            elif format.lower() == "json":
                with open(file_path, "w", encoding="utf-8") as f:
                    json.dump(logs, f, indent=2)
            else:
                logger.error(f"Unsupported export format: {format}")
                return False
            
            logger.info(f"Exported {len(logs)} security audit logs to {file_path}")
            return True
        except Exception as e:
            logger.error(f"Error exporting security audit logs: {e}")
            logger.error(traceback.format_exc())
            return False
    
    def cleanup_old_logs(self) -> int:
        """
        Clean up old security audit logs based on retention period.
        
        Returns:
            int: Number of logs deleted
        """
        try:
            cursor = self.database.conn.cursor()
            
            # Calculate cutoff date
            cutoff_date = (datetime.now() - timedelta(days=self.retention_period)).isoformat()
            
            # Delete old logs
            cursor.execute(
                "DELETE FROM security_audit_logs WHERE timestamp < ?",
                (cutoff_date,)
            )
            self.database.conn.commit()
            
            deleted_count = cursor.rowcount
            logger.info(f"Cleaned up {deleted_count} old security audit logs")
            
            return deleted_count
        except Exception as e:
            logger.error(f"Error cleaning up old security audit logs: {e}")
            logger.error(traceback.format_exc())
            return 0
