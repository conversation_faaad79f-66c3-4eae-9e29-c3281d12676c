"""
Rate limiting utilities for VoicePal.

This module provides rate limiting functionality to prevent abuse of the bot's
commands and API endpoints, especially for credit-related operations.
"""

import logging
import time
from typing import Dict, Any, Optional, Tuple, List
from datetime import datetime, timedelta
import sqlite3
import threading
import functools

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class RateLimiter:
    """
    Rate limiter using the token bucket algorithm.
    
    This class implements rate limiting using the token bucket algorithm,
    which allows for bursts of requests up to a certain limit, but then
    enforces a steady rate of requests over time.
    """
    
    def __init__(self, db_connection, rate: float = 1.0, max_tokens: int = 10):
        """
        Initialize the rate limiter.
        
        Args:
            db_connection: SQLite database connection
            rate: Rate at which tokens are added (tokens per second)
            max_tokens: Maximum number of tokens in the bucket
        """
        self.db = db_connection
        self.rate = rate
        self.max_tokens = max_tokens
        self.lock = threading.RLock()
        
        # Create rate limiting table if it doesn't exist
        self._setup_database()
    
    def _setup_database(self) -> None:
        """Create rate limiting table if it doesn't exist."""
        try:
            cursor = self.db.cursor()
            
            # Create rate_limits table
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS rate_limits (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                key TEXT UNIQUE,
                tokens REAL,
                last_update TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''')
            
            # Create rate_limit_logs table for tracking
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS rate_limit_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                key TEXT,
                action TEXT,
                allowed BOOLEAN,
                tokens_remaining REAL,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''')
            
            self.db.commit()
            logger.info("Rate limiting tables created")
        except sqlite3.Error as e:
            logger.error(f"Error setting up rate limiting tables: {e}")
            raise
    
    def _get_bucket(self, key: str) -> Dict[str, Any]:
        """
        Get the token bucket for a key.
        
        Args:
            key: The rate limiting key
            
        Returns:
            Dict containing bucket information
        """
        cursor = self.db.cursor()
        cursor.execute(
            "SELECT tokens, last_update FROM rate_limits WHERE key = ?",
            (key,)
        )
        result = cursor.fetchone()
        
        if result:
            return {
                'tokens': result[0],
                'last_update': datetime.fromisoformat(result[1])
            }
        else:
            # Create new bucket with max tokens
            current_time = datetime.now().isoformat()
            cursor.execute(
                "INSERT INTO rate_limits (key, tokens, last_update) VALUES (?, ?, ?)",
                (key, self.max_tokens, current_time)
            )
            self.db.commit()
            
            return {
                'tokens': self.max_tokens,
                'last_update': datetime.now()
            }
    
    def _update_bucket(self, key: str, tokens: float, last_update: datetime) -> None:
        """
        Update the token bucket for a key.
        
        Args:
            key: The rate limiting key
            tokens: New token count
            last_update: New last update time
        """
        cursor = self.db.cursor()
        cursor.execute(
            "UPDATE rate_limits SET tokens = ?, last_update = ? WHERE key = ?",
            (tokens, last_update.isoformat(), key)
        )
        self.db.commit()
    
    def _log_attempt(self, key: str, action: str, allowed: bool, tokens_remaining: float) -> None:
        """
        Log a rate limiting attempt.
        
        Args:
            key: The rate limiting key
            action: The action being rate limited
            allowed: Whether the action was allowed
            tokens_remaining: Remaining tokens after the attempt
        """
        cursor = self.db.cursor()
        cursor.execute(
            """INSERT INTO rate_limit_logs 
               (key, action, allowed, tokens_remaining) 
               VALUES (?, ?, ?, ?)""",
            (key, action, allowed, tokens_remaining)
        )
        self.db.commit()
    
    def check_rate_limit(self, key: str, tokens: int = 1, action: str = "default") -> Tuple[bool, float]:
        """
        Check if an action is allowed under rate limiting.
        
        Args:
            key: The rate limiting key
            tokens: Number of tokens to consume
            action: The action being rate limited
            
        Returns:
            Tuple of (allowed, tokens_remaining)
        """
        with self.lock:
            try:
                # Get current bucket
                bucket = self._get_bucket(key)
                current_tokens = bucket['tokens']
                last_update = bucket['last_update']
                
                # Calculate tokens to add based on time elapsed
                now = datetime.now()
                time_elapsed = (now - last_update).total_seconds()
                tokens_to_add = time_elapsed * self.rate
                
                # Add tokens up to max_tokens
                current_tokens = min(current_tokens + tokens_to_add, self.max_tokens)
                
                # Check if we have enough tokens
                if current_tokens >= tokens:
                    # Consume tokens
                    current_tokens -= tokens
                    allowed = True
                else:
                    # Not enough tokens
                    allowed = False
                
                # Update bucket
                self._update_bucket(key, current_tokens, now)
                
                # Log attempt
                self._log_attempt(key, action, allowed, current_tokens)
                
                return allowed, current_tokens
            except Exception as e:
                logger.error(f"Error checking rate limit for {key}: {e}")
                # In case of error, allow the action but log it
                return True, 0
    
    def get_retry_after(self, key: str, tokens: int = 1) -> float:
        """
        Get the time in seconds to wait before retrying.
        
        Args:
            key: The rate limiting key
            tokens: Number of tokens needed
            
        Returns:
            Seconds to wait before retrying
        """
        with self.lock:
            try:
                # Get current bucket
                bucket = self._get_bucket(key)
                current_tokens = bucket['tokens']
                
                # If we already have enough tokens, no need to wait
                if current_tokens >= tokens:
                    return 0
                
                # Calculate time needed to get enough tokens
                tokens_needed = tokens - current_tokens
                seconds_needed = tokens_needed / self.rate
                
                return max(0, seconds_needed)
            except Exception as e:
                logger.error(f"Error calculating retry time for {key}: {e}")
                # In case of error, suggest a default wait time
                return 60


def rate_limit(
    rate: float = 1.0,
    max_tokens: int = 10,
    key_func=None,
    cost: int = 1,
    action: str = None,
    exempt_func=None
):
    """
    Decorator for rate limiting functions.
    
    Args:
        rate: Rate at which tokens are added (tokens per second)
        max_tokens: Maximum number of tokens in the bucket
        key_func: Function to generate the rate limiting key
        cost: Number of tokens to consume
        action: The action name for logging
        exempt_func: Function to check if rate limiting should be bypassed
        
    Returns:
        Decorated function
    """
    def decorator(func):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # Get database connection from the first argument (assumed to be self)
            if not args or not hasattr(args[0], 'database') or not hasattr(args[0].database, 'conn'):
                logger.error("Rate limiting requires database connection")
                return await func(*args, **kwargs)
            
            db_connection = args[0].database.conn
            
            # Create rate limiter
            limiter = RateLimiter(db_connection, rate, max_tokens)
            
            # Get rate limiting key
            if key_func:
                key = key_func(*args, **kwargs)
            else:
                # Default to user_id if available
                if len(args) >= 2 and hasattr(args[1], 'effective_user'):
                    key = f"{func.__name__}:{args[1].effective_user.id}"
                else:
                    key = func.__name__
            
            # Get action name
            action_name = action or func.__name__
            
            # Check if exempt from rate limiting
            if exempt_func and exempt_func(*args, **kwargs):
                return await func(*args, **kwargs)
            
            # Check rate limit
            allowed, tokens_remaining = limiter.check_rate_limit(key, cost, action_name)
            
            if not allowed:
                # Rate limit exceeded
                retry_after = limiter.get_retry_after(key, cost)
                
                # If this is a Telegram update, reply with rate limit message
                if len(args) >= 2 and hasattr(args[1], 'effective_message'):
                    await args[1].effective_message.reply_text(
                        f"Rate limit exceeded. Please try again in {int(retry_after)} seconds."
                    )
                
                logger.warning(f"Rate limit exceeded for {key} on {action_name}")
                return None
            
            # Execute the function
            return await func(*args, **kwargs)
        
        return wrapper
    
    return decorator
