# VoicePal: System Architecture

## 1. Overview

VoicePal is designed with a modular architecture to ensure maintainability, scalability, and ease of debugging. The system consists of several core components that interact with each other and with external services.

## 2. System Components

### 2.1 High-Level Architecture Diagram

```plaintext
┌─────────────────────────────────────────────────────────────────────────────┐
│                              Oracle Cloud VM                                 │
│                                                                              │
│  ┌─────────────┐       ┌───────────────────────────────┐       ┌─────────┐  │
│  │ Telegram Bot│◄─────►│        Core Logic             │◄─────►│ Database │  │
│  └─────────────┘       │ ┌─────────────────────────┐   │       └─────────┘  │
│         ▲              │ │ Initialization Manager   │   │           ▲        │
│         │              │ └─────────────────────────┘   │           │        │
│         │              │ ┌─────────────┐ ┌───────────┐ │           │        │
│         │              │ │Dialog Engine│ │Feature Reg│ │           │        │
│         │              │ └─────────────┘ └───────────┘ │           │        │
│         │              └───────────────────────────────┘           │        │
│         │                            ▲                              │        │
│         ▼                            │                              ▼        │
│  ┌─────────────────┐                 │                      ┌─────────────┐  │
│  │  API Providers  │                 │                      │    Reflex   │  │
│  │ ┌───────────┐   │                 │                      │  Dashboard  │  │
│  │ │ Deepgram  │   │                 │                      └─────────────┘  │
│  │ └───────────┘   │                 │                              ▲        │
│  │ ┌───────────┐   │                 │                              │        │
│  │ │ Google AI │   │◄────────────────┘                              │        │
│  │ └───────────┘   │                                                │        │
│  │ ┌───────────┐   │                                                │        │
│  │ │ ElevenLabs│   │                                                │        │
│  │ └───────────┘   │                                                │        │
│  └─────────────────┘                                                │        │
│                                                                     │        │
└─────────────────────────────────────────────────────────────────────┼────────┘
                                                                      │
                                                                      ▼
                                                              ┌─────────────┐
                                                              │   Admin     │
                                                              │   Browser   │
                                                              └─────────────┘
```

### 2.2 Component Descriptions

#### 2.2.1 Telegram Bot Interface

- Handles all interactions with Telegram's Bot API
- Processes incoming messages (text and voice)
- Sends responses back to users
- Manages payment processing through Telegram Payments API

#### 2.2.2 Core Logic

- Coordinates between different system components through a modular initialization system
- Manages the conversation flow with a dialog engine
- Handles credit deduction and tracking through a payment system
- Implements business rules and logic with feature managers
- Uses dependency injection for better testability and maintainability

#### 2.2.3 Database

- Stores user information
- Tracks credit balances
- Records conversation history
- Stores system configuration
- Manages user preferences

#### 2.2.4 Voice Processing

- Integrates with Deepgram API for speech-to-text
- Prioritizes Deepgram for text-to-speech with fallback options
- Handles audio file processing and conversion
- Manages voice settings and preferences

#### 2.2.5 AI Conversation

- Implements conversation logic
- Manages different AI personalities
- Generates appropriate responses to user inputs
- Maintains conversation context and memory

#### 2.2.6 Admin Dashboard

- Provides a web interface for system administration
- Displays usage statistics and metrics
- Allows management of users and credits
- Enables configuration of system parameters
- Monitors system health and performance

## 3. Data Flow

### 3.1 Text Message Flow

```plaintext
User sends text message → Telegram Bot → Core Logic → AI Conversation →
Core Logic → Credit Deduction → Database Update →
Text Response → Telegram Bot → User receives text response
```

### 3.2 Voice Message Flow

```plaintext
User sends voice message → Telegram Bot → Voice Processing →
Deepgram API → Transcribed Text → Core Logic → AI Conversation →
Core Logic → Credit Deduction → Database Update →
Text Response → Deepgram TTS → Voice Response →
Telegram Bot → User receives voice response
```

### 3.3 Payment Flow

```plaintext
User requests credits → Telegram Bot → Payment Options →
User selects package → Telegram Stars API → Payment Processing →
Payment Confirmation → Core Logic → Credit Addition →
Database Update → Confirmation Message → Telegram Bot →
User receives confirmation
```

## 4. Database Schema

### 4.1 Entity Relationship Diagram

```plaintext
┌───────────────────┐       ┌───────────────────┐       ┌───────────────────┐
│      users        │       │   transactions    │       │   conversations   │
├───────────────────┤       ├───────────────────┤       ├───────────────────┤
│ user_id (PK)      │       │ id (PK)           │       │ id (PK)           │
│ username          │       │ user_id (FK)      │◄──────┤ user_id (FK)      │
│ first_name        │◄──────┤ amount            │       │ message           │
│ last_name         │       │ credits           │       │ response          │
│ credits           │       │ transaction_id    │       │ is_voice          │
│ created_at        │       │ status            │       │ credits_used      │
│ last_active       │       │ created_at        │       │ created_at        │
│ preferences       │       │ payment_method    │       │ context_id        │
└───────────────────┘       └───────────────────┘       └───────────────────┘
```

### 4.2 Table Descriptions

#### 4.2.1 users

- Stores information about registered users
- Tracks credit balances and activity
- Stores user preferences for voice and personality

#### 4.2.2 transactions

- Records all credit transactions
- Includes purchases and usage
- Tracks payment methods (Telegram Stars, etc.)

#### 4.2.3 conversations

- Stores conversation history
- Tracks credit usage per conversation
- Links to context for enhanced memory

## 5. Module Structure

### 5.1 Bot Module

```plaintext
bot/
├── __init__.py
├── main.py           # Entry point and Telegram bot setup
├── database/         # Database operations
│   ├── database.py   # Core database functionality
│   └── extensions/   # Database extensions for features
├── providers/        # Service providers
│   ├── ai/           # AI providers (Google AI, Groq)
│   ├── tts/          # Text-to-speech providers
│   └── voice/        # Voice processing
├── core/             # Core components
│   ├── dialog_engine.py # Dialog processing
│   ├── user_manager.py  # User management
│   ├── feature_registry.py # Feature registration
│   ├── initialization_manager.py # Modular initialization system
│   ├── navigation_router.py # Navigation routing system
│   ├── menu_manager.py # Menu management system
│   └── security_monitor.py # Security monitoring
├── features/         # Feature implementations
│   ├── memory_manager.py   # Conversation memory
│   ├── mood_tracker.py     # Mood tracking
│   ├── welcome_manager.py  # Welcome messages
│   └── button_state_manager.py # Button state management
├── payment/          # Payment handling
│   ├── telegram_stars_payment.py # Telegram Stars
│   └── payment_integration.py    # Payment integration
├── handlers/         # Message handlers
│   └── voice_settings_handlers.py # Voice settings
└── config_manager.py # Configuration management
```

### 5.2 Admin Module

```plaintext
admin/
├── __init__.py
├── app.py            # Main Reflex app
└── pages/            # Dashboard pages
```

## 6. External Dependencies

### 6.1 APIs

- Telegram Bot API: For bot interactions and UI
- Telegram Payments API: For handling payments through Telegram Stars
- Deepgram API: For speech-to-text and text-to-speech with streaming capability
- Google AI API: For AI-powered conversations using Gemini
- Groq API: For alternative AI provider using Mixtral model
- ElevenLabs API: For high-quality voice synthesis (fallback)
- Google TTS API: For text-to-speech fallback

### 6.2 Libraries

- python-telegram-bot: Framework for Telegram bot development
- deepgram-sdk: For speech-to-text and text-to-speech capabilities
- google-generativeai: For AI-powered conversations using Gemini
- groq: For alternative AI provider using Mixtral model
- elevenlabs: For high-quality voice synthesis (fallback)
- gTTS: For Google Text-to-Speech (fallback)
- SQLite3: Lightweight database for storing user data

## 7. Deployment Architecture

The system is designed to be deployed on a single Oracle Cloud Free Tier VM, with the following components:

- Python application (bot and admin modules)
- SQLite database
- Nginx web server (for admin dashboard)
- Systemd services (for process management)

## 8. Security Considerations

- API keys and tokens are stored securely using a key manager
- Secure provider factory ensures proper API key handling
- Comprehensive security monitoring and audit logging
- Payment security monitoring for fraud prevention
- Database access is restricted to the application
- Admin dashboard requires authentication
- Payment processing is handled by Telegram's secure system
- Modular initialization system ensures proper component setup
