"""
Tests for database migrations.

This module tests the MigrationManager class.
"""

import os
import pytest
import tempfile
import json
from pathlib import Path

from bot.database.core.migrations import Migration, MigrationManager
from bot.database.core.exceptions import DatabaseMigrationError

def test_migration_init():
    """Test Migration initialization."""
    migration = Migration(
        version=1,
        description="Test migration",
        up_statements=["CREATE TABLE test (id INTEGER PRIMARY KEY)"],
        down_statements=["DROP TABLE test"]
    )
    
    assert migration.version == 1
    assert migration.description == "Test migration"
    assert len(migration.up_statements) == 1
    assert len(migration.down_statements) == 1

def test_ensure_migrations_table(db_connection):
    """Test ensuring migrations table exists."""
    migration_manager = MigrationManager(db_connection)
    
    # Verify migrations table was created
    cursor = db_connection.execute(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='schema_migrations'"
    )
    table = cursor.fetchone()
    assert table is not None

def test_add_migration(db_connection):
    """Test adding a migration."""
    migration_manager = MigrationManager(db_connection)
    
    migration = Migration(
        version=2,
        description="Test migration",
        up_statements=["CREATE TABLE test (id INTEGER PRIMARY KEY)"],
        down_statements=["DROP TABLE test"]
    )
    
    migration_manager.add_migration(migration)
    assert 2 in migration_manager.migrations
    
    # Test adding duplicate migration
    with pytest.raises(ValueError):
        migration_manager.add_migration(migration)

def test_get_current_version(db_connection):
    """Test getting current schema version."""
    migration_manager = MigrationManager(db_connection)
    
    # Empty migrations table
    version = migration_manager.get_current_version()
    assert version == 0
    
    # Add a migration record
    db_connection.execute(
        "INSERT INTO schema_migrations (version, description) VALUES (?, ?)",
        (1, "Initial schema")
    )
    db_connection.commit()
    
    version = migration_manager.get_current_version()
    assert version == 1

def test_get_pending_migrations(db_connection):
    """Test getting pending migrations."""
    migration_manager = MigrationManager(db_connection)
    
    # Add migration records
    db_connection.execute(
        "INSERT INTO schema_migrations (version, description) VALUES (?, ?)",
        (1, "Initial schema")
    )
    db_connection.commit()
    
    # Add migrations to manager
    migration1 = Migration(
        version=1,
        description="Initial schema",
        up_statements=[],
        down_statements=[]
    )
    migration2 = Migration(
        version=2,
        description="Second migration",
        up_statements=["CREATE TABLE test (id INTEGER PRIMARY KEY)"],
        down_statements=["DROP TABLE test"]
    )
    migration3 = Migration(
        version=3,
        description="Third migration",
        up_statements=["ALTER TABLE test ADD COLUMN name TEXT"],
        down_statements=["ALTER TABLE test DROP COLUMN name"]
    )
    
    migration_manager.add_migration(migration1)
    migration_manager.add_migration(migration2)
    migration_manager.add_migration(migration3)
    
    # Get pending migrations
    pending = migration_manager.get_pending_migrations()
    assert len(pending) == 2
    assert pending[0].version == 2
    assert pending[1].version == 3

def test_migrate_up(db_connection):
    """Test migrating up."""
    migration_manager = MigrationManager(db_connection)
    
    # Add migrations to manager
    migration1 = Migration(
        version=1,
        description="Initial schema",
        up_statements=["CREATE TABLE test1 (id INTEGER PRIMARY KEY)"],
        down_statements=["DROP TABLE test1"]
    )
    migration2 = Migration(
        version=2,
        description="Second migration",
        up_statements=["CREATE TABLE test2 (id INTEGER PRIMARY KEY)"],
        down_statements=["DROP TABLE test2"]
    )
    
    migration_manager.add_migration(migration1)
    migration_manager.add_migration(migration2)
    
    # Migrate to version 2
    migration_manager.migrate(2)
    
    # Verify tables were created
    cursor = db_connection.execute(
        "SELECT name FROM sqlite_master WHERE type='table' AND name IN ('test1', 'test2')"
    )
    tables = [row["name"] for row in cursor.fetchall()]
    assert "test1" in tables
    assert "test2" in tables
    
    # Verify migration records
    cursor = db_connection.execute(
        "SELECT version FROM schema_migrations ORDER BY version"
    )
    versions = [row["version"] for row in cursor.fetchall()]
    assert versions == [1, 2]

def test_migrate_down(db_connection):
    """Test migrating down."""
    migration_manager = MigrationManager(db_connection)
    
    # Add migrations to manager
    migration1 = Migration(
        version=1,
        description="Initial schema",
        up_statements=["CREATE TABLE test1 (id INTEGER PRIMARY KEY)"],
        down_statements=["DROP TABLE test1"]
    )
    migration2 = Migration(
        version=2,
        description="Second migration",
        up_statements=["CREATE TABLE test2 (id INTEGER PRIMARY KEY)"],
        down_statements=["DROP TABLE test2"]
    )
    
    migration_manager.add_migration(migration1)
    migration_manager.add_migration(migration2)
    
    # Migrate to version 2
    migration_manager.migrate(2)
    
    # Migrate back to version 1
    migration_manager.migrate(1)
    
    # Verify test2 was dropped
    cursor = db_connection.execute(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='test2'"
    )
    table = cursor.fetchone()
    assert table is None
    
    # Verify test1 still exists
    cursor = db_connection.execute(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='test1'"
    )
    table = cursor.fetchone()
    assert table is not None
    
    # Verify migration records
    cursor = db_connection.execute(
        "SELECT version FROM schema_migrations ORDER BY version"
    )
    versions = [row["version"] for row in cursor.fetchall()]
    assert versions == [1]
