#!/usr/bin/env python3
"""
Simple test runner that doesn't depend on external packages.
"""

import sys
import traceback
from unittest.mock import MagicMock

def test_basic_functionality():
    """Test basic Python functionality."""
    print("Testing basic functionality...")
    
    # Test 1: Basic arithmetic
    assert 1 + 1 == 2, "Basic arithmetic failed"
    print("✅ Basic arithmetic works")
    
    # Test 2: String operations
    assert "hello" + " world" == "hello world", "String concatenation failed"
    print("✅ String operations work")
    
    # Test 3: List operations
    test_list = [1, 2, 3]
    test_list.append(4)
    assert len(test_list) == 4, "List operations failed"
    print("✅ List operations work")
    
    return True

def test_mock_functionality():
    """Test that mocking works."""
    print("\nTesting mock functionality...")
    
    try:
        # Test 1: Create a mock
        mock_obj = MagicMock()
        mock_obj.test_method.return_value = "mocked_result"
        
        result = mock_obj.test_method()
        assert result == "mocked_result", "Mock return value failed"
        print("✅ Mock return values work")
        
        # Test 2: Mock call tracking
        mock_obj.another_method("test_arg")
        mock_obj.another_method.assert_called_with("test_arg")
        print("✅ Mock call tracking works")
        
        return True
    except Exception as e:
        print(f"❌ Mock functionality failed: {e}")
        return False

def test_redis_provider_mock():
    """Test a mock version of the Redis provider."""
    print("\nTesting Redis provider mock...")
    
    try:
        class MockRedisProvider:
            def __init__(self, config=None):
                self.config = config or {}
                self._available = True
                self._data = {}
            
            def is_available(self):
                return self._available
            
            def set(self, key, value, ttl=None):
                if not self._available:
                    return False
                self._data[key] = value
                return True
            
            def get(self, key):
                if not self._available:
                    return None
                return self._data.get(key)
            
            def delete(self, key):
                if not self._available:
                    return False
                if key in self._data:
                    del self._data[key]
                return True
        
        # Test the mock Redis provider
        provider = MockRedisProvider({"url": "redis://localhost:6379"})
        
        # Test availability
        assert provider.is_available() is True, "Redis provider should be available"
        print("✅ Redis provider availability check works")
        
        # Test set/get
        assert provider.set("test_key", "test_value") is True, "Redis set should succeed"
        assert provider.get("test_key") == "test_value", "Redis get should return correct value"
        print("✅ Redis provider set/get works")
        
        # Test delete
        assert provider.delete("test_key") is True, "Redis delete should succeed"
        assert provider.get("test_key") is None, "Redis get should return None after delete"
        print("✅ Redis provider delete works")
        
        return True
    except Exception as e:
        print(f"❌ Redis provider mock failed: {e}")
        traceback.print_exc()
        return False

def test_qdrant_provider_mock():
    """Test a mock version of the Qdrant provider."""
    print("\nTesting Qdrant provider mock...")
    
    try:
        class MockQdrantProvider:
            def __init__(self, config=None):
                self.config = config or {}
                self._available = True
                self._memories = []
            
            def is_available(self):
                return self._available
            
            def store_memory(self, user_id, vector, text, metadata=None, memory_id=None):
                if not self._available:
                    return False
                
                memory = {
                    "id": memory_id or f"memory_{len(self._memories)}",
                    "user_id": user_id,
                    "vector": vector,
                    "text": text,
                    "metadata": metadata or {}
                }
                self._memories.append(memory)
                return True
            
            def search_memories(self, user_id, vector, limit=5, score_threshold=0.5):
                if not self._available:
                    return []
                
                # Simple mock search - return memories for the user
                user_memories = [m for m in self._memories if m["user_id"] == user_id]
                return user_memories[:limit]
        
        # Test the mock Qdrant provider
        provider = MockQdrantProvider({"collection_name": "test_memories"})
        
        # Test availability
        assert provider.is_available() is True, "Qdrant provider should be available"
        print("✅ Qdrant provider availability check works")
        
        # Test store memory
        test_vector = [0.1] * 384
        assert provider.store_memory(
            user_id=123,
            vector=test_vector,
            text="Test memory",
            metadata={"importance": 0.8}
        ) is True, "Qdrant store_memory should succeed"
        print("✅ Qdrant provider store_memory works")
        
        # Test search memories
        memories = provider.search_memories(user_id=123, vector=test_vector, limit=5)
        assert len(memories) == 1, "Should find one memory"
        assert memories[0]["text"] == "Test memory", "Memory text should match"
        print("✅ Qdrant provider search_memories works")
        
        return True
    except Exception as e:
        print(f"❌ Qdrant provider mock failed: {e}")
        traceback.print_exc()
        return False

def test_embedding_utils_mock():
    """Test a mock version of the embedding utilities."""
    print("\nTesting embedding utilities mock...")
    
    try:
        import random
        
        class MockEmbeddingProvider:
            def __init__(self, config=None):
                self.config = config or {}
                self._available = True
                self._vector_size = self.config.get("vector_size", 384)
            
            def is_available(self):
                return self._available
            
            def generate_embedding(self, text):
                if not self._available:
                    return None
                
                # Generate a mock embedding
                return [random.random() for _ in range(self._vector_size)]
            
            def cosine_similarity(self, vec1, vec2):
                if len(vec1) != len(vec2):
                    return 0.0
                
                # Simple dot product for mock similarity
                dot_product = sum(a * b for a, b in zip(vec1, vec2))
                magnitude1 = sum(a * a for a in vec1) ** 0.5
                magnitude2 = sum(b * b for b in vec2) ** 0.5
                
                if magnitude1 == 0 or magnitude2 == 0:
                    return 0.0
                
                return dot_product / (magnitude1 * magnitude2)
        
        # Test the mock embedding provider
        provider = MockEmbeddingProvider({"vector_size": 384})
        
        # Test availability
        assert provider.is_available() is True, "Embedding provider should be available"
        print("✅ Embedding provider availability check works")
        
        # Test generate embedding
        embedding = provider.generate_embedding("Test text")
        assert embedding is not None, "Should generate an embedding"
        assert len(embedding) == 384, "Embedding should have correct size"
        print("✅ Embedding provider generate_embedding works")
        
        # Test cosine similarity
        vec1 = [1.0] * 10
        vec2 = [1.0] * 10
        similarity = provider.cosine_similarity(vec1, vec2)
        assert abs(similarity - 1.0) < 0.001, "Identical vectors should have similarity 1.0"
        print("✅ Embedding provider cosine_similarity works")
        
        return True
    except Exception as e:
        print(f"❌ Embedding utilities mock failed: {e}")
        traceback.print_exc()
        return False

def run_all_tests():
    """Run all tests."""
    print("Simple Test Runner for Hierarchical Memory System")
    print("=" * 60)
    
    tests = [
        ("Basic Functionality", test_basic_functionality),
        ("Mock Functionality", test_mock_functionality),
        ("Redis Provider Mock", test_redis_provider_mock),
        ("Qdrant Provider Mock", test_qdrant_provider_mock),
        ("Embedding Utils Mock", test_embedding_utils_mock),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                failed += 1
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            failed += 1
            print(f"❌ {test_name} FAILED with exception: {e}")
            traceback.print_exc()
    
    print(f"\n{'='*60}")
    print(f"Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All tests passed!")
        return True
    else:
        print("❌ Some tests failed.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
