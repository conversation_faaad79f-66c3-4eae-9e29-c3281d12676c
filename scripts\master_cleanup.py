"""
Master script to clean up the codebase.

This script runs all the cleanup scripts in the correct order to remove
duplicate implementations, update imports, and clean up outdated components.
"""

import os
import sys
import logging
import subprocess
from typing import List, Tuple

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# Define cleanup scripts to run in order
CLEANUP_SCRIPTS = [
    "scripts/update_imports_for_enhanced.py",  # Update imports to use enhanced implementations
    "scripts/cleanup_tts_providers.py",  # Clean up TTS providers
    "scripts/cleanup_codebase.py",  # Remove duplicate implementations and outdated components
    "scripts/update_requirements.py",  # Update requirements.txt
]

def run_script(script_path: str) -> Tuple[bool, str]:
    """
    Run a Python script.
    
    Args:
        script_path: Path to the script
        
    Returns:
        Tuple containing success flag and output
    """
    try:
        logger.info(f"Running {script_path}...")
        
        # Run script
        result = subprocess.run(
            [sys.executable, script_path],
            capture_output=True,
            text=True
        )
        
        # Check result
        if result.returncode == 0:
            logger.info(f"{script_path} completed successfully")
            return True, result.stdout
        else:
            logger.error(f"{script_path} failed with exit code {result.returncode}")
            logger.error(f"Error output: {result.stderr}")
            return False, result.stderr
    except Exception as e:
        logger.error(f"Error running {script_path}: {e}")
        return False, str(e)

def run_cleanup_scripts() -> List[Tuple[str, bool, str]]:
    """
    Run all cleanup scripts.
    
    Returns:
        List of tuples containing script path, success flag, and output
    """
    results = []
    
    for script_path in CLEANUP_SCRIPTS:
        success, output = run_script(script_path)
        results.append((script_path, success, output))
        
        # Stop if a script fails
        if not success:
            logger.error(f"Stopping cleanup due to failure in {script_path}")
            break
    
    return results

def main():
    """Main function."""
    logger.info("Starting master cleanup")
    
    # Run cleanup scripts
    results = run_cleanup_scripts()
    
    # Print summary
    logger.info("\nCleanup Summary:")
    for script_path, success, _ in results:
        status = "✅ Success" if success else "❌ Failed"
        logger.info(f"  {status}: {script_path}")
    
    # Check if all scripts succeeded
    if all(success for _, success, _ in results):
        logger.info("\nAll cleanup scripts completed successfully")
        return 0
    else:
        logger.error("\nSome cleanup scripts failed. See log for details.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
