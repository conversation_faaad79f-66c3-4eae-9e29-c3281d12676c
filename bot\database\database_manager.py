"""
Database manager for VoicePal.

This module provides the main Database class that integrates all database components.
"""

import os
import logging
import sqlite3
from typing import Dict, List, Any, Optional, Type, TypeVar, Union
from pathlib import Path
from contextlib import contextmanager

from bot.database.core.exceptions import (
    DatabaseError,
    DatabaseConnectionError,
    DatabaseSchemaError,
    DatabaseMigrationError
)
from bot.database.core.connection import DatabaseConnection
from bot.database.schema_manager import SchemaManager

from bot.database.repositories.user_repository import (
    UserRepository,
    UserPreferenceRepository,
    UserStatRepository
)
from bot.database.repositories.conversation_repository import (
    ConversationRepository,
    MessageRepository,
    MessageMetadataRepository
)
from bot.database.repositories.payment_repository import (
    TransactionRepository,
    PaymentPackageRepository,
    SubscriptionRepository
)
from bot.database.repositories.memory_repository import (
    MemoryRepository,
    MemoryTagRepository
)
from bot.database.repositories.voice_repository import (
    VoiceSettingRepository,
    VoiceRecordingRepository
)

# Set up logging
logger = logging.getLogger(__name__)

class DatabaseManager:
    """Main Database class for VoicePal."""
    
    def __init__(self, db_path: Union[str, Path], migrations_dir: Optional[Union[str, Path]] = None):
        """Initialize database.
        
        Args:
            db_path: Path to SQLite database file
            migrations_dir: Directory containing migration files
        """
        self.db_path = Path(db_path)
        self.migrations_dir = Path(migrations_dir) if migrations_dir else None
        self.connection = None
        self.schema_manager = None
        
        # Create database directory if it doesn't exist
        os.makedirs(self.db_path.parent, exist_ok=True)
        
        # Initialize database
        self._initialize()
        
        # Initialize repositories
        self._init_repositories()
    
    def _initialize(self) -> None:
        """Initialize database connection and managers.
        
        Raises:
            DatabaseConnectionError: If connection fails
            DatabaseSchemaError: If schema initialization fails
        """
        try:
            # Initialize connection
            self.connection = DatabaseConnection(self.db_path)
            
            # Initialize schema manager
            self.schema_manager = SchemaManager(self.connection, self.migrations_dir)
            
            # Initialize schema
            self.schema_manager.initialize_schema()
            
            logger.info(f"Database initialized: {self.db_path}")
        except Exception as e:
            logger.error(f"Failed to initialize database: {e}")
            raise DatabaseError(f"Failed to initialize database: {e}") from e
    
    def _init_repositories(self) -> None:
        """Initialize repositories."""
        # User domain
        self.users = UserRepository(self.connection)
        self.user_preferences = UserPreferenceRepository(self.connection)
        self.user_stats = UserStatRepository(self.connection)
        
        # Conversation domain
        self.conversations = ConversationRepository(self.connection)
        self.messages = MessageRepository(self.connection)
        self.message_metadata = MessageMetadataRepository(self.connection)
        
        # Payment domain
        self.transactions = TransactionRepository(self.connection)
        self.payment_packages = PaymentPackageRepository(self.connection)
        self.subscriptions = SubscriptionRepository(self.connection)
        
        # Memory domain
        self.memories = MemoryRepository(self.connection)
        self.memory_tags = MemoryTagRepository(self.connection)
        
        # Voice domain
        self.voice_settings = VoiceSettingRepository(self.connection)
        self.voice_recordings = VoiceRecordingRepository(self.connection)
    
    def close(self) -> None:
        """Close database connection.
        
        Raises:
            DatabaseConnectionError: If close fails
        """
        if self.connection:
            self.connection.close()
            logger.info("Database connection closed")
    
    @contextmanager
    def transaction(self):
        """Context manager for database transactions.
        
        Yields:
            None
            
        Raises:
            DatabaseError: If transaction fails
        """
        if not self.connection:
            raise DatabaseConnectionError("Database connection not initialized")
        
        try:
            self.connection.begin_transaction()
            yield
            self.connection.commit()
        except Exception as e:
            self.connection.rollback()
            logger.error(f"Transaction failed: {e}")
            raise DatabaseError(f"Transaction failed: {e}") from e
    
    def migrate(self, target_version: Optional[int] = None) -> None:
        """Migrate database schema to target version.
        
        Args:
            target_version: Target schema version (default: latest)
            
        Raises:
            DatabaseMigrationError: If migration fails
        """
        if not self.schema_manager:
            raise DatabaseMigrationError("Schema manager not initialized")
        
        self.schema_manager.migrate(target_version)
    
    def get_current_version(self) -> int:
        """Get current schema version.
        
        Returns:
            Current schema version
            
        Raises:
            DatabaseSchemaError: If version retrieval fails
        """
        if not self.schema_manager:
            raise DatabaseSchemaError("Schema manager not initialized")
        
        return self.schema_manager.get_current_version()
    
    def validate_schema(self) -> bool:
        """Validate database schema.
        
        Returns:
            True if schema is valid, False otherwise
            
        Raises:
            DatabaseSchemaError: If validation fails
        """
        if not self.schema_manager:
            raise DatabaseSchemaError("Schema manager not initialized")
        
        return self.schema_manager.validate_schema()
    
    def execute(self, query: str, params: Optional[Union[tuple, dict]] = None):
        """Execute SQL query.
        
        Args:
            query: SQL query
            params: Query parameters
            
        Returns:
            Cursor object
            
        Raises:
            DatabaseError: If query execution fails
        """
        if not self.connection:
            raise DatabaseConnectionError("Database connection not initialized")
        
        return self.connection.execute(query, params)
    
    def executemany(self, query: str, params_list: List[Union[tuple, dict]]):
        """Execute SQL query with multiple parameter sets.
        
        Args:
            query: SQL query
            params_list: List of parameter sets
            
        Returns:
            Cursor object
            
        Raises:
            DatabaseError: If query execution fails
        """
        if not self.connection:
            raise DatabaseConnectionError("Database connection not initialized")
        
        return self.connection.executemany(query, params_list)
    
    def commit(self) -> None:
        """Commit transaction.
        
        Raises:
            DatabaseError: If commit fails
        """
        if not self.connection:
            raise DatabaseConnectionError("Database connection not initialized")
        
        self.connection.commit()
    
    def rollback(self) -> None:
        """Rollback transaction.
        
        Raises:
            DatabaseError: If rollback fails
        """
        if not self.connection:
            raise DatabaseConnectionError("Database connection not initialized")
        
        self.connection.rollback()
    
    def __enter__(self):
        """Enter context manager.
        
        Returns:
            Database instance
        """
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Exit context manager.
        
        Args:
            exc_type: Exception type
            exc_val: Exception value
            exc_tb: Exception traceback
        """
        self.close()
