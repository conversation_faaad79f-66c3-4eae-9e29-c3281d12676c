"""
Full flow test script for VoicePal.

This script demonstrates the full flow of VoicePal:
1. Audio to Audio: Send audio and get audio back with Dia TTS
2. Text to Text: Send text and get text back with Google AI

Usage:
    python test_full_flow.py --mode audio  # Test audio to audio flow
    python test_full_flow.py --mode text   # Test text to text flow
"""

import os
import logging
import asyncio
import tempfile
import argparse
from pathlib import Path
from gtts import gTTS

# Load environment variables
import load_env

from bot.config import Config
from bot.providers.ai.google_ai_conversation import GoogleAIConversation
from bot.ai_conversation import AIConversation
from bot.providers.voice.processor import VoiceProcessor
from bot.tts_providers import TTSProviderFactory

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

async def create_test_audio(text: str) -> str:
    """
    Create a test audio file with the given text.

    Args:
        text: Text to convert to speech

    Returns:
        Path to the audio file
    """
    # Create a temporary file
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".mp3")
    temp_file_path = temp_file.name
    temp_file.close()

    # Generate speech
    tts = gTTS(text=text)
    tts.save(temp_file_path)

    logger.info(f"Created test audio file: {temp_file_path}")
    return temp_file_path

async def test_audio_to_audio_flow():
    """Test the audio to audio flow with Dia TTS."""
    logger.info("Testing Audio to Audio flow with Dia TTS")

    # Check if required API keys are available
    if not Config.DEEPGRAM_API_KEY:
        logger.error("No Deepgram API key provided. Set DEEPGRAM_API_KEY in environment variables.")
        return

    if not Config.HF_API_TOKEN:
        logger.error("No Hugging Face API token provided. Set HF_API_TOKEN in environment variables.")
        return

    # Initialize AI conversation (using simple for this test)
    ai_conversation = AIConversation(personality="friendly")

    # Initialize voice processor with Dia TTS
    voice_processor = VoiceProcessor(
        deepgram_api_key=Config.DEEPGRAM_API_KEY,
        default_language="en",
        tts_provider="dia",
        tts_provider_options={
            "api_token": Config.HF_API_TOKEN,
            "model_id": "nari-labs/Dia-1.6B",
            "use_cache": True
        },
        personality="friendly"
    )

    # Test phrases
    test_phrases = [
        "Hello, how are you today?",
        "I'm feeling great, thanks for asking!",
        "What's the weather like?",
        "Tell me a joke."
    ]

    for i, phrase in enumerate(test_phrases):
        logger.info(f"\nTest {i+1}: '{phrase}'")

        # Create test audio
        audio_path = await create_test_audio(phrase)

        try:
            # Transcribe audio
            transcript = await voice_processor.transcribe_audio(audio_path)

            if not transcript:
                logger.error("Failed to transcribe audio")
                continue

            logger.info(f"Transcribed: '{transcript}'")

            # Get AI response
            ai_response = ai_conversation.get_response(transcript)
            logger.info(f"AI response: '{ai_response}'")

            # Generate voice response
            voice_file_path = voice_processor.generate_voice_response(
                text=ai_response,
                personality="friendly"
            )

            if voice_file_path:
                logger.info(f"Voice response generated: {voice_file_path}")

                # Play audio if on a system with audio playback
                if os.name == 'posix':  # Linux/Mac
                    try:
                        if os.path.exists("/usr/bin/aplay"):  # Linux
                            os.system(f"aplay {voice_file_path}")
                        elif os.path.exists("/usr/bin/afplay"):  # Mac
                            os.system(f"afplay {voice_file_path}")
                    except Exception as e:
                        logger.error(f"Error playing audio: {e}")
                elif os.name == 'nt':  # Windows
                    try:
                        os.system(f"start {voice_file_path}")
                    except Exception as e:
                        logger.error(f"Error playing audio: {e}")
            else:
                logger.error("Failed to generate voice response")

        finally:
            # Clean up
            if os.path.exists(audio_path):
                os.unlink(audio_path)

    logger.info("\nAudio to Audio test completed")

async def test_text_to_text_flow():
    """Test the text to text flow with Google AI."""
    logger.info("Testing Text to Text flow with Google AI")

    # Check if required API keys are available
    if not Config.GOOGLE_AI_API_KEY:
        logger.error("No Google AI API key provided. Set GOOGLE_AI_API_KEY in environment variables.")
        return

    # Initialize Google AI conversation
    ai_conversation = GoogleAIConversation(
        api_key=Config.GOOGLE_AI_API_KEY,
        personality="friendly"
    )

    # Test phrases
    test_phrases = [
        "Hello, how are you today?",
        "Tell me about yourself.",
        "What's the weather like?",
        "I'm feeling sad today.",
        "Tell me a joke."
    ]

    for i, phrase in enumerate(test_phrases):
        logger.info(f"\nTest {i+1}: '{phrase}'")

        # Get AI response
        ai_response = ai_conversation.get_response(phrase)
        logger.info(f"Google AI response: '{ai_response}'")

    logger.info("\nText to Text test completed")

async def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Test full flow of VoicePal")
    parser.add_argument("--mode", choices=["audio", "text"], default="text",
                        help="Test mode: audio (Audio to Audio with Dia) or text (Text to Text with Google AI)")
    args = parser.parse_args()

    if args.mode == "audio":
        await test_audio_to_audio_flow()
    else:
        await test_text_to_text_flow()

if __name__ == "__main__":
    asyncio.run(main())
