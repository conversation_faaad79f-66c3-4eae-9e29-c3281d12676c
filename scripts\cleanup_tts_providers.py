"""
<PERSON><PERSON><PERSON> to clean up TTS providers in the codebase.

This script identifies and removes duplicate or outdated TTS providers,
and updates imports to use the preferred implementations.
"""

import os
import sys
import re
import shutil
import logging
from typing import Dict, List, Set, Tuple, Any
from pathlib import Path

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# Define TTS provider preferences
# Higher number means higher preference
TTS_PROVIDER_PREFERENCES = {
    "deepgram_provider.py": 5,  # Preferred provider
    "elevenlabs_provider.py": 4,
    "google_provider.py": 3,
    "dia_provider.py": 1,  # Outdated provider
}

# Define import replacements for TTS providers
TTS_IMPORT_REPLACEMENTS = {
    "from bot.providers.tts.dia_provider import DiaTTSProvider": 
        "from bot.providers.tts.deepgram_provider import DeepgramTTSProvider",
    "import bot.providers.tts.dia_provider": 
        "import bot.providers.tts.deepgram_provider",
    "from bot.providers.tts import dia_provider": 
        "from bot.providers.tts import deepgram_provider",
}

def backup_file(file_path: str) -> str:
    """
    Create a backup of a file.
    
    Args:
        file_path: Path to the file
        
    Returns:
        Path to the backup file
    """
    backup_path = f"{file_path}.bak"
    try:
        shutil.copy2(file_path, backup_path)
        logger.info(f"Created backup: {backup_path}")
        return backup_path
    except Exception as e:
        logger.error(f"Error creating backup of {file_path}: {e}")
        return ""

def find_tts_providers() -> Dict[str, List[str]]:
    """
    Find all TTS providers in the codebase.
    
    Returns:
        Dict mapping provider types to file paths
    """
    providers = {}
    
    # Look for TTS providers in the providers directory
    tts_dir = "bot/providers/tts"
    if os.path.exists(tts_dir):
        for file in os.listdir(tts_dir):
            if file.endswith(".py") and file != "__init__.py" and file != "base.py":
                provider_type = file.split("_")[0] if "_" in file else file.split(".")[0]
                if provider_type not in providers:
                    providers[provider_type] = []
                providers[provider_type].append(os.path.join(tts_dir, file))
    
    # Look for TTS providers in the root directory
    for file in os.listdir("bot"):
        if file.endswith("_tts_provider.py") or file.endswith("_tts.py"):
            provider_type = file.split("_")[0]
            if provider_type not in providers:
                providers[provider_type] = []
            providers[provider_type].append(os.path.join("bot", file))
    
    return providers

def get_preferred_provider(providers: List[str]) -> str:
    """
    Get the preferred provider from a list of providers.
    
    Args:
        providers: List of provider file paths
        
    Returns:
        Path to the preferred provider
    """
    if not providers:
        return ""
    
    # Get the provider with the highest preference
    preferred = providers[0]
    max_preference = 0
    
    for provider in providers:
        file_name = os.path.basename(provider)
        preference = TTS_PROVIDER_PREFERENCES.get(file_name, 0)
        
        if preference > max_preference:
            max_preference = preference
            preferred = provider
    
    return preferred

def update_imports_in_file(file_path: str, replacements: Dict[str, str]) -> bool:
    """
    Update imports in a file.
    
    Args:
        file_path: Path to the file
        replacements: Dict mapping old imports to new imports
        
    Returns:
        bool: True if file was updated, False otherwise
    """
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()
        
        updated_content = content
        for old_import, new_import in replacements.items():
            updated_content = updated_content.replace(old_import, new_import)
        
        if updated_content != content:
            # Create backup
            backup_file(file_path)
            
            # Write updated content
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(updated_content)
            
            logger.info(f"Updated imports in {file_path}")
            return True
        
        return False
    except Exception as e:
        logger.error(f"Error updating imports in {file_path}: {e}")
        return False

def find_files_with_imports(directory: str, import_patterns: List[str]) -> Dict[str, List[str]]:
    """
    Find files that contain specific import patterns.
    
    Args:
        directory: Directory to search
        import_patterns: List of import patterns to search for
        
    Returns:
        Dict mapping file paths to lists of found import patterns
    """
    result = {}
    
    for root, _, files in os.walk(directory):
        for file in files:
            if not file.endswith(".py"):
                continue
                
            file_path = os.path.join(root, file)
            
            try:
                with open(file_path, "r", encoding="utf-8") as f:
                    content = f.read()
                
                found_patterns = []
                for pattern in import_patterns:
                    if pattern in content:
                        found_patterns.append(pattern)
                
                if found_patterns:
                    result[file_path] = found_patterns
            except Exception as e:
                logger.error(f"Error reading {file_path}: {e}")
    
    return result

def remove_file(file_path: str) -> bool:
    """
    Remove a file after creating a backup.
    
    Args:
        file_path: Path to the file
        
    Returns:
        bool: True if file was removed, False otherwise
    """
    try:
        if not os.path.exists(file_path):
            logger.warning(f"File does not exist: {file_path}")
            return False
        
        # Create backup
        backup_file(file_path)
        
        # Remove file
        os.remove(file_path)
        logger.info(f"Removed file: {file_path}")
        return True
    except Exception as e:
        logger.error(f"Error removing {file_path}: {e}")
        return False

def cleanup_tts_providers() -> Tuple[int, int, int]:
    """
    Clean up TTS providers in the codebase.
    
    Returns:
        Tuple containing counts of files updated, files removed, and errors
    """
    files_updated = 0
    files_removed = 0
    errors = 0
    
    try:
        # Find all TTS providers
        providers = find_tts_providers()
        logger.info(f"Found {sum(len(p) for p in providers.values())} TTS providers")
        
        # Get preferred providers for each type
        preferred_providers = {}
        for provider_type, provider_files in providers.items():
            preferred_providers[provider_type] = get_preferred_provider(provider_files)
        
        # Remove non-preferred providers
        for provider_type, provider_files in providers.items():
            preferred = preferred_providers[provider_type]
            
            for provider in provider_files:
                if provider != preferred:
                    if remove_file(provider):
                        files_removed += 1
                    else:
                        errors += 1
        
        # Find files with imports that need to be updated
        import_patterns = list(TTS_IMPORT_REPLACEMENTS.keys())
        files_with_imports = find_files_with_imports("bot", import_patterns)
        
        logger.info(f"Found {len(files_with_imports)} files with TTS imports that need to be updated")
        
        # Update imports
        for file_path, imports in files_with_imports.items():
            # Create a dict with only the replacements needed for this file
            replacements = {old_import: TTS_IMPORT_REPLACEMENTS[old_import] 
                           for old_import in imports if old_import in TTS_IMPORT_REPLACEMENTS}
            
            if update_imports_in_file(file_path, replacements):
                files_updated += 1
        
        return files_updated, files_removed, errors
    except Exception as e:
        logger.error(f"Error cleaning up TTS providers: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return files_updated, files_removed, errors + 1

def main():
    """Main function."""
    logger.info("Starting TTS provider cleanup")
    
    files_updated, files_removed, errors = cleanup_tts_providers()
    
    logger.info(f"TTS provider cleanup completed:")
    logger.info(f"  - Files updated: {files_updated}")
    logger.info(f"  - Files removed: {files_removed}")
    logger.info(f"  - Errors: {errors}")
    
    if errors > 0:
        logger.warning("Cleanup completed with errors. Check the log for details.")
        return 1
    
    logger.info("Cleanup completed successfully")
    return 0

if __name__ == "__main__":
    sys.exit(main())
