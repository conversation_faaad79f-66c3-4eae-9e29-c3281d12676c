"""
Deepgram TTS provider for VoicePal.

This module provides a Deepgram-based TTS provider using their Aura models.

TODO: Implement the following improvements:
- Add proper caching mechanism with TTL (time-to-live) for cached audio files
- Implement voice customization parameters (pitch, speed, etc.)
- Add support for more languages as they become available in Deepgram
- Implement streaming audio for longer texts
- Add proper cleanup of temporary files
"""

import tempfile
import logging
import os
import json
import time
import re
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, Tuple, List

import httpx

# Import Deepgram SDK
try:
    from deepgram import DeepgramClient, DeepgramClientOptions
    from deepgram.clients.speak.v1.speak import SpeakOptions
    DEEPGRAM_SDK_AVAILABLE = True
except ImportError:
    DEEPGRAM_SDK_AVAILABLE = False

from bot.providers.tts_provider_interface import TTSProviderInterface

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class DeepgramTTSProvider(TTSProviderInterface):
    """Deepgram-based TTS provider."""

    def __init__(self, api_key: str = None, voice_id: str = "aura-stella-en", model_id: str = None,
                 cache_ttl: int = 3600, use_cache: bool = True):
        """
        Initialize the Deepgram TTS provider.

        Args:
            api_key: Deepgram API key
            voice_id: Voice ID (e.g., "aura-stella-en")
            model_id: Model ID (not used directly, included for backward compatibility)
            cache_ttl: Cache time-to-live in seconds (default: 1 hour)
            use_cache: Whether to use caching (default: True)

        Note: Deepgram's Aura-2 model has been replaced with just "aura" in their API.
        The voice_id format is now "aura-{voice_name}-{language_code}" (e.g., "aura-stella-en").

        TODO: Implement the following improvements:
        - Add voice customization parameters (pitch, speed, etc.)
        - Implement proper API key validation with better error handling
        """
        # Try to get API key from environment if not provided
        if not api_key:
            api_key = os.getenv("DEEPGRAM_API_KEY")
            if api_key:
                logger.info("Using Deepgram API key from environment")
            else:
                logger.warning("No Deepgram API key provided or found in environment")

        self.api_key = api_key
        self.voice_id = voice_id
        self.model_id = model_id
        self.personality = "friendly"
        self._supported_features = ["text_to_speech"]
        self.use_cache = use_cache
        self.cache_ttl = cache_ttl
        self.cache = {}
        self.cache_timestamps = {}

        # Initialize Deepgram SDK client if available
        self.deepgram_client = None

        # Create cache directory if it doesn't exist
        self.cache_dir = Path(tempfile.gettempdir()) / "voicepal_tts_cache"
        self.cache_dir.mkdir(exist_ok=True)

        # Clean expired cache files on startup
        self._clean_expired_cache()

        # Initialize Deepgram client
        self.api_initialized = False

        # Try to initialize the SDK client if available
        if DEEPGRAM_SDK_AVAILABLE and self.api_key:
            try:
                logger.info("Initializing Deepgram client using SDK")
                self.deepgram_client = DeepgramClient(self.api_key)
                self.api_initialized = True
                logger.info(f"Successfully initialized Deepgram SDK client with voice {voice_id}")
            except Exception as e:
                logger.error(f"Error initializing Deepgram SDK client: {e}")
                self.deepgram_client = None
                # Will fall back to direct API calls

        # If SDK not available or initialization failed, try direct API
        if not DEEPGRAM_SDK_AVAILABLE or not self.deepgram_client:
            logger.info("Using direct API calls for Deepgram TTS")

            # Set api_initialized to True by default and only set to False if we confirm the API key doesn't work
            # This prevents unnecessary fallbacks to Google TTS
            self.api_initialized = True

            try:
                # Test API key validity with a simple request
                max_retries = 3
                retry_count = 0
                success = False

                while retry_count < max_retries and not success:
                    try:
                        response = httpx.get(
                            "https://api.deepgram.com/v1/projects",
                            headers={"Authorization": f"Token {self.api_key}"},
                            timeout=5.0
                        )

                        if response.status_code == 200:
                            success = True
                            self.api_initialized = True
                            logger.info(f"Deepgram TTS provider initialized with voice {voice_id}")
                            break
                        else:
                            logger.warning(f"Failed to initialize Deepgram API (attempt {retry_count+1}/{max_retries}): {response.status_code} - {response.text}")
                            retry_count += 1
                            time.sleep(1 * (2 ** retry_count))  # Exponential backoff
                    except Exception as e:
                        logger.warning(f"Failed to initialize Deepgram API (attempt {retry_count+1}/{max_retries}): {e}")
                        retry_count += 1
                        time.sleep(1 * (2 ** retry_count))  # Exponential backoff

                # Only set api_initialized to False if all retries failed
                if not success:
                    self.api_initialized = False
                    logger.error("All attempts to initialize Deepgram API failed")
            except Exception as e:
                logger.warning(f"Failed to initialize Deepgram API: {e}")
                # Don't immediately set api_initialized to False - we'll try again when generating speech

    def supports_feature(self, feature_name: str) -> bool:
        """
        Check if a feature is supported.

        Args:
            feature_name: Feature name

        Returns:
            bool: True if feature is supported, False otherwise
        """
        return feature_name in self._supported_features

    def _clean_expired_cache(self) -> None:
        """Clean expired cache files based on cache_ttl."""
        try:
            now = datetime.now()
            expired_keys = []

            # Check in-memory cache for expired entries
            for key, timestamp in self.cache_timestamps.items():
                if (now - timestamp).total_seconds() > self.cache_ttl:
                    expired_keys.append(key)

            # Remove expired entries
            for key in expired_keys:
                if key in self.cache:
                    # Delete the file if it exists
                    try:
                        file_path = self.cache[key]
                        if os.path.exists(file_path):
                            os.remove(file_path)
                            logger.debug(f"Removed expired cache file: {file_path}")
                    except Exception as e:
                        logger.warning(f"Error removing cache file: {e}")

                    # Remove from cache dictionaries
                    del self.cache[key]
                    del self.cache_timestamps[key]

            # Also clean any orphaned files in the cache directory older than cache_ttl
            for file_path in self.cache_dir.glob("*.mp3"):
                file_age = (now - datetime.fromtimestamp(file_path.stat().st_mtime)).total_seconds()
                if file_age > self.cache_ttl:
                    try:
                        os.remove(file_path)
                        logger.debug(f"Removed orphaned cache file: {file_path}")
                    except Exception as e:
                        logger.warning(f"Error removing orphaned cache file: {e}")

        except Exception as e:
            logger.warning(f"Error cleaning cache: {e}")

    def generate_speech(self, text: str, language: Optional[str] = None,
                       voice: Optional[str] = None, personality: Optional[str] = None,
                       **kwargs) -> Optional[str]:
        """
        Generate speech from text.

        Args:
            text: Text to convert to speech
            language: Language code (e.g., 'en', 'fr', etc.)
            voice: Voice ID (overrides the default voice)
            personality: Personality type to influence speech style
            **kwargs: Additional provider-specific parameters

        Returns:
            Path to the generated audio file or None if generation failed

        TODO: Implement the following improvements:
        - Refactor to reduce cognitive complexity
        - Split into smaller methods for better maintainability
        - Add support for streaming audio for longer texts
        - Implement better error handling with specific exception types
        - Add support for voice customization parameters (pitch, speed, etc.)
        """
        # Clean text by removing emoji descriptions that might be in the text
        import re
        # Remove emoji descriptions like (smile), (laughing), etc.
        text = re.sub(r'\([a-zA-Z]+\)', '', text)
        # Remove emoji characters - using a simpler pattern to avoid regex issues
        emoji_pattern = re.compile("["
                               u"\U0001F600-\U0001F64F"  # emoticons
                               u"\U0001F300-\U0001F5FF"  # symbols & pictographs
                               u"\U0001F680-\U0001F6FF"  # transport & map symbols
                               u"\U0001F700-\U0001F77F"  # alchemical symbols
                               u"\U0001F780-\U0001F7FF"  # Geometric Shapes
                               u"\U0001F800-\U0001F8FF"  # Supplemental Arrows-C
                               u"\U0001F900-\U0001F9FF"  # Supplemental Symbols and Pictographs
                               u"\U0001FA00-\U0001FA6F"  # Chess Symbols
                               u"\U0001FA70-\U0001FAFF"  # Symbols and Pictographs Extended-A
                               u"\U00002702-\U000027B0"  # Dingbats
                               u"\U000024C2-\U0001F251"
                               "]+", flags=re.UNICODE)
        text = emoji_pattern.sub(r'', text)
        try:
            # Check cache first if enabled
            cache_key = f"{text}_{voice or self.voice_id}_{personality or self.personality}"
            cache_key_hash = str(hash(cache_key))

            if self.use_cache:
                # Check if cache entry exists and is not expired
                if cache_key in self.cache and cache_key in self.cache_timestamps:
                    cache_age = (datetime.now() - self.cache_timestamps[cache_key]).total_seconds()

                    # If cache entry is still valid
                    if cache_age <= self.cache_ttl:
                        cache_path = self.cache[cache_key]
                        if os.path.exists(cache_path):
                            logger.info(f"Using cached audio for: {text[:30]}...")
                            return cache_path
                        else:
                            # File was deleted, remove from cache
                            del self.cache[cache_key]
                            del self.cache_timestamps[cache_key]
                    else:
                        # Cache entry expired, remove it
                        try:
                            if os.path.exists(self.cache[cache_key]):
                                os.remove(self.cache[cache_key])
                        except Exception as e:
                            logger.warning(f"Error removing expired cache file: {e}")

                        del self.cache[cache_key]
                        del self.cache_timestamps[cache_key]

            # Check if API is initialized - if not, try to initialize it now
            if not self.api_initialized:
                logger.warning("Deepgram API not initialized. Attempting to initialize now.")
                try:
                    # Try to get API key from environment if not already set
                    if not self.api_key:
                        self.api_key = os.getenv("DEEPGRAM_API_KEY")
                        if self.api_key:
                            logger.info("Retrieved Deepgram API key from environment")

                    # Test the API key
                    if self.api_key:
                        try:
                            response = httpx.get(
                                "https://api.deepgram.com/v1/projects",
                                headers={"Authorization": f"Token {self.api_key}"},
                                timeout=5.0
                            )
                            if response.status_code == 200:
                                self.api_initialized = True
                                logger.info("Successfully initialized Deepgram API")
                            else:
                                logger.error(f"Failed to initialize Deepgram API: {response.status_code} - {response.text}")
                                logger.error("Please check your Deepgram API key in the environment variables")
                                return self._fallback_to_google_tts(text, language)
                        except httpx.HTTPError as http_err:
                            logger.error(f"HTTP error during Deepgram API initialization: {http_err}")
                            logger.error("Network error connecting to Deepgram. Check your internet connection.")
                            return self._fallback_to_google_tts(text, language)
                        except Exception as e:
                            logger.error(f"Unexpected error initializing Deepgram API: {e}")
                            import traceback
                            logger.error(traceback.format_exc())
                            return self._fallback_to_google_tts(text, language)
                    else:
                        logger.error("No Deepgram API key available. Falling back to Google TTS.")
                        logger.error("Please set the DEEPGRAM_API_KEY environment variable")
                        return self._fallback_to_google_tts(text, language)
                except Exception as e:
                    logger.error(f"Error initializing Deepgram API: {e}")
                    import traceback
                    logger.error(traceback.format_exc())
                    return self._fallback_to_google_tts(text, language)

            # Use provided voice or default
            voice_id = voice or self.voice_id

            # Apply personality to voice settings if needed
            personality_type = personality or self.personality

            logger.info(f"Generating speech with Deepgram TTS for: {text[:30]}...")
            logger.info(f"Using voice: {voice_id}")
            logger.info(f"Personality: {personality_type}")

            # Create a cache file with a hash-based name for better organization
            cache_key_hash = str(hash(cache_key))
            cache_file_path = self.cache_dir / f"{cache_key_hash}.mp3"
            temp_file_path = str(cache_file_path)

            # Try to use the Deepgram SDK if available
            if DEEPGRAM_SDK_AVAILABLE and self.deepgram_client:
                try:
                    logger.info(f"Using Deepgram SDK to generate speech for: {text[:50]}...")
                    logger.info(f"Using voice: {voice_id}")

                    # Ensure voice_id is in the correct format (e.g., "aura-thalia-en")
                    # Strip any version numbers like "aura-2-" prefix
                    clean_voice_id = voice_id
                    if "-2-" in voice_id:
                        clean_voice_id = voice_id.replace("-2-", "-")
                    elif "aura-2" in voice_id:
                        clean_voice_id = voice_id.replace("aura-2", "aura")

                    logger.info(f"Using cleaned voice ID: {clean_voice_id}")

                    # Create speak options with the voice ID
                    try:
                        options = SpeakOptions(
                            model=clean_voice_id,  # Use the cleaned voice_id as the model parameter
                        )

                        # Call the SDK to generate speech
                        response = self.deepgram_client.speak.v1.speak(text, options)

                        # Save the audio data to the temporary file
                        with open(temp_file_path, "wb") as f:
                            f.write(response.audio_content)

                        # Cache the result if enabled
                        if self.use_cache:
                            self.cache[cache_key] = temp_file_path
                            self.cache_timestamps[cache_key] = datetime.now()

                        logger.info(f"Successfully generated voice response with Deepgram SDK: {temp_file_path}")
                        return temp_file_path
                    except Exception as sdk_err:
                        logger.error(f"Error with Deepgram SDK speak options: {sdk_err}")
                        logger.info("Trying alternative SDK approach...")

                        # Try alternative approach without SpeakOptions
                        try:
                            response = self.deepgram_client.speak.v1.speak(text, model=clean_voice_id)

                            # Save the audio data to the temporary file
                            with open(temp_file_path, "wb") as f:
                                f.write(response.audio_content)

                            # Cache the result if enabled
                            if self.use_cache:
                                self.cache[cache_key] = temp_file_path
                                self.cache_timestamps[cache_key] = datetime.now()

                            logger.info(f"Successfully generated voice response with alternative SDK approach: {temp_file_path}")
                            return temp_file_path
                        except Exception as alt_err:
                            logger.error(f"Error with alternative SDK approach: {alt_err}")
                            # Continue to direct API calls

                except Exception as e:
                    logger.error(f"Error using Deepgram SDK for TTS: {e}")
                    import traceback
                    logger.error(traceback.format_exc())
                    logger.info("Falling back to direct API calls")
                    # Continue with direct API calls

            # Generate audio using Deepgram REST API directly if SDK is not available or failed
            try:
                # Create a payload with text and voice parameters
                # The Deepgram API requires specific format - only text is allowed in the payload
                # Voice and model are specified in the URL
                payload = {
                    "text": text
                }

                # Log the request details for debugging
                logger.info(f"Sending request to Deepgram API with model: {voice_id}")
                logger.info(f"Text to convert (first 50 chars): {text[:50]}...")

                # Make the API request with retry logic
                max_retries = 3
                retry_count = 0
                response = None

                while retry_count < max_retries:
                    try:
                                # Ensure voice_id is in the correct format (e.g., "aura-thalia-en")
                        # Strip any version numbers like "aura-2-" prefix
                        clean_voice_id = voice_id
                        if "-2-" in voice_id:
                            clean_voice_id = voice_id.replace("-2-", "-")
                        elif "aura-2" in voice_id:
                            clean_voice_id = voice_id.replace("aura-2", "aura")

                        logger.info(f"Using cleaned voice ID for direct API: {clean_voice_id}")

                        # Build URL with model parameter (which includes the voice)
                        # The correct format is model=aura-thalia-en, not separate model and voice
                        deepgram_url = f"https://api.deepgram.com/v1/speak?model={clean_voice_id}"

                        response = httpx.post(
                            deepgram_url,
                            headers={
                                "Authorization": f"Token {self.api_key}",
                                "Content-Type": "application/json"
                            },
                            json=payload,  # Use json parameter instead of content
                            timeout=30.0
                        )

                        # If successful, break out of retry loop
                        if response.status_code == 200:
                            logger.info("Deepgram API request successful")
                            break

                        # Log the error and retry
                        logger.error(f"Deepgram API error (attempt {retry_count+1}/{max_retries}): {response.status_code} - {response.text}")
                        retry_count += 1

                        # Wait before retrying (exponential backoff)
                        import time
                        time.sleep(1 * (2 ** retry_count))

                    except Exception as e:
                        logger.error(f"Error calling Deepgram API (attempt {retry_count+1}/{max_retries}): {e}")
                        retry_count += 1

                        # Wait before retrying
                        import time
                        time.sleep(1 * (2 ** retry_count))

                # If all retries failed and we don't have a response
                if not response:
                    logger.error("All Deepgram API retries failed")
                    # Create a mock error response for compatibility
                    class MockResponse:
                        def __init__(self):
                            self.status_code = 400
                            self.text = "All API retries failed"
                            self.content = b""

                    response = MockResponse()

            except Exception as e:
                logger.error(f"Error calling Deepgram API: {e}")
                import traceback
                logger.error(traceback.format_exc())

                # Create a mock error response for compatibility
                class MockResponse:
                    def __init__(self):
                        self.status_code = 400
                        self.text = str(e)
                        self.content = b""

                response = MockResponse()

            if response.status_code == 200:
                # Save the audio data to the temporary file
                with open(temp_file_path, "wb") as f:
                    f.write(response.content)

                # Cache the result if enabled
                if self.use_cache:
                    self.cache[cache_key] = temp_file_path
                    self.cache_timestamps[cache_key] = datetime.now()

                logger.info(f"Successfully generated voice response with Deepgram: {temp_file_path}")
                return temp_file_path
            else:
                # Handle API errors
                error_message = f"Deepgram API error: {response.status_code} - {response.text}"
                logger.error(error_message)
                logger.error("Please check your Deepgram API key and network connection")

                # Fall back to Google TTS
                return self._fallback_to_google_tts(text, language)

        except httpx.HTTPError as http_err:
            logger.error(f"HTTP error during Deepgram TTS request: {http_err}")
            logger.error("Network error connecting to Deepgram. Check your internet connection.")
            return self._fallback_to_google_tts(text, language)
        except Exception as e:
            logger.error(f"Error generating speech with Deepgram: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return self._fallback_to_google_tts(text, language)

    def _fallback_to_google_tts(self, text: str, language: Optional[str] = None) -> Optional[str]:
        """
        Fall back to Google TTS when Deepgram TTS fails.

        Args:
            text: Text to convert to speech
            language: Language code

        Returns:
            Path to the generated audio file or None if generation failed
        """
        try:
            from gtts import gTTS

            logger.info(f"Falling back to Google TTS for: {text[:30]}...")

            # Use provided language or default to English
            lang = language or 'en'

            # Create a cache file for Google TTS fallback
            fallback_cache_key = f"google_tts_{text}_{lang}"
            fallback_cache_key_hash = str(hash(fallback_cache_key))
            cache_file_path = self.cache_dir / f"{fallback_cache_key_hash}.mp3"
            temp_file_path = str(cache_file_path)

            # Generate speech with Google TTS
            tts = gTTS(text=text, lang=lang, slow=False)
            tts.save(temp_file_path)

            logger.info(f"Successfully generated voice response with Google TTS fallback: {temp_file_path}")
            return temp_file_path

        except Exception as e:
            logger.error(f"Error generating voice response with Google TTS fallback: {e}")
            return None

    def get_available_languages(self) -> Dict[str, str]:
        """
        Get available languages.

        Returns:
            Dictionary of language codes and names
        """
        # Deepgram Aura currently only supports English
        return {
            "en": "English"
        }

    def get_available_voices(self, language: Optional[str] = None) -> Dict[str, Dict[str, Any]]:
        """
        Get available voices.

        Args:
            language: Language code to filter voices

        Returns:
            Dictionary of voice IDs and metadata

        TODO: Implement the following improvements:
        - Fetch available voices dynamically from Deepgram API
        - Add support for filtering by gender, accent, etc.
        - Add voice samples or preview functionality
        """
        # Voice description constants to avoid duplication
        FEMALE_VOICE_DESC = "Professional female voice with neutral American accent"
        MALE_VOICE_DESC = "Professional male voice with neutral American accent"

        # Deepgram Aura voices (updated from Aura-2)
        voices = {
            "aura-stella-en": {
                "name": "Stella",
                "language": "en",
                "gender": "female",
                "description": FEMALE_VOICE_DESC
            },
            "aura-athena-en": {
                "name": "Athena",
                "language": "en",
                "gender": "female",
                "description": FEMALE_VOICE_DESC
            },
            "aura-bella-en": {
                "name": "Bella",
                "language": "en",
                "gender": "female",
                "description": FEMALE_VOICE_DESC
            },
            "aura-callum-en": {
                "name": "Callum",
                "language": "en",
                "gender": "male",
                "description": MALE_VOICE_DESC
            },
            "aura-ryan-en": {
                "name": "Ryan",
                "language": "en",
                "gender": "male",
                "description": MALE_VOICE_DESC
            },
            "aura-thomas-en": {
                "name": "Thomas",
                "language": "en",
                "gender": "male",
                "description": MALE_VOICE_DESC
            }
        }

        # If language is specified, filter voices
        if language and language != "en":
            # Currently only English is supported
            return {}

        return voices

    def generate_speech_with_emotion(self, text: str, emotion: str = "neutral") -> Optional[str]:
        """
        Generate speech with a specific emotion.

        Args:
            text: Text to convert to speech
            emotion: Emotion to apply (e.g., 'happy', 'sad', 'angry', etc.)

        Returns:
            str: Path to generated audio file
        """
        # Deepgram doesn't directly support emotion control, but we can add a note about the emotion
        # that the LLM can use to adjust its response tone
        logger.info(f"Generating speech with emotion {emotion}")
        return self.generate_speech(text, personality=emotion)

    async def stream_speech(self, text: str, language: Optional[str] = None,
                          voice: Optional[str] = None, personality: Optional[str] = None,
                          **kwargs) -> bool:
        """
        Stream audio from Deepgram TTS API.

        Args:
            text: Text to convert to speech
            language: Language code (e.g., 'en', 'fr', etc.)
            voice: Voice ID (overrides the default voice)
            personality: Personality type to influence speech style
            **kwargs: Additional provider-specific parameters

        Returns:
            bool: True if streaming was successful, False otherwise
        """
        if not self.api_initialized:
            logger.warning("Deepgram API not initialized. Falling back to non-streaming TTS.")
            file_path = self.generate_speech(text, language, voice, personality, **kwargs)
            if file_path:
                # Play the file using a subprocess
                try:
                    import subprocess
                    subprocess.run(["ffplay", "-autoexit", "-nodisp", file_path],
                                  stdout=subprocess.DEVNULL,
                                  stderr=subprocess.DEVNULL)
                    return True
                except Exception as e:
                    logger.error(f"Error playing audio file: {e}")
                    return False
            return False

        # Clean text by removing emoji descriptions that might be in the text
        text = re.sub(r'\([a-zA-Z]+\)', '', text)
        # Remove emoji characters
        emoji_pattern = re.compile("["
                           u"\U0001F600-\U0001F64F"  # emoticons
                           u"\U0001F300-\U0001F5FF"  # symbols & pictographs
                           u"\U0001F680-\U0001F6FF"  # transport & map symbols
                           u"\U0001F700-\U0001F77F"  # alchemical symbols
                           u"\U0001F780-\U0001F7FF"  # Geometric Shapes
                           u"\U0001F800-\U0001F8FF"  # Supplemental Arrows-C
                           u"\U0001F900-\U0001F9FF"  # Supplemental Symbols and Pictographs
                           u"\U0001FA00-\U0001FA6F"  # Chess Symbols
                           u"\U0001FA70-\U0001FAFF"  # Symbols and Pictographs Extended-A
                           u"\U00002702-\U000027B0"  # Dingbats
                           u"\U000024C2-\U0001F251"
                           "]+", flags=re.UNICODE)
        text = emoji_pattern.sub(r'', text)

        try:
            # Use provided voice or default
            voice_id = voice or self.voice_id

            # Apply personality to voice settings if needed
            personality_type = personality or self.personality

            logger.info(f"Streaming speech with Deepgram TTS for: {text[:30]}...")
            logger.info(f"Using voice: {voice_id}")
            logger.info(f"Personality: {personality_type}")

            # Use ffplay to stream audio directly
            import subprocess
            player_command = ["ffplay", "-autoexit", "-", "-nodisp"]
            player_process = subprocess.Popen(
                player_command,
                stdin=subprocess.PIPE,
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL
            )

            # Try to use the Deepgram SDK if available
            if DEEPGRAM_SDK_AVAILABLE and self.deepgram_client:
                try:
                    logger.info("Using Deepgram SDK for streaming TTS")

                    # Ensure voice_id is in the correct format (e.g., "aura-thalia-en")
                    # Strip any version numbers like "aura-2-" prefix
                    clean_voice_id = voice_id
                    if "-2-" in voice_id:
                        clean_voice_id = voice_id.replace("-2-", "-")
                    elif "aura-2" in voice_id:
                        clean_voice_id = voice_id.replace("aura-2", "aura")

                    logger.info(f"Using cleaned voice ID for streaming: {clean_voice_id}")

                    # Create speak options with the voice ID
                    options = SpeakOptions(
                        model=clean_voice_id,  # Use the cleaned voice_id as the model parameter
                    )

                    # Call the SDK to generate speech
                    response = self.deepgram_client.speak.v1.speak(text, options)

                    # Stream the audio data to ffplay
                    if player_process.stdin:
                        player_process.stdin.write(response.audio_content)
                        player_process.stdin.flush()
                        player_process.stdin.close()

                    player_process.wait()
                    return True

                except Exception as e:
                    logger.error(f"Error using Deepgram SDK for streaming TTS: {e}")
                    import traceback
                    logger.error(traceback.format_exc())
                    logger.info("Falling back to direct API calls for streaming")
                    # Continue with direct API calls

            # If SDK is not available or failed, use direct API calls
            # Ensure voice_id is in the correct format (e.g., "aura-thalia-en")
            # Strip any version numbers like "aura-2-" prefix
            clean_voice_id = voice_id
            if "-2-" in voice_id:
                clean_voice_id = voice_id.replace("-2-", "-")
            elif "aura-2" in voice_id:
                clean_voice_id = voice_id.replace("aura-2", "aura")

            logger.info(f"Using cleaned voice ID for direct API streaming: {clean_voice_id}")

            # Set up the API URL with parameters - use only the voice_id as the model parameter
            DEEPGRAM_URL = f"https://api.deepgram.com/v1/speak?model={clean_voice_id}"

            headers = {
                "Authorization": f"Token {self.api_key}",
                "Content-Type": "application/json"
            }

            payload = {
                "text": text
            }

            # Stream the audio data
            with httpx.stream("POST", DEEPGRAM_URL, headers=headers, json=payload, timeout=30.0) as response:
                if response.status_code == 200:
                    for chunk in response.iter_bytes(chunk_size=1024):
                        if chunk:
                            player_process.stdin.write(chunk)
                            player_process.stdin.flush()

                    if player_process.stdin:
                        player_process.stdin.close()
                    player_process.wait()
                    return True
                else:
                    logger.error(f"Deepgram API error: {response.status_code} - {response.text}")
                    # Fall back to non-streaming TTS
                    return await self._fallback_to_non_streaming(text, language, voice, personality)
        except Exception as e:
            logger.error(f"Error streaming speech: {e}")
            # Fall back to non-streaming TTS
            return await self._fallback_to_non_streaming(text, language, voice, personality)

    async def _fallback_to_non_streaming(self, text: str, language: Optional[str] = None,
                                       voice: Optional[str] = None, personality: Optional[str] = None) -> bool:
        """
        Fall back to non-streaming TTS when streaming fails.

        Args:
            text: Text to convert to speech
            language: Language code
            voice: Voice ID
            personality: Personality type

        Returns:
            bool: True if successful, False otherwise
        """
        logger.info("Falling back to non-streaming TTS")
        file_path = self.generate_speech(text, language, voice, personality)
        if file_path:
            # Play the file using a subprocess
            try:
                import subprocess
                subprocess.run(["ffplay", "-autoexit", "-nodisp", file_path],
                              stdout=subprocess.DEVNULL,
                              stderr=subprocess.DEVNULL)
                return True
            except Exception as e:
                logger.error(f"Error playing audio file: {e}")
                return False
        return False
