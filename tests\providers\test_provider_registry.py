"""
Tests for the provider registry.
"""

import unittest
import asyncio
from typing import Dict, Any, Optional, List, Type
from unittest.mock import MagicMock, patch

from bot.providers.core.provider import Provider
from bot.providers.core.registry import ProviderRegistry
from bot.providers.core.exceptions import (
    ProviderError,
    ProviderRegistryError,
    ProviderNotRegisteredError,
    ProviderAlreadyRegisteredError,
    ProviderTypeNotRegisteredError,
    ProviderTypeAlreadyRegisteredError
)

class TestProviderRegistry(unittest.TestCase):
    """Tests for the ProviderRegistry class."""
    
    def setUp(self):
        """Set up test case."""
        # Create a mock provider class
        class MockProvider(Provider):
            provider_type = "mock"
            provider_name = "mock"
            
            def validate_config(self):
                pass
            
            def initialize(self):
                self.initialized = True
            
            def shutdown(self):
                self.initialized = False
        
        self.MockProvider = MockProvider
        
        # Create registry
        self.registry = ProviderRegistry()
    
    def test_registry_init(self):
        """Test ProviderRegistry initialization."""
        # Check registry attributes
        self.assertEqual(self.registry.factories, {})
        self.assertEqual(self.registry.default_providers, {})
    
    def test_register_provider_type(self):
        """Test ProviderRegistry.register_provider_type method."""
        # Register provider type
        factory = self.registry.register_provider_type("mock", "default_provider")
        
        # Check registered provider type
        self.assertIn("mock", self.registry.factories)
        self.assertEqual(self.registry.factories["mock"], factory)
        self.assertEqual(self.registry.default_providers["mock"], "default_provider")
        
        # Try to register the same provider type again
        with self.assertRaises(ProviderTypeAlreadyRegisteredError):
            self.registry.register_provider_type("mock")
    
    def test_unregister_provider_type(self):
        """Test ProviderRegistry.unregister_provider_type method."""
        # Register provider type
        self.registry.register_provider_type("mock", "default_provider")
        
        # Unregister provider type
        self.registry.unregister_provider_type("mock")
        
        # Check unregistered provider type
        self.assertNotIn("mock", self.registry.factories)
        self.assertNotIn("mock", self.registry.default_providers)
        
        # Try to unregister a non-existent provider type
        with self.assertRaises(ProviderTypeNotRegisteredError):
            self.registry.unregister_provider_type("non_existent_type")
    
    def test_get_factory(self):
        """Test ProviderRegistry.get_factory method."""
        # Register provider type
        factory = self.registry.register_provider_type("mock")
        
        # Get factory
        factory2 = self.registry.get_factory("mock")
        
        # Check that the same factory is returned
        self.assertIs(factory2, factory)
        
        # Try to get a non-existent factory
        with self.assertRaises(ProviderTypeNotRegisteredError):
            self.registry.get_factory("non_existent_type")
    
    def test_register_provider(self):
        """Test ProviderRegistry.register_provider method."""
        # Register provider type
        self.registry.register_provider_type("mock")
        
        # Register provider
        self.registry.register_provider("mock", "mock_provider", self.MockProvider)
        
        # Check registered provider
        factory = self.registry.get_factory("mock")
        self.assertIn("mock_provider", factory.providers)
        self.assertEqual(factory.providers["mock_provider"], self.MockProvider)
        
        # Try to register a provider for a non-existent provider type
        with self.assertRaises(ProviderTypeNotRegisteredError):
            self.registry.register_provider("non_existent_type", "mock_provider", self.MockProvider)
    
    def test_register_provider_from_module(self):
        """Test ProviderRegistry.register_provider_from_module method."""
        # Register provider type
        self.registry.register_provider_type("mock")
        
        # Mock the importlib.import_module function
        with patch("importlib.import_module") as mock_import_module:
            # Set up mock module
            mock_module = MagicMock()
            mock_module.MockProvider = self.MockProvider
            mock_import_module.return_value = mock_module
            
            # Register provider from module
            self.registry.register_provider_from_module("mock", "mock_provider", "mock_module", "MockProvider")
            
            # Check registered provider
            factory = self.registry.get_factory("mock")
            self.assertIn("mock_provider", factory.providers)
            self.assertEqual(factory.providers["mock_provider"], self.MockProvider)
            
            # Try to register a provider for a non-existent provider type
            with self.assertRaises(ProviderTypeNotRegisteredError):
                self.registry.register_provider_from_module("non_existent_type", "mock_provider", "mock_module", "MockProvider")
            
            # Try to register a provider from a non-existent module
            mock_import_module.side_effect = ImportError("Module not found")
            with self.assertRaises(ProviderRegistryError):
                self.registry.register_provider_from_module("mock", "mock_provider2", "non_existent_module", "MockProvider")
            
            # Try to register a provider with a non-existent class
            mock_import_module.side_effect = None
            mock_module = MagicMock()
            mock_import_module.return_value = mock_module
            with self.assertRaises(ProviderRegistryError):
                self.registry.register_provider_from_module("mock", "mock_provider2", "mock_module", "NonExistentProvider")
    
    def test_unregister_provider(self):
        """Test ProviderRegistry.unregister_provider method."""
        # Register provider type
        self.registry.register_provider_type("mock", "mock_provider")
        
        # Register provider
        self.registry.register_provider("mock", "mock_provider", self.MockProvider)
        
        # Unregister provider
        self.registry.unregister_provider("mock", "mock_provider")
        
        # Check unregistered provider
        factory = self.registry.get_factory("mock")
        self.assertNotIn("mock_provider", factory.providers)
        
        # Check that default provider is removed
        self.assertNotIn("mock", self.registry.default_providers)
        
        # Try to unregister a non-existent provider
        with self.assertRaises(ProviderNotRegisteredError):
            self.registry.unregister_provider("mock", "non_existent_provider")
        
        # Try to unregister a provider for a non-existent provider type
        with self.assertRaises(ProviderTypeNotRegisteredError):
            self.registry.unregister_provider("non_existent_type", "mock_provider")
    
    def test_set_default_provider(self):
        """Test ProviderRegistry.set_default_provider method."""
        # Register provider type
        self.registry.register_provider_type("mock")
        
        # Register provider
        self.registry.register_provider("mock", "mock_provider", self.MockProvider)
        
        # Set default provider
        self.registry.set_default_provider("mock", "mock_provider")
        
        # Check default provider
        self.assertEqual(self.registry.default_providers["mock"], "mock_provider")
        
        # Try to set a non-existent provider as default
        with self.assertRaises(ProviderNotRegisteredError):
            self.registry.set_default_provider("mock", "non_existent_provider")
        
        # Try to set a default provider for a non-existent provider type
        with self.assertRaises(ProviderTypeNotRegisteredError):
            self.registry.set_default_provider("non_existent_type", "mock_provider")
    
    def test_get_default_provider(self):
        """Test ProviderRegistry.get_default_provider method."""
        # Register provider type
        self.registry.register_provider_type("mock", "mock_provider")
        
        # Get default provider
        default_provider = self.registry.get_default_provider("mock")
        
        # Check default provider
        self.assertEqual(default_provider, "mock_provider")
        
        # Try to get default provider for a non-existent provider type
        with self.assertRaises(ProviderTypeNotRegisteredError):
            self.registry.get_default_provider("non_existent_type")
    
    def test_create_provider(self):
        """Test ProviderRegistry.create_provider method."""
        # Register provider type
        self.registry.register_provider_type("mock", "mock_provider")
        
        # Register provider
        self.registry.register_provider("mock", "mock_provider", self.MockProvider)
        
        # Create provider
        provider = self.registry.create_provider("mock", {})
        
        # Check provider
        self.assertIsInstance(provider, self.MockProvider)
        self.assertTrue(provider.initialized)
        
        # Try to create a provider for a non-existent provider type
        with self.assertRaises(ProviderTypeNotRegisteredError):
            self.registry.create_provider("non_existent_type", {})
    
    def test_get_or_create_provider(self):
        """Test ProviderRegistry.get_or_create_provider method."""
        # Register provider type
        self.registry.register_provider_type("mock", "mock_provider")
        
        # Register provider
        self.registry.register_provider("mock", "mock_provider", self.MockProvider)
        
        # Get or create provider
        provider1 = self.registry.get_or_create_provider("mock", {})
        
        # Check provider
        self.assertIsInstance(provider1, self.MockProvider)
        self.assertTrue(provider1.initialized)
        
        # Get or create provider again
        provider2 = self.registry.get_or_create_provider("mock", {})
        
        # Check that the same provider instance is returned
        self.assertIs(provider2, provider1)
        
        # Try to get or create a provider for a non-existent provider type
        with self.assertRaises(ProviderTypeNotRegisteredError):
            self.registry.get_or_create_provider("non_existent_type", {})
    
    def test_get_provider(self):
        """Test ProviderRegistry.get_provider method."""
        # Register provider type
        self.registry.register_provider_type("mock", "mock_provider")
        
        # Register provider
        self.registry.register_provider("mock", "mock_provider", self.MockProvider)
        
        # Create provider
        provider = self.registry.create_provider("mock", {})
        
        # Get provider
        provider2 = self.registry.get_provider("mock")
        
        # Check that the same provider instance is returned
        self.assertIs(provider2, provider)
        
        # Try to get a provider for a non-existent provider type
        with self.assertRaises(ProviderTypeNotRegisteredError):
            self.registry.get_provider("non_existent_type")
    
    def test_shutdown_provider(self):
        """Test ProviderRegistry.shutdown_provider method."""
        # Register provider type
        self.registry.register_provider_type("mock", "mock_provider")
        
        # Register provider
        self.registry.register_provider("mock", "mock_provider", self.MockProvider)
        
        # Create provider
        provider = self.registry.create_provider("mock", {})
        
        # Shutdown provider
        self.registry.shutdown_provider("mock")
        
        # Check that provider is shutdown
        self.assertFalse(provider.initialized)
        
        # Try to shutdown a provider for a non-existent provider type
        with self.assertRaises(ProviderTypeNotRegisteredError):
            self.registry.shutdown_provider("non_existent_type")
    
    def test_shutdown_all(self):
        """Test ProviderRegistry.shutdown_all method."""
        # Register provider types
        self.registry.register_provider_type("mock1", "mock_provider1")
        self.registry.register_provider_type("mock2", "mock_provider2")
        
        # Register providers
        self.registry.register_provider("mock1", "mock_provider1", self.MockProvider)
        self.registry.register_provider("mock2", "mock_provider2", self.MockProvider)
        
        # Create providers
        provider1 = self.registry.create_provider("mock1", {})
        provider2 = self.registry.create_provider("mock2", {})
        
        # Shutdown all providers
        self.registry.shutdown_all()
        
        # Check that providers are shutdown
        self.assertFalse(provider1.initialized)
        self.assertFalse(provider2.initialized)

if __name__ == "__main__":
    unittest.main()
