"""
Transaction wrapper for database operations.

This module provides a transaction wrapper to ensure proper transaction
handling and rollback on errors.
"""

import logging
import sqlite3
import functools
from typing import Any, Callable, TypeVar, cast

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# Define a type variable for the return type of the wrapped function
T = TypeVar('T')

def transactional(func: Callable[..., T]) -> Callable[..., T]:
    """
    Decorator to wrap a database method in a transaction.
    
    This decorator ensures that the method is executed within a transaction
    and that the transaction is rolled back if an exception occurs.
    
    Args:
        func: The function to wrap
        
    Returns:
        Wrapped function
    """
    @functools.wraps(func)
    def wrapper(self, *args, **kwargs) -> T:
        # Check if we're already in a transaction
        in_transaction = getattr(self, '_in_transaction', False)
        
        if in_transaction:
            # If we're already in a transaction, just call the function
            return func(self, *args, **kwargs)
        
        # Start a new transaction
        setattr(self, '_in_transaction', True)
        
        try:
            # Call the function
            result = func(self, *args, **kwargs)
            
            # Commit the transaction
            if hasattr(self, 'conn') and self.conn:
                self.conn.commit()
            
            return result
        except Exception as e:
            # Roll back the transaction
            if hasattr(self, 'conn') and self.conn:
                self.conn.rollback()
                logger.warning(f"Transaction rolled back: {e}")
            
            # Re-raise the exception
            raise
        finally:
            # End the transaction
            setattr(self, '_in_transaction', False)
    
    return wrapper

class Transaction:
    """
    Context manager for database transactions.
    
    This class provides a context manager for database transactions,
    ensuring that the transaction is committed if no exceptions occur
    and rolled back if an exception occurs.
    
    Example:
        ```python
        with Transaction(database.conn):
            database.add_credits(user_id, 100)
            database.add_transaction(user_id, 10.0, 100, "tx123", "completed")
        ```
    """
    
    def __init__(self, connection: sqlite3.Connection):
        """
        Initialize the transaction.
        
        Args:
            connection: SQLite connection object
        """
        self.connection = connection
    
    def __enter__(self) -> 'Transaction':
        """
        Enter the transaction context.
        
        Returns:
            Transaction object
        """
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb) -> bool:
        """
        Exit the transaction context.
        
        Args:
            exc_type: Exception type
            exc_val: Exception value
            exc_tb: Exception traceback
            
        Returns:
            True if the exception was handled, False otherwise
        """
        if exc_type is None:
            # No exception occurred, commit the transaction
            try:
                self.connection.commit()
                return False  # Don't suppress any exceptions
            except sqlite3.Error as e:
                logger.error(f"Error committing transaction: {e}")
                self.connection.rollback()
                raise
        else:
            # An exception occurred, roll back the transaction
            try:
                self.connection.rollback()
                logger.warning(f"Transaction rolled back due to {exc_type.__name__}: {exc_val}")
                return False  # Don't suppress the exception
            except sqlite3.Error as e:
                logger.error(f"Error rolling back transaction: {e}")
                raise
