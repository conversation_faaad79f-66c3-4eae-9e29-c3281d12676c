"""
Sentiment database extension for VoicePal.

This module extends the database with sentiment-related functionality.
"""

import logging
import json
from datetime import datetime
from typing import Dict, List, Optional, Any

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

def extend_database_for_sentiment(database) -> None:
    """
    Extend database with sentiment methods.

    Args:
        database: Database instance
    """
    try:
        conn = database.conn
        cursor = database.cursor

        # Check if mood_entries table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='mood_entries'")
        table_exists = cursor.fetchone() is not None

        if not table_exists:
            # Create mood_entries table
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS mood_entries (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                sentiment TEXT,
                confidence REAL,
                source TEXT,
                message_text TEXT,
                emotions TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIG<PERSON> KEY (user_id) REFERENCES users (user_id)
            )
            ''')
        else:
            # Check if emotions column exists
            cursor.execute("PRAGMA table_info(mood_entries)")
            columns = cursor.fetchall()
            column_names = [column[1] for column in columns]

            if "emotions" not in column_names:
                # Add emotions column
                cursor.execute("ALTER TABLE mood_entries ADD COLUMN emotions TEXT")

            if "message_text" not in column_names:
                # Add message_text column
                cursor.execute("ALTER TABLE mood_entries ADD COLUMN message_text TEXT")

        # Create sentiment_responses table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS sentiment_responses (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            sentiment TEXT,
            response_template TEXT,
            tone TEXT,
            empathy_level TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')

        conn.commit()
        logger.info("Database schema extended for sentiment analysis")
    except Exception as e:
        logger.error(f"Error extending database schema for sentiment: {e}")
        if conn:
            conn.rollback()
        raise

    # Add methods to Database class
    def add_mood_entry(self, user_id: int, mood_data: Dict[str, Any]) -> int:
        """
        Add a mood entry.

        Args:
            user_id: User ID
            mood_data: Mood data

        Returns:
            int: Mood entry ID
        """
        try:
            sentiment = mood_data.get('sentiment', 'neutral')
            confidence = mood_data.get('confidence', 0.0)
            source = mood_data.get('source', 'text')
            message_text = mood_data.get('message_text', '')
            emotions = mood_data.get('emotions', '{}')

            # Convert emotions to JSON string if it's a dict
            if isinstance(emotions, dict):
                emotions = json.dumps(emotions)

            self.cursor.execute(
                """INSERT INTO mood_entries
                   (user_id, sentiment, confidence, source, message_text, emotions)
                   VALUES (?, ?, ?, ?, ?, ?)""",
                (user_id, sentiment, confidence, source, message_text, emotions)
            )
            self.conn.commit()
            mood_entry_id = self.cursor.lastrowid
            logger.info(f"Added mood entry {mood_entry_id} for user {user_id}")
            return mood_entry_id
        except Exception as e:
            logger.error(f"Error adding mood entry for user {user_id}: {e}")
            self.conn.rollback()
            raise

    def get_mood_history(self, user_id: int, days: int = 7) -> List[Dict[str, Any]]:
        """
        Get mood history.

        Args:
            user_id: User ID
            days: Number of days to look back

        Returns:
            List of mood entries
        """
        try:
            self.cursor.execute(
                """SELECT id, user_id, sentiment, confidence, source, message_text, emotions, created_at
                   FROM mood_entries
                   WHERE user_id = ? AND created_at >= datetime('now', ?)
                   ORDER BY created_at DESC""",
                (user_id, f'-{days} days')
            )
            mood_entries = self.cursor.fetchall()

            # Convert JSON strings to dicts
            result = []
            for entry in mood_entries:
                entry_dict = dict(entry)
                if 'emotions' in entry_dict and entry_dict['emotions']:
                    try:
                        entry_dict['emotions'] = json.loads(entry_dict['emotions'])
                    except json.JSONDecodeError:
                        entry_dict['emotions'] = {}
                result.append(entry_dict)

            return result
        except Exception as e:
            logger.error(f"Error getting mood history for user {user_id}: {e}")
            return []

    def get_sentiment_distribution(self, user_id: int, days: int = 30) -> Dict[str, int]:
        """
        Get sentiment distribution.

        Args:
            user_id: User ID
            days: Number of days to look back

        Returns:
            Dict mapping sentiment to count
        """
        try:
            self.cursor.execute(
                """SELECT sentiment, COUNT(*) as count
                   FROM mood_entries
                   WHERE user_id = ? AND created_at >= datetime('now', ?)
                   GROUP BY sentiment""",
                (user_id, f'-{days} days')
            )
            distribution = self.cursor.fetchall()
            return {d['sentiment']: d['count'] for d in distribution}
        except Exception as e:
            logger.error(f"Error getting sentiment distribution for user {user_id}: {e}")
            return {}

    def get_dominant_emotions(self, user_id: int, days: int = 30, limit: int = 5) -> Dict[str, float]:
        """
        Get dominant emotions.

        Args:
            user_id: User ID
            days: Number of days to look back
            limit: Maximum number of emotions to return

        Returns:
            Dict mapping emotion to average intensity
        """
        try:
            self.cursor.execute(
                """SELECT emotions
                   FROM mood_entries
                   WHERE user_id = ? AND created_at >= datetime('now', ?)
                   ORDER BY created_at DESC""",
                (user_id, f'-{days} days')
            )
            entries = self.cursor.fetchall()

            # Process emotions
            emotion_counts = {}
            emotion_intensities = {}

            for entry in entries:
                if not entry['emotions']:
                    continue

                try:
                    emotions = json.loads(entry['emotions'])
                    for emotion, data in emotions.items():
                        intensity = data.get('intensity', 0.5)
                        emotion_counts[emotion] = emotion_counts.get(emotion, 0) + 1
                        emotion_intensities[emotion] = emotion_intensities.get(emotion, 0) + intensity
                except json.JSONDecodeError:
                    continue

            # Calculate average intensities
            result = {}
            for emotion, count in emotion_counts.items():
                result[emotion] = emotion_intensities[emotion] / count

            # Sort by intensity and limit
            sorted_emotions = sorted(result.items(), key=lambda x: x[1], reverse=True)
            return dict(sorted_emotions[:limit])
        except Exception as e:
            logger.error(f"Error getting dominant emotions for user {user_id}: {e}")
            return {}

    def add_sentiment_response_template(self, sentiment: str, response_template: str,
                                      tone: str = "neutral", empathy_level: str = "medium") -> int:
        """
        Add a sentiment response template.

        Args:
            sentiment: Sentiment type
            response_template: Response template
            tone: Tone of the response
            empathy_level: Empathy level

        Returns:
            int: Template ID
        """
        try:
            self.cursor.execute(
                """INSERT INTO sentiment_responses
                   (sentiment, response_template, tone, empathy_level)
                   VALUES (?, ?, ?, ?)""",
                (sentiment, response_template, tone, empathy_level)
            )
            self.conn.commit()
            template_id = self.cursor.lastrowid
            logger.info(f"Added sentiment response template {template_id} for {sentiment}")
            return template_id
        except Exception as e:
            logger.error(f"Error adding sentiment response template for {sentiment}: {e}")
            self.conn.rollback()
            raise

    def get_sentiment_response_template(self, sentiment: str, tone: str = None,
                                      empathy_level: str = None) -> Optional[str]:
        """
        Get a sentiment response template.

        Args:
            sentiment: Sentiment type
            tone: Tone of the response
            empathy_level: Empathy level

        Returns:
            str: Response template or None if not found
        """
        try:
            query = "SELECT response_template FROM sentiment_responses WHERE sentiment = ?"
            params = [sentiment]

            if tone:
                query += " AND tone = ?"
                params.append(tone)

            if empathy_level:
                query += " AND empathy_level = ?"
                params.append(empathy_level)

            query += " ORDER BY RANDOM() LIMIT 1"

            self.cursor.execute(query, params)
            result = self.cursor.fetchone()

            if result:
                return result['response_template']
            return None
        except Exception as e:
            logger.error(f"Error getting sentiment response template for {sentiment}: {e}")
            return None

    # Replace or add methods to Database class
    setattr(database.__class__, 'add_mood_entry', add_mood_entry)
    setattr(database.__class__, 'get_mood_history', get_mood_history)
    setattr(database.__class__, 'get_sentiment_distribution', get_sentiment_distribution)
    setattr(database.__class__, 'get_dominant_emotions', get_dominant_emotions)
    setattr(database.__class__, 'add_sentiment_response_template', add_sentiment_response_template)
    setattr(database.__class__, 'get_sentiment_response_template', get_sentiment_response_template)

    logger.info("Database extended with methods for sentiment analysis")
