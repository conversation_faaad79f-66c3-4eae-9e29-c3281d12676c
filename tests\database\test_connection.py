"""
Tests for database connection.

This module tests the DatabaseConnection class.
"""

import pytest
import sqlite3
from pathlib import Path

from bot.database.core.connection import DatabaseConnection
from bot.database.core.exceptions import (
    DatabaseConnectionError,
    DatabaseQueryError,
    DatabaseTransactionError
)

def test_connection_init(temp_db_path):
    """Test database connection initialization."""
    # Test successful connection
    connection = DatabaseConnection(temp_db_path)
    assert connection.conn is not None
    
    # Test connection to non-existent directory
    non_existent_path = Path(temp_db_path).parent / "non_existent" / "db.sqlite"
    connection = DatabaseConnection(non_existent_path)
    assert connection.conn is not None
    assert non_existent_path.parent.exists()
    
    # Clean up
    connection.close()

def test_execute(db_connection):
    """Test query execution."""
    # Create a test table
    db_connection.execute("""
        CREATE TABLE test (
            id INTEGER PRIMARY KEY,
            name TEXT
        )
    """)
    
    # Insert data
    db_connection.execute(
        "INSERT INTO test (id, name) VALUES (?, ?)",
        (1, "Test")
    )
    
    # Query data
    cursor = db_connection.execute("SELECT * FROM test WHERE id = ?", (1,))
    row = cursor.fetchone()
    
    assert row is not None
    assert row["id"] == 1
    assert row["name"] == "Test"
    
    # Test invalid query
    with pytest.raises(DatabaseQueryError):
        db_connection.execute("SELECT * FROM non_existent_table")

def test_executemany(db_connection):
    """Test executemany."""
    # Create a test table
    db_connection.execute("""
        CREATE TABLE test (
            id INTEGER PRIMARY KEY,
            name TEXT
        )
    """)
    
    # Insert multiple rows
    db_connection.executemany(
        "INSERT INTO test (id, name) VALUES (?, ?)",
        [(1, "Test 1"), (2, "Test 2"), (3, "Test 3")]
    )
    
    # Query data
    cursor = db_connection.execute("SELECT COUNT(*) FROM test")
    count = cursor.fetchone()[0]
    
    assert count == 3
    
    # Test invalid query
    with pytest.raises(DatabaseQueryError):
        db_connection.executemany(
            "INSERT INTO non_existent_table (id, name) VALUES (?, ?)",
            [(4, "Test 4")]
        )

def test_transaction(db_connection):
    """Test transaction management."""
    # Create a test table
    db_connection.execute("""
        CREATE TABLE test (
            id INTEGER PRIMARY KEY,
            name TEXT
        )
    """)
    
    # Test successful transaction
    with db_connection.transaction():
        db_connection.execute(
            "INSERT INTO test (id, name) VALUES (?, ?)",
            (1, "Test 1")
        )
        db_connection.execute(
            "INSERT INTO test (id, name) VALUES (?, ?)",
            (2, "Test 2")
        )
    
    # Verify data was committed
    cursor = db_connection.execute("SELECT COUNT(*) FROM test")
    count = cursor.fetchone()[0]
    assert count == 2
    
    # Test failed transaction
    try:
        with db_connection.transaction():
            db_connection.execute(
                "INSERT INTO test (id, name) VALUES (?, ?)",
                (3, "Test 3")
            )
            # This should fail and trigger a rollback
            db_connection.execute(
                "INSERT INTO non_existent_table (id, name) VALUES (?, ?)",
                (4, "Test 4")
            )
    except DatabaseTransactionError:
        pass
    
    # Verify data was rolled back
    cursor = db_connection.execute("SELECT COUNT(*) FROM test")
    count = cursor.fetchone()[0]
    assert count == 2  # Still 2, not 3

def test_close(temp_db_path):
    """Test connection close."""
    connection = DatabaseConnection(temp_db_path)
    connection.close()
    
    # Verify connection is closed
    with pytest.raises(Exception):
        connection.execute("SELECT 1")

def test_context_manager(temp_db_path):
    """Test context manager."""
    with DatabaseConnection(temp_db_path) as connection:
        connection.execute("""
            CREATE TABLE test (
                id INTEGER PRIMARY KEY,
                name TEXT
            )
        """)
        
        connection.execute(
            "INSERT INTO test (id, name) VALUES (?, ?)",
            (1, "Test")
        )
    
    # Verify connection is closed
    with pytest.raises(Exception):
        connection.execute("SELECT 1")
    
    # Verify data was committed
    with DatabaseConnection(temp_db_path) as connection:
        cursor = connection.execute("SELECT COUNT(*) FROM test")
        count = cursor.fetchone()[0]
        assert count == 1
