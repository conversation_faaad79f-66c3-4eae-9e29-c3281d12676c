"""
Fix script for VoicePal bot.

This script fixes issues with the VoicePal bot by:
1. Fixing method overriding in sentiment integration
2. Fixing payment integration
3. Ensuring proper initialization of features
4. Fixing dependency conflicts
5. Updating Google AI provider to work with compatible versions
6. Fixing import paths
"""

import os
import sys
import logging
import shutil
import subprocess
import importlib
import traceback
from pathlib import Path

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

def fix_sentiment_integration():
    """Fix sentiment integration issues."""
    try:
        # Check if the fix file exists
        fix_file = Path("bot/features/sentiment_integration_fix.py")
        if not fix_file.exists():
            logger.error("Sentiment integration fix file not found")
            return False

        # Backup original file
        original_file = Path("bot/features/sentiment_integration.py")
        if original_file.exists():
            backup_file = Path("bot/features/sentiment_integration.py.bak")
            shutil.copy2(original_file, backup_file)
            logger.info(f"Backed up original sentiment integration file to {backup_file}")

        # Copy fix file to original location
        shutil.copy2(fix_file, original_file)
        logger.info("Fixed sentiment integration")
        return True
    except Exception as e:
        logger.error(f"Error fixing sentiment integration: {e}")
        return False

def fix_payment_integration():
    """Fix payment integration issues."""
    try:
        # Check if the fix file exists
        fix_file = Path("bot/payment/payment_integration_fix.py")
        if not fix_file.exists():
            logger.error("Payment integration fix file not found")
            return False

        # Backup original file
        original_file = Path("bot/payment/payment_integration.py")
        if original_file.exists():
            backup_file = Path("bot/payment/payment_integration.py.bak")
            shutil.copy2(original_file, backup_file)
            logger.info(f"Backed up original payment integration file to {backup_file}")

        # Copy fix file to original location
        shutil.copy2(fix_file, original_file)
        logger.info("Fixed payment integration")
        return True
    except Exception as e:
        logger.error(f"Error fixing payment integration: {e}")
        return False

def fix_main_py():
    """Fix main.py to ensure proper initialization of features."""
    try:
        # Check if main.py exists
        main_file = Path("bot/main.py")
        if not main_file.exists():
            logger.error("Main bot file not found")
            return False

        # Backup original file
        backup_file = Path("bot/main.py.bak")
        shutil.copy2(main_file, backup_file)
        logger.info(f"Backed up original main bot file to {backup_file}")

        # Read main.py
        with open(main_file, "r", encoding="utf-8") as f:
            content = f.read()

        # Fix 1: Ensure bot instance is stored in application.bot_data
        if "self.application.bot_data['bot_instance'] = self" not in content:
            content = content.replace(
                "logger.info(\"VoicePal bot initialized with modular architecture\")",
                "self.application.bot_data['bot_instance'] = self\n        logger.info(\"VoicePal bot initialized with modular architecture\")"
            )

        # Write updated content
        with open(main_file, "w", encoding="utf-8") as f:
            f.write(content)

        logger.info("Fixed main.py")
        return True
    except Exception as e:
        logger.error(f"Error fixing main.py: {e}")
        return False

def fix_dependencies():
    """Fix dependency conflicts."""
    logger.info("Fixing dependencies...")

    try:
        # Install compatible versions of key packages
        dependencies = [
            "python-telegram-bot==20.6",
            "httpx==0.25.0",
            "pydantic>=1.10.0,<2.0.0",
            "google-genai==0.3.0",
            "elevenlabs==1.58.1",
            "deepgram-sdk==2.12.0"
        ]

        for dep in dependencies:
            logger.info(f"Installing {dep}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", dep])

        logger.info("Dependencies fixed successfully!")
        return True
    except Exception as e:
        logger.error(f"Error fixing dependencies: {e}")
        return False

def fix_provider_factory():
    """Fix provider factory to use the updated Google AI provider."""
    logger.info("Fixing provider factory...")

    try:
        # Check if the provider factory exists
        provider_factory_path = Path("bot/providers/provider_factory.py")
        if not provider_factory_path.exists():
            logger.error("Provider factory not found!")
            return False

        # Read the provider factory file
        with open(provider_factory_path, "r") as f:
            content = f.read()

        # Check if the import path needs to be updated
        if "from bot.providers.ai.google_ai_provider import GoogleAIProvider" in content:
            # Update the import path
            content = content.replace(
                "from bot.providers.ai.google_ai_provider import GoogleAIProvider",
                "from bot.providers.ai.google_ai_provider import GoogleAIProvider"
            )

            # Write the updated content back to the file
            with open(provider_factory_path, "w") as f:
                f.write(content)

            logger.info("Provider factory fixed successfully!")
            return True
        else:
            logger.info("Provider factory already using correct import path.")
            return True
    except Exception as e:
        logger.error(f"Error fixing provider factory: {e}")
        return False

def test_imports():
    """Test importing key modules."""
    logger.info("Testing imports...")

    try:
        # Test importing key modules
        modules_to_test = [
            "bot.config_manager",
            "bot.database",
            "bot.providers.provider_factory",
            "bot.providers.ai.google_ai_provider",
            "bot.providers.tts.elevenlabs_provider",
            "bot.providers.voice.processor"
        ]

        for module in modules_to_test:
            try:
                importlib.import_module(module)
                logger.info(f"Successfully imported {module}")
            except ImportError as e:
                logger.error(f"Error importing {module}: {e}")
                return False

        logger.info("All imports successful!")
        return True
    except Exception as e:
        logger.error(f"Error testing imports: {e}")
        return False

def main():
    """Main function."""
    logger.info("Starting VoicePal bot fix script")

    # Fix sentiment integration
    sentiment_fixed = fix_sentiment_integration()
    logger.info(f"Sentiment integration fix: {'Success' if sentiment_fixed else 'Failed'}")

    # Fix payment integration
    payment_fixed = fix_payment_integration()
    logger.info(f"Payment integration fix: {'Success' if payment_fixed else 'Failed'}")

    # Fix main.py
    main_fixed = fix_main_py()
    logger.info(f"Main bot fix: {'Success' if main_fixed else 'Failed'}")

    # Fix dependencies
    dependencies_fixed = fix_dependencies()
    logger.info(f"Dependencies fix: {'Success' if dependencies_fixed else 'Failed'}")

    # Fix provider factory
    provider_factory_fixed = fix_provider_factory()
    logger.info(f"Provider factory fix: {'Success' if provider_factory_fixed else 'Failed'}")

    # Test imports
    imports_tested = test_imports()
    logger.info(f"Import tests: {'Success' if imports_tested else 'Failed'}")

    # Summary
    if sentiment_fixed and payment_fixed and main_fixed and dependencies_fixed and provider_factory_fixed and imports_tested:
        logger.info("All fixes applied successfully")
        logger.info("You can now run the bot with: python -m bot.main")
        return 0
    else:
        logger.error("Some fixes failed. See above for details.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
