"""
Test script for VoicePal bot imports.

This script tests the basic imports and configuration of the VoicePal bot.
"""

import os
import logging
import sys

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.DEBUG  # Set to DEBUG for more detailed logs
)
logger = logging.getLogger(__name__)

def main():
    """Test the VoicePal bot imports."""
    try:
        logger.info("Python version: %s", sys.version)
        logger.info("Current directory: %s", os.getcwd())
        
        # Try to import key modules
        logger.info("Trying to import key modules...")
        
        try:
            import telegram
            logger.info("Successfully imported telegram module: %s", telegram.__version__)
        except ImportError as e:
            logger.error("Failed to import telegram module: %s", e)
        
        try:
            import dotenv
            logger.info("Successfully imported dotenv module: %s", dotenv.__version__)
        except ImportError as e:
            logger.error("Failed to import dotenv module: %s", e)
        
        try:
            from bot.config_manager import ConfigManager
            logger.info("Successfully imported ConfigManager")
            
            # Try to initialize ConfigManager
            config_manager = ConfigManager()
            logger.info("Successfully initialized ConfigManager")
            
            # Check if config file exists
            logger.info("Config file exists: %s", os.path.exists("config.json"))
            
        except ImportError as e:
            logger.error("Failed to import ConfigManager: %s", e)
        except Exception as e:
            logger.error("Error initializing ConfigManager: %s", e)
        
        logger.info("Test completed")
        
    except Exception as e:
        logger.error("Unexpected error: %s", e)
        import traceback
        logger.error(traceback.format_exc())
        return 1

if __name__ == "__main__":
    main()
