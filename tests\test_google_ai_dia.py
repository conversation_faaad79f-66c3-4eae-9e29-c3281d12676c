"""
Test script for Google AI and Dia TTS integration.

This script tests the integration of Google AI for text generation and Dia for TTS.
It simulates a conversation with the AI and generates voice responses using Dia TTS.

Usage:
    python test_google_ai_dia.py
"""

import os
import logging
import asyncio
import tempfile
from pathlib import Path
import argparse

from bot.config import Config
from bot.providers.ai.google_ai_conversation import GoogleAIConversation
from bot.ai_conversation import AIConversation
from bot.providers.voice.processor import VoiceProcessor
from bot.tts_providers import TTSProviderFactory

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

async def test_conversation(ai_provider="simple", tts_provider="google", test_phrases=None):
    """
    Test conversation with AI and TTS.

    Args:
        ai_provider: AI provider to use (simple, google_ai)
        tts_provider: TTS provider to use (google, dia)
        test_phrases: List of test phrases to use
    """
    logger.info(f"Testing conversation with AI provider: {ai_provider}, TTS provider: {tts_provider}")

    # Default test phrases
    if test_phrases is None:
        test_phrases = [
            "Hello, how are you today?",
            "Tell me about yourself.",
            "What's the weather like?",
            "I'm feeling sad today.",
            "Tell me a joke."
        ]

    # Initialize AI conversation
    if ai_provider == "google_ai":
        if not Config.GOOGLE_AI_API_KEY:
            logger.error("No Google AI API key provided. Set GOOGLE_AI_API_KEY in environment variables.")
            return

        ai_conversation = GoogleAIConversation(
            api_key=Config.GOOGLE_AI_API_KEY,
            personality="friendly"
        )
        logger.info("Using Google AI for conversation")
    else:
        ai_conversation = AIConversation(
            personality="friendly"
        )
        logger.info("Using simple rule-based AI for conversation")

    # Initialize voice processor
    tts_provider_options = {}
    if tts_provider == "dia":
        if not Config.HF_API_TOKEN:
            logger.error("No Hugging Face API token provided. Set HF_API_TOKEN in environment variables.")
            return

        tts_provider_options = {
            "api_token": Config.HF_API_TOKEN,
            "model_id": "nari-labs/Dia-1.6B",
            "use_cache": True
        }

    voice_processor = VoiceProcessor(
        deepgram_api_key=Config.DEEPGRAM_API_KEY,
        default_language="en",
        tts_provider=tts_provider,
        tts_provider_options=tts_provider_options,
        personality="friendly"
    )

    # Test conversation
    for i, phrase in enumerate(test_phrases):
        logger.info(f"\nTest phrase {i+1}: '{phrase}'")

        # Get AI response
        ai_response = ai_conversation.get_response(phrase)
        logger.info(f"AI response: '{ai_response}'")

        # Generate voice response
        voice_file_path = voice_processor.generate_voice_response(
            text=ai_response,
            personality="friendly"
        )

        if voice_file_path:
            logger.info(f"Voice response generated: {voice_file_path}")

            # Play audio if on a system with audio playback
            if os.name == 'posix':  # Linux/Mac
                try:
                    if os.path.exists("/usr/bin/aplay"):  # Linux
                        os.system(f"aplay {voice_file_path}")
                    elif os.path.exists("/usr/bin/afplay"):  # Mac
                        os.system(f"afplay {voice_file_path}")
                except Exception as e:
                    logger.error(f"Error playing audio: {e}")
            elif os.name == 'nt':  # Windows
                try:
                    os.system(f"start {voice_file_path}")
                except Exception as e:
                    logger.error(f"Error playing audio: {e}")
        else:
            logger.error("Failed to generate voice response")

    logger.info("\nTest completed")

async def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Test Google AI and Dia TTS integration")
    parser.add_argument("--ai", choices=["simple", "google_ai"], default="simple",
                        help="AI provider to use (simple, google_ai)")
    parser.add_argument("--tts", choices=["google", "dia"], default="google",
                        help="TTS provider to use (google, dia)")
    args = parser.parse_args()

    await test_conversation(ai_provider=args.ai, tts_provider=args.tts)

if __name__ == "__main__":
    asyncio.run(main())
