"""
Tests for the Deepgram TTS provider.
"""

import os
import unittest
from unittest.mock import patch, MagicMock

from bot.providers.tts.deepgram_provider import DeepgramTTSProvider

class TestDeepgramTTSProvider(unittest.TestCase):
    """Tests for the Deepgram TTS provider."""

    def setUp(self):
        """Set up test fixtures."""
        self.api_key = "test_api_key"
        self.provider = DeepgramTTSProvider(api_key=self.api_key)

    def test_init(self):
        """Test initialization."""
        self.assertEqual(self.provider.api_key, self.api_key)
        self.assertEqual(self.provider.voice_id, "aura-2-thalia-en")
        self.assertEqual(self.provider.model_id, "aura-2")
        self.assertEqual(self.provider.personality, "friendly")
        self.assertIn("text_to_speech", self.provider._supported_features)

    def test_supports_feature(self):
        """Test feature support checking."""
        self.assertTrue(self.provider.supports_feature("text_to_speech"))
        self.assertFalse(self.provider.supports_feature("unknown_feature"))

    @patch("httpx.post")
    @patch("tempfile.NamedTemporaryFile")
    @patch("httpx.get")
    def test_generate_speech(self, mock_get, mock_temp_file, mock_post):
        """Test speech generation."""
        # Mock the API initialization check
        mock_get_response = MagicMock()
        mock_get_response.status_code = 200
        mock_get.return_value = mock_get_response

        # Create a new provider with mocked API initialization
        provider = DeepgramTTSProvider(api_key=self.api_key)

        # Mock the temporary file
        mock_temp = MagicMock()
        mock_temp.name = "/tmp/test.mp3"
        mock_temp_file.return_value.__enter__.return_value = mock_temp

        # Mock the HTTP response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.content = b"audio_data"
        mock_post.return_value = mock_response

        # Call the method
        result = provider.generate_speech("Hello, world!")

        # Verify the result is not None
        self.assertIsNotNone(result)

        # Verify the API call
        mock_post.assert_called_once()
        args, kwargs = mock_post.call_args
        self.assertEqual(args[0], "https://api.deepgram.com/v1/speak")
        self.assertEqual(kwargs["headers"]["Authorization"], f"Token {self.api_key}")
        self.assertEqual(kwargs["json"]["text"], "Hello, world!")
        self.assertEqual(kwargs["json"]["voice"], "aura-2-thalia-en")
        self.assertEqual(kwargs["json"]["model"], "aura-2")

    @patch("httpx.post")
    @patch("tempfile.NamedTemporaryFile")
    @patch("httpx.get")
    def test_generate_speech_with_custom_voice(self, mock_get, mock_temp_file, mock_post):
        """Test speech generation with custom voice."""
        # Mock the API initialization check
        mock_get_response = MagicMock()
        mock_get_response.status_code = 200
        mock_get.return_value = mock_get_response

        # Create a new provider with mocked API initialization
        provider = DeepgramTTSProvider(api_key=self.api_key)

        # Mock the temporary file
        mock_temp = MagicMock()
        mock_temp.name = "/tmp/test.mp3"
        mock_temp_file.return_value.__enter__.return_value = mock_temp

        # Mock the HTTP response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.content = b"audio_data"
        mock_post.return_value = mock_response

        # Call the method with custom voice
        result = provider.generate_speech("Hello, world!", voice="aura-2-leo-en")

        # Verify the result is not None
        self.assertIsNotNone(result)

        # Verify the API call
        mock_post.assert_called_once()
        _, kwargs = mock_post.call_args
        self.assertEqual(kwargs["json"]["voice"], "aura-2-leo-en")

    @patch("httpx.post")
    @patch("tempfile.NamedTemporaryFile")
    @patch("httpx.get")
    def test_generate_speech_with_personality(self, mock_get, mock_temp_file, mock_post):
        """Test speech generation with personality."""
        # Mock the API initialization check
        mock_get_response = MagicMock()
        mock_get_response.status_code = 200
        mock_get.return_value = mock_get_response

        # Create a new provider with mocked API initialization
        provider = DeepgramTTSProvider(api_key=self.api_key)

        # Mock the temporary file
        mock_temp = MagicMock()
        mock_temp.name = "/tmp/test.mp3"
        mock_temp_file.return_value.__enter__.return_value = mock_temp

        # Mock the HTTP response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.content = b"audio_data"
        mock_post.return_value = mock_response

        # Call the method with personality
        result = provider.generate_speech("Hello, world!", personality="calm")

        # Verify the result is not None
        self.assertIsNotNone(result)

    @patch("httpx.post")
    @patch("tempfile.NamedTemporaryFile")
    @patch("gtts.gTTS")
    @patch("httpx.get")
    def test_generate_speech_with_api_error(self, mock_get, mock_gtts, mock_temp_file, mock_post):
        """Test speech generation with API error."""
        # Mock the API initialization check
        mock_get_response = MagicMock()
        mock_get_response.status_code = 200
        mock_get.return_value = mock_get_response

        # Create a new provider with mocked API initialization
        provider = DeepgramTTSProvider(api_key=self.api_key)

        # Mock the temporary file
        mock_temp = MagicMock()
        mock_temp.name = "/tmp/test.mp3"
        mock_temp_file.return_value.__enter__.return_value = mock_temp

        # Mock the HTTP response with error
        mock_response = MagicMock()
        mock_response.status_code = 400
        mock_response.text = "Bad request"
        mock_post.return_value = mock_response

        # Mock gTTS for fallback
        mock_gtts_instance = MagicMock()
        mock_gtts.return_value = mock_gtts_instance

        # Call the method
        _ = provider.generate_speech("Hello, world!")

        # Verify fallback to Google TTS
        mock_gtts.assert_called_once()
        mock_gtts_instance.save.assert_called_once()

    def test_get_available_voices(self):
        """Test getting available voices."""
        voices = self.provider.get_available_voices()
        self.assertIn("aura-2-thalia-en", voices)
        self.assertIn("aura-2-stella-en", voices)
        self.assertIn("aura-2-cael-en", voices)
        self.assertIn("aura-2-leo-en", voices)

        # Check voice metadata
        self.assertEqual(voices["aura-2-thalia-en"]["name"], "Thalia")
        self.assertEqual(voices["aura-2-thalia-en"]["gender"], "female")
        self.assertEqual(voices["aura-2-thalia-en"]["language"], "en")

    def test_get_available_languages(self):
        """Test getting available languages."""
        languages = self.provider.get_available_languages()
        self.assertIn("en", languages)
        self.assertEqual(languages["en"], "English")

    def test_generate_speech_with_emotion(self):
        """Test generating speech with emotion."""
        with patch.object(self.provider, 'generate_speech') as mock_generate:
            mock_generate.return_value = "/tmp/test.mp3"

            result = self.provider.generate_speech_with_emotion("Hello, world!", "happy")

            mock_generate.assert_called_once()
            args, kwargs = mock_generate.call_args
            self.assertEqual(args[0], "Hello, world!")
            self.assertEqual(kwargs["personality"], "happy")
            self.assertEqual(result, "/tmp/test.mp3")

if __name__ == "__main__":
    unittest.main()
