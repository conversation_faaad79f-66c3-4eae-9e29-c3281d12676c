"""
Core provider components for VoicePal.

This package provides the core provider functionality for VoicePal.
"""

from bot.providers.core.exceptions import (
    ProviderError,
    ProviderInitializationError,
    ProviderAPIError,
    ProviderTimeoutError,
    ProviderAuthenticationError,
    ProviderRateLimitError,
    ProviderFeatureNotSupportedError,
    ProviderConfigurationError,
    ProviderResourceNotFoundError,
    ProviderInvalidInputError,
    ProviderQuotaExceededError,
    ProviderServiceUnavailableError
)
from bot.providers.core.http_client import HTTPClient
from bot.providers.core.cache_service import CacheService
from bot.providers.core.utils import (
    get_api_key,
    create_temp_file,
    clean_text_for_tts,
    generate_cache_key,
    ensure_directory_exists,
    get_file_extension_for_content_type,
    truncate_text,
    parse_language_code
)

__all__ = [
    'ProviderError',
    'ProviderInitializationError',
    'ProviderAPIError',
    'ProviderTimeoutError',
    'ProviderAuthenticationError',
    'ProviderRateLimitError',
    'ProviderFeatureNotSupportedError',
    'ProviderConfigurationError',
    'ProviderResourceNotFoundError',
    'ProviderInvalidInputError',
    'ProviderQuotaExceededError',
    'ProviderServiceUnavailableError',
    'HTTPClient',
    'CacheService',
    'get_api_key',
    'create_temp_file',
    'clean_text_for_tts',
    'generate_cache_key',
    'ensure_directory_exists',
    'get_file_extension_for_content_type',
    'truncate_text',
    'parse_language_code'
]
