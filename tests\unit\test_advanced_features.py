"""
Unit tests for advanced features module.
"""

import pytest
import tempfile
import os
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

from bot.advanced.ab_testing import ABTestManager, ABTestType, ABTestVariant, ABTestStatus
from bot.advanced.notification_system import NotificationManager, NotificationType, NotificationChannel
from bot.advanced.feature_flags import FeatureFlagManager, FeatureFlagType, RolloutStrategy, UserContext
from bot.advanced.scalability import ScalabilityManager, ServiceRegistry, LoadBalancer, LoadBalancerConfig
from bot.advanced.advanced_manager import AdvancedFeaturesManager


@pytest.fixture
def temp_db():
    """Create temporary database for testing."""
    fd, path = tempfile.mkstemp()
    os.close(fd)
    
    # Create mock database
    db = Mock()
    db.execute = Mock()
    db.commit = Mock()
    db.fetchall = Mock(return_value=[])
    db.fetchone = Mock(return_value=None)
    db.executemany = Mock()
    db.rowcount = 1
    
    yield db
    
    try:
        os.unlink(path)
    except OSError:
        pass


@pytest.fixture
def cache_manager():
    """Create mock cache manager."""
    cache = Mock()
    cache.get = Mock(return_value=None)
    cache.set = Mock()
    cache.delete = Mock()
    return cache


@pytest.fixture
def redis_client():
    """Create mock Redis client."""
    redis = Mock()
    redis.lpush = Mock()
    redis.expire = Mock()
    redis.get = Mock(return_value=None)
    redis.set = Mock()
    redis.publish = Mock()
    redis.hset = Mock()
    redis.sadd = Mock()
    redis.srem = Mock()
    redis.delete = Mock()
    return redis


@pytest.fixture
def telegram_bot():
    """Create mock Telegram bot."""
    bot = Mock()
    bot.send_message = Mock()
    return bot


class TestABTestManager:
    """Test A/B testing functionality."""
    
    def test_initialization(self, temp_db, cache_manager):
        """Test A/B test manager initialization."""
        manager = ABTestManager(temp_db, cache_manager)
        assert manager.database == temp_db
        assert manager.cache_manager == cache_manager
    
    def test_create_test(self, temp_db, cache_manager):
        """Test A/B test creation."""
        manager = ABTestManager(temp_db, cache_manager)
        
        variants = [
            ABTestVariant("control", "Control", "Control variant", 0.5, {}, True),
            ABTestVariant("treatment", "Treatment", "Treatment variant", 0.5, {"feature_enabled": True})
        ]
        
        test_id = manager.create_test(
            name="Test Feature",
            description="Testing new feature",
            test_type=ABTestType.FEATURE_FLAG,
            variants=variants,
            start_date=datetime.utcnow(),
            end_date=datetime.utcnow() + timedelta(days=7)
        )
        
        assert test_id.startswith("test_")
        assert temp_db.execute.called
        assert temp_db.commit.called
    
    def test_assign_user_to_test(self, temp_db, cache_manager):
        """Test user assignment to A/B test."""
        manager = ABTestManager(temp_db, cache_manager)
        
        # Mock get_test to return a test
        mock_test = Mock()
        mock_test.status = ABTestStatus.ACTIVE
        mock_test.target_audience = {}
        mock_test.variants = [
            ABTestVariant("control", "Control", "Control variant", 0.5, {}, True),
            ABTestVariant("treatment", "Treatment", "Treatment variant", 0.5, {"feature_enabled": True})
        ]
        
        with patch.object(manager, 'get_test', return_value=mock_test):
            with patch.object(manager, '_user_matches_audience', return_value=True):
                variant_id = manager.assign_user_to_test("test_123", 456)
                
                assert variant_id in ["control", "treatment"]
                assert temp_db.execute.called
    
    def test_track_conversion(self, temp_db, cache_manager):
        """Test conversion tracking."""
        manager = ABTestManager(temp_db, cache_manager)
        
        # Mock existing assignment
        temp_db.execute.return_value.fetchone.return_value = {'variant_id': 'treatment'}
        
        manager.track_conversion("test_123", 456, "purchase", {"amount": 29.99})
        
        assert temp_db.execute.called
        assert temp_db.commit.called
    
    def test_get_test_results(self, temp_db, cache_manager):
        """Test getting A/B test results."""
        manager = ABTestManager(temp_db, cache_manager)
        
        # Mock test data
        mock_test = Mock()
        mock_test.variants = [
            ABTestVariant("control", "Control", "Control variant", 0.5, {}, True),
            ABTestVariant("treatment", "Treatment", "Treatment variant", 0.5, {"feature_enabled": True})
        ]
        
        # Mock database responses
        temp_db.execute.return_value.fetchone.side_effect = [
            [100],  # participants for control
            [15],   # conversions for control
            [95],   # participants for treatment
            [20],   # conversions for treatment
            [0],    # revenue for control
            [0]     # revenue for treatment
        ]
        
        with patch.object(manager, 'get_test', return_value=mock_test):
            results = manager.get_test_results("test_123")
            
            assert len(results) == 2
            assert results[0].variant_id == "control"
            assert results[1].variant_id == "treatment"


class TestNotificationManager:
    """Test notification system functionality."""
    
    def test_initialization(self, temp_db, telegram_bot, redis_client):
        """Test notification manager initialization."""
        manager = NotificationManager(temp_db, telegram_bot, None, redis_client)
        assert manager.database == temp_db
        assert manager.telegram_bot == telegram_bot
        assert manager.redis_client == redis_client
    
    def test_create_template(self, temp_db, telegram_bot, redis_client):
        """Test notification template creation."""
        manager = NotificationManager(temp_db, telegram_bot, None, redis_client)
        
        template_id = manager.create_template(
            name="Welcome Message",
            notification_type=NotificationType.WELCOME,
            channel=NotificationChannel.TELEGRAM,
            content="Welcome to VoicePal, {name}!",
            variables=["name"]
        )
        
        assert template_id.startswith("template_")
        assert temp_db.execute.called
        assert temp_db.commit.called
    
    def test_schedule_notification(self, temp_db, telegram_bot, redis_client):
        """Test notification scheduling."""
        manager = NotificationManager(temp_db, telegram_bot, None, redis_client)
        
        # Mock template
        mock_template = Mock()
        mock_template.notification_type = NotificationType.WELCOME
        mock_template.content = "Welcome {name}!"
        mock_template.subject = None
        
        with patch.object(manager, 'get_template', return_value=mock_template):
            notification_id = manager.schedule_notification(
                user_id=123,
                template_id="template_456",
                channel=NotificationChannel.TELEGRAM,
                scheduled_at=datetime.utcnow() + timedelta(hours=1),
                variables={"name": "John"}
            )
            
            assert notification_id.startswith("notif_")
            assert temp_db.execute.called
    
    def test_send_immediate_notification(self, temp_db, telegram_bot, redis_client):
        """Test immediate notification sending."""
        manager = NotificationManager(temp_db, telegram_bot, None, redis_client)
        
        with patch.object(manager, '_send_notification', return_value=True):
            success = manager.send_immediate_notification(
                user_id=123,
                channel=NotificationChannel.TELEGRAM,
                notification_type=NotificationType.REMINDER,
                content="Don't forget to chat with VoicePal!"
            )
            
            assert success is True


class TestFeatureFlagManager:
    """Test feature flags functionality."""
    
    def test_initialization(self, temp_db, cache_manager, redis_client):
        """Test feature flag manager initialization."""
        manager = FeatureFlagManager(temp_db, cache_manager, redis_client)
        assert manager.database == temp_db
        assert manager.cache_manager == cache_manager
        assert manager.redis_client == redis_client
    
    def test_create_flag(self, temp_db, cache_manager, redis_client):
        """Test feature flag creation."""
        manager = FeatureFlagManager(temp_db, cache_manager, redis_client)
        
        flag_id = manager.create_flag(
            name="New Feature",
            description="Testing new feature",
            flag_type=FeatureFlagType.BOOLEAN,
            default_value=True,
            rollout_strategy=RolloutStrategy.PERCENTAGE,
            rollout_config={"percentage": 50}
        )
        
        assert flag_id.startswith("flag_")
        assert temp_db.execute.called
        assert temp_db.commit.called
    
    def test_evaluate_flag_all_users(self, temp_db, cache_manager, redis_client):
        """Test feature flag evaluation for all users."""
        manager = FeatureFlagManager(temp_db, cache_manager, redis_client)
        
        # Mock flag data
        mock_flag = Mock()
        mock_flag.enabled = True
        mock_flag.rollout_strategy = RolloutStrategy.ALL_USERS
        mock_flag.default_value = "true"
        mock_flag.flag_type = FeatureFlagType.BOOLEAN
        
        with patch.object(manager, 'get_flag', return_value=mock_flag):
            result = manager.evaluate_flag("test_flag", UserContext(123, {}, []))
            
            assert result is True
    
    def test_evaluate_flag_percentage(self, temp_db, cache_manager, redis_client):
        """Test feature flag evaluation with percentage rollout."""
        manager = FeatureFlagManager(temp_db, cache_manager, redis_client)
        
        # Mock flag data
        mock_flag = Mock()
        mock_flag.enabled = True
        mock_flag.rollout_strategy = RolloutStrategy.PERCENTAGE
        mock_flag.rollout_config = {"percentage": 100}  # 100% to ensure it's enabled
        mock_flag.default_value = "true"
        mock_flag.flag_type = FeatureFlagType.BOOLEAN
        
        with patch.object(manager, 'get_flag', return_value=mock_flag):
            with patch.object(manager, '_get_fallback_value', return_value=False):
                result = manager.evaluate_flag("test_flag", UserContext(123, {}, []))
                
                assert result is True
    
    def test_update_flag(self, temp_db, cache_manager, redis_client):
        """Test feature flag update."""
        manager = FeatureFlagManager(temp_db, cache_manager, redis_client)
        
        # Mock existing flag
        mock_flag = Mock()
        mock_flag.flag_type = FeatureFlagType.BOOLEAN
        
        with patch.object(manager, 'get_flag', return_value=mock_flag):
            success = manager.update_flag(
                "flag_123",
                enabled=False,
                rollout_config={"percentage": 25}
            )
            
            assert success is True
            assert temp_db.execute.called
            assert cache_manager.delete.called


class TestScalabilityManager:
    """Test scalability features."""
    
    def test_service_registry_initialization(self, redis_client):
        """Test service registry initialization."""
        registry = ServiceRegistry(redis_client)
        assert registry.redis_client == redis_client
        assert registry.services == {}
    
    def test_register_service(self, redis_client):
        """Test service registration."""
        registry = ServiceRegistry(redis_client)
        
        instance_id = registry.register_service(
            service_name="voicepal_bot",
            host="localhost",
            port=8000,
            weight=1,
            metadata={"version": "1.0.0"}
        )
        
        assert instance_id.startswith("voicepal_bot_localhost_8000_")
        assert "voicepal_bot" in registry.services
        assert len(registry.services["voicepal_bot"]) == 1
    
    def test_deregister_service(self, redis_client):
        """Test service deregistration."""
        registry = ServiceRegistry(redis_client)
        
        # Register a service first
        instance_id = registry.register_service("test_service", "localhost", 8000)
        
        # Deregister it
        success = registry.deregister_service(instance_id)
        
        assert success is True
        assert len(registry.services.get("test_service", [])) == 0
    
    def test_scalability_manager_initialization(self, redis_client):
        """Test scalability manager initialization."""
        manager = ScalabilityManager(redis_client)
        assert manager.redis_client == redis_client
        assert manager.service_registry is not None
        assert manager.load_balancer is not None
    
    def test_register_bot_instance(self, redis_client):
        """Test bot instance registration."""
        manager = ScalabilityManager(redis_client)
        
        instance_id = manager.register_bot_instance(
            host="localhost",
            port=8000,
            instance_metadata={"version": "1.0.0"}
        )
        
        assert instance_id.startswith("voicepal_bot_localhost_8000_")
    
    def test_get_bot_instance(self, redis_client):
        """Test bot instance retrieval."""
        manager = ScalabilityManager(redis_client)
        
        # Register an instance first
        manager.register_bot_instance("localhost", 8000)
        
        # Mock healthy instance
        with patch.object(manager.service_registry, 'get_healthy_instances') as mock_healthy:
            mock_instance = Mock()
            mock_instance.instance_id = "test_instance"
            mock_instance.host = "localhost"
            mock_instance.port = 8000
            mock_healthy.return_value = [mock_instance]
            
            instance = manager.get_bot_instance("request_123")
            
            assert instance is not None


class TestAdvancedFeaturesManager:
    """Test advanced features manager."""
    
    def test_initialization(self, temp_db, cache_manager, redis_client, telegram_bot):
        """Test advanced features manager initialization."""
        manager = AdvancedFeaturesManager(
            database=temp_db,
            cache_manager=cache_manager,
            redis_client=redis_client,
            telegram_bot=telegram_bot
        )
        
        assert manager.database == temp_db
        assert manager.ab_test_manager is not None
        assert manager.notification_manager is not None
        assert manager.scalability_manager is not None
        assert manager.feature_flag_manager is not None
    
    def test_create_ab_test(self, temp_db, cache_manager, redis_client, telegram_bot):
        """Test A/B test creation through manager."""
        manager = AdvancedFeaturesManager(temp_db, cache_manager, redis_client, telegram_bot)
        
        variants = [
            ABTestVariant("control", "Control", "Control variant", 0.5, {}, True),
            ABTestVariant("treatment", "Treatment", "Treatment variant", 0.5, {"feature_enabled": True})
        ]
        
        with patch.object(manager.ab_test_manager, 'create_test', return_value="test_123"):
            test_id = manager.create_ab_test(
                name="Test Feature",
                description="Testing new feature",
                test_type=ABTestType.FEATURE_FLAG,
                variants=variants,
                start_date=datetime.utcnow()
            )
            
            assert test_id == "test_123"
    
    def test_send_notification(self, temp_db, cache_manager, redis_client, telegram_bot):
        """Test notification sending through manager."""
        manager = AdvancedFeaturesManager(temp_db, cache_manager, redis_client, telegram_bot)
        
        with patch.object(manager.notification_manager, 'send_immediate_notification', return_value=True):
            success = manager.send_notification(
                user_id=123,
                channel=NotificationChannel.TELEGRAM,
                notification_type=NotificationType.REMINDER,
                content="Test notification"
            )
            
            assert success is True
    
    def test_is_feature_enabled(self, temp_db, cache_manager, redis_client, telegram_bot):
        """Test feature flag checking through manager."""
        manager = AdvancedFeaturesManager(temp_db, cache_manager, redis_client, telegram_bot)
        
        with patch.object(manager.feature_flag_manager, 'is_feature_enabled', return_value=True):
            enabled = manager.is_feature_enabled("test_feature", user_id=123)
            
            assert enabled is True
    
    def test_setup_user_onboarding_experiment(self, temp_db, cache_manager, redis_client, telegram_bot):
        """Test user onboarding experiment setup."""
        manager = AdvancedFeaturesManager(temp_db, cache_manager, redis_client, telegram_bot)
        
        with patch.object(manager, 'assign_user_to_test', return_value="treatment"):
            with patch.object(manager, 'send_notification', return_value=True):
                with patch.object(manager, 'is_feature_enabled', return_value=True):
                    result = manager.setup_user_onboarding_experiment(123)
                    
                    assert "ab_test_variant" in result
                    assert "notifications_scheduled" in result
                    assert "feature_flags" in result
                    assert result["ab_test_variant"] == "treatment"
    
    def test_handle_user_retention(self, temp_db, cache_manager, redis_client, telegram_bot):
        """Test user retention handling."""
        manager = AdvancedFeaturesManager(temp_db, cache_manager, redis_client, telegram_bot)
        
        with patch.object(manager, 'send_notification', return_value=True):
            with patch.object(manager, 'assign_user_to_test', return_value="strategy_a"):
                result = manager.handle_user_retention(123, days_inactive=5)
                
                assert "retention_strategy" in result
                assert "notifications_sent" in result
                assert "ab_test_assigned" in result
                assert result["retention_strategy"] == "feature_highlight"
    
    def test_get_advanced_features_status(self, temp_db, cache_manager, redis_client, telegram_bot):
        """Test advanced features status retrieval."""
        manager = AdvancedFeaturesManager(temp_db, cache_manager, redis_client, telegram_bot)
        
        with patch.object(manager.ab_test_manager, 'list_active_tests', return_value=[]):
            with patch.object(manager.feature_flag_manager, 'list_flags', return_value=[]):
                with patch.object(manager.scalability_manager, 'get_scalability_status', return_value={}):
                    status = manager.get_advanced_features_status()
                    
                    assert "timestamp" in status
                    assert "config" in status
                    assert "components" in status
                    assert status["components"]["ab_testing"]["enabled"] is True
                    assert status["components"]["notifications"]["enabled"] is True
                    assert status["components"]["feature_flags"]["enabled"] is True
