"""
API key rotation utility for VoicePal.

This script provides utilities for rotating API keys used by the VoicePal bot.
It can be run manually or scheduled to automatically rotate keys.
"""

import os
import sys
import argparse
import logging
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List

# Add parent directory to path to import bot modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from bot.database import Database
from bot.core.key_manager import KeyManager
from bot.config_manager import ConfigManager

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

def get_key_manager() -> KeyManager:
    """
    Get a KeyManager instance.
    
    Returns:
        KeyManager: Initialized key manager
    """
    # Initialize configuration
    config_manager = ConfigManager()
    
    # Initialize database
    db_config = config_manager.get_database_config()
    db_file = db_config.get("file", "voicepal.db")
    database = Database(db_file)
    
    # Get master key from environment
    master_key = os.environ.get("VOICEPAL_MASTER_KEY")
    
    # Initialize key manager
    key_manager = KeyManager(database.conn, master_key)
    
    return key_manager

def rotate_keys(services: Optional[List[str]] = None, force: bool = False) -> Dict[str, bool]:
    """
    Rotate API keys for specified services.
    
    Args:
        services: List of service names to rotate keys for (if None, rotates all keys)
        force: Force rotation even if keys are not expired
        
    Returns:
        Dict mapping service names to rotation success
    """
    key_manager = get_key_manager()
    config_manager = ConfigManager()
    
    # Get all services if not specified
    if not services:
        services = [
            "deepgram",
            "elevenlabs",
            "google_ai",
            "huggingface"
        ]
    
    results = {}
    
    for service in services:
        # Get current key info
        key_info = key_manager.get_key_info(service)
        
        if not key_info:
            logger.warning(f"No key found for service {service}")
            results[service] = False
            continue
        
        # Check if key needs rotation
        needs_rotation = force
        
        if not needs_rotation and key_info.get('is_expired'):
            needs_rotation = True
            logger.info(f"Key for service {service} is expired")
        
        # Get rotation interval from config
        provider_config = config_manager.get_provider_config(service)
        rotation_days = provider_config.get("key_rotation_days", 90)
        
        # Check if key is older than rotation interval
        if not needs_rotation and key_info.get('updated_at'):
            updated_at = datetime.fromisoformat(key_info['updated_at'])
            if datetime.now() - updated_at > timedelta(days=rotation_days):
                needs_rotation = True
                logger.info(f"Key for service {service} is older than {rotation_days} days")
        
        if needs_rotation:
            # Get new key from environment or config
            new_key = None
            
            # Try environment variable first
            env_var_name = f"{service.upper()}_API_KEY"
            new_key = os.environ.get(env_var_name)
            
            # If not in environment, try config
            if not new_key:
                new_key = provider_config.get("api_key")
            
            if new_key:
                # Calculate expiration date
                expires_at = datetime.now() + timedelta(days=rotation_days)
                
                # Rotate key
                success = key_manager.rotate_key(
                    service=service,
                    new_api_key=new_key,
                    reason="scheduled_rotation" if not force else "forced_rotation"
                )
                
                results[service] = success
                logger.info(f"Rotated key for service {service}: {success}")
            else:
                logger.error(f"No new key available for service {service}")
                results[service] = False
        else:
            logger.info(f"Key for service {service} does not need rotation")
            results[service] = True
    
    return results

def list_keys() -> None:
    """List all API keys and their status."""
    key_manager = get_key_manager()
    config_manager = ConfigManager()
    
    services = [
        "deepgram",
        "elevenlabs",
        "google_ai",
        "huggingface"
    ]
    
    print("\nAPI Key Status:")
    print("==============")
    
    for service in services:
        key_info = key_manager.get_key_info(service)
        
        if key_info:
            status = "ACTIVE" if key_info['is_active'] else "INACTIVE"
            if key_info['is_expired']:
                status += " (EXPIRED)"
                
            created = datetime.fromisoformat(key_info['created_at']).strftime('%Y-%m-%d')
            updated = datetime.fromisoformat(key_info['updated_at']).strftime('%Y-%m-%d')
            
            expires = "Never"
            if key_info['expires_at']:
                expires = datetime.fromisoformat(key_info['expires_at']).strftime('%Y-%m-%d')
            
            print(f"{service.upper()}: {status}")
            print(f"  Created: {created}")
            print(f"  Updated: {updated}")
            print(f"  Expires: {expires}")
            print()
        else:
            print(f"{service.upper()}: NOT FOUND")
            print()

def list_access_logs(service: Optional[str] = None, limit: int = 20) -> None:
    """
    List access logs for API keys.
    
    Args:
        service: Service name (optional)
        limit: Maximum number of logs to show
    """
    key_manager = get_key_manager()
    
    logs = key_manager.get_access_logs(service, limit)
    
    print("\nAPI Key Access Logs:")
    print("===================")
    
    if not logs:
        print("No access logs found.")
        return
    
    for log in logs:
        timestamp = datetime.fromisoformat(log['timestamp']).strftime('%Y-%m-%d %H:%M:%S')
        status = "SUCCESS" if log['success'] else "FAILED"
        
        print(f"{timestamp} | {log['service'].upper()} | {log['access_type'].upper()} | {status}")
        if log['user_id']:
            print(f"  User: {log['user_id']}")
        if log['ip_address']:
            print(f"  IP: {log['ip_address']}")
        print()

def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="API Key Rotation Utility")
    
    # Create subparsers for different commands
    subparsers = parser.add_subparsers(dest="command", help="Command to run")
    
    # Rotate command
    rotate_parser = subparsers.add_parser("rotate", help="Rotate API keys")
    rotate_parser.add_argument("--services", nargs="+", help="Services to rotate keys for")
    rotate_parser.add_argument("--force", action="store_true", help="Force rotation even if keys are not expired")
    
    # List command
    list_parser = subparsers.add_parser("list", help="List API keys")
    
    # Logs command
    logs_parser = subparsers.add_parser("logs", help="Show access logs")
    logs_parser.add_argument("--service", help="Service to show logs for")
    logs_parser.add_argument("--limit", type=int, default=20, help="Maximum number of logs to show")
    
    args = parser.parse_args()
    
    if args.command == "rotate":
        results = rotate_keys(args.services, args.force)
        
        print("\nRotation Results:")
        print("================")
        for service, success in results.items():
            print(f"{service}: {'SUCCESS' if success else 'FAILED'}")
    
    elif args.command == "list":
        list_keys()
    
    elif args.command == "logs":
        list_access_logs(args.service, args.limit)
    
    else:
        parser.print_help()

if __name__ == "__main__":
    main()
