"""
Provider exceptions for VoicePal.

This module provides exception classes for provider-related errors.
"""

class ProviderError(Exception):
    """Base exception for provider errors."""

    def __init__(self, message: str, cause: Exception = None):
        """Initialize exception.

        Args:
            message: Error message
            cause: Cause of the error
        """
        super().__init__(message)
        self.__cause__ = cause

class ProviderConfigError(ProviderError):
    """Exception for provider configuration errors."""
    pass

class ProviderAuthError(ProviderError):
    """Exception for provider authentication errors."""
    pass

class ProviderRateLimitError(ProviderError):
    """Exception for provider rate limit errors."""
    pass

class ProviderTimeoutError(ProviderError):
    """Exception for provider timeout errors."""
    pass

class ProviderNotFoundError(ProviderError):
    """Exception for provider not found errors."""
    pass

class ProviderValidationError(ProviderError):
    """Exception for provider validation errors."""
    pass

class ProviderNetworkError(ProviderError):
    """Exception for provider network errors."""
    pass

class ProviderServiceError(ProviderError):
    """Exception for provider service errors."""
    pass

class ProviderQuotaExceededError(ProviderError):
    """Exception for provider quota exceeded errors."""
    pass

class ProviderUnsupportedError(ProviderError):
    """Exception for unsupported provider features."""
    pass

class ProviderInvalidInputError(ProviderError):
    """Exception for invalid input to provider."""
    pass

class ProviderInvalidOutputError(ProviderError):
    """Exception for invalid output from provider."""
    pass

class ProviderInitializationError(ProviderError):
    """Exception for provider initialization errors."""
    pass

class ProviderShutdownError(ProviderError):
    """Exception for provider shutdown errors."""
    pass

class ProviderNotInitializedError(ProviderError):
    """Exception for provider not initialized errors."""
    pass

class ProviderAlreadyInitializedError(ProviderError):
    """Exception for provider already initialized errors."""
    pass

class ProviderFactoryError(ProviderError):
    """Exception for provider factory errors."""
    pass

class ProviderRegistryError(ProviderError):
    """Exception for provider registry errors."""
    pass

class ProviderNotRegisteredError(ProviderRegistryError):
    """Exception for provider not registered errors."""
    pass

class ProviderAlreadyRegisteredError(ProviderRegistryError):
    """Exception for provider already registered errors."""
    pass

class ProviderTypeNotRegisteredError(ProviderRegistryError):
    """Exception for provider type not registered errors."""
    pass

class ProviderTypeAlreadyRegisteredError(ProviderRegistryError):
    """Exception for provider type already registered errors."""
    pass

# Aliases for backward compatibility
ProviderConfigurationError = ProviderConfigError
ProviderAuthenticationError = ProviderAuthError
ProviderFeatureNotSupportedError = ProviderUnsupportedError
ProviderResourceNotFoundError = ProviderNotFoundError
ProviderServiceUnavailableError = ProviderServiceError
ProviderAPIError = ProviderNetworkError
