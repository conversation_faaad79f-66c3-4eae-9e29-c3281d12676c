"""
Test script for Deepgram integration.

This script tests the Deepgram integration with the provided API key.
It creates a simple test audio file and transcribes it using different models and parameters.

Usage:
    python test_deepgram.py
"""

import os
import asyncio
import logging
import tempfile
from gtts import gTTS
from bot.providers.voice.processor import VoiceProcessor

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# Deepgram API key
DEEPGRAM_API_KEY = "****************************************"

# Test phrases
TEST_PHRASES = [
    "Hello, this is a test of the speech recognition system.",
    "VoicePal is a Telegram bot that provides companionship through conversation.",
    "The weather today is sunny with a chance of rain later."
]

# Deepgram models to test
MODELS = ["nova-3", "nova-2", "enhanced"]

# Parameter sets to test
PARAMETER_SETS = [
    {"smart_format": True},
    {"smart_format": True, "diarize": True},
    {"smart_format": True, "detect_topics": True},
    {"smart_format": True, "diarize": True, "detect_topics": True}
]

async def create_test_audio(text):
    """Create a test audio file with the given text."""
    # Create a temporary file
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".mp3")
    temp_file_path = temp_file.name
    temp_file.close()

    # Generate speech
    tts = gTTS(text=text, lang='en', slow=False)
    tts.save(temp_file_path)

    return temp_file_path

async def test_models():
    """Test different Deepgram models."""
    logger.info("Testing different Deepgram models...")

    # Create voice processor with API key
    voice_processor = VoiceProcessor(deepgram_api_key=DEEPGRAM_API_KEY)

    # Create test audio
    audio_path = await create_test_audio(TEST_PHRASES[0])

    try:
        # Test each model
        for model in MODELS:
            logger.info(f"\nTesting model: {model}")

            # Transcribe audio
            transcript = await voice_processor.transcribe_audio(
                audio_file_path=audio_path,
                model=model
            )

            logger.info(f"Original: '{TEST_PHRASES[0]}'")
            logger.info(f"Transcript: '{transcript}'")
    finally:
        # Clean up
        if os.path.exists(audio_path):
            os.remove(audio_path)

async def test_parameters():
    """Test different Deepgram parameters."""
    logger.info("\nTesting different Deepgram parameters...")

    # Create voice processor with API key
    voice_processor = VoiceProcessor(deepgram_api_key=DEEPGRAM_API_KEY)

    # Create test audio
    audio_path = await create_test_audio(TEST_PHRASES[1])

    try:
        # Test each parameter set
        for i, params in enumerate(PARAMETER_SETS):
            logger.info(f"\nTesting parameter set {i+1}: {params}")

            # Transcribe audio
            transcript = await voice_processor.transcribe_audio_with_params(
                audio_file_path=audio_path,
                **params
            )

            logger.info(f"Original: '{TEST_PHRASES[1]}'")
            logger.info(f"Transcript: '{transcript}'")
    finally:
        # Clean up
        if os.path.exists(audio_path):
            os.remove(audio_path)

async def test_confidence_threshold():
    """Test confidence threshold."""
    logger.info("\nTesting confidence threshold...")

    # Create voice processor with API key
    voice_processor = VoiceProcessor(deepgram_api_key=DEEPGRAM_API_KEY)

    # Create test audio
    audio_path = await create_test_audio(TEST_PHRASES[2])

    try:
        # Test different confidence thresholds
        for threshold in [0.0, 0.5, 0.8, 0.95]:
            logger.info(f"\nTesting confidence threshold: {threshold}")

            # Transcribe audio
            transcript = await voice_processor.transcribe_audio(
                audio_file_path=audio_path,
                confidence_threshold=threshold
            )

            logger.info(f"Original: '{TEST_PHRASES[2]}'")
            logger.info(f"Transcript: '{transcript if transcript else 'Rejected (below threshold)'}'")
    finally:
        # Clean up
        if os.path.exists(audio_path):
            os.remove(audio_path)

async def main():
    """Run all tests."""
    logger.info("Testing Deepgram integration with API key: " + DEEPGRAM_API_KEY[:5] + "...")

    # Test different models
    await test_models()

    # Test different parameters
    await test_parameters()

    # Test confidence threshold
    await test_confidence_threshold()

    logger.info("\nAll tests completed!")

if __name__ == "__main__":
    asyncio.run(main())
