# VoicePal Codebase Cleanup

This directory contains scripts to clean up the VoicePal codebase by removing duplicate implementations, updating imports, and cleaning up outdated components.

## Cleanup Scripts

### Master Cleanup Script

- `master_cleanup.py`: Runs all the cleanup scripts in the correct order.

### Individual Cleanup Scripts

- `update_imports_for_enhanced.py`: Updates imports to use enhanced implementations of memory manager and dialog engine.
- `cleanup_tts_providers.py`: Cleans up TTS providers by removing duplicates and updating imports.
- `cleanup_codebase.py`: Removes duplicate implementations and outdated components.
- `update_requirements.py`: Updates requirements.txt to remove outdated dependencies.

## Running the Cleanup

To run the complete cleanup process, use the master cleanup script:

```bash
python scripts/master_cleanup.py
```

This will run all the cleanup scripts in the correct order and provide a summary of the results.

## Running Individual Scripts

You can also run individual cleanup scripts if needed:

```bash
python scripts/update_imports_for_enhanced.py
python scripts/cleanup_tts_providers.py
python scripts/cleanup_codebase.py
python scripts/update_requirements.py
```

## Backup Files

Before making any changes, the cleanup scripts create backup files with the `.bak` extension. If something goes wrong, you can restore the original files from these backups.

## Cleanup Details

### Memory System and Dialog Engine

The codebase had duplicate implementations of the memory system and dialog engine:

- `MemoryManager` and `EnhancedMemoryManager`
- `DialogEngine` and `EnhancedDialogEngine`

The cleanup scripts update imports to use the enhanced implementations and remove the original implementations.

### TTS Providers

The codebase had multiple TTS providers with overlapping functionality:

- `DeepgramTTSProvider`
- `ElevenLabsProvider`
- `GoogleTTSProvider`
- `DiaTTSProvider`

The cleanup scripts identify the preferred provider for each type and remove the others, updating imports as needed.

### Outdated Components

The cleanup scripts remove outdated components such as:

- `dia_provider.py`: Outdated TTS provider
- `main.py.bak`: Backup file

### Requirements

The cleanup scripts update requirements.txt to:

- Remove outdated dependencies
- Update dependencies to newer versions
- Add missing dependencies for testing
- Ensure compatibility between packages

## Verification

After running the cleanup scripts, you should verify that the codebase still works correctly by:

1. Running the tests:

```bash
python tests/run_tests.py
```

2. Running the bot:

```bash
python run_bot.py
```

If you encounter any issues, you can restore the original files from the backup files created during the cleanup process.

## Troubleshooting

If you encounter any issues during the cleanup process, check the log output for details. The cleanup scripts log all actions and errors to help diagnose problems.

Common issues:

- **Import errors**: If you see import errors after running the cleanup scripts, check that all imports have been updated correctly.
- **Missing files**: If you see errors about missing files, check that the cleanup scripts haven't removed files that are still needed.
- **Dependency conflicts**: If you see dependency conflicts after updating requirements.txt, you may need to manually resolve them.

## Manual Cleanup

If the automatic cleanup scripts don't work as expected, you can perform the cleanup manually:

1. Replace imports of `MemoryManager` with `EnhancedMemoryManager`
2. Replace imports of `DialogEngine` with `EnhancedDialogEngine`
3. Remove duplicate TTS providers and update imports
4. Remove outdated components
5. Update requirements.txt

## Contributing

If you find any issues with the cleanup scripts or have suggestions for improvement, please open an issue or pull request on the repository.
