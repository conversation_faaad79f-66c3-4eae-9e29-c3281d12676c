"""
Database schema for VoicePal.

This module defines the unified database schema for VoicePal.
It ensures consistent table definitions across the application.
"""

import sqlite3
import logging
from typing import Dict, Any, Optional, List, Tuple

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# Table definitions with proper foreign keys and ON DELETE CASCADE
SCHEMA_DEFINITIONS = {
    "users": """
        CREATE TABLE IF NOT EXISTS users (
            user_id INTEGER PRIMARY KEY,
            username TEXT,
            first_name TEXT,
            last_name TEXT,
            credits INTEGER DEFAULT 0,
            personality TEXT DEFAULT 'friendly',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_active TIMESTAMP,
            free_credits_received BOOLEAN DEFAULT 0,
            free_credits_date TIMESTAMP,
            is_verified BOOLEAN DEFAULT 0,
            verification_date TIMESTAMP,
            device_id TEXT,
            registration_ip TEXT,
            last_ip TEXT,
            visit_count INTEGER DEFAULT 0,
            timezone TEXT,
            preferred_name TEXT
        )
    """,
    "transactions": """
        CREATE TABLE IF NOT EXISTS transactions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            amount INTEGER,
            credits INTEGER,
            transaction_id TEXT UNIQUE,
            status TEXT,
            source TEXT DEFAULT 'purchase',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
        )
    """,
    "conversations": """
        CREATE TABLE IF NOT EXISTS conversations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            message TEXT,
            response TEXT,
            is_voice BOOLEAN,
            credits_used INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
        )
    """,
    "user_preferences": """
        CREATE TABLE IF NOT EXISTS user_preferences (
            user_id INTEGER NOT NULL,
            preference_key TEXT NOT NULL,
            preference_value TEXT,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (user_id, preference_key),
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
        )
    """,
    "mood_entries": """
        CREATE TABLE IF NOT EXISTS mood_entries (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            sentiment TEXT,
            confidence REAL,
            source TEXT,
            message_text TEXT,
            emotions TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
        )
    """,
    "user_summaries": """
        CREATE TABLE IF NOT EXISTS user_summaries (
            user_id INTEGER PRIMARY KEY,
            summary TEXT,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
        )
    """,
    "user_interests": """
        CREATE TABLE IF NOT EXISTS user_interests (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            interest TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
        )
    """,
    "invoices": """
        CREATE TABLE IF NOT EXISTS invoices (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            package_id TEXT,
            payload TEXT UNIQUE,
            amount INTEGER,
            currency TEXT,
            status TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
        )
    """,
    "sentiment_responses": """
        CREATE TABLE IF NOT EXISTS sentiment_responses (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            sentiment TEXT,
            response_template TEXT,
            tone TEXT,
            empathy_level TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    """,
    "verification_codes": """
        CREATE TABLE IF NOT EXISTS verification_codes (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            code TEXT,
            verification_type TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            expires_at TIMESTAMP,
            used BOOLEAN DEFAULT 0,
            UNIQUE(user_id, verification_type),
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
        )
    """,
    "verification_attempts": """
        CREATE TABLE IF NOT EXISTS verification_attempts (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            verification_type TEXT,
            attempt_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            success BOOLEAN,
            ip_address TEXT,
            device_id TEXT,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
        )
    """
}

# Indexes for performance optimization
INDEXES = [
    "CREATE INDEX IF NOT EXISTS idx_transactions_user_id ON transactions(user_id)",
    "CREATE INDEX IF NOT EXISTS idx_conversations_user_id ON conversations(user_id)",
    "CREATE INDEX IF NOT EXISTS idx_mood_entries_user_id ON mood_entries(user_id)",
    "CREATE INDEX IF NOT EXISTS idx_user_interests_user_id ON user_interests(user_id)",
    "CREATE INDEX IF NOT EXISTS idx_invoices_user_id ON invoices(user_id)",
    "CREATE INDEX IF NOT EXISTS idx_verification_codes_user_id ON verification_codes(user_id)",
    "CREATE INDEX IF NOT EXISTS idx_verification_attempts_user_id ON verification_attempts(user_id)"
]

def initialize_database(db_path: str) -> sqlite3.Connection:
    """
    Initialize the database with the schema.

    Args:
        db_path: Path to the database file

    Returns:
        sqlite3.Connection: Database connection
    """
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row

        # Enable foreign keys
        conn.execute("PRAGMA foreign_keys = ON")

        # Create tables
        for table_name, table_schema in SCHEMA_DEFINITIONS.items():
            conn.execute(table_schema)

        # Create indexes
        for index in INDEXES:
            conn.execute(index)

        # Run migrations to ensure schema is up to date
        run_migrations(conn)

        conn.commit()
        logger.info(f"Database initialized successfully: {db_path}")
        return conn
    except Exception as e:
        logger.error(f"Error initializing database: {e}")
        if conn:
            conn.close()
        raise

def run_migrations(conn: sqlite3.Connection) -> None:
    """
    Run database migrations to ensure schema is up to date.

    Args:
        conn: Database connection
    """
    try:
        cursor = conn.cursor()

        # Check if login_count column exists in users table
        cursor.execute("PRAGMA table_info(users)")
        columns = cursor.fetchall()
        column_names = [column['name'] for column in columns]

        logger.info(f"Current columns in users table: {column_names}")

        # Login count column is no longer used
        # We don't add it to avoid errors

        # Add visit_count column if it doesn't exist
        if 'visit_count' not in column_names:
            logger.info("Adding visit_count column to users table")
            try:
                cursor.execute("ALTER TABLE users ADD COLUMN visit_count INTEGER DEFAULT 0")
                conn.commit()
                logger.info("Successfully added visit_count column")
            except Exception as e:
                logger.error(f"Error adding visit_count column: {e}")
                conn.rollback()

        # Add timezone column if it doesn't exist
        if 'timezone' not in column_names:
            logger.info("Adding timezone column to users table")
            try:
                cursor.execute("ALTER TABLE users ADD COLUMN timezone TEXT")
                conn.commit()
                logger.info("Successfully added timezone column")
            except Exception as e:
                logger.error(f"Error adding timezone column: {e}")
                conn.rollback()

        # Add preferred_name column if it doesn't exist
        if 'preferred_name' not in column_names:
            logger.info("Adding preferred_name column to users table")
            try:
                cursor.execute("ALTER TABLE users ADD COLUMN preferred_name TEXT")
                conn.commit()
                logger.info("Successfully added preferred_name column")
            except Exception as e:
                logger.error(f"Error adding preferred_name column: {e}")
                conn.rollback()

        # Verify columns after migrations
        cursor.execute("PRAGMA table_info(users)")
        columns_after = cursor.fetchall()
        column_names_after = [column['name'] for column in columns_after]
        logger.info(f"Columns in users table after migrations: {column_names_after}")

        logger.info("Database migrations completed successfully")
    except Exception as e:
        logger.error(f"Error running database migrations: {e}")
        conn.rollback()
        raise
