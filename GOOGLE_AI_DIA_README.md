# Google AI + Dia TTS Integration for VoicePal

This document provides instructions for using the Google AI and Dia TTS integration in VoicePal.

## Overview

The integration adds two new components to VoicePal:

1. **Google AI Conversation**: Uses Google's Gemini model for generating more natural and contextual responses
2. **Dia TTS Provider**: Uses Hugging Face's Dia-1.6B model for high-quality text-to-speech (with fallback to Google TTS)

## Setup

1. Add the following environment variables to your `.env` file:

```
# API Keys
GOOGLE_AI_API_KEY=your_google_ai_api_key_here
HF_API_TOKEN=your_hugging_face_token_here

# AI and TTS Configuration
AI_PROVIDER=google_ai  # simple or google_ai
TTS_PROVIDER=dia       # google or dia
DIA_MODEL_ID=spaces/nari-labs/Dia-1.6B
```

2. Install the required dependencies:

```bash
pip install -r requirements.txt
```

## Testing

You can test the integration using the provided test scripts:

### Basic Component Tests

The `test_google_ai_dia.py` script allows you to test individual components:

```bash
# Test Google AI with Google TTS
python test_google_ai_dia.py --ai google_ai --tts google

# Test simple AI with Dia TTS
python test_google_ai_dia.py --ai simple --tts dia

# Test Google AI with Dia TTS (full upgrade)
python test_google_ai_dia.py --ai google_ai --tts dia
```

### Full Flow Tests

The `test_full_flow.py` script demonstrates the complete flow:

```bash
# Test Audio to Audio flow with Dia TTS
python test_full_flow.py --mode audio

# Test Text to Text flow with Google AI
python test_full_flow.py --mode text
```

## Implementation Details

### Google AI Conversation

The `GoogleAIConversation` class extends the existing `AIConversation` class to use Google's Gemini model. It supports the same personality system as the original implementation, but provides more natural and contextual responses.

Key features:
- Supports multiple personalities (friendly, witty, calm, motivational, thoughtful)
- Maintains conversation history
- Falls back to the original implementation if the API key is missing or there's an error

### Dia TTS Provider

The `DiaTTSProvider` class implements the `TTSProvider` interface to use Hugging Face's Dia-1.6B model. It currently falls back to Google TTS due to API limitations, but the code is structured to easily switch to Dia when the API is working.

Key features:
- Supports caching to reduce API calls
- Falls back to Google TTS if there's an error
- Maintains the same interface as other TTS providers

## Configuration Options

### AI Provider

- `simple`: Uses the original rule-based conversation system
- `google_ai`: Uses Google's Gemini model for more natural responses

### TTS Provider

- `google`: Uses Google TTS (default)
- `dia`: Uses Dia TTS (currently falls back to Google TTS)

## Troubleshooting

### Google AI Issues

- If you see "Error getting response from Google AI", check your API key and quota
- The API may have rate limits, so you might need to wait between requests
- If you see "ChatSession.send_message() got an unexpected keyword argument", the API might have changed

### Dia TTS Issues

- If you see "Error from Hugging Face API: 404 Not Found", the model ID might be incorrect
- The Hugging Face API may have rate limits
- The implementation currently falls back to Google TTS if there's an error

## Next Steps

1. Implement a proper Dia TTS integration using the Hugging Face Inference API
2. Add more personality options to the Google AI conversation
3. Implement caching for both Google AI and Dia TTS to reduce API calls
4. Add more error handling and fallback options
