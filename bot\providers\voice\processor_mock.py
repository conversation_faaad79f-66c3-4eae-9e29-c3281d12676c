"""
Mock voice processing module for VoicePal testing.

This module provides a mock implementation of the VoiceProcessor class for testing.
"""

import os
import logging
import tempfile
from typing import Optional, Dict, Any, Union
from pathlib import Path

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class VoiceProcessor:
    """Mock voice processing handler for VoicePal testing."""

    def __init__(self, deepgram_api_key: Optional[str] = None,
                default_language: str = 'en',
                default_voice: str = 'en',
                tts_provider: str = 'google',
                tts_provider_options: Optional[Dict[str, Any]] = None,
                personality: str = 'friendly'):
        """
        Initialize the mock voice processor.
        
        Args:
            deepgram_api_key: Deepgram API key (defaults to DEEPGRAM_API_KEY env var)
            default_language: Default language for speech recognition
            default_voice: Default voice for speech synthesis
            tts_provider: TTS provider type (google, huggingface)
            tts_provider_options: Additional options for the TTS provider
            personality: AI personality type (friendly, witty, calm, motivational, thoughtful)
        """
        self.deepgram_api_key = deepgram_api_key or os.getenv("DEEPGRAM_API_KEY")
        self.default_language = default_language
        self.default_voice = default_voice
        self.personality = personality
        self.tts_provider_type = tts_provider
        self.tts_provider_options = tts_provider_options or {}
        
        # Log initialization
        logger.info("Initialized mock voice processor with %s provider", tts_provider)
    
    async def transcribe_audio(self, audio_file_path: Union[str, Path], **kwargs) -> Dict[str, Any]:
        """
        Mock transcribe audio file to text.
        
        Args:
            audio_file_path: Path to the audio file
            **kwargs: Additional parameters
            
        Returns:
            Dict containing transcript and metadata
        """
        logger.info("Mock transcribing audio file: %s", audio_file_path)
        
        # Return mock transcript
        return {
            "transcript": "This is a mock transcript for testing purposes.",
            "language": self.default_language
        }
    
    async def transcribe_audio_with_language_detection(self, audio_file_path: Union[str, Path], **kwargs) -> tuple:
        """
        Mock transcribe audio file to text with language detection.
        
        Args:
            audio_file_path: Path to the audio file
            **kwargs: Additional parameters
            
        Returns:
            Tuple of (transcribed_text, detected_language_code)
        """
        logger.info("Mock transcribing audio file with language detection: %s", audio_file_path)
        
        # Return mock transcript and language
        return "This is a mock transcript for testing purposes.", self.default_language
    
    async def transcribe_audio_with_params(self, audio_file_path: Union[str, Path], **kwargs) -> Optional[str]:
        """
        Mock transcribe audio file to text with custom parameters.
        
        Args:
            audio_file_path: Path to the audio file
            **kwargs: Additional parameters
            
        Returns:
            Transcribed text or None if transcription failed
        """
        logger.info("Mock transcribing audio file with params: %s", audio_file_path)
        
        # Return mock transcript
        return "This is a mock transcript for testing purposes."
    
    def generate_voice_response(self, text: str, **kwargs) -> Optional[str]:
        """
        Mock generate voice response from text.
        
        Args:
            text: Text to convert to speech
            **kwargs: Additional parameters
            
        Returns:
            Path to the generated audio file or None if generation failed
        """
        logger.info("Mock generating voice response for: %s", text[:30] + "..." if len(text) > 30 else text)
        
        # Create a temporary file
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".mp3")
        temp_file_path = temp_file.name
        temp_file.close()
        
        # Write some dummy data to the file
        with open(temp_file_path, "wb") as f:
            f.write(b"MOCK_AUDIO_DATA")
        
        return temp_file_path
    
    def set_personality(self, personality: str) -> None:
        """
        Set the AI personality for voice generation.
        
        Args:
            personality: AI personality type
        """
        self.personality = personality
        logger.info("Set mock voice personality to %s", personality)
    
    def set_tts_provider(self, provider_type: str, **provider_options) -> bool:
        """
        Change the TTS provider.
        
        Args:
            provider_type: Type of provider (elevenlabs, deepgram, google)
            **provider_options: Additional provider-specific parameters
            
        Returns:
            bool: True if provider was changed successfully, False otherwise
        """
        self.tts_provider_type = provider_type
        self.tts_provider_options = provider_options
        logger.info("Changed mock TTS provider to %s", provider_type)
        return True
    
    def get_available_languages(self) -> Dict[str, str]:
        """
        Get available TTS languages.
        
        Returns:
            Dictionary of language codes and names
        """
        return {
            "en": "English",
            "fr": "French",
            "es": "Spanish",
            "de": "German",
            "it": "Italian"
        }
    
    def get_available_voices(self, language: Optional[str] = None) -> Dict[str, str]:
        """
        Get available voices.
        
        Args:
            language: Language code to filter voices
            
        Returns:
            Dictionary of voice identifiers and names
        """
        return {
            "voice1": "Female Voice 1",
            "voice2": "Male Voice 1",
            "voice3": "Female Voice 2",
            "voice4": "Male Voice 2"
        }
    
    def cleanup_temp_file(self, file_path: Optional[str]) -> None:
        """
        Clean up temporary file.
        
        Args:
            file_path: Path to the file to clean up
        """
        if file_path and os.path.exists(file_path):
            try:
                os.unlink(file_path)
                logger.debug("Cleaned up temporary file: %s", file_path)
            except Exception as e:
                logger.error("Error cleaning up temporary file %s: %s", file_path, str(e))
    
    async def close(self) -> None:
        """Close the voice processor and release resources."""
        # No cleanup needed for mock
