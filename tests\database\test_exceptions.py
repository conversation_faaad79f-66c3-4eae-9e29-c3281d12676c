"""
Tests for database exceptions.

This module tests the database exception classes.
"""

import unittest

from bot.database.core.exceptions import (
    DatabaseError,
    DatabaseConnectionError,
    DatabaseQueryError,
    DatabaseIntegrityError,
    DatabaseMigrationError,
    DatabaseSchemaError,
    DatabaseTransactionError,
    DatabaseTimeoutError,
    DatabaseNotFoundError,
    DatabaseDuplicateError,
    InsufficientCreditsError
)

class TestDatabaseExceptions(unittest.TestCase):
    """Test case for database exceptions."""
    
    def test_database_error(self):
        """Test DatabaseError."""
        # Test with message
        error = DatabaseError("Test error")
        self.assertEqual(str(error), "Test error")
        
        # Test with cause
        cause = ValueError("Cause error")
        error = DatabaseError("Test error", cause)
        self.assertEqual(str(error), "Test error")
        self.assertEqual(error.__cause__, cause)
    
    def test_database_connection_error(self):
        """Test DatabaseConnectionError."""
        # Test with message
        error = DatabaseConnectionError("Connection error")
        self.assertEqual(str(error), "Connection error")
        self.assertIsInstance(error, DatabaseError)
        
        # Test with cause
        cause = ValueError("Cause error")
        error = DatabaseConnectionError("Connection error", cause)
        self.assertEqual(str(error), "Connection error")
        self.assertEqual(error.__cause__, cause)
    
    def test_database_query_error(self):
        """Test DatabaseQueryError."""
        # Test with message
        error = DatabaseQueryError("Query error")
        self.assertEqual(str(error), "Query error")
        self.assertIsInstance(error, DatabaseError)
        
        # Test with cause
        cause = ValueError("Cause error")
        error = DatabaseQueryError("Query error", cause)
        self.assertEqual(str(error), "Query error")
        self.assertEqual(error.__cause__, cause)
    
    def test_database_integrity_error(self):
        """Test DatabaseIntegrityError."""
        # Test with message
        error = DatabaseIntegrityError("Integrity error")
        self.assertEqual(str(error), "Integrity error")
        self.assertIsInstance(error, DatabaseError)
        
        # Test with cause
        cause = ValueError("Cause error")
        error = DatabaseIntegrityError("Integrity error", cause)
        self.assertEqual(str(error), "Integrity error")
        self.assertEqual(error.__cause__, cause)
    
    def test_database_migration_error(self):
        """Test DatabaseMigrationError."""
        # Test with message
        error = DatabaseMigrationError("Migration error")
        self.assertEqual(str(error), "Migration error")
        self.assertIsInstance(error, DatabaseError)
        
        # Test with cause
        cause = ValueError("Cause error")
        error = DatabaseMigrationError("Migration error", cause)
        self.assertEqual(str(error), "Migration error")
        self.assertEqual(error.__cause__, cause)
    
    def test_database_schema_error(self):
        """Test DatabaseSchemaError."""
        # Test with message
        error = DatabaseSchemaError("Schema error")
        self.assertEqual(str(error), "Schema error")
        self.assertIsInstance(error, DatabaseError)
        
        # Test with cause
        cause = ValueError("Cause error")
        error = DatabaseSchemaError("Schema error", cause)
        self.assertEqual(str(error), "Schema error")
        self.assertEqual(error.__cause__, cause)
    
    def test_database_transaction_error(self):
        """Test DatabaseTransactionError."""
        # Test with message
        error = DatabaseTransactionError("Transaction error")
        self.assertEqual(str(error), "Transaction error")
        self.assertIsInstance(error, DatabaseError)
        
        # Test with cause
        cause = ValueError("Cause error")
        error = DatabaseTransactionError("Transaction error", cause)
        self.assertEqual(str(error), "Transaction error")
        self.assertEqual(error.__cause__, cause)
    
    def test_database_timeout_error(self):
        """Test DatabaseTimeoutError."""
        # Test with message
        error = DatabaseTimeoutError("Timeout error")
        self.assertEqual(str(error), "Timeout error")
        self.assertIsInstance(error, DatabaseError)
        
        # Test with cause
        cause = ValueError("Cause error")
        error = DatabaseTimeoutError("Timeout error", cause)
        self.assertEqual(str(error), "Timeout error")
        self.assertEqual(error.__cause__, cause)
    
    def test_database_not_found_error(self):
        """Test DatabaseNotFoundError."""
        # Test with message
        error = DatabaseNotFoundError("Not found error")
        self.assertEqual(str(error), "Not found error")
        self.assertIsInstance(error, DatabaseError)
        
        # Test with cause
        cause = ValueError("Cause error")
        error = DatabaseNotFoundError("Not found error", cause)
        self.assertEqual(str(error), "Not found error")
        self.assertEqual(error.__cause__, cause)
    
    def test_database_duplicate_error(self):
        """Test DatabaseDuplicateError."""
        # Test with message
        error = DatabaseDuplicateError("Duplicate error")
        self.assertEqual(str(error), "Duplicate error")
        self.assertIsInstance(error, DatabaseError)
        
        # Test with cause
        cause = ValueError("Cause error")
        error = DatabaseDuplicateError("Duplicate error", cause)
        self.assertEqual(str(error), "Duplicate error")
        self.assertEqual(error.__cause__, cause)
    
    def test_insufficient_credits_error(self):
        """Test InsufficientCreditsError."""
        # Test with message
        error = InsufficientCreditsError("Insufficient credits")
        self.assertEqual(str(error), "Insufficient credits")
        self.assertIsInstance(error, DatabaseError)
        
        # Test with cause
        cause = ValueError("Cause error")
        error = InsufficientCreditsError("Insufficient credits", cause)
        self.assertEqual(str(error), "Insufficient credits")
        self.assertEqual(error.__cause__, cause)
    
    def test_exception_hierarchy(self):
        """Test exception hierarchy."""
        # All exceptions should inherit from DatabaseError
        self.assertTrue(issubclass(DatabaseConnectionError, DatabaseError))
        self.assertTrue(issubclass(DatabaseQueryError, DatabaseError))
        self.assertTrue(issubclass(DatabaseIntegrityError, DatabaseError))
        self.assertTrue(issubclass(DatabaseMigrationError, DatabaseError))
        self.assertTrue(issubclass(DatabaseSchemaError, DatabaseError))
        self.assertTrue(issubclass(DatabaseTransactionError, DatabaseError))
        self.assertTrue(issubclass(DatabaseTimeoutError, DatabaseError))
        self.assertTrue(issubclass(DatabaseNotFoundError, DatabaseError))
        self.assertTrue(issubclass(DatabaseDuplicateError, DatabaseError))
        self.assertTrue(issubclass(InsufficientCreditsError, DatabaseError))

if __name__ == "__main__":
    unittest.main()
