"""
Base model class for VoicePal database models.

This module provides the base model class for all database models.
"""

import logging
import uuid
import json
from typing import Dict, List, Any, Optional, ClassVar, Type, TypeVar, Set, Union, Tuple
from datetime import datetime

from bot.database.core.exceptions import (
    DatabaseError,
    DatabaseNotFoundError,
    DatabaseDuplicateError
)

# Set up logging
logger = logging.getLogger(__name__)

# Type variable for Model subclasses
T = TypeVar('T', bound='Model')

class Model:
    """Base class for all database models."""

    # Table name (to be overridden by subclasses)
    _table_name: ClassVar[str] = ""

    # Primary key column (to be overridden by subclasses if different)
    _primary_key: ClassVar[str] = "id"

    # Columns that should be serialized as JSON (to be overridden by subclasses)
    _json_columns: ClassVar[List[str]] = []

    # Columns that should be excluded from dictionary representation
    _hidden_columns: ClassVar[List[str]] = []

    # Timestamp columns
    _timestamp_columns: ClassVar[List[str]] = ["created_at", "updated_at"]

    # Foreign key relationships (to be overridden by subclasses)
    # Format: {"column_name": ("referenced_table", "referenced_column")}
    _foreign_keys: ClassVar[Dict[str, Tuple[str, str]]] = {}

    # Required columns (to be overridden by subclasses)
    _required_columns: ClassVar[List[str]] = []

    # Default values for columns (to be overridden by subclasses)
    _defaults: ClassVar[Dict[str, Any]] = {}

    def __init__(self, **kwargs):
        """Initialize model with attributes.

        Args:
            **kwargs: Model attributes
        """
        # Set attributes from kwargs
        for key, value in kwargs.items():
            setattr(self, key, value)

        # Set default values for missing attributes
        if self._primary_key not in kwargs:
            setattr(self, self._primary_key, str(uuid.uuid4()))

        # Set timestamp defaults
        now = datetime.now().isoformat()
        for column in self._timestamp_columns:
            if column not in kwargs:
                setattr(self, column, now)

        # Set other defaults
        for column, default_value in self._defaults.items():
            if column not in kwargs:
                setattr(self, column, default_value)

    @classmethod
    def from_dict(cls: Type[T], data: Dict[str, Any]) -> T:
        """Create model instance from dictionary.

        Args:
            data: Dictionary of model attributes

        Returns:
            Model instance
        """
        # Deserialize JSON columns
        for column in cls._json_columns:
            if column in data and isinstance(data[column], str):
                try:
                    data[column] = json.loads(data[column])
                except json.JSONDecodeError:
                    logger.warning(f"Failed to decode JSON for column {column}")

        return cls(**data)

    def to_dict(self, include_hidden: bool = False) -> Dict[str, Any]:
        """Convert model to dictionary.

        Args:
            include_hidden: Whether to include hidden columns

        Returns:
            Dictionary representation of model
        """
        result = {}

        for key, value in self.__dict__.items():
            # Skip hidden columns unless explicitly included
            if not include_hidden and key in self._hidden_columns:
                continue

            # Skip private attributes
            if key.startswith('_'):
                continue

            # Serialize JSON columns
            if key in self._json_columns and not isinstance(value, str):
                result[key] = json.dumps(value)
            else:
                result[key] = value

        return result

    def validate(self) -> List[str]:
        """Validate model attributes.

        Returns:
            List of validation error messages (empty if valid)
        """
        errors = []

        # Check required columns
        for column in self._required_columns:
            if not hasattr(self, column) or getattr(self, column) is None:
                errors.append(f"Required column '{column}' is missing")

        return errors

    def is_valid(self) -> bool:
        """Check if model is valid.

        Returns:
            True if model is valid, False otherwise
        """
        return len(self.validate()) == 0

    def __str__(self) -> str:
        """String representation of model.

        Returns:
            String representation
        """
        pk_value = getattr(self, self._primary_key, None)
        return f"{self.__class__.__name__}({self._primary_key}={pk_value})"

    def __repr__(self) -> str:
        """Detailed string representation of model.

        Returns:
            Detailed string representation
        """
        attrs = ', '.join(f"{k}={v!r}" for k, v in self.to_dict().items())
        return f"{self.__class__.__name__}({attrs})"

    def __eq__(self, other: object) -> bool:
        """Check if two models are equal.

        Args:
            other: Other model

        Returns:
            True if models are equal, False otherwise
        """
        if not isinstance(other, Model):
            return False

        if self.__class__ != other.__class__:
            return False

        return getattr(self, self._primary_key) == getattr(other, self._primary_key)

    def __hash__(self) -> int:
        """Hash of model.

        Returns:
            Hash value
        """
        return hash((self.__class__, getattr(self, self._primary_key)))

    def update(self, **kwargs) -> None:
        """Update model attributes.

        Args:
            **kwargs: Attributes to update
        """
        for key, value in kwargs.items():
            setattr(self, key, value)

        # Update updated_at timestamp
        if "updated_at" in self._timestamp_columns:
            self.updated_at = datetime.now().isoformat()

    @classmethod
    def get_table_name(cls) -> str:
        """Get table name.

        Returns:
            Table name

        Raises:
            ValueError: If table name is not set
        """
        if not cls._table_name:
            raise ValueError(f"Table name not set for {cls.__name__}")

        return cls._table_name

    @classmethod
    def get_primary_key(cls) -> str:
        """Get primary key column name.

        Returns:
            Primary key column name
        """
        return cls._primary_key

    @classmethod
    def get_columns(cls) -> Set[str]:
        """Get all column names.

        Returns:
            Set of column names
        """
        columns = set()

        # Add primary key
        columns.add(cls._primary_key)

        # Add JSON columns
        columns.update(cls._json_columns)

        # Add hidden columns
        columns.update(cls._hidden_columns)

        # Add timestamp columns
        columns.update(cls._timestamp_columns)

        # Add foreign key columns
        columns.update(cls._foreign_keys.keys())

        # Add required columns
        columns.update(cls._required_columns)

        # Add default columns
        columns.update(cls._defaults.keys())

        return columns

    @classmethod
    def get_foreign_keys(cls) -> Dict[str, Tuple[str, str]]:
        """Get foreign key relationships.

        Returns:
            Dictionary of foreign key relationships
        """
        return cls._foreign_keys
