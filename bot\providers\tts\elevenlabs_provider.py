"""
ElevenLabs TTS provider for VoicePal.

This module provides an ElevenLabs-based TTS provider.
"""

import logging
import tempfile
from typing import Dict, Any, Optional

from bot.providers.tts_provider_interface import TTSProviderInterface

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# Always use mock implementation for ElevenLabs
# This avoids dependency issues and costs since we're using Deepgram as primary TTS
ELEVENLABS_AVAILABLE = False
logger.info("Using ElevenLabs mock implementation by default")

class ElevenLabsProvider(TTSProviderInterface):
    """ElevenLabs-based TTS provider."""

    def __init__(self, api_key: str, voice_id: str = "Bella", model_id: str = "eleven_multilingual_v2"):
        """
        Initialize the ElevenLabs provider.

        Args:
            api_key: ElevenLabs API key
            voice_id: Voice ID
            model_id: Model ID
        """
        self.api_key = api_key
        self.voice_id = voice_id
        self.model_id = model_id
        self.personality = "friendly"
        self._supported_features = ["text_to_speech", "multilingual"]

        # Initialize ElevenLabs SDK if available
        if ELEVENLABS_AVAILABLE:
            try:
                # These imports should be available since we checked at module level
                from elevenlabs import set_api_key, generate, save
                set_api_key(api_key)
                self.generate = generate
                self.save = save
                logger.info(f"ElevenLabs provider initialized with voice {voice_id}")
            except Exception as e:
                logger.error(f"Error initializing ElevenLabs SDK: {e}")
                self.generate = None
                self.save = None
        else:
            logger.warning("ElevenLabs SDK not available, using mock implementation")
            self.generate = None
            self.save = None

    def supports_feature(self, feature_name: str) -> bool:
        """
        Check if a feature is supported.

        Args:
            feature_name: Feature name

        Returns:
            bool: True if feature is supported, False otherwise
        """
        return feature_name in self._supported_features

    def set_personality(self, personality: str) -> None:
        """
        Set the personality for voice generation.

        Args:
            personality: Personality type
        """
        self.personality = personality
        logger.info(f"Set personality to {personality}")

    def set_voice(self, voice_id: str) -> None:
        """
        Set the voice for generation.

        Args:
            voice_id: Voice ID
        """
        self.voice_id = voice_id
        logger.info(f"Set voice to {voice_id}")

    def get_available_languages(self) -> Dict[str, str]:
        """
        Get available languages.

        Returns:
            Dictionary of language codes and names
        """
        return {
            "en": "English",
            "es": "Spanish",
            "fr": "French",
            "de": "German",
            "it": "Italian",
            "pt": "Portuguese",
            "pl": "Polish",
            "hi": "Hindi",
            "ja": "Japanese",
            "ko": "Korean"
        }

    def get_available_voices(self, language: Optional[str] = None) -> Dict[str, str]:
        """
        Get available voices.

        Args:
            language: Language code to filter voices

        Returns:
            Dictionary of voice identifiers and names
        """
        voices = {
            "Bella": "Female, warm and friendly",
            "Antoni": "Male, deep and clear",
            "Arnold": "Male, strong and confident",
            "Adam": "Male, balanced and neutral",
            "Domi": "Female, soft and gentle",
            "Elli": "Female, young and bright",
            "Josh": "Male, friendly and conversational",
            "Rachel": "Female, professional and clear",
            "Sam": "Male, calm and thoughtful",
            "Thomas": "Male, authoritative and mature"
        }

        # If language is specified, filter voices (mock implementation)
        if language:
            # In a real implementation, we would filter based on language
            # For now, just return all voices
            return voices

        return voices

    def generate_speech(self, text: str, language: Optional[str] = None,
                       voice: Optional[str] = None, personality: Optional[str] = None,
                       **kwargs) -> Optional[str]:
        """
        Generate speech from text.

        Args:
            text: Text to convert to speech
            language: Language code
            voice: Voice identifier
            personality: Personality type to influence speech style
            **kwargs: Additional parameters

        Returns:
            Path to the generated audio file or None if generation failed
        """
        try:
            # Use provided voice or default
            voice_id = voice or self.voice_id

            # Use provided personality or default
            personality_type = personality or self.personality

            logger.info(f"Generating speech with ElevenLabs for: {text[:30]}...")
            logger.info(f"Using voice: {voice_id}")
            logger.info(f"Personality: {personality_type}")

            if not ELEVENLABS_AVAILABLE or not self.generate or not self.save:
                logger.warning("ElevenLabs SDK not available, using mock implementation")
                # Mock implementation for testing
                # Create a temporary file with actual content
                with tempfile.NamedTemporaryFile(suffix=".mp3", delete=False) as temp_file:
                    # Create a simple MP3 file with silence
                    # This is a minimal valid MP3 file header (silence)
                    mp3_header = bytes.fromhex('FFFB9064000E2000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000')
                    temp_file.write(mp3_header)
                    temp_path = temp_file.name
                    logger.info(f"Created mock audio file at {temp_path}")
                    return temp_path

            # Apply personality to voice settings
            voice_settings = self._get_voice_settings(personality_type)

            try:
                # Import the necessary functions
                from elevenlabs import set_api_key, generate, save

                # Set the API key
                set_api_key(self.api_key)

                # Generate audio
                audio = generate(
                    text=text,
                    voice=voice_id,
                    model=self.model_id,
                    voice_settings=voice_settings
                )

                # Save to temporary file
                with tempfile.NamedTemporaryFile(suffix=".mp3", delete=False) as temp_file:
                    temp_path = temp_file.name
                    save(audio, temp_path)
                    logger.info(f"Generated audio file at {temp_path}")
                    return temp_path
            except ImportError:
                logger.error("ElevenLabs SDK not properly imported")
                return self._fallback_to_google_tts(text, language)
            except Exception as e:
                logger.error(f"Error generating speech with ElevenLabs: {e}")
                import traceback
                logger.error(traceback.format_exc())
                return self._fallback_to_google_tts(text, language)

        except Exception as e:
            logger.error(f"Error in generate_speech: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return self._fallback_to_google_tts(text, language)

    def generate_speech_with_emotion(self, text: str, emotion: str) -> str:
        """
        Generate speech with emotion.

        Args:
            text: Text to convert to speech
            emotion: Emotion to convey

        Returns:
            str: Path to generated audio file
        """
        try:
            if not self.generate or not self.save:
                # Mock implementation for testing
                # Create a temporary file with actual content
                with tempfile.NamedTemporaryFile(suffix=".mp3", delete=False) as temp_file:
                    # Create a simple MP3 file with silence
                    # This is a minimal valid MP3 file header (silence)
                    mp3_header = bytes.fromhex('FFFB9064000E2000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000')
                    temp_file.write(mp3_header)
                    temp_path = temp_file.name
                    logger.info(f"Created mock audio file with emotion {emotion} at {temp_path}")
                    return temp_path

            # Apply emotion to voice settings
            voice_settings = self._get_emotion_settings(emotion)

            # Generate audio
            audio = self.generate(
                text=text,
                voice=self.voice_id,
                model=self.model_id,
                voice_settings=voice_settings
            )

            # Save to temporary file
            with tempfile.NamedTemporaryFile(suffix=".mp3", delete=False) as temp_file:
                temp_path = temp_file.name
                self.save(audio, temp_path)
                logger.info(f"Generated audio file with emotion {emotion} at {temp_path}")
                return temp_path
        except Exception as e:
            logger.error(f"Error generating speech with emotion: {e}")
            return ""

    def _get_voice_settings(self, personality: str) -> Dict[str, float]:
        """
        Get voice settings based on personality.

        Args:
            personality: Personality type

        Returns:
            Dict containing voice settings
        """
        if personality == "friendly":
            return {"stability": 0.5, "similarity_boost": 0.75}
        elif personality == "witty":
            return {"stability": 0.3, "similarity_boost": 0.7}
        elif personality == "calm":
            return {"stability": 0.8, "similarity_boost": 0.5}
        elif personality == "motivational":
            return {"stability": 0.4, "similarity_boost": 0.8}
        elif personality == "thoughtful":
            return {"stability": 0.6, "similarity_boost": 0.6}
        else:
            return {"stability": 0.5, "similarity_boost": 0.5}

    def _get_emotion_settings(self, emotion: str) -> Dict[str, float]:
        """
        Get voice settings based on emotion.

        Args:
            emotion: Emotion type

        Returns:
            Dict containing voice settings
        """
        if emotion == "happy":
            return {"stability": 0.3, "similarity_boost": 0.7}
        elif emotion == "sad":
            return {"stability": 0.7, "similarity_boost": 0.4}
        elif emotion == "angry":
            return {"stability": 0.2, "similarity_boost": 0.9}
        elif emotion == "calm":
            return {"stability": 0.8, "similarity_boost": 0.5}
        elif emotion == "excited":
            return {"stability": 0.3, "similarity_boost": 0.8}
        else:
            return {"stability": 0.5, "similarity_boost": 0.5}

    def _fallback_to_google_tts(self, text: str, language: Optional[str] = None) -> Optional[str]:
        """
        Fall back to Google TTS when ElevenLabs TTS fails.

        Args:
            text: Text to convert to speech
            language: Language code

        Returns:
            Path to the generated audio file or None if generation failed
        """
        try:
            from gtts import gTTS

            logger.info("Falling back to Google TTS")

            # Use provided language or default to English
            lang = language or 'en'

            # Ensure language code is compatible with gTTS
            if len(lang) > 2:
                lang = lang[:2]  # Use only the first two characters (e.g., 'en-US' -> 'en')

            # Create a temporary file
            with tempfile.NamedTemporaryFile(suffix=".mp3", delete=False) as temp_file:
                temp_path = temp_file.name

                # Generate speech with Google TTS
                tts = gTTS(text=text, lang=lang, slow=False)
                tts.save(temp_path)

                logger.info(f"Successfully generated voice response with Google TTS fallback: {temp_path}")
                return temp_path

        except Exception as e:
            logger.error(f"Error generating voice response with Google TTS fallback: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return None
