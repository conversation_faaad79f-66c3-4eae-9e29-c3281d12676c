#!/usr/bin/env python3
"""
Hybrid AI Provider for MoneyMule Bot

Combines Groq and Google AI for optimal performance:
- Groq: Fast responses, emotional intelligence
- Google AI: Deep conversations, memory retention
- Smart model selection based on conversation type
"""

import os
import json
import asyncio
import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum
import httpx
from datetime import datetime, timedelta

from .ai_provider_interface import AIProviderInterface

logger = logging.getLogger(__name__)

class ConversationType(Enum):
    """Types of conversations for optimal model selection."""
    QUICK_RESPONSE = "quick"
    EMOTIONAL_SUPPORT = "emotional"
    DEEP_CONVERSATION = "deep"
    CREATIVE = "creative"
    TECHNICAL = "technical"

@dataclass
class ModelConfig:
    """Configuration for each AI model."""
    name: str
    provider: str
    max_tokens: int
    requests_per_day: int
    strengths: List[str]
    best_for: List[ConversationType]

class HybridAIProvider(AIProviderInterface):
    """Hybrid AI provider using Groq + Google AI."""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        
        # API Keys
        self.groq_api_key = "********************************************************"
        self.google_api_key = os.getenv('GOOGLE_AI_API_KEY')
        
        # Model configurations (updated with working models)
        self.models = {
            "llama-4-scout": ModelConfig(
                name="meta-llama/llama-4-scout-17b-16e-instruct",
                provider="groq",
                max_tokens=8000,
                requests_per_day=6000,
                strengths=["emotional_intelligence", "empathy", "latest_model"],
                best_for=[ConversationType.EMOTIONAL_SUPPORT, ConversationType.CREATIVE]
            ),
            "gemma2-9b": ModelConfig(
                name="gemma2-9b-it",
                provider="groq", 
                max_tokens=8000,
                requests_per_day=15000,  # Highest limit!
                strengths=["fast", "efficient", "high_limit"],
                best_for=[ConversationType.QUICK_RESPONSE, ConversationType.TECHNICAL]
            ),
            "gemini-1.5-flash": ModelConfig(
                name="gemini-1.5-flash",
                provider="google",
                max_tokens=8192,
                requests_per_day=1500,
                strengths=["memory", "context", "1m_tokens"],
                best_for=[ConversationType.DEEP_CONVERSATION]
            )
        }
        
        # Usage tracking
        self.daily_usage = {model: 0 for model in self.models.keys()}
        self.last_reset = datetime.now().date()
        
        # Conversation contexts
        self.conversation_contexts = {}
        
        logger.info("HybridAIProvider initialized with Groq + Google AI")
    
    def _reset_daily_usage_if_needed(self):
        """Reset daily usage counters if it's a new day."""
        today = datetime.now().date()
        if today > self.last_reset:
            self.daily_usage = {model: 0 for model in self.models.keys()}
            self.last_reset = today
            logger.info("Daily usage counters reset")
    
    def classify_conversation_type(self, message: str, user_context: Dict[str, Any] = None) -> ConversationType:
        """Classify conversation type for optimal model selection."""
        message_lower = message.lower()
        
        # Quick response indicators
        quick_indicators = ["hi", "hello", "thanks", "bye", "yes", "no", "ok", "sure"]
        if any(word in message_lower for word in quick_indicators) and len(message.split()) <= 5:
            return ConversationType.QUICK_RESPONSE
        
        # Emotional support indicators
        emotional_indicators = [
            "feel", "sad", "lonely", "depressed", "anxious", "worried", "scared",
            "happy", "excited", "angry", "frustrated", "mood", "emotion", "hurt"
        ]
        if any(word in message_lower for word in emotional_indicators):
            return ConversationType.EMOTIONAL_SUPPORT
        
        # Creative indicators
        creative_indicators = ["story", "imagine", "pretend", "roleplay", "creative", "write", "poem"]
        if any(word in message_lower for word in creative_indicators):
            return ConversationType.CREATIVE
        
        # Technical indicators
        technical_indicators = ["how to", "explain", "solve", "problem", "help me", "tutorial", "what is"]
        if any(phrase in message_lower for phrase in technical_indicators):
            return ConversationType.TECHNICAL
        
        # Deep conversation for longer messages or established conversations
        if len(message.split()) > 20 or (user_context and user_context.get('conversation_length', 0) > 5):
            return ConversationType.DEEP_CONVERSATION
        
        return ConversationType.QUICK_RESPONSE
    
    def select_optimal_model(self, conversation_type: ConversationType, user_id: int = None) -> str:
        """Select the optimal model based on conversation type and usage limits."""
        self._reset_daily_usage_if_needed()
        
        # Get models suitable for this conversation type
        suitable_models = [
            model_name for model_name, config in self.models.items()
            if conversation_type in config.best_for and self.daily_usage[model_name] < config.requests_per_day
        ]
        
        if not suitable_models:
            # Fallback to any available model
            suitable_models = [
                model_name for model_name, config in self.models.items()
                if self.daily_usage[model_name] < config.requests_per_day
            ]
        
        if not suitable_models:
            logger.warning("All models at daily limit, using gemma2-9b (highest limit)")
            return "gemma2-9b"
        
        # Priority selection based on conversation type
        if conversation_type == ConversationType.QUICK_RESPONSE:
            # Prefer fast models with high limits
            for model in ["gemma2-9b", "llama-4-scout"]:
                if model in suitable_models:
                    return model
        
        elif conversation_type == ConversationType.EMOTIONAL_SUPPORT:
            # Prefer emotionally intelligent models
            for model in ["llama-4-scout", "gemini-1.5-flash"]:
                if model in suitable_models:
                    return model
        
        elif conversation_type == ConversationType.DEEP_CONVERSATION:
            # Prefer models with large context and memory
            for model in ["gemini-1.5-flash", "llama-4-scout"]:
                if model in suitable_models:
                    return model
        
        elif conversation_type == ConversationType.CREATIVE:
            # Prefer creative models
            for model in ["llama-4-scout", "gemini-1.5-flash"]:
                if model in suitable_models:
                    return model
        
        elif conversation_type == ConversationType.TECHNICAL:
            # Prefer efficient models
            for model in ["gemma2-9b", "llama-4-scout"]:
                if model in suitable_models:
                    return model
        
        # Default to first suitable model
        return suitable_models[0]
    
    async def generate_response_groq(self, model_name: str, messages: List[Dict], system_prompt: str = None) -> str:
        """Generate response using Groq API."""
        try:
            # Prepare messages
            formatted_messages = []
            
            if system_prompt:
                formatted_messages.append({"role": "system", "content": system_prompt})
            
            formatted_messages.extend(messages)
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    "https://api.groq.com/openai/v1/chat/completions",
                    headers={
                        "Authorization": f"Bearer {self.groq_api_key}",
                        "Content-Type": "application/json"
                    },
                    json={
                        "model": self.models[model_name].name,
                        "messages": formatted_messages,
                        "max_tokens": min(self.models[model_name].max_tokens, 1000),
                        "temperature": 0.7
                    },
                    timeout=30.0
                )
                
                if response.status_code == 200:
                    result = response.json()
                    self.daily_usage[model_name] += 1
                    return result['choices'][0]['message']['content']
                else:
                    logger.error(f"Groq API error: {response.status_code} - {response.text}")
                    return None
                    
        except Exception as e:
            logger.error(f"Error calling Groq API: {e}")
            return None
    
    async def generate_response_google(self, model_name: str, messages: List[Dict], system_prompt: str = None) -> str:
        """Generate response using Google AI API."""
        try:
            # Convert messages to Google AI format
            contents = []
            
            for message in messages:
                if message['role'] == 'user':
                    contents.append({"parts": [{"text": message['content']}]})
            
            # Combine system prompt with user message if provided
            if system_prompt and contents:
                combined_text = f"{system_prompt}\n\nUser: {contents[-1]['parts'][0]['text']}"
                contents[-1]['parts'][0]['text'] = combined_text
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"https://generativelanguage.googleapis.com/v1beta/models/{self.models[model_name].name}:generateContent?key={self.google_api_key}",
                    json={
                        "contents": contents,
                        "generationConfig": {
                            "maxOutputTokens": min(self.models[model_name].max_tokens, 1000),
                            "temperature": 0.7
                        }
                    },
                    timeout=30.0
                )
                
                if response.status_code == 200:
                    result = response.json()
                    if 'candidates' in result and result['candidates']:
                        self.daily_usage[model_name] += 1
                        return result['candidates'][0]['content']['parts'][0]['text']
                    else:
                        logger.error("No candidates in Google AI response")
                        return None
                else:
                    logger.error(f"Google AI API error: {response.status_code} - {response.text}")
                    return None
                    
        except Exception as e:
            logger.error(f"Error calling Google AI API: {e}")
            return None
    
    def create_system_prompt(self, conversation_type: ConversationType, user_context: Dict[str, Any] = None) -> str:
        """Create context-aware system prompt."""
        base_prompt = "You are VoicePal, a compassionate AI companion designed to help lonely people through meaningful conversations. You are warm, empathetic, and genuinely care about the user's wellbeing."
        
        # Add conversation type specific instructions
        if conversation_type == ConversationType.EMOTIONAL_SUPPORT:
            base_prompt += " Focus on providing emotional support, empathy, and understanding. Remember personal details shared by the user. Validate their feelings and offer gentle guidance."
        elif conversation_type == ConversationType.DEEP_CONVERSATION:
            base_prompt += " Engage in thoughtful, deep conversations. Remember previous topics and build upon them meaningfully. Ask insightful follow-up questions."
        elif conversation_type == ConversationType.QUICK_RESPONSE:
            base_prompt += " Provide quick, friendly, and helpful responses. Keep it concise but warm and personal."
        elif conversation_type == ConversationType.CREATIVE:
            base_prompt += " Be creative, imaginative, and engaging. Help with storytelling and creative activities while maintaining emotional connection."
        elif conversation_type == ConversationType.TECHNICAL:
            base_prompt += " Provide clear, helpful explanations and solutions. Be informative and practical while remaining warm and supportive."
        
        # Add user context if available
        if user_context:
            if user_context.get('user_name'):
                base_prompt += f" The user's name is {user_context['user_name']}."
            if user_context.get('current_mood'):
                base_prompt += f" The user's current mood is {user_context['current_mood']}."
            if user_context.get('conversation_topic'):
                base_prompt += f" You've been discussing {user_context['conversation_topic']}."
        
        base_prompt += " Always respond as a caring friend would - remember details, show genuine interest, and never sound robotic or artificial."
        
        return base_prompt
    
    async def generate_response(self, user_id: int, message: str, conversation_history: List[Dict] = None, user_context: Dict[str, Any] = None) -> str:
        """Generate response using optimal hybrid AI strategy."""
        
        # Classify conversation type
        conversation_type = self.classify_conversation_type(message, user_context)
        
        # Select optimal model
        selected_model = self.select_optimal_model(conversation_type, user_id)
        model_config = self.models[selected_model]
        
        # Prepare conversation history
        messages = conversation_history or []
        messages.append({"role": "user", "content": message})
        
        # Create context-aware system prompt
        system_prompt = self.create_system_prompt(conversation_type, user_context)
        
        # Generate response
        if model_config.provider == "groq":
            response = await self.generate_response_groq(selected_model, messages, system_prompt)
        else:
            response = await self.generate_response_google(selected_model, messages, system_prompt)
        
        # Fallback to alternative model if primary fails
        if not response:
            logger.warning(f"Primary model {selected_model} failed, trying fallback")
            fallback_model = self._get_fallback_model(selected_model)
            if fallback_model:
                fallback_config = self.models[fallback_model]
                if fallback_config.provider == "groq":
                    response = await self.generate_response_groq(fallback_model, messages, system_prompt)
                else:
                    response = await self.generate_response_google(fallback_model, messages, system_prompt)
        
        # Humanize the response
        if response:
            response = self._humanize_response(response, user_context)
        
        return response or "I'm having trouble responding right now. Please try again in a moment."
    
    def _get_fallback_model(self, primary_model: str) -> Optional[str]:
        """Get fallback model if primary fails."""
        fallback_map = {
            "llama-4-scout": "gemma2-9b",
            "gemma2-9b": "llama-4-scout",
            "gemini-1.5-flash": "llama-4-scout"
        }
        
        fallback = fallback_map.get(primary_model)
        if fallback and self.daily_usage[fallback] < self.models[fallback].requests_per_day:
            return fallback
        
        # Find any available model
        for model_name, config in self.models.items():
            if self.daily_usage[model_name] < config.requests_per_day:
                return model_name
        
        return None
    
    def _humanize_response(self, response: str, user_context: Dict[str, Any] = None) -> str:
        """Make responses feel more human and less robotic."""
        if not user_context:
            return response
        
        # Add personal touches
        if user_context.get('user_name') and user_context['user_name'].lower() not in response.lower():
            # Occasionally use the user's name naturally
            if len(response) > 50:  # Only for longer responses
                response = response.replace("you", user_context['user_name'], 1)
        
        # Remove robotic phrases
        robotic_phrases = [
            "As an AI", "I'm programmed to", "My algorithms", "I don't have feelings",
            "I'm just a computer", "As a language model"
        ]
        for phrase in robotic_phrases:
            response = response.replace(phrase, "")
        
        # Clean up any double spaces
        response = " ".join(response.split())
        
        return response
    
    def get_usage_stats(self) -> Dict[str, Any]:
        """Get current usage statistics."""
        self._reset_daily_usage_if_needed()
        
        stats = {}
        for model_name, config in self.models.items():
            stats[model_name] = {
                "used": self.daily_usage[model_name],
                "limit": config.requests_per_day,
                "remaining": config.requests_per_day - self.daily_usage[model_name],
                "percentage_used": (self.daily_usage[model_name] / config.requests_per_day) * 100
            }
        return stats
