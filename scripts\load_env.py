"""
Load environment variables from .env file.

This script loads environment variables from a .env file into the environment.
It should be imported at the beginning of the main script.
"""

import os
import logging
from dotenv import load_dotenv

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# Load environment variables from .env file
load_dotenv()

# Log loaded environment variables (without sensitive values)
logger.info("Loaded environment variables:")
logger.info(f"DEEPGRAM_API_KEY: {'*' * 8 if os.getenv('DEEPGRAM_API_KEY') else 'Not set'}")
logger.info(f"GOOGLE_AI_API_KEY: {'*' * 8 if os.getenv('GOOGLE_AI_API_KEY') else 'Not set'}")
logger.info(f"HF_API_TOKEN: {'*' * 8 if os.getenv('HF_API_TOKEN') else 'Not set'}")
logger.info(f"AI_PROVIDER: {os.getenv('AI_PROVIDER', 'Not set')}")
logger.info(f"TTS_PROVIDER: {os.getenv('TTS_PROVIDER', 'Not set')}")
