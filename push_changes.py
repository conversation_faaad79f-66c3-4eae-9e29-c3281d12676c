import subprocess
import sys

def run_command(command):
    print(f"Running: {command}")
    result = subprocess.run(command, shell=True, capture_output=True, text=True)
    print(f"Return code: {result.returncode}")
    print(f"Output: {result.stdout}")
    if result.stderr:
        print(f"Error: {result.stderr}")
    return result.returncode

commands = [
    "git add bot/core/secure_provider_factory.py",
    "git commit -m \"Fix provider interface imports in secure_provider_factory.py\"",
    "git push"
]

for cmd in commands:
    if run_command(cmd) != 0:
        print(f"Command failed: {cmd}")
        sys.exit(1)

print("All commands completed successfully!")
