"""
Tests for the provider interface.
"""

import unittest
import asyncio
from typing import Dict, Any, Optional, List, Type
from unittest.mock import MagicMock, patch

from bot.providers.core.provider import Provider, AIProvider, TTSProvider, STTProvider, PaymentProvider
from bot.providers.core.exceptions import (
    ProviderError,
    ProviderConfigError,
    ProviderNotInitializedError
)

class TestProvider(unittest.TestCase):
    """Tests for the Provider class."""
    
    def test_provider_init(self):
        """Test Provider initialization."""
        # Create a mock provider class
        class MockProvider(Provider):
            provider_type = "mock"
            provider_name = "mock"
            
            def validate_config(self):
                pass
            
            def initialize(self):
                self.initialized = True
            
            def shutdown(self):
                self.initialized = False
        
        # Initialize provider
        provider = MockProvider({})
        
        # Check provider attributes
        self.assertEqual(provider.provider_type, "mock")
        self.assertEqual(provider.provider_name, "mock")
        self.assertFalse(provider.initialized)
        
        # Initialize provider
        provider.initialize()
        self.assertTrue(provider.initialized)
        
        # Shutdown provider
        provider.shutdown()
        self.assertFalse(provider.initialized)
    
    def test_provider_get_info(self):
        """Test Provider.get_info method."""
        # Create a mock provider class
        class MockProvider(Provider):
            provider_type = "mock"
            provider_name = "mock"
            provider_version = "1.0.0"
            provider_description = "Mock provider"
            
            def validate_config(self):
                pass
            
            def initialize(self):
                self.initialized = True
            
            def shutdown(self):
                self.initialized = False
        
        # Initialize provider
        provider = MockProvider({})
        
        # Check provider info
        info = provider.get_info()
        self.assertEqual(info["type"], "mock")
        self.assertEqual(info["name"], "mock")
        self.assertEqual(info["version"], "1.0.0")
        self.assertEqual(info["description"], "Mock provider")
        self.assertFalse(info["initialized"])
        
        # Initialize provider
        provider.initialize()
        
        # Check provider info again
        info = provider.get_info()
        self.assertTrue(info["initialized"])
    
    def test_provider_str_repr(self):
        """Test Provider.__str__ and __repr__ methods."""
        # Create a mock provider class
        class MockProvider(Provider):
            provider_type = "mock"
            provider_name = "mock"
            provider_version = "1.0.0"
            
            def validate_config(self):
                pass
            
            def initialize(self):
                pass
            
            def shutdown(self):
                pass
        
        # Initialize provider
        provider = MockProvider({})
        
        # Check string representation
        self.assertEqual(str(provider), "mock:mock (v1.0.0)")
        
        # Check detailed representation
        self.assertEqual(repr(provider), "MockProvider(type=mock, name=mock, version=1.0.0, initialized=False)")

class TestAIProvider(unittest.TestCase):
    """Tests for the AIProvider class."""
    
    def setUp(self):
        """Set up test case."""
        # Create a mock AI provider class
        class MockAIProvider(AIProvider):
            provider_type = "ai"
            provider_name = "mock_ai"
            
            def validate_config(self):
                pass
            
            def initialize(self):
                self.initialized = True
            
            def shutdown(self):
                self.initialized = False
            
            async def generate_text(self, prompt, **kwargs):
                if not self.initialized:
                    raise ProviderNotInitializedError("Provider not initialized")
                return f"Generated text for: {prompt}"
            
            async def generate_chat_response(self, messages, **kwargs):
                if not self.initialized:
                    raise ProviderNotInitializedError("Provider not initialized")
                return {"role": "assistant", "content": "Chat response"}
            
            async def embed_text(self, text, **kwargs):
                if not self.initialized:
                    raise ProviderNotInitializedError("Provider not initialized")
                return [0.1, 0.2, 0.3]
        
        self.MockAIProvider = MockAIProvider
    
    def test_ai_provider_init(self):
        """Test AIProvider initialization."""
        # Initialize provider
        provider = self.MockAIProvider({})
        
        # Check provider attributes
        self.assertEqual(provider.provider_type, "ai")
        self.assertEqual(provider.provider_name, "mock_ai")
        self.assertFalse(provider.initialized)
    
    def test_ai_provider_generate_text(self):
        """Test AIProvider.generate_text method."""
        # Initialize provider
        provider = self.MockAIProvider({})
        provider.initialize()
        
        # Test generate_text
        result = asyncio.run(provider.generate_text("Hello"))
        self.assertEqual(result, "Generated text for: Hello")
    
    def test_ai_provider_generate_chat_response(self):
        """Test AIProvider.generate_chat_response method."""
        # Initialize provider
        provider = self.MockAIProvider({})
        provider.initialize()
        
        # Test generate_chat_response
        messages = [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "Hello"}
        ]
        result = asyncio.run(provider.generate_chat_response(messages))
        self.assertEqual(result["role"], "assistant")
        self.assertEqual(result["content"], "Chat response")
    
    def test_ai_provider_embed_text(self):
        """Test AIProvider.embed_text method."""
        # Initialize provider
        provider = self.MockAIProvider({})
        provider.initialize()
        
        # Test embed_text
        result = asyncio.run(provider.embed_text("Hello"))
        self.assertEqual(result, [0.1, 0.2, 0.3])
    
    def test_ai_provider_not_initialized(self):
        """Test AIProvider methods when not initialized."""
        # Initialize provider without calling initialize
        provider = self.MockAIProvider({})
        
        # Test generate_text
        with self.assertRaises(ProviderNotInitializedError):
            asyncio.run(provider.generate_text("Hello"))
        
        # Test generate_chat_response
        with self.assertRaises(ProviderNotInitializedError):
            asyncio.run(provider.generate_chat_response([]))
        
        # Test embed_text
        with self.assertRaises(ProviderNotInitializedError):
            asyncio.run(provider.embed_text("Hello"))

class TestTTSProvider(unittest.TestCase):
    """Tests for the TTSProvider class."""
    
    def setUp(self):
        """Set up test case."""
        # Create a mock TTS provider class
        class MockTTSProvider(TTSProvider):
            provider_type = "tts"
            provider_name = "mock_tts"
            
            def validate_config(self):
                pass
            
            def initialize(self):
                self.initialized = True
            
            def shutdown(self):
                self.initialized = False
            
            async def text_to_speech(self, text, voice_id=None, **kwargs):
                if not self.initialized:
                    raise ProviderNotInitializedError("Provider not initialized")
                return b"audio_data"
            
            def get_available_voices(self):
                if not self.initialized:
                    raise ProviderNotInitializedError("Provider not initialized")
                return [{"id": "voice1", "name": "Voice 1"}]
        
        self.MockTTSProvider = MockTTSProvider
    
    def test_tts_provider_init(self):
        """Test TTSProvider initialization."""
        # Initialize provider
        provider = self.MockTTSProvider({})
        
        # Check provider attributes
        self.assertEqual(provider.provider_type, "tts")
        self.assertEqual(provider.provider_name, "mock_tts")
        self.assertFalse(provider.initialized)
    
    def test_tts_provider_text_to_speech(self):
        """Test TTSProvider.text_to_speech method."""
        # Initialize provider
        provider = self.MockTTSProvider({})
        provider.initialize()
        
        # Test text_to_speech
        result = asyncio.run(provider.text_to_speech("Hello"))
        self.assertEqual(result, b"audio_data")
    
    def test_tts_provider_get_available_voices(self):
        """Test TTSProvider.get_available_voices method."""
        # Initialize provider
        provider = self.MockTTSProvider({})
        provider.initialize()
        
        # Test get_available_voices
        result = provider.get_available_voices()
        self.assertEqual(len(result), 1)
        self.assertEqual(result[0]["id"], "voice1")
        self.assertEqual(result[0]["name"], "Voice 1")
    
    def test_tts_provider_not_initialized(self):
        """Test TTSProvider methods when not initialized."""
        # Initialize provider without calling initialize
        provider = self.MockTTSProvider({})
        
        # Test text_to_speech
        with self.assertRaises(ProviderNotInitializedError):
            asyncio.run(provider.text_to_speech("Hello"))
        
        # Test get_available_voices
        with self.assertRaises(ProviderNotInitializedError):
            provider.get_available_voices()

if __name__ == "__main__":
    unittest.main()
