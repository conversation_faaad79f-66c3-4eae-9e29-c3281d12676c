"""
Run script for VoicePal bot with enhanced error handling.

This script provides a robust way to start the VoicePal bot with
comprehensive error handling and diagnostics for deployment issues.
"""

import os
import sys
import logging
import asyncio
import traceback
import json
from dotenv import load_dotenv
import telegram.error

# Add the current directory to the path to ensure imports work
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import centralized logging configuration
from bot.core.logging_config import configure_logging, get_logger

# Configure logging for development
configure_logging(
    environment="development",
    log_to_console=True
)
logger = get_logger(__name__)

# Import the import handler first to patch the import system
try:
    from scripts.import_handler import patch_import_system, verify_critical_imports, print_import_verification_results
    # Patch the import system for better error handling
    patch_import_system()
except ImportError as e:
    logger.error(f"Error importing import_handler: {e}")
    logger.error("Continuing without enhanced import error handling")

async def main():
    """Run the VoicePal bot with enhanced error handling."""
    # Load environment variables
    load_dotenv()

    logger.info("Starting VoicePal bot")
    logger.info(f"Python version: {sys.version}")
    logger.info(f"Current working directory: {os.getcwd()}")
    logger.info(f"Module search path: {sys.path}")

    # Verify critical imports before attempting to start the bot
    try:
        # If import_handler was imported successfully, use it to verify imports
        if 'verify_critical_imports' in globals():
            results = verify_critical_imports()
            all_successful = print_import_verification_results(results)
            if not all_successful:
                logger.warning("Some imports failed or are using fallbacks. The bot may not function correctly.")
    except Exception as e:
        logger.error(f"Error verifying imports: {e}")

    try:
        # Import the bot module
        logger.info("Importing VoicePalBot...")
        from bot.main import VoicePalBot

        # Create the bot
        logger.info("Creating VoicePalBot instance...")
        bot = VoicePalBot()

        # Check if running in production (Render) or development mode
        webhook_url = os.environ.get("WEBHOOK_URL")
        port = int(os.environ.get("PORT", 10000))
        is_render = os.environ.get("RENDER", "").lower() == "true"

        # Run the bot
        if webhook_url:
            logger.info(f"Running in production mode with webhook: {webhook_url}")

            # Initialize the application
            await bot.application.initialize()
            await bot.application.start()

            # Set up webhook with retry logic
            max_retries = 3
            retry_count = 0
            success = False

            while retry_count < max_retries and not success:
                try:
                    # Make sure webhook URL ends with /webhook
                    if not webhook_url.endswith("/webhook"):
                        if webhook_url.endswith("/"):
                            webhook_url = webhook_url + "webhook"
                        else:
                            webhook_url = webhook_url + "/webhook"

                    try:
                        await bot.application.bot.set_webhook(
                            url=webhook_url,
                            drop_pending_updates=True,
                            allowed_updates=["message", "callback_query", "pre_checkout_query", "successful_payment"]
                        )
                    except telegram.error.RetryAfter as e:
                        # If we hit a rate limit, wait and retry
                        retry_seconds = e.retry_after
                        logger.warning(f"Rate limit hit when setting webhook. Retrying in {retry_seconds} seconds")
                        await asyncio.sleep(retry_seconds + 1)  # Add 1 second buffer
                        await bot.application.bot.set_webhook(
                            url=webhook_url,
                            drop_pending_updates=True,
                            allowed_updates=["message", "callback_query", "pre_checkout_query", "successful_payment"]
                        )

                    # Verify webhook was set correctly
                    webhook_info = await bot.application.bot.get_webhook_info()
                    if webhook_info.url == webhook_url:
                        logger.info("Webhook set successfully")
                        success = True
                    else:
                        logger.error(f"Webhook URL mismatch: set {webhook_url}, got {webhook_info.url}")
                        retry_count += 1
                except Exception as e:
                    retry_count += 1
                    logger.error(f"Error setting webhook (attempt {retry_count}/{max_retries}): {e}")
                    if retry_count < max_retries:
                        logger.info("Retrying in 5 seconds...")
                        await asyncio.sleep(5)

            if not success:
                logger.error("Failed to set webhook after multiple attempts")
                return 1

            # Create a simple web server to handle webhook requests
            from aiohttp import web

            async def webhook_handler(request):
                try:
                    logger.info(f"Received webhook request from {request.remote}")

                    # Get the request data
                    update_data = await request.json()

                    # Create an Update object
                    from telegram import Update
                    update = Update.de_json(update_data, bot.application.bot)

                    # Process the update
                    await bot.application.process_update(update)

                    return web.Response(text="OK")
                except Exception as e:
                    logger.error(f"Error processing webhook update: {e}")
                    logger.error(traceback.format_exc())
                    return web.Response(text="Error", status=500)

            async def health_check_handler(request):
                logger.info(f"Health check request from {request.remote}")
                return web.Response(text="VoicePal Bot is running!")

            # Create the web app
            app = web.Application()
            app.router.add_post("/webhook", webhook_handler)
            app.router.add_get("/", health_check_handler)

            # Run the web server
            runner = web.AppRunner(app)
            await runner.setup()
            site = web.TCPSite(runner, "0.0.0.0", port)

            try:
                await site.start()
                logger.info(f"Webhook server started on port {port}")

                # Keep the application running
                while True:
                    await asyncio.sleep(3600)  # Sleep for an hour
            except KeyboardInterrupt:
                logger.info("Bot stopped by user")
            finally:
                await runner.cleanup()
        else:
            logger.info("Running in development mode with polling")
            await bot.application.initialize()
            await bot.application.start()
            await bot.application.updater.start_polling()

            # Keep the application running
            await bot.application.updater.start_polling()
            await bot.application.idle()

    except ImportError as e:
        logger.error(f"Error importing bot module: {e}")
        logger.error(traceback.format_exc())
        return 1
    except Exception as e:
        logger.error(f"Error initializing bot: {e}")
        logger.error(traceback.format_exc())
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code or 0)
    except KeyboardInterrupt:
        logger.info("Bot stopped by user")
        sys.exit(0)
    except Exception as e:
        logger.error(f"Unhandled exception: {e}")
        logger.error(traceback.format_exc())
        sys.exit(1)
