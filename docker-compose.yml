# VoicePal Bot - Docker Compose Configuration
# ==========================================
# Development and production deployment setup

version: '3.8'

services:
  voicepal-bot:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: voicepal-bot
    restart: unless-stopped
    
    # Environment variables
    environment:
      - PYTHONPATH=/app
      - TELEGRAM_TOKEN=${TELEGRAM_TOKEN}
      - DEEPGRAM_API_KEY=${DEEPGRAM_API_KEY}
      - GOOGLE_AI_API_KEY=${GOOGLE_AI_API_KEY}
      - ELEVENLABS_API_KEY=${ELEVENLABS_API_KEY}
      - GROQ_API_KEY=${GROQ_API_KEY}
      - WEBHOOK_URL=${WEBHOOK_URL}
      - PORT=8443
      - RENDER=${RENDER:-false}
      - RENDER_SERVICE_NAME=${RENDER_SERVICE_NAME}
    
    # Port mapping
    ports:
      - "8443:8443"
    
    # Volume mounts for persistence
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./voicepal.db:/app/voicepal.db
    
    # Health check
    healthcheck:
      test: ["CMD", "python", "run.py", "--health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # Resource limits
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
    
    # Logging configuration
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Optional: Redis for caching (uncomment if needed)
  # redis:
  #   image: redis:7-alpine
  #   container_name: voicepal-redis
  #   restart: unless-stopped
  #   ports:
  #     - "6379:6379"
  #   volumes:
  #     - redis_data:/data
  #   command: redis-server --appendonly yes
  #   deploy:
  #     resources:
  #       limits:
  #         memory: 128M
  #         cpus: '0.1'

# Volumes
volumes:
  redis_data:
    driver: local

# Networks
networks:
  default:
    name: voicepal-network
