"""
Test script for the voice processing module.

This script tests the basic functionality of the voice processing module.
It requires a Deepgram API key to be set in the environment variable DEEPGRAM_API_KEY.

Usage:
    python test_voice_processing.py
"""

import os
import asyncio
import unittest
from bot.providers.voice.processor import VoiceProcessor
import tempfile
import wave
import struct
import numpy as np

class TestVoiceProcessing(unittest.TestCase):
    """Test cases for the VoiceProcessor class."""

    def setUp(self):
        """Set up test environment."""
        # Create a voice processor instance
        self.voice_processor = VoiceProcessor()

        # Create a test audio file
        self.test_audio_path = self._create_test_audio_file()

    def tearDown(self):
        """Clean up after tests."""
        # Clean up test audio file
        if hasattr(self, 'test_audio_path') and os.path.exists(self.test_audio_path):
            os.remove(self.test_audio_path)

        # Clean up any generated voice files
        if hasattr(self, 'voice_file_path') and self.voice_file_path and os.path.exists(self.voice_file_path):
            os.remove(self.voice_file_path)

    def _create_test_audio_file(self):
        """Create a simple test audio file with a sine wave."""
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".wav")
        temp_file_path = temp_file.name
        temp_file.close()

        # Audio parameters
        duration = 1  # seconds
        sample_rate = 16000  # Hz
        frequency = 440  # Hz (A4 note)

        # Generate sine wave
        t = np.linspace(0, duration, int(sample_rate * duration), endpoint=False)
        audio_data = (32767 * 0.5 * np.sin(2 * np.pi * frequency * t)).astype(np.int16)

        # Write to WAV file
        with wave.open(temp_file_path, 'wb') as wav_file:
            wav_file.setnchannels(1)  # Mono
            wav_file.setsampwidth(2)  # 16-bit
            wav_file.setframerate(sample_rate)
            wav_file.writeframes(audio_data.tobytes())

        return temp_file_path

    def test_generate_voice_response(self):
        """Test generating voice response."""
        # Generate voice response
        self.voice_file_path = self.voice_processor.generate_voice_response("Hello, this is a test.")

        # Check if file was created
        self.assertIsNotNone(self.voice_file_path)
        self.assertTrue(os.path.exists(self.voice_file_path))

        # Check if file has content
        self.assertGreater(os.path.getsize(self.voice_file_path), 0)

    def test_get_available_languages(self):
        """Test getting available languages."""
        languages = self.voice_processor.get_available_languages()

        # Check if languages were returned
        self.assertIsNotNone(languages)
        self.assertIsInstance(languages, dict)

        # Check if English is available
        self.assertIn('en', languages)

    @unittest.skipIf(not os.getenv("DEEPGRAM_API_KEY"), "Deepgram API key not set")
    def test_transcribe_audio(self):
        """Test transcribing audio."""
        # This test is skipped if no Deepgram API key is set

        # Run the async test
        result = asyncio.run(self._async_test_transcribe_audio())

        # Check if transcription was successful
        self.assertIsNotNone(result)

    async def _async_test_transcribe_audio(self):
        """Async helper for testing transcription."""
        # Transcribe audio
        transcript = await self.voice_processor.transcribe_audio(self.test_audio_path)
        return transcript

if __name__ == "__main__":
    unittest.main()
