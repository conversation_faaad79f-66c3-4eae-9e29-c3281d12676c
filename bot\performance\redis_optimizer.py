"""
Redis optimization module for VoicePal.

This module provides Redis optimization and connection management.
"""

import json
import logging
import time
from typing import Any, Optional, Dict, List, Union
from dataclasses import dataclass
import redis
from redis.connection import ConnectionPool

logger = logging.getLogger(__name__)

@dataclass
class RedisStats:
    """Redis statistics."""
    total_commands: int
    cache_hits: int
    cache_misses: int
    hit_rate: float
    active_connections: int
    total_connections: int
    memory_usage: int
    keyspace_hits: int
    keyspace_misses: int

class RedisOptimizer:
    """Redis optimizer with connection pooling and caching strategies."""
    
    def __init__(
        self,
        host: str = "localhost",
        port: int = 6379,
        password: Optional[str] = None,
        db: int = 0,
        max_connections: int = 20,
        socket_timeout: int = 5,
        socket_connect_timeout: int = 5,
        retry_on_timeout: bool = True,
        health_check_interval: int = 30
    ):
        """
        Initialize Redis optimizer.
        
        Args:
            host: Redis host
            port: Redis port
            password: Redis password
            db: Redis database number
            max_connections: Maximum connections in pool
            socket_timeout: Socket timeout in seconds
            socket_connect_timeout: Socket connect timeout in seconds
            retry_on_timeout: Whether to retry on timeout
            health_check_interval: Health check interval in seconds
        """
        self.host = host
        self.port = port
        self.password = password
        self.db = db
        self.max_connections = max_connections
        
        # Create connection pool
        self.pool = ConnectionPool(
            host=host,
            port=port,
            password=password,
            db=db,
            max_connections=max_connections,
            socket_timeout=socket_timeout,
            socket_connect_timeout=socket_connect_timeout,
            retry_on_timeout=retry_on_timeout,
            health_check_interval=health_check_interval
        )
        
        # Create Redis client
        self.redis_client = redis.Redis(connection_pool=self.pool)
        
        # Statistics
        self.stats = RedisStats(
            total_commands=0,
            cache_hits=0,
            cache_misses=0,
            hit_rate=0.0,
            active_connections=0,
            total_connections=0,
            memory_usage=0,
            keyspace_hits=0,
            keyspace_misses=0
        )
        
        # Cache configuration
        self.default_ttl = 3600  # 1 hour
        self.key_prefixes = {
            "user": "user:",
            "conversation": "conv:",
            "message": "msg:",
            "session": "sess:",
            "rate_limit": "rl:",
            "cache": "cache:",
            "temp": "temp:"
        }
        
        # Test connection
        self._test_connection()
        
        logger.info(f"Redis optimizer initialized: {host}:{port}")
    
    def _test_connection(self) -> bool:
        """Test Redis connection."""
        try:
            self.redis_client.ping()
            logger.info("Redis connection successful")
            return True
        except Exception as e:
            logger.error(f"Redis connection failed: {e}")
            return False
    
    def is_available(self) -> bool:
        """Check if Redis is available."""
        try:
            self.redis_client.ping()
            return True
        except Exception:
            return False
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        Get value from Redis with automatic deserialization.
        
        Args:
            key: Redis key
            default: Default value if key not found
            
        Returns:
            Deserialized value or default
        """
        try:
            self.stats.total_commands += 1
            
            value = self.redis_client.get(key)
            if value is None:
                self.stats.cache_misses += 1
                return default
            
            self.stats.cache_hits += 1
            self._update_hit_rate()
            
            # Try to deserialize JSON
            try:
                return json.loads(value.decode('utf-8'))
            except (json.JSONDecodeError, UnicodeDecodeError):
                return value.decode('utf-8')
                
        except Exception as e:
            logger.error(f"Redis GET failed for key {key}: {e}")
            self.stats.cache_misses += 1
            return default
    
    def set(
        self,
        key: str,
        value: Any,
        ttl: Optional[int] = None,
        nx: bool = False,
        xx: bool = False
    ) -> bool:
        """
        Set value in Redis with automatic serialization.
        
        Args:
            key: Redis key
            value: Value to store
            ttl: Time to live in seconds
            nx: Only set if key doesn't exist
            xx: Only set if key exists
            
        Returns:
            True if successful, False otherwise
        """
        try:
            self.stats.total_commands += 1
            
            # Serialize value
            if isinstance(value, (dict, list, tuple)):
                serialized_value = json.dumps(value)
            else:
                serialized_value = str(value)
            
            # Set value with options
            result = self.redis_client.set(
                key,
                serialized_value,
                ex=ttl or self.default_ttl,
                nx=nx,
                xx=xx
            )
            
            return bool(result)
            
        except Exception as e:
            logger.error(f"Redis SET failed for key {key}: {e}")
            return False
    
    def delete(self, *keys: str) -> int:
        """
        Delete keys from Redis.
        
        Args:
            keys: Keys to delete
            
        Returns:
            Number of keys deleted
        """
        try:
            self.stats.total_commands += 1
            return self.redis_client.delete(*keys)
        except Exception as e:
            logger.error(f"Redis DELETE failed: {e}")
            return 0
    
    def exists(self, *keys: str) -> int:
        """
        Check if keys exist in Redis.
        
        Args:
            keys: Keys to check
            
        Returns:
            Number of existing keys
        """
        try:
            self.stats.total_commands += 1
            return self.redis_client.exists(*keys)
        except Exception as e:
            logger.error(f"Redis EXISTS failed: {e}")
            return 0
    
    def expire(self, key: str, ttl: int) -> bool:
        """
        Set expiration for a key.
        
        Args:
            key: Redis key
            ttl: Time to live in seconds
            
        Returns:
            True if successful, False otherwise
        """
        try:
            self.stats.total_commands += 1
            return bool(self.redis_client.expire(key, ttl))
        except Exception as e:
            logger.error(f"Redis EXPIRE failed for key {key}: {e}")
            return False
    
    def incr(self, key: str, amount: int = 1) -> Optional[int]:
        """
        Increment a key's value.
        
        Args:
            key: Redis key
            amount: Amount to increment
            
        Returns:
            New value or None if failed
        """
        try:
            self.stats.total_commands += 1
            return self.redis_client.incr(key, amount)
        except Exception as e:
            logger.error(f"Redis INCR failed for key {key}: {e}")
            return None
    
    def hget(self, name: str, key: str) -> Any:
        """
        Get field from hash.
        
        Args:
            name: Hash name
            key: Field key
            
        Returns:
            Field value or None
        """
        try:
            self.stats.total_commands += 1
            value = self.redis_client.hget(name, key)
            if value is None:
                self.stats.cache_misses += 1
                return None
            
            self.stats.cache_hits += 1
            self._update_hit_rate()
            
            try:
                return json.loads(value.decode('utf-8'))
            except (json.JSONDecodeError, UnicodeDecodeError):
                return value.decode('utf-8')
                
        except Exception as e:
            logger.error(f"Redis HGET failed for {name}.{key}: {e}")
            self.stats.cache_misses += 1
            return None
    
    def hset(self, name: str, key: str, value: Any) -> bool:
        """
        Set field in hash.
        
        Args:
            name: Hash name
            key: Field key
            value: Field value
            
        Returns:
            True if successful, False otherwise
        """
        try:
            self.stats.total_commands += 1
            
            # Serialize value
            if isinstance(value, (dict, list, tuple)):
                serialized_value = json.dumps(value)
            else:
                serialized_value = str(value)
            
            result = self.redis_client.hset(name, key, serialized_value)
            return True
            
        except Exception as e:
            logger.error(f"Redis HSET failed for {name}.{key}: {e}")
            return False
    
    def hgetall(self, name: str) -> Dict[str, Any]:
        """
        Get all fields from hash.
        
        Args:
            name: Hash name
            
        Returns:
            Dictionary of all fields
        """
        try:
            self.stats.total_commands += 1
            result = self.redis_client.hgetall(name)
            
            if not result:
                self.stats.cache_misses += 1
                return {}
            
            self.stats.cache_hits += 1
            self._update_hit_rate()
            
            # Deserialize values
            deserialized = {}
            for key, value in result.items():
                try:
                    deserialized[key.decode('utf-8')] = json.loads(value.decode('utf-8'))
                except (json.JSONDecodeError, UnicodeDecodeError):
                    deserialized[key.decode('utf-8')] = value.decode('utf-8')
            
            return deserialized
            
        except Exception as e:
            logger.error(f"Redis HGETALL failed for {name}: {e}")
            self.stats.cache_misses += 1
            return {}
    
    def lpush(self, key: str, *values: Any) -> Optional[int]:
        """
        Push values to the left of a list.
        
        Args:
            key: List key
            values: Values to push
            
        Returns:
            New list length or None if failed
        """
        try:
            self.stats.total_commands += 1
            serialized_values = []
            for value in values:
                if isinstance(value, (dict, list, tuple)):
                    serialized_values.append(json.dumps(value))
                else:
                    serialized_values.append(str(value))
            
            return self.redis_client.lpush(key, *serialized_values)
            
        except Exception as e:
            logger.error(f"Redis LPUSH failed for key {key}: {e}")
            return None
    
    def rpop(self, key: str) -> Any:
        """
        Pop value from the right of a list.
        
        Args:
            key: List key
            
        Returns:
            Popped value or None
        """
        try:
            self.stats.total_commands += 1
            value = self.redis_client.rpop(key)
            if value is None:
                return None
            
            try:
                return json.loads(value.decode('utf-8'))
            except (json.JSONDecodeError, UnicodeDecodeError):
                return value.decode('utf-8')
                
        except Exception as e:
            logger.error(f"Redis RPOP failed for key {key}: {e}")
            return None
    
    def get_key(self, prefix: str, identifier: str) -> str:
        """
        Generate Redis key with prefix.
        
        Args:
            prefix: Key prefix
            identifier: Key identifier
            
        Returns:
            Full Redis key
        """
        prefix_value = self.key_prefixes.get(prefix, prefix)
        return f"{prefix_value}{identifier}"
    
    def cache_user_data(self, user_id: int, data: Dict[str, Any], ttl: int = 3600) -> bool:
        """
        Cache user data.
        
        Args:
            user_id: User ID
            data: User data to cache
            ttl: Time to live in seconds
            
        Returns:
            True if successful, False otherwise
        """
        key = self.get_key("user", str(user_id))
        return self.set(key, data, ttl)
    
    def get_cached_user_data(self, user_id: int) -> Optional[Dict[str, Any]]:
        """
        Get cached user data.
        
        Args:
            user_id: User ID
            
        Returns:
            Cached user data or None
        """
        key = self.get_key("user", str(user_id))
        return self.get(key)
    
    def invalidate_user_cache(self, user_id: int) -> bool:
        """
        Invalidate user cache.
        
        Args:
            user_id: User ID
            
        Returns:
            True if successful, False otherwise
        """
        key = self.get_key("user", str(user_id))
        return bool(self.delete(key))
    
    def _update_hit_rate(self):
        """Update cache hit rate."""
        total_requests = self.stats.cache_hits + self.stats.cache_misses
        if total_requests > 0:
            self.stats.hit_rate = self.stats.cache_hits / total_requests
    
    def get_stats(self) -> RedisStats:
        """
        Get Redis statistics.
        
        Returns:
            Redis statistics
        """
        try:
            # Get Redis info
            info = self.redis_client.info()
            
            self.stats.active_connections = info.get('connected_clients', 0)
            self.stats.total_connections = info.get('total_connections_received', 0)
            self.stats.memory_usage = info.get('used_memory', 0)
            self.stats.keyspace_hits = info.get('keyspace_hits', 0)
            self.stats.keyspace_misses = info.get('keyspace_misses', 0)
            
            self._update_hit_rate()
            
        except Exception as e:
            logger.error(f"Failed to get Redis stats: {e}")
        
        return self.stats
    
    def cleanup_expired_keys(self, pattern: str = "*") -> int:
        """
        Clean up expired keys matching pattern.
        
        Args:
            pattern: Key pattern to match
            
        Returns:
            Number of keys cleaned up
        """
        try:
            keys = self.redis_client.keys(pattern)
            if not keys:
                return 0
            
            # Check which keys are expired and delete them
            deleted_count = 0
            for key in keys:
                try:
                    ttl = self.redis_client.ttl(key)
                    if ttl == -2:  # Key doesn't exist
                        deleted_count += 1
                except Exception:
                    pass
            
            return deleted_count
            
        except Exception as e:
            logger.error(f"Failed to cleanup expired keys: {e}")
            return 0
    
    def close(self):
        """Close Redis connection pool."""
        try:
            self.pool.disconnect()
            logger.info("Redis connection pool closed")
        except Exception as e:
            logger.error(f"Failed to close Redis connection pool: {e}")
