"""
Notifications endpoints for VoicePal API.
"""

import logging
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from bot.api.dependencies import get_advanced_features_manager

logger = logging.getLogger(__name__)
router = APIRouter()

class NotificationSend(BaseModel):
    user_id: int
    channel: str
    notification_type: str
    content: str
    subject: str = None

class NotificationSchedule(BaseModel):
    user_id: int
    template_id: str
    channel: str
    scheduled_at: str
    variables: dict = {}

@router.post("/notifications/send")
async def send_notification(
    notification: NotificationSend,
    advanced=Depends(get_advanced_features_manager)
):
    """Send an immediate notification."""
    try:
        success = advanced.send_notification(**notification.dict())
        if success:
            return {"message": "Notification sent successfully"}
        else:
            raise HTTPException(status_code=500, detail="Failed to send notification")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error sending notification: {e}")
        raise HTTPException(status_code=500, detail="Error sending notification")

@router.post("/notifications/schedule")
async def schedule_notification(
    notification: NotificationSchedule,
    advanced=Depends(get_advanced_features_manager)
):
    """Schedule a notification for later delivery."""
    try:
        notification_id = advanced.notification_manager.schedule_notification(**notification.dict())
        return {"notification_id": notification_id, "message": "Notification scheduled successfully"}
    except Exception as e:
        logger.error(f"Error scheduling notification: {e}")
        raise HTTPException(status_code=500, detail="Error scheduling notification")
