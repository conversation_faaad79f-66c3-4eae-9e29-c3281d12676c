"""
Decorator utilities for VoicePal.

This module provides decorators for command handlers and other functions
to add functionality like input validation, rate limiting, and logging.
"""

import functools
import logging
import inspect
from typing import Callable, Dict, Any, Optional, List, Union
from telegram import Update
from telegram.ext import ContextTypes

from bot.core.security import InputValidator

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

def validate_input(
    input_type: str = 'text',
    arg_index: int = 0,
    extract_text: bool = True,
    error_message: Optional[str] = None,
    **validation_kwargs
) -> Callable:
    """
    Decorator to validate input for command handlers.
    
    Args:
        input_type: Type of input to validate ('text', 'command', 'number', etc.)
        arg_index: Index of the argument containing the text to validate (for non-Update objects)
        extract_text: Whether to extract text from Update object
        error_message: Custom error message to send on validation failure
        **validation_kwargs: Additional validation parameters
        
    Returns:
        Decorator function
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # Extract the text to validate
            text = None
            
            # If first argument is an Update object and extract_text is True
            if args and isinstance(args[0], Update) and extract_text:
                update = args[0]
                
                # Extract text based on update type
                if update.message and update.message.text:
                    text = update.message.text
                elif update.callback_query and update.callback_query.data:
                    text = update.callback_query.data
                    # Override input_type for callback data
                    nonlocal input_type
                    if input_type == 'text':
                        input_type = 'callback_data'
                elif update.inline_query and update.inline_query.query:
                    text = update.inline_query.query
            else:
                # Get text from the specified argument index
                if len(args) > arg_index:
                    text = args[arg_index]
            
            # Validate the input
            if text is not None:
                is_valid = InputValidator.validate(text, input_type, **validation_kwargs)
                
                if not is_valid:
                    # Handle validation failure
                    if error_message and args and isinstance(args[0], Update):
                        update = args[0]
                        context = args[1] if len(args) > 1 else None
                        
                        if update.message:
                            await update.message.reply_text(error_message)
                        elif update.callback_query:
                            await update.callback_query.answer(error_message)
                    
                    logger.warning(f"Input validation failed for {func.__name__}: {text}")
                    return None
            
            # Call the original function
            return await func(*args, **kwargs)
        
        return wrapper
    
    return decorator

def sanitize_inputs(
    input_specs: List[Dict[str, Any]] = None
) -> Callable:
    """
    Decorator to sanitize multiple inputs for a function.
    
    Args:
        input_specs: List of dictionaries specifying inputs to sanitize
                    Each dict should have:
                    - 'arg_name': Name of the argument to sanitize
                    - 'input_type': Type of input ('text', 'command', etc.)
                    - 'required': Whether the argument is required
                    - Additional validation parameters
        
    Returns:
        Decorator function
    """
    if input_specs is None:
        input_specs = []
        
    def decorator(func: Callable) -> Callable:
        # Get function signature
        sig = inspect.signature(func)
        
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # Create a mapping of argument names to values
            bound_args = sig.bind(*args, **kwargs)
            bound_args.apply_defaults()
            
            # Sanitize each specified input
            for spec in input_specs:
                arg_name = spec.get('arg_name')
                input_type = spec.get('input_type', 'text')
                required = spec.get('required', False)
                
                if arg_name in bound_args.arguments:
                    value = bound_args.arguments[arg_name]
                    
                    # Skip if None and not required
                    if value is None and not required:
                        continue
                        
                    # Sanitize the value
                    sanitized = InputValidator.sanitize(
                        value, 
                        input_type, 
                        **{k: v for k, v in spec.items() if k not in ['arg_name', 'input_type', 'required']}
                    )
                    
                    # Update the argument value
                    bound_args.arguments[arg_name] = sanitized
            
            # Call the original function with sanitized arguments
            return await func(*bound_args.args, **bound_args.kwargs)
        
        return wrapper
    
    return decorator

def admin_only(func: Callable) -> Callable:
    """
    Decorator to restrict command access to admin users only.
    
    Args:
        func: The function to decorate
        
    Returns:
        Decorated function
    """
    @functools.wraps(func)
    async def wrapper(update: Update, context: ContextTypes.DEFAULT_TYPE, *args, **kwargs):
        user_id = update.effective_user.id
        
        # Get admin IDs from context
        admin_ids = []
        if context.bot_data and "bot_instance" in context.bot_data:
            bot_instance = context.bot_data["bot_instance"]
            if hasattr(bot_instance, "config_manager"):
                admin_ids = bot_instance.config_manager.get_admin_user_ids()
        
        # Check if user is an admin
        if user_id not in admin_ids:
            await update.message.reply_text("This command is only available to administrators.")
            logger.warning(f"Unauthorized access attempt to admin command by user {user_id}")
            return None
        
        # Call the original function
        return await func(update, context, *args, **kwargs)
    
    return wrapper
