"""
Payment provider interface for VoicePal.

This module defines the interface for payment providers.
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Tuple

from telegram import (
    Update, 
    PreCheckoutQuery, 
    SuccessfulPayment, 
    InlineKeyboardMarkup
)
from telegram.ext import ContextTypes

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class PaymentProviderInterface(ABC):
    """Interface for payment providers."""

    @abstractmethod
    def get_credit_packages(self) -> Dict[str, Dict[str, Any]]:
        """
        Get available credit packages.

        Returns:
            Dict of credit packages
        """
        pass

    @abstractmethod
    def get_credit_package(self, package_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a specific credit package.

        Args:
            package_id: ID of the credit package

        Returns:
            Dict containing credit package details or None if not found
        """
        pass

    @abstractmethod
    def get_payment_keyboard(self) -> InlineKeyboardMarkup:
        """
        Get keyboard with payment options.

        Returns:
            InlineKeyboardMarkup with payment options
        """
        pass

    @abstractmethod
    async def start_payment(self, update: Update, context: ContextTypes.DEFAULT_TYPE, package_id: str) -> None:
        """
        Start the payment process for a credit package.

        Args:
            update: Telegram update
            context: Callback context
            package_id: ID of the credit package
        """
        pass

    @abstractmethod
    def validate_pre_checkout(self, query: PreCheckoutQuery) -> bool:
        """
        Validate pre-checkout query.

        Args:
            query: Pre-checkout query

        Returns:
            bool: True if valid, False otherwise
        """
        pass

    @abstractmethod
    async def precheckout_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """
        Answer the PreCheckoutQuery.

        Args:
            update: Telegram update
            context: Callback context
        """
        pass

    @abstractmethod
    def process_payment(self, user_id: int, payment: SuccessfulPayment) -> int:
        """
        Process successful payment.

        Args:
            user_id: User ID
            payment: Successful payment

        Returns:
            int: Number of credits added
        """
        pass

    @abstractmethod
    async def successful_payment_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """
        Handle successful payment.

        Args:
            update: Telegram update
            context: Callback context
        """
        pass

    @abstractmethod
    async def create_invoice(self, chat_id: int, title: str, description: str, payload: str, 
                            price_cents: int, context: ContextTypes.DEFAULT_TYPE) -> bool:
        """
        Create and send an invoice.

        Args:
            chat_id: Chat ID to send the invoice to
            title: Invoice title
            description: Invoice description
            payload: Invoice payload
            price_cents: Price in cents
            context: Callback context

        Returns:
            bool: True if invoice was sent successfully, False otherwise
        """
        pass
