"""
Test runner script for updated VoicePal tests.

This script runs the updated tests for the VoicePal bot,
focusing on the database, payment system, and TTS provider.
"""

import os
import sys
import unittest
import logging

# Add the project root directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

def create_test_suite():
    """Create a test suite containing all updated tests."""
    test_suite = unittest.TestSuite()
    
    # Import test modules
    try:
        import test_database_core
        logger.info("Imported test_database_core")
        test_suite.addTest(unittest.makeSuite(test_database_core.TestDatabaseCore))
    except ImportError as e:
        logger.error(f"Error importing test_database_core: {e}")
    
    try:
        import test_telegram_stars_payment_updated
        logger.info("Imported test_telegram_stars_payment_updated")
        test_suite.addTest(unittest.makeSuite(test_telegram_stars_payment_updated.TestTelegramStarsPaymentUpdated))
    except ImportError as e:
        logger.error(f"Error importing test_telegram_stars_payment_updated: {e}")
    
    try:
        import test_deepgram_tts_provider
        logger.info("Imported test_deepgram_tts_provider")
        test_suite.addTest(unittest.makeSuite(test_deepgram_tts_provider.TestDeepgramTTSProvider))
    except ImportError as e:
        logger.error(f"Error importing test_deepgram_tts_provider: {e}")
    
    return test_suite

def run_tests():
    """Run all tests and return the result."""
    # Create test suite
    test_suite = create_test_suite()
    
    # Run tests
    test_runner = unittest.TextTestRunner(verbosity=2)
    result = test_runner.run(test_suite)
    
    # Return True if all tests passed, False otherwise
    return result.wasSuccessful()

if __name__ == '__main__':
    logger.info("Starting updated VoicePal tests")
    
    # Run tests
    success = run_tests()
    
    # Exit with appropriate code
    if success:
        logger.info("All tests passed!")
        sys.exit(0)
    else:
        logger.error("Some tests failed!")
        sys.exit(1)
