"""
Base TTS provider interface for VoicePal.

This module defines the base TTS provider interface that all TTS providers must implement.
"""

import os
import logging
import tempfile
from abc import ABC, abstractmethod
from typing import Dict, Optional, Any

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class BaseTTSProvider(ABC):
    """Base class for TTS providers."""

    @abstractmethod
    def generate_speech(self, text: str,
                       language: Optional[str] = None,
                       voice: Optional[str] = None,
                       **kwargs) -> Optional[str]:
        """
        Generate speech from text.

        Args:
            text: Text to convert to speech
            language: Language code
            voice: Voice identifier
            **kwargs: Additional parameters

        Returns:
            Path to the generated audio file or None if generation failed
        """
        pass

    @abstractmethod
    def get_available_languages(self) -> Dict[str, str]:
        """
        Get available languages.

        Returns:
            Dictionary of language codes and names
        """
        pass

    @abstractmethod
    def get_available_voices(self, language: Optional[str] = None) -> Dict[str, Dict[str, Any]]:
        """
        Get available voices.

        Args:
            language: Optional language code to filter voices

        Returns:
            Dictionary of voice identifiers and metadata
        """
        pass

    @abstractmethod
    def supports_feature(self, feature_name: str) -> bool:
        """
        Check if provider supports a specific feature.

        Args:
            feature_name: Name of the feature to check

        Returns:
            bool: True if the feature is supported, False otherwise
        """
        pass
