"""
Tests for provider exceptions.

This module tests the provider exception classes.
"""

import pytest

from bot.providers.core.exceptions import (
    ProviderError,
    ProviderInitializationError,
    ProviderAPIError,
    ProviderTimeoutError,
    ProviderAuthenticationError,
    ProviderRateLimitError,
    ProviderFeatureNotSupportedError,
    ProviderConfigurationError,
    ProviderResourceNotFoundError,
    ProviderInvalidInputError,
    ProviderQuotaExceededError,
    ProviderServiceUnavailableError
)

def test_provider_error():
    """Test ProviderError."""
    error = ProviderError("Test error")
    assert str(error) == "Test error"
    assert isinstance(error, Exception)

def test_provider_initialization_error():
    """Test ProviderInitializationError."""
    error = ProviderInitializationError("Test error")
    assert str(error) == "Provider initialization error: Test error"
    assert isinstance(error, ProviderError)

def test_provider_api_error():
    """Test ProviderAPIError."""
    # Test basic error
    error = ProviderAPIError("Test error")
    assert str(error) == "Provider API error: Test error"
    assert isinstance(error, ProviderError)
    
    # Test with status code and response text
    error = ProviderAPIError("Test error", status_code=400, response_text="Bad request")
    assert str(error) == "Provider API error: Test error"
    assert error.status_code == 400
    assert error.response_text == "Bad request"

def test_provider_timeout_error():
    """Test ProviderTimeoutError."""
    error = ProviderTimeoutError("Test error")
    assert str(error) == "Provider timeout error: Test error"
    assert isinstance(error, ProviderError)

def test_provider_authentication_error():
    """Test ProviderAuthenticationError."""
    # Test basic error
    error = ProviderAuthenticationError("Test error")
    assert str(error) == "Provider authentication error: Test error"
    assert isinstance(error, ProviderError)
    
    # Test with status code and response text
    error = ProviderAuthenticationError("Test error", status_code=401, response_text="Unauthorized")
    assert str(error) == "Provider authentication error: Test error"
    assert error.status_code == 401
    assert error.response_text == "Unauthorized"

def test_provider_rate_limit_error():
    """Test ProviderRateLimitError."""
    # Test basic error
    error = ProviderRateLimitError("Test error")
    assert str(error) == "Provider rate limit error: Test error"
    assert isinstance(error, ProviderError)
    
    # Test with status code and response text
    error = ProviderRateLimitError("Test error", status_code=429, response_text="Too many requests")
    assert str(error) == "Provider rate limit error: Test error"
    assert error.status_code == 429
    assert error.response_text == "Too many requests"

def test_provider_feature_not_supported_error():
    """Test ProviderFeatureNotSupportedError."""
    error = ProviderFeatureNotSupportedError("Test error")
    assert str(error) == "Provider feature not supported: Test error"
    assert isinstance(error, ProviderError)

def test_provider_configuration_error():
    """Test ProviderConfigurationError."""
    error = ProviderConfigurationError("Test error")
    assert str(error) == "Provider configuration error: Test error"
    assert isinstance(error, ProviderError)

def test_provider_resource_not_found_error():
    """Test ProviderResourceNotFoundError."""
    # Test basic error
    error = ProviderResourceNotFoundError("Test error")
    assert str(error) == "Provider resource not found: Test error"
    assert isinstance(error, ProviderError)
    
    # Test with status code and response text
    error = ProviderResourceNotFoundError("Test error", status_code=404, response_text="Not found")
    assert str(error) == "Provider resource not found: Test error"
    assert error.status_code == 404
    assert error.response_text == "Not found"

def test_provider_invalid_input_error():
    """Test ProviderInvalidInputError."""
    error = ProviderInvalidInputError("Test error")
    assert str(error) == "Provider invalid input: Test error"
    assert isinstance(error, ProviderError)

def test_provider_quota_exceeded_error():
    """Test ProviderQuotaExceededError."""
    error = ProviderQuotaExceededError("Test error")
    assert str(error) == "Provider quota exceeded: Test error"
    assert isinstance(error, ProviderError)

def test_provider_service_unavailable_error():
    """Test ProviderServiceUnavailableError."""
    # Test basic error
    error = ProviderServiceUnavailableError("Test error")
    assert str(error) == "Provider service unavailable: Test error"
    assert isinstance(error, ProviderError)
    
    # Test with status code and response text
    error = ProviderServiceUnavailableError("Test error", status_code=503, response_text="Service unavailable")
    assert str(error) == "Provider service unavailable: Test error"
    assert error.status_code == 503
    assert error.response_text == "Service unavailable"
