"""
Database migration management for VoicePal.

This module handles database migrations for schema changes.
"""

import os
import logging
import sqlite3
import json
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
from datetime import datetime

from bot.database.core.exceptions import DatabaseMigrationError
from bot.database.core.connection import DatabaseConnection

# Set up logging
logger = logging.getLogger(__name__)

class Migration:
    """Database migration."""
    
    def __init__(self, version: int, description: str, up_statements: List[str], down_statements: List[str]):
        """Initialize migration.
        
        Args:
            version: Migration version
            description: Migration description
            up_statements: SQL statements for migrating up
            down_statements: SQL statements for migrating down
        """
        self.version = version
        self.description = description
        self.up_statements = up_statements
        self.down_statements = down_statements
    
    def __str__(self) -> str:
        """String representation of migration.
        
        Returns:
            String representation
        """
        return f"Migration(version={self.version}, description={self.description})"
    
    def __repr__(self) -> str:
        """Detailed string representation of migration.
        
        Returns:
            Detailed string representation
        """
        return f"Migration(version={self.version}, description={self.description}, up_statements={len(self.up_statements)}, down_statements={len(self.down_statements)})"

class MigrationManager:
    """Manages database migrations."""
    
    def __init__(self, db_connection: DatabaseConnection, migrations_dir: Optional[str] = None):
        """Initialize migration manager.
        
        Args:
            db_connection: Database connection
            migrations_dir: Directory containing migration files
        """
        self.conn = db_connection
        self.migrations_dir = Path(migrations_dir) if migrations_dir else None
        self.migrations: Dict[int, Migration] = {}
        
        # Ensure migrations table exists
        self._ensure_migrations_table()
        
        # Load migrations
        if self.migrations_dir and self.migrations_dir.exists():
            self._load_migrations_from_directory()
    
    def _ensure_migrations_table(self) -> None:
        """Ensure migrations table exists.
        
        Raises:
            DatabaseMigrationError: If table creation fails
        """
        try:
            self.conn.execute("""
                CREATE TABLE IF NOT EXISTS schema_migrations (
                    version INTEGER PRIMARY KEY,
                    applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    description TEXT
                )
            """)
            self.conn.commit()
        except Exception as e:
            logger.error(f"Failed to create migrations table: {e}")
            raise DatabaseMigrationError(f"Failed to create migrations table: {e}") from e
    
    def _load_migrations_from_directory(self) -> None:
        """Load migrations from directory.
        
        Raises:
            DatabaseMigrationError: If migration loading fails
        """
        try:
            for file_path in sorted(self.migrations_dir.glob("*.json")):
                try:
                    with open(file_path, "r") as f:
                        migration_data = json.load(f)
                    
                    version = migration_data.get("version")
                    description = migration_data.get("description", "")
                    up_statements = migration_data.get("up", [])
                    down_statements = migration_data.get("down", [])
                    
                    if not version:
                        logger.warning(f"Skipping migration file without version: {file_path}")
                        continue
                    
                    migration = Migration(
                        version=version,
                        description=description,
                        up_statements=up_statements,
                        down_statements=down_statements
                    )
                    
                    self.migrations[version] = migration
                    logger.debug(f"Loaded migration: {migration}")
                except Exception as e:
                    logger.warning(f"Failed to load migration file {file_path}: {e}")
            
            logger.info(f"Loaded {len(self.migrations)} migrations")
        except Exception as e:
            logger.error(f"Failed to load migrations: {e}")
            raise DatabaseMigrationError(f"Failed to load migrations: {e}") from e
    
    def add_migration(self, migration: Migration) -> None:
        """Add migration.
        
        Args:
            migration: Migration to add
            
        Raises:
            ValueError: If migration version already exists
        """
        if migration.version in self.migrations:
            raise ValueError(f"Migration version {migration.version} already exists")
        
        self.migrations[migration.version] = migration
        logger.debug(f"Added migration: {migration}")
    
    def get_current_version(self) -> int:
        """Get current schema version.
        
        Returns:
            Current schema version
            
        Raises:
            DatabaseMigrationError: If version retrieval fails
        """
        try:
            cursor = self.conn.execute(
                "SELECT MAX(version) FROM schema_migrations"
            )
            version = cursor.fetchone()[0]
            return version or 0
        except Exception as e:
            logger.error(f"Failed to get current schema version: {e}")
            raise DatabaseMigrationError(f"Failed to get current schema version: {e}") from e
    
    def get_pending_migrations(self) -> List[Migration]:
        """Get pending migrations.
        
        Returns:
            List of pending migrations
            
        Raises:
            DatabaseMigrationError: If migration retrieval fails
        """
        try:
            current_version = self.get_current_version()
            
            pending_migrations = []
            for version, migration in sorted(self.migrations.items()):
                if version > current_version:
                    pending_migrations.append(migration)
            
            return pending_migrations
        except Exception as e:
            logger.error(f"Failed to get pending migrations: {e}")
            raise DatabaseMigrationError(f"Failed to get pending migrations: {e}") from e
    
    def migrate(self, target_version: Optional[int] = None) -> None:
        """Migrate database schema to target version.
        
        Args:
            target_version: Target schema version (default: latest)
            
        Raises:
            DatabaseMigrationError: If migration fails
        """
        try:
            current_version = self.get_current_version()
            
            if target_version is None:
                # Get latest version
                target_version = max(self.migrations.keys()) if self.migrations else current_version
            
            logger.info(f"Current schema version: {current_version}")
            logger.info(f"Target schema version: {target_version}")
            
            if current_version == target_version:
                logger.info("Database schema is up to date")
                return
            
            with self.conn.transaction():
                if current_version < target_version:
                    # Migrate up
                    for version, migration in sorted(self.migrations.items()):
                        if current_version < version <= target_version:
                            logger.info(f"Applying migration {version}: {migration.description}")
                            
                            for statement in migration.up_statements:
                                self.conn.execute(statement)
                            
                            self.conn.execute(
                                "INSERT INTO schema_migrations (version, description) VALUES (?, ?)",
                                (version, migration.description)
                            )
                else:
                    # Migrate down
                    for version, migration in sorted(self.migrations.items(), reverse=True):
                        if target_version < version <= current_version:
                            logger.info(f"Reverting migration {version}: {migration.description}")
                            
                            for statement in migration.down_statements:
                                self.conn.execute(statement)
                            
                            self.conn.execute(
                                "DELETE FROM schema_migrations WHERE version = ?",
                                (version,)
                            )
            
            logger.info(f"Database migrated successfully to version {target_version}")
        except Exception as e:
            logger.error(f"Migration failed: {e}")
            raise DatabaseMigrationError(f"Migration failed: {e}") from e
    
    def create_migration_file(self, description: str, up_statements: List[str], down_statements: List[str]) -> Path:
        """Create migration file.
        
        Args:
            description: Migration description
            up_statements: SQL statements for migrating up
            down_statements: SQL statements for migrating down
            
        Returns:
            Path to created migration file
            
        Raises:
            DatabaseMigrationError: If file creation fails
        """
        if not self.migrations_dir:
            raise ValueError("Migrations directory not set")
        
        try:
            # Ensure migrations directory exists
            os.makedirs(self.migrations_dir, exist_ok=True)
            
            # Get next version
            current_version = self.get_current_version()
            next_version = current_version + 1
            
            # Create migration data
            migration_data = {
                "version": next_version,
                "description": description,
                "up": up_statements,
                "down": down_statements
            }
            
            # Create file name
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            file_name = f"{timestamp}_{next_version}_{description.lower().replace(' ', '_')}.json"
            file_path = self.migrations_dir / file_name
            
            # Write migration file
            with open(file_path, "w") as f:
                json.dump(migration_data, f, indent=2)
            
            logger.info(f"Created migration file: {file_path}")
            
            # Add migration to manager
            migration = Migration(
                version=next_version,
                description=description,
                up_statements=up_statements,
                down_statements=down_statements
            )
            self.add_migration(migration)
            
            return file_path
        except Exception as e:
            logger.error(f"Failed to create migration file: {e}")
            raise DatabaseMigrationError(f"Failed to create migration file: {e}") from e
