"""
Integration tests for the database module.

This module provides integration tests for the database module.
"""

import os
import unittest
import tempfile
from pathlib import Path
from datetime import datetime, timedelta

from bot.database.database_manager import DatabaseManager
from bot.database.core.exceptions import (
    DatabaseError,
    DatabaseNotFoundError,
    DatabaseDuplicateError,
    InsufficientCreditsError
)
from bot.database.models import (
    User,
    UserPreference,
    UserStat,
    Conversation,
    Message,
    MessageMetadata,
    Transaction,
    PaymentPackage,
    Subscription,
    Memory,
    MemoryTag,
    VoiceSetting,
    VoiceRecording
)

class TestDatabaseIntegration(unittest.TestCase):
    """Integration test case for database module."""
    
    def setUp(self):
        """Set up test environment."""
        # Create temporary directory for database
        self.temp_dir = tempfile.TemporaryDirectory()
        self.db_path = Path(self.temp_dir.name) / "test.db"
        
        # Initialize database
        self.db = DatabaseManager(self.db_path)
    
    def tearDown(self):
        """Clean up test environment."""
        # Close database connection
        self.db.close()
        
        # Remove temporary directory
        self.temp_dir.cleanup()
    
    def test_user_conversation_flow(self):
        """Test user conversation flow."""
        # Create user
        user_id = "test_user"
        username = "test_username"
        first_name = "Test"
        last_name = "User"
        
        user = User(
            user_id=user_id,
            username=username,
            first_name=first_name,
            last_name=last_name
        )
        
        self.db.users.create(user)
        
        # Create conversation
        conversation, created = self.db.conversations.get_or_create_active_conversation(user_id)
        self.assertTrue(created)
        
        # Add user message
        user_message = "Hello, bot!"
        user_msg = self.db.messages.add_message(
            conversation.conversation_id,
            user_id,
            user_message,
            Message.ROLE_USER
        )
        
        # Add bot message
        bot_message = "Hello, human!"
        bot_msg = self.db.messages.add_message(
            conversation.conversation_id,
            user_id,
            bot_message,
            Message.ROLE_ASSISTANT
        )
        
        # Add message metadata
        sentiment_key = "sentiment"
        sentiment_value = "positive"
        self.db.message_metadata.set_metadata(bot_msg.message_id, sentiment_key, sentiment_value)
        
        # Create memory
        memory_content = "User greeted the bot"
        memory = self.db.memories.create_memory(
            user_id,
            memory_content,
            importance=5,
            tags=["greeting"]
        )
        
        # Get conversation history
        history = self.db.messages.get_conversation_history(conversation.conversation_id)
        self.assertEqual(len(history), 2)
        self.assertEqual(history[0]["role"], Message.ROLE_USER)
        self.assertEqual(history[0]["content"], user_message)
        self.assertEqual(history[1]["role"], Message.ROLE_ASSISTANT)
        self.assertEqual(history[1]["content"], bot_message)
        
        # Get message metadata
        sentiment = self.db.message_metadata.get_metadata(bot_msg.message_id, sentiment_key)
        self.assertEqual(sentiment, sentiment_value)
        
        # Get memory
        memories = self.db.memories.find_by_user(user_id)
        self.assertEqual(len(memories), 1)
        self.assertEqual(memories[0].content, memory_content)
        
        # Get memory tags
        memory_tags = self.db.memory_tags.get_memory_tags(memory.memory_id)
        self.assertEqual(len(memory_tags), 1)
        self.assertEqual(memory_tags[0], "greeting")
    
    def test_payment_flow(self):
        """Test payment flow."""
        # Create user
        user_id = "test_user"
        user = User(user_id=user_id, username="test_username")
        self.db.users.create(user)
        
        # Create payment package
        package = PaymentPackage(
            name="Basic",
            description="Basic package",
            credits=100,
            price=9.99
        )
        self.db.payment_packages.create(package)
        
        # Create transaction
        transaction = self.db.transactions.create_purchase_transaction(
            user_id,
            package.credits,
            "stripe"
        )
        
        # Complete transaction
        provider_transaction_id = "txn_123456"
        self.db.transactions.complete_transaction(transaction.transaction_id, provider_transaction_id)
        
        # Add credits to user
        self.db.users.add_credits(user_id, package.credits)
        
        # Create subscription
        subscription = self.db.subscriptions.create_subscription(
            user_id,
            package.package_id,
            "stripe",
            "sub_123456"
        )
        
        # Activate subscription
        start_date = datetime.now().isoformat()
        end_date = (datetime.now() + timedelta(days=30)).isoformat()
        
        self.db.subscriptions.activate_subscription(
            subscription.subscription_id,
            start_date,
            end_date
        )
        
        # Use credits
        self.db.users.use_credits(user_id, 10)
        
        # Check user credits
        user = self.db.users.find_by_id(user_id)
        self.assertEqual(user.credits, 190)  # 100 (initial) + 100 (package) - 10 (used)
        
        # Check if user has active subscription
        has_subscription = self.db.subscriptions.has_active_subscription(user_id)
        self.assertTrue(has_subscription)
    
    def test_voice_flow(self):
        """Test voice flow."""
        # Create user
        user_id = "test_user"
        user = User(user_id=user_id, username="test_username")
        self.db.users.create(user)
        
        # Create conversation
        conversation, _ = self.db.conversations.get_or_create_active_conversation(user_id)
        
        # Add message
        message = self.db.messages.add_message(
            conversation.conversation_id,
            user_id,
            "Hello, bot!",
            Message.ROLE_USER
        )
        
        # Create voice setting
        provider = "deepgram"
        voice_id = "aura"
        
        setting, created = self.db.voice_settings.get_or_create(
            user_id,
            provider,
            voice_id
        )
        
        self.assertTrue(created)
        self.assertEqual(setting.provider, provider)
        self.assertEqual(setting.voice_id, voice_id)
        
        # Update voice settings
        pitch = 1.2
        rate = 0.9
        
        self.db.voice_settings.update_settings(
            user_id,
            pitch=pitch,
            rate=rate
        )
        
        setting = self.db.voice_settings.find_by_user(user_id)
        self.assertEqual(setting.pitch, pitch)
        self.assertEqual(setting.rate, rate)
        
        # Create recording
        file_path = "/tmp/recording.mp3"
        duration = 5.5
        
        recording = self.db.voice_recordings.create_recording(
            user_id,
            file_path,
            message.message_id,
            duration
        )
        
        self.assertEqual(recording.user_id, user_id)
        self.assertEqual(recording.file_path, file_path)
        self.assertEqual(recording.message_id, message.message_id)
        self.assertEqual(recording.duration, duration)
        
        # Get recording by message
        recording = self.db.voice_recordings.find_by_message(message.message_id)
        self.assertIsNotNone(recording)
        self.assertEqual(recording.file_path, file_path)
        
        # Get total duration
        total_duration = self.db.voice_recordings.get_total_duration(user_id)
        self.assertEqual(total_duration, duration)
    
    def test_error_handling(self):
        """Test error handling."""
        # Create user
        user_id = "test_user"
        user = User(user_id=user_id, username="test_username")
        self.db.users.create(user)
        
        # Test duplicate user
        duplicate_user = User(user_id=user_id, username="duplicate_username")
        with self.assertRaises(DatabaseDuplicateError):
            self.db.users.create(duplicate_user)
        
        # Test not found
        with self.assertRaises(DatabaseNotFoundError):
            self.db.users.update(User(user_id="non_existent", username="test"))
        
        # Test insufficient credits
        user = self.db.users.find_by_id(user_id)
        self.assertEqual(user.credits, 100)  # Default credits
        
        with self.assertRaises(ValueError):
            self.db.users.use_credits(user_id, 200)  # More than available
        
        # Test transaction rollback
        try:
            with self.db.transaction():
                self.db.users.add_credits(user_id, 50)
                raise ValueError("Test exception")
        except ValueError:
            pass
        
        # Check if credits were not added
        user = self.db.users.find_by_id(user_id)
        self.assertEqual(user.credits, 100)  # Still the default

if __name__ == "__main__":
    unittest.main()
