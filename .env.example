# Telegram Bot Configuration
TELEGRAM_TOKEN=your_telegram_bot_token_here
PAYMENT_PROVIDER_TOKEN=your_payment_provider_token_here

# API Keys
DEEPGRAM_API_KEY=your_deepgram_api_key_here
GOOGLE_AI_API_KEY=your_google_ai_api_key_here
HF_API_TOKEN=your_hugging_face_token_here
ELEVENLABS_API_KEY=your_elevenlabs_api_key_here

# AI and TTS Configuration
AI_PROVIDER=google_ai  # google_ai or groq
GROQ_API_KEY=your_groq_api_key_here
GROQ_MODEL_NAME=mixtral-8x7b-32768  # For Groq
TTS_PROVIDER=deepgram  # google, dia, elevenlabs, or deepgram
DIA_MODEL_ID=nari-labs/Dia-1.6B  # only used if TTS_PROVIDER=dia
ELEVENLABS_VOICE_ID=Bella  # only used if TTS_PROVIDER=elevenlabs
DEEPGRAM_VOICE_ID=aura-2-thalia-en  # only used if TTS_PROVIDER=deepgram
DEFAULT_PERSONALITY=friendly  # friendly, witty, calm, motivational, thoughtful
DEFAULT_LANGUAGE=en

# Admin Configuration
ADMIN_USER_IDS=comma_separated_telegram_user_ids

# Credit System Configuration
FREE_TRIAL_CREDITS=1000
TEXT_CREDIT_COST=1
VOICE_CREDIT_COST=3

# Database Configuration
DATABASE_FILE=voicepal.db

# Logging Configuration
LOGGING_LEVEL=INFO

# Security Configuration
ENABLE_SECURITY_MONITORING=true
SECURITY_FAILED_LOGIN_THRESHOLD=5
SECURITY_PAYMENT_FAILURES_THRESHOLD=3
SECURITY_API_KEY_ACCESS_THRESHOLD=10
SECURITY_CREDIT_USAGE_THRESHOLD=50
SECURITY_MULTIPLE_ACCOUNTS_THRESHOLD=2
SECURITY_VERIFICATION_FAILURES_THRESHOLD=3
SECURITY_AUDIT_RETENTION_DAYS=90
SECURITY_FILE_LOGGING_ENABLED=false
SECURITY_LOG_DIRECTORY=logs

# Payment Security Configuration
ENABLE_PAYMENT_SECURITY=true
PAYMENT_MAX_DAILY_PAYMENTS=10
PAYMENT_MAX_DAILY_AMOUNT=100
PAYMENT_MAX_ATTEMPTS=5
PAYMENT_SUSPICIOUS_TIME_WINDOW=5
PAYMENT_MAX_VELOCITY=3
PAYMENT_MAX_DIFFERENT_METHODS=3
PAYMENT_MIN_ACCOUNT_AGE_DAYS=1
PAYMENT_MAX_CREDITS=1000
PAYMENT_MAX_AMOUNT=100
PAYMENT_MAX_FREQUENCY=10

# Reflex Admin Dashboard
REFLEX_PORT=3000
