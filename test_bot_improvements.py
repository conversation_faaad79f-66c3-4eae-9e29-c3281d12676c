#!/usr/bin/env python
"""
Comprehensive test script for VoicePal bot improvements.

This script tests the entire bot with the memory and voice improvements.
It simulates a conversation with the bot and verifies that:
1. Short messages preserve context
2. User names are properly remembered
3. Deepgram TTS is used for voice responses

Usage:
    python test_bot_improvements.py [--no-voice]

Options:
    --no-voice    Don't generate voice responses (text only)
"""

import os
import sys
import logging
import asyncio
import argparse
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath("."))

# Import the necessary components
from bot.core.enhanced_dialog_engine import EnhancedDialogEngine
from bot.features.enhanced_memory_manager import EnhancedMemoryManager
from bot.database.database import Database
from bot.database.extensions.user_preferences import extend_database_for_user_preferences
from bot.providers.ai.google_ai_provider import GoogleAIProvider
from bot.providers.tts.deepgram_provider import DeepgramTTSProvider
from bot.providers.voice.processor import VoiceProcessor
from bot.core.user_manager import UserManager

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class BotTester:
    """Class to test the VoicePal bot with improvements."""

    def __init__(self, use_voice=True):
        """Initialize the bot tester."""
        self.use_voice = use_voice

        # Create a temporary database
        self.db_path = Path("test_bot.db")
        if self.db_path.exists():
            self.db_path.unlink()

        # Initialize components
        self.database = Database(self.db_path)

        # Extend database with user preferences
        extend_database_for_user_preferences(self.database)

        # Get API keys from environment
        self.google_ai_key = os.getenv("GOOGLE_AI_API_KEY")
        self.deepgram_key = os.getenv("DEEPGRAM_API_KEY")

        if not self.google_ai_key:
            logger.warning("GOOGLE_AI_API_KEY not set, using mock AI provider")
            self.ai_provider = self._create_mock_ai_provider()
        else:
            logger.info("Using Google AI provider")
            self.ai_provider = GoogleAIProvider(api_key=self.google_ai_key)

        # Initialize memory manager
        self.memory_manager = EnhancedMemoryManager(self.database, self.ai_provider)

        # Initialize user manager
        self.user_manager = UserManager(self.database)

        # Initialize voice processor
        if self.use_voice:
            if not self.deepgram_key:
                logger.warning("DEEPGRAM_API_KEY not set, voice responses will use Google TTS fallback")

            self.voice_processor = VoiceProcessor(
                deepgram_api_key=self.deepgram_key,
                tts_provider="deepgram",
                tts_provider_options={
                    "api_key": self.deepgram_key,
                    "voice_id": "aura-bella-en",
                    "model_id": None  # Not used directly, included for backward compatibility
                }
            )
        else:
            self.voice_processor = None

        # Initialize dialog engine
        self.dialog_engine = EnhancedDialogEngine(
            ai_provider=self.ai_provider,
            memory_manager=self.memory_manager,
            user_manager=self.user_manager
        )

        # Test user
        self.user_id = 12345
        self.database.add_user(self.user_id, "John", "Doe", "johndoe")

        # Add some initial credits
        self.user_manager.add_credits(self.user_id, 100)

        logger.info("Bot tester initialized")

    def _create_mock_ai_provider(self):
        """Create a mock AI provider for testing."""
        class MockAIProvider:
            def __init__(self):
                self.personality = "friendly"
                self.supported_features = ["text_generation"]

            def supports_feature(self, feature_name):
                return feature_name in self.supported_features

            def set_personality(self, personality):
                self.personality = personality

            async def generate_response(self, message, language=None, user_context=None):
                # Extract user name from context if available
                user_name = "there"
                if user_context and "context" in user_context and "user_name" in user_context["context"]:
                    user_name = user_context["context"]["user_name"]

                # Check if this is a short message
                is_short = False
                if user_context and "context" in user_context and "message_analysis" in user_context["context"]:
                    is_short = user_context["context"]["message_analysis"].get("is_short_response", False)

                # Generate a response based on the message
                if "hello" in message.lower() or "hi" in message.lower():
                    response = f"Hello {user_name}! How can I help you today?"
                elif "how are you" in message.lower():
                    response = f"I'm doing well, {user_name}! Thanks for asking. How about you?"
                elif is_short:
                    response = "I'm keeping my response brief since your message was short."
                else:
                    response = f"Thanks for your message, {user_name}. I'm a mock AI provider for testing purposes."

                return {"text": response, "language": language or "en"}

        return MockAIProvider()

    async def cleanup(self):
        """Clean up resources."""
        if self.db_path.exists():
            self.db_path.unlink()
        logger.info("Cleaned up resources")

    async def send_message(self, message, is_voice=False):
        """Send a message to the bot and get a response."""
        logger.info(f"User: {message}")

        # Process the message
        response = await self.dialog_engine.process_message(
            user_id=self.user_id,
            message=message,
            is_voice=is_voice
        )

        logger.info(f"Bot: {response['text']}")

        # Generate voice response if enabled
        if self.use_voice and self.voice_processor:
            audio_path = self.voice_processor.tts_provider.generate_speech(
                text=response["text"],
                language=response.get("language", "en")
            )

            if audio_path:
                logger.info(f"Generated voice response: {audio_path}")

                # Play the audio
                try:
                    if sys.platform == "win32":
                        os.system(f'start {audio_path}')
                    else:
                        try:
                            import subprocess
                            subprocess.run(
                                ["ffplay", "-autoexit", "-nodisp", str(audio_path)],
                                stdout=subprocess.DEVNULL,
                                stderr=subprocess.DEVNULL
                            )
                        except FileNotFoundError:
                            logger.warning("ffplay not found, skipping audio playback")

                    # Sleep briefly to allow audio to start playing
                    import time
                    time.sleep(2)
                except Exception as e:
                    logger.error(f"Error playing audio: {e}")

        return response

    async def run_conversation_test(self):
        """Run a test conversation with the bot."""
        logger.info("=== Starting Conversation Test ===")

        # Initial greeting
        await self.send_message("Hello")

        # Ask a question
        await self.send_message("How are you doing today?")

        # Send a short message to test context preservation
        await self.send_message("Great!")

        # Another short message
        await self.send_message("lol")

        # Send an emoji
        await self.send_message("👍")

        # Ask about the bot's name
        await self.send_message("What's your name?")

        # Send a longer message
        await self.send_message(
            "I've been thinking about artificial intelligence and how it's changing our world. "
            "What do you think about the future of AI and its impact on society?"
        )

        # Test voice message
        await self.send_message("Can you tell me a short story?", is_voice=True)

        logger.info("=== Conversation Test Completed ===")
        return True

async def main():
    """Run the bot improvement tests."""
    parser = argparse.ArgumentParser(description="Test VoicePal bot improvements")
    parser.add_argument("--no-voice", action="store_true", help="Don't generate voice responses")

    args = parser.parse_args()

    try:
        tester = BotTester(use_voice=not args.no_voice)
        await tester.run_conversation_test()
        await tester.cleanup()

        logger.info("All tests completed successfully!")
        return True
    except Exception as e:
        logger.error(f"Test failed: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    asyncio.run(main())
