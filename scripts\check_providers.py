"""
Check providers script for VoicePal.

This script checks for redundant provider implementations and suggests which ones to keep.
"""

import os
import logging
from typing import Dict, List, Set

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

def find_provider_files() -> Dict[str, List[str]]:
    """
    Find all provider implementation files.
    
    Returns:
        Dict mapping provider types to lists of file paths
    """
    provider_files = {
        "stt": [],
        "ai": [],
        "tts": []
    }
    
    # Walk through the bot directory
    for root, dirs, files in os.walk("bot"):
        for file in files:
            file_path = os.path.join(root, file)
            
            # Skip __init__.py and interface files
            if file == "__init__.py" or "interface" in file:
                continue
            
            # Check if file is a provider implementation
            if "provider" in file.lower() or "tts_" in file.lower() or "stt_" in file.lower() or "ai_" in file.lower():
                # Determine provider type
                if "stt" in file.lower() or "speech" in file.lower() or "deepgram" in file.lower():
                    provider_files["stt"].append(file_path)
                elif "ai" in file.lower() or "google_ai" in file.lower() or "gemini" in file.lower():
                    provider_files["ai"].append(file_path)
                elif "tts" in file.lower() or "elevenlabs" in file.lower() or "voice" in file.lower():
                    provider_files["tts"].append(file_path)
    
    return provider_files

def analyze_provider_files(provider_files: Dict[str, List[str]]) -> Dict[str, List[str]]:
    """
    Analyze provider files and suggest which ones to keep.
    
    Args:
        provider_files: Dict mapping provider types to lists of file paths
        
    Returns:
        Dict mapping provider types to lists of file paths to keep
    """
    files_to_keep = {
        "stt": [],
        "ai": [],
        "tts": []
    }
    
    # For each provider type
    for provider_type, files in provider_files.items():
        logger.info(f"Found {len(files)} {provider_type.upper()} provider implementations:")
        
        # Print all files
        for i, file_path in enumerate(files):
            file_size = os.path.getsize(file_path)
            logger.info(f"  {i+1}. {file_path} ({file_size} bytes)")
        
        # Suggest which files to keep
        if files:
            # Prefer files in the providers directory structure
            preferred_files = [f for f in files if f"providers/{provider_type}" in f]
            
            if preferred_files:
                files_to_keep[provider_type] = preferred_files
            else:
                # If no files in the providers directory, keep the largest file
                largest_file = max(files, key=os.path.getsize)
                files_to_keep[provider_type] = [largest_file]
    
    return files_to_keep

def suggest_actions(provider_files: Dict[str, List[str]], files_to_keep: Dict[str, List[str]]) -> None:
    """
    Suggest actions to take for each provider type.
    
    Args:
        provider_files: Dict mapping provider types to lists of file paths
        files_to_keep: Dict mapping provider types to lists of file paths to keep
    """
    logger.info("\nSuggested actions:")
    
    for provider_type in provider_files:
        all_files = set(provider_files[provider_type])
        keep_files = set(files_to_keep[provider_type])
        remove_files = all_files - keep_files
        
        logger.info(f"\n{provider_type.upper()} providers:")
        
        if keep_files:
            logger.info("  Files to keep:")
            for file_path in keep_files:
                logger.info(f"    - {file_path}")
        
        if remove_files:
            logger.info("  Files to consider removing:")
            for file_path in remove_files:
                logger.info(f"    - {file_path}")
        
        if not all_files:
            logger.info("  No files found")

def main():
    """Main function to check provider implementations."""
    try:
        # Find provider files
        provider_files = find_provider_files()
        
        # Analyze provider files
        files_to_keep = analyze_provider_files(provider_files)
        
        # Suggest actions
        suggest_actions(provider_files, files_to_keep)
        
        logger.info("\nProvider check completed successfully")
    except Exception as e:
        logger.error(f"Error during provider check: {e}")

if __name__ == "__main__":
    main()
