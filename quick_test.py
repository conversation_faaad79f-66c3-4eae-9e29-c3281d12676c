#!/usr/bin/env python3
"""
Quick test to verify basic functionality.
"""

import sys
import os
import tempfile
import sqlite3
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_database_basic():
    """Test basic database operations."""
    print("Testing basic database operations...")
    
    try:
        # Create temporary database
        fd, db_path = tempfile.mkstemp(suffix='.db')
        os.close(fd)
        
        try:
            # Test basic SQLite operations
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # Create a simple table
            cursor.execute('''
                CREATE TABLE test_users (
                    id INTEGER PRIMARY KEY,
                    username TEXT NOT NULL,
                    credits INTEGER DEFAULT 0
                )
            ''')
            
            # Insert test data
            cursor.execute(
                "INSERT INTO test_users (id, username, credits) VALUES (?, ?, ?)",
                (123, "test_user", 100)
            )
            
            # Query data
            cursor.execute("SELECT * FROM test_users WHERE id = ?", (123,))
            result = cursor.fetchone()
            
            conn.commit()
            conn.close()
            
            assert result is not None
            assert result[1] == "test_user"
            assert result[2] == 100
            
            print("✅ Basic database operations work!")
            return True
            
        finally:
            try:
                os.unlink(db_path)
            except OSError:
                pass
                
    except Exception as e:
        print(f"❌ Basic database test failed: {e}")
        return False

def test_imports():
    """Test basic imports."""
    print("Testing basic imports...")
    
    try:
        # Test database manager import
        from bot.database.database_manager import DatabaseManager
        print("✅ DatabaseManager import successful")
        
        # Test analytics imports
        from bot.analytics.conversation_analytics import ConversationAnalytics
        print("✅ ConversationAnalytics import successful")
        
        from bot.analytics.user_analytics import UserAnalytics
        print("✅ UserAnalytics import successful")
        
        from bot.analytics.business_analytics import BusinessAnalytics
        print("✅ BusinessAnalytics import successful")
        
        # Test security imports
        from bot.security.encryption import EncryptionManager
        print("✅ EncryptionManager import successful")
        
        # Test performance imports
        from bot.performance.cache_manager import CacheManager
        print("✅ CacheManager import successful")
        
        print("✅ All basic imports successful!")
        return True
        
    except Exception as e:
        print(f"❌ Import test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_manager():
    """Test database manager functionality."""
    print("Testing database manager...")
    
    try:
        from bot.database.database_manager import DatabaseManager
        
        # Create temporary database
        fd, db_path = tempfile.mkstemp(suffix='.db')
        os.close(fd)
        
        try:
            # Initialize database manager
            db_manager = DatabaseManager(db_path)
            db_manager.initialize_database()
            
            # Test user creation
            user_id = 12345
            db_manager.create_user(
                user_id=user_id,
                username="test_user",
                first_name="Test"
            )
            
            # Test user retrieval
            user = db_manager.get_user(user_id)
            assert user is not None
            assert user['username'] == "test_user"
            
            # Test credit operations
            db_manager.update_user_credits(user_id, 100)
            user = db_manager.get_user(user_id)
            assert user['credits'] == 100
            
            print("✅ Database manager test successful!")
            return True
            
        finally:
            try:
                os.unlink(db_path)
            except OSError:
                pass
                
    except Exception as e:
        print(f"❌ Database manager test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_encryption():
    """Test encryption functionality."""
    print("Testing encryption...")
    
    try:
        from bot.security.encryption import EncryptionManager
        
        # Test encryption manager
        encryption = EncryptionManager()
        
        # Test data encryption/decryption
        original_data = "This is sensitive data"
        encrypted_data = encryption.encrypt_data(original_data)
        decrypted_data = encryption.decrypt_data(encrypted_data)
        
        assert decrypted_data == original_data
        assert encrypted_data != original_data
        
        print("✅ Encryption test successful!")
        return True
        
    except Exception as e:
        print(f"❌ Encryption test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_cache():
    """Test cache functionality."""
    print("Testing cache...")
    
    try:
        from bot.performance.cache_manager import CacheManager
        
        # Create cache manager without Redis
        cache = CacheManager(redis_client=None)
        
        # Test set/get operations
        cache.set("test_key", "test_value", ttl=3600)
        value = cache.get("test_key")
        
        assert value == "test_value"
        
        # Test cache stats
        stats = cache.get_stats()
        assert stats.total_entries >= 1
        
        print("✅ Cache test successful!")
        return True
        
    except Exception as e:
        print(f"❌ Cache test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run quick tests."""
    print("🚀 Running Quick VoicePal Tests")
    print("=" * 50)
    
    tests = [
        test_database_basic,
        test_imports,
        test_database_manager,
        test_encryption,
        test_cache
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            failed += 1
        print()
    
    print("=" * 50)
    print(f"Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All tests passed!")
        return True
    else:
        print("💥 Some tests failed!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
