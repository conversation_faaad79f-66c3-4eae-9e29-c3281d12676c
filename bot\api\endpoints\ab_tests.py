"""
A/B testing endpoints for VoicePal API.
"""

import logging
from typing import List
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from bot.api.dependencies import get_advanced_features_manager

logger = logging.getLogger(__name__)
router = APIRouter()

class ABTestCreate(BaseModel):
    name: str
    description: str
    test_type: str
    variants: List[dict]
    start_date: str
    target_audience: dict = {}

@router.post("/ab-tests", status_code=201)
async def create_ab_test(
    test_data: ABTestCreate,
    advanced=Depends(get_advanced_features_manager)
):
    """Create a new A/B test."""
    try:
        test_id = advanced.create_ab_test(**test_data.dict())
        return {"test_id": test_id, "message": "A/B test created successfully"}
    except Exception as e:
        logger.error(f"Error creating A/B test: {e}")
        raise HTTPException(status_code=500, detail="Error creating A/B test")

@router.get("/ab-tests")
async def list_ab_tests(advanced=Depends(get_advanced_features_manager)):
    """List all A/B tests."""
    try:
        tests = advanced.ab_test_manager.list_active_tests()
        return {"tests": [test.__dict__ if hasattr(test, '__dict__') else test for test in tests]}
    except Exception as e:
        logger.error(f"Error listing A/B tests: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving A/B tests")

@router.get("/ab-tests/{test_id}/results")
async def get_ab_test_results(
    test_id: str,
    advanced=Depends(get_advanced_features_manager)
):
    """Get A/B test results."""
    try:
        results = advanced.get_ab_test_results(test_id)
        return {"results": results}
    except Exception as e:
        logger.error(f"Error getting A/B test results: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving A/B test results")
