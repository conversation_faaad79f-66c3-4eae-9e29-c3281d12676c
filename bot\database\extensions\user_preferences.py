"""
Database extension for user preferences and summaries.

This module extends the database schema to support user preferences and summaries.
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

def extend_database_for_user_preferences(database) -> None:
    """
    Extend the database schema with user preferences and summaries tables.

    Args:
        database: Database instance
    """
    try:
        conn = database.conn
        cursor = database.cursor

        # Create user_preferences table if it doesn't exist
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS user_preferences (
            user_id INTEGER,
            preference_key TEXT,
            preference_value TEXT,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (user_id, preference_key),
            FOREIGN KEY (user_id) REFERENCES users (user_id)
        )
        ''')

        # Create user_summaries table if it doesn't exist
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS user_summaries (
            user_id INTEGER PRIMARY KEY,
            summary TEXT,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (user_id)
        )
        ''')

        # Check if user_interests table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='user_interests'")
        if not cursor.fetchone():
            # Create user_interests table if it doesn't exist
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS user_interests (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                interest TEXT,
                confidence REAL DEFAULT 1.0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (user_id)
            )
            ''')
        else:
            # Check if confidence column exists
            try:
                cursor.execute("SELECT confidence FROM user_interests LIMIT 1")
            except Exception:
                # Add confidence column if it doesn't exist
                cursor.execute("ALTER TABLE user_interests ADD COLUMN confidence REAL DEFAULT 1.0")
                logger.info("Added confidence column to user_interests table")

        conn.commit()
        logger.info("Database schema extended for user preferences and summaries")

        # Add methods to Database class
        _extend_database_methods(database)

    except Exception as e:
        logger.error(f"Error extending database schema for user preferences: {e}")
        if conn:
            conn.rollback()
        raise

def _extend_database_methods(database) -> None:
    """
    Extend the Database class with user preferences and summaries methods.

    Args:
        database: Database instance
    """
    # Add method to get user preferences
    def get_user_preferences(self, user_id: int) -> Dict[str, Any]:
        """
        Get user preferences.

        Args:
            user_id: User ID

        Returns:
            Dict containing user preferences
        """
        try:
            self.cursor.execute(
                """SELECT preference_key, preference_value
                   FROM user_preferences
                   WHERE user_id = ?""",
                (user_id,)
            )
            preferences = self.cursor.fetchall()

            if preferences:
                return {p['preference_key']: p['preference_value'] for p in preferences}
            return {}
        except Exception as e:
            logger.error(f"Error getting preferences for user {user_id}: {e}")
            return {}

    # Add method to get a single user preference
    def get_user_preference(self, user_id: int, preference_key: str, default_value: Any = None) -> Any:
        """
        Get a single user preference.

        Args:
            user_id: User ID
            preference_key: Preference key
            default_value: Default value if preference doesn't exist

        Returns:
            The preference value or default_value if not found
        """
        try:
            self.cursor.execute(
                """SELECT preference_value
                   FROM user_preferences
                   WHERE user_id = ? AND preference_key = ?""",
                (user_id, preference_key)
            )
            preference = self.cursor.fetchone()

            if preference:
                return preference['preference_value']
            return default_value
        except Exception as e:
            logger.error(f"Error getting preference '{preference_key}' for user {user_id}: {e}")
            return default_value

    # Add method to update user preference
    def update_user_preference(self, user_id: int, preference_key: str, preference_value: Any) -> bool:
        """
        Update user preference.

        Args:
            user_id: User ID
            preference_key: Preference key
            preference_value: Preference value

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            # Convert preference_value to string if it's not already
            if not isinstance(preference_value, str):
                preference_value = str(preference_value)

            self.cursor.execute(
                """INSERT OR REPLACE INTO user_preferences
                   (user_id, preference_key, preference_value, updated_at)
                   VALUES (?, ?, ?, ?)""",
                (user_id, preference_key, preference_value, current_time)
            )
            self.conn.commit()
            logger.info(f"Updated preference '{preference_key}' for user {user_id}")
            return True
        except Exception as e:
            logger.error(f"Error updating preference '{preference_key}' for user {user_id}: {e}")
            self.conn.rollback()
            return False

    # Add method to get user summary
    def get_user_summary(self, user_id: int) -> Optional[str]:
        """
        Get user summary.

        Args:
            user_id: User ID

        Returns:
            str: User summary or None if not available
        """
        try:
            self.cursor.execute(
                """SELECT summary
                   FROM user_summaries
                   WHERE user_id = ?""",
                (user_id,)
            )
            summary = self.cursor.fetchone()

            if summary:
                return summary['summary']
            return None
        except Exception as e:
            logger.error(f"Error getting summary for user {user_id}: {e}")
            return None

    # Add method to update user summary
    def update_user_summary(self, user_id: int, summary: str) -> bool:
        """
        Update user summary.

        Args:
            user_id: User ID
            summary: User summary

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            self.cursor.execute(
                """INSERT OR REPLACE INTO user_summaries
                   (user_id, summary, updated_at)
                   VALUES (?, ?, ?)""",
                (user_id, summary, current_time)
            )
            self.conn.commit()
            logger.info(f"Updated summary for user {user_id}")
            return True
        except Exception as e:
            logger.error(f"Error updating summary for user {user_id}: {e}")
            self.conn.rollback()
            return False

    # Add method to get user summary update time
    def get_user_summary_update_time(self, user_id: int) -> Optional[datetime]:
        """
        Get user summary update time.

        Args:
            user_id: User ID

        Returns:
            datetime: Update time or None if not available
        """
        try:
            self.cursor.execute(
                """SELECT updated_at
                   FROM user_summaries
                   WHERE user_id = ?""",
                (user_id,)
            )
            result = self.cursor.fetchone()

            if result and result['updated_at']:
                return datetime.strptime(result['updated_at'], '%Y-%m-%d %H:%M:%S')
            return None
        except Exception as e:
            logger.error(f"Error getting summary update time for user {user_id}: {e}")
            return None

    # Add method to get user interests
    def get_user_interests(self, user_id: int) -> List[str]:
        """
        Get user interests.

        Args:
            user_id: User ID

        Returns:
            List of user interests
        """
        try:
            self.cursor.execute(
                """SELECT interest
                   FROM user_interests
                   WHERE user_id = ?
                   ORDER BY confidence DESC""",
                (user_id,)
            )
            interests = self.cursor.fetchall()

            if interests:
                return [i['interest'] for i in interests]
            return []
        except Exception as e:
            logger.error(f"Error getting interests for user {user_id}: {e}")
            return []

    # Add method to update user interests
    def update_user_interests(self, user_id: int, interests: List[str]) -> bool:
        """
        Update user interests.

        Args:
            user_id: User ID
            interests: List of interests

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Begin transaction
            self.conn.execute("BEGIN TRANSACTION")

            # Delete existing interests for this user
            self.cursor.execute(
                """DELETE FROM user_interests
                   WHERE user_id = ?""",
                (user_id,)
            )

            # Insert new interests
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            for interest in interests:
                self.cursor.execute(
                    """INSERT INTO user_interests
                       (user_id, interest, created_at)
                       VALUES (?, ?, ?)""",
                    (user_id, interest, current_time)
                )

            # Commit transaction
            self.conn.commit()
            logger.info(f"Updated interests for user {user_id}")
            return True
        except Exception as e:
            logger.error(f"Error updating interests for user {user_id}: {e}")
            self.conn.rollback()
            return False

    # Add method to add a single interest
    def add_user_interest(self, user_id: int, interest: str, confidence: float = 1.0) -> bool:
        """
        Add a single interest for a user.

        Args:
            user_id: User ID
            interest: Interest
            confidence: Confidence score (0.0 to 1.0)

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            self.cursor.execute(
                """INSERT INTO user_interests
                   (user_id, interest, confidence, created_at)
                   VALUES (?, ?, ?, ?)""",
                (user_id, interest, confidence, current_time)
            )
            self.conn.commit()
            logger.info(f"Added interest '{interest}' for user {user_id}")
            return True
        except Exception as e:
            logger.error(f"Error adding interest '{interest}' for user {user_id}: {e}")
            self.conn.rollback()
            return False

    # Add update_user_personality as an alias for set_user_personality
    def update_user_personality(self, user_id: int, personality: str) -> bool:
        """
        Update user's preferred AI personality (alias for set_user_personality).

        Args:
            user_id: Telegram user ID
            personality: Personality name

        Returns:
            bool: True if successful, False otherwise
        """
        return self.set_user_personality(user_id, personality)

    # Add methods to Database class
    setattr(database.__class__, 'get_user_preferences', get_user_preferences)
    setattr(database.__class__, 'get_user_preference', get_user_preference)
    setattr(database.__class__, 'update_user_preference', update_user_preference)
    setattr(database.__class__, 'get_user_summary', get_user_summary)
    setattr(database.__class__, 'update_user_summary', update_user_summary)
    setattr(database.__class__, 'get_user_summary_update_time', get_user_summary_update_time)
    setattr(database.__class__, 'get_user_interests', get_user_interests)
    setattr(database.__class__, 'update_user_interests', update_user_interests)
    setattr(database.__class__, 'add_user_interest', add_user_interest)
    setattr(database.__class__, 'update_user_personality', update_user_personality)

    logger.info("Database extended with methods for user preferences and summaries")
