"""
Unified schema management for VoicePal.

This module provides a centralized schema management system for the VoicePal database.
It defines all database tables, indexes, and migrations in one place.
"""

import os
import logging
import json
import sqlite3
from typing import Dict, List, Any, Optional, Tuple, Union
from pathlib import Path
from datetime import datetime

from bot.database.core.connection import DatabaseConnection
from bot.database.core.exceptions import (
    DatabaseSchemaError,
    DatabaseMigrationError
)

# Set up logging
logger = logging.getLogger(__name__)

# Current schema version
CURRENT_SCHEMA_VERSION = 1

# Schema definitions
SCHEMA = {
    # User domain
    "users": """
        CREATE TABLE IF NOT EXISTS users (
            user_id TEXT PRIMARY KEY,
            username TEXT,
            first_name TEXT,
            last_name TEXT,
            language_code TEXT DEFAULT 'en',
            credits INTEGER DEFAULT 100,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_interaction TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            is_active BOOLEAN DEFAULT 1,
            personality TEXT DEFAULT 'friendly'
        )
    """,
    
    "user_preferences": """
        CREATE TABLE IF NOT EXISTS user_preferences (
            preference_id TEXT PRIMARY KEY,
            user_id TEXT NOT NULL,
            key TEXT NOT NULL,
            value TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
            UNIQUE(user_id, key)
        )
    """,
    
    "user_stats": """
        CREATE TABLE IF NOT EXISTS user_stats (
            stat_id TEXT PRIMARY KEY,
            user_id TEXT NOT NULL,
            key TEXT NOT NULL,
            value INTEGER DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
            UNIQUE(user_id, key)
        )
    """,
    
    # Conversation domain
    "conversations": """
        CREATE TABLE IF NOT EXISTS conversations (
            conversation_id TEXT PRIMARY KEY,
            user_id TEXT NOT NULL,
            title TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            is_active BOOLEAN DEFAULT 1,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
        )
    """,
    
    "messages": """
        CREATE TABLE IF NOT EXISTS messages (
            message_id TEXT PRIMARY KEY,
            conversation_id TEXT NOT NULL,
            user_id TEXT NOT NULL,
            content TEXT NOT NULL,
            role TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (conversation_id) REFERENCES conversations(conversation_id) ON DELETE CASCADE,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
        )
    """,
    
    "message_metadata": """
        CREATE TABLE IF NOT EXISTS message_metadata (
            metadata_id TEXT PRIMARY KEY,
            message_id TEXT NOT NULL,
            key TEXT NOT NULL,
            value TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (message_id) REFERENCES messages(message_id) ON DELETE CASCADE,
            UNIQUE(message_id, key)
        )
    """,
    
    # Memory domain
    "memories": """
        CREATE TABLE IF NOT EXISTS memories (
            memory_id TEXT PRIMARY KEY,
            user_id TEXT NOT NULL,
            content TEXT NOT NULL,
            importance INTEGER DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_accessed TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
        )
    """,
    
    "memory_tags": """
        CREATE TABLE IF NOT EXISTS memory_tags (
            tag_id TEXT PRIMARY KEY,
            memory_id TEXT NOT NULL,
            tag TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (memory_id) REFERENCES memories(memory_id) ON DELETE CASCADE,
            UNIQUE(memory_id, tag)
        )
    """,
    
    # Payment domain
    "transactions": """
        CREATE TABLE IF NOT EXISTS transactions (
            transaction_id TEXT PRIMARY KEY,
            user_id TEXT NOT NULL,
            amount INTEGER NOT NULL,
            type TEXT NOT NULL,
            status TEXT NOT NULL,
            provider TEXT,
            provider_transaction_id TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
        )
    """,
    
    "payment_packages": """
        CREATE TABLE IF NOT EXISTS payment_packages (
            package_id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            description TEXT,
            credits INTEGER NOT NULL,
            price REAL NOT NULL,
            currency TEXT DEFAULT 'USD',
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    """,
    
    "subscriptions": """
        CREATE TABLE IF NOT EXISTS subscriptions (
            subscription_id TEXT PRIMARY KEY,
            user_id TEXT NOT NULL,
            package_id TEXT NOT NULL,
            status TEXT NOT NULL,
            provider TEXT NOT NULL,
            provider_subscription_id TEXT,
            start_date TIMESTAMP,
            end_date TIMESTAMP,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
            FOREIGN KEY (package_id) REFERENCES payment_packages(package_id) ON DELETE CASCADE
        )
    """,
    
    # Voice domain
    "voice_settings": """
        CREATE TABLE IF NOT EXISTS voice_settings (
            setting_id TEXT PRIMARY KEY,
            user_id TEXT NOT NULL,
            provider TEXT NOT NULL,
            voice_id TEXT NOT NULL,
            pitch REAL DEFAULT 1.0,
            rate REAL DEFAULT 1.0,
            volume REAL DEFAULT 1.0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
            UNIQUE(user_id)
        )
    """,
    
    "voice_recordings": """
        CREATE TABLE IF NOT EXISTS voice_recordings (
            recording_id TEXT PRIMARY KEY,
            user_id TEXT NOT NULL,
            message_id TEXT,
            file_path TEXT NOT NULL,
            duration REAL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
            FOREIGN KEY (message_id) REFERENCES messages(message_id) ON DELETE SET NULL
        )
    """,
    
    # System tables
    "schema_migrations": """
        CREATE TABLE IF NOT EXISTS schema_migrations (
            version INTEGER PRIMARY KEY,
            applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            description TEXT
        )
    """,
    
    "system_settings": """
        CREATE TABLE IF NOT EXISTS system_settings (
            key TEXT PRIMARY KEY,
            value TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    """
}

# Indexes
INDEXES = [
    # User domain
    "CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)",
    "CREATE INDEX IF NOT EXISTS idx_user_preferences_user_id ON user_preferences(user_id)",
    "CREATE INDEX IF NOT EXISTS idx_user_stats_user_id ON user_stats(user_id)",
    
    # Conversation domain
    "CREATE INDEX IF NOT EXISTS idx_conversations_user_id ON conversations(user_id)",
    "CREATE INDEX IF NOT EXISTS idx_messages_conversation_id ON messages(conversation_id)",
    "CREATE INDEX IF NOT EXISTS idx_messages_user_id ON messages(user_id)",
    "CREATE INDEX IF NOT EXISTS idx_message_metadata_message_id ON message_metadata(message_id)",
    
    # Memory domain
    "CREATE INDEX IF NOT EXISTS idx_memories_user_id ON memories(user_id)",
    "CREATE INDEX IF NOT EXISTS idx_memory_tags_memory_id ON memory_tags(memory_id)",
    "CREATE INDEX IF NOT EXISTS idx_memory_tags_tag ON memory_tags(tag)",
    
    # Payment domain
    "CREATE INDEX IF NOT EXISTS idx_transactions_user_id ON transactions(user_id)",
    "CREATE INDEX IF NOT EXISTS idx_transactions_status ON transactions(status)",
    "CREATE INDEX IF NOT EXISTS idx_payment_packages_is_active ON payment_packages(is_active)",
    "CREATE INDEX IF NOT EXISTS idx_subscriptions_user_id ON subscriptions(user_id)",
    "CREATE INDEX IF NOT EXISTS idx_subscriptions_status ON subscriptions(status)",
    
    # Voice domain
    "CREATE INDEX IF NOT EXISTS idx_voice_settings_user_id ON voice_settings(user_id)",
    "CREATE INDEX IF NOT EXISTS idx_voice_recordings_user_id ON voice_recordings(user_id)",
    "CREATE INDEX IF NOT EXISTS idx_voice_recordings_message_id ON voice_recordings(message_id)"
]

# Migrations
MIGRATIONS = [
    {
        "version": 1,
        "description": "Initial schema",
        "up": [],  # No up migration for initial schema
        "down": []  # No down migration for initial schema
    }
]

class SchemaManager:
    """Unified schema manager for VoicePal database."""
    
    def __init__(self, db_connection: DatabaseConnection, migrations_dir: Optional[str] = None):
        """Initialize schema manager.
        
        Args:
            db_connection: Database connection
            migrations_dir: Directory containing migration files
        """
        self.conn = db_connection
        self.migrations_dir = Path(migrations_dir) if migrations_dir else None
    
    def initialize_schema(self) -> None:
        """Initialize database schema.
        
        Creates tables and indexes if they don't exist.
        
        Raises:
            DatabaseSchemaError: If schema initialization fails
        """
        try:
            # Create tables
            for table_name, create_statement in SCHEMA.items():
                self.conn.execute(create_statement)
            
            # Create indexes
            for index_statement in INDEXES:
                self.conn.execute(index_statement)
            
            # Check if schema_migrations table is empty
            cursor = self.conn.execute("SELECT COUNT(*) FROM schema_migrations")
            count = cursor.fetchone()[0]
            
            if count == 0:
                # Record initial schema version
                self.conn.execute(
                    "INSERT INTO schema_migrations (version, description) VALUES (?, ?)",
                    (1, "Initial schema")
                )
                self.conn.commit()
                
            logger.info("Database schema initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize schema: {e}")
            raise DatabaseSchemaError(f"Failed to initialize schema: {e}") from e
    
    def get_current_version(self) -> int:
        """Get current schema version.
        
        Returns:
            Current schema version
            
        Raises:
            DatabaseSchemaError: If version retrieval fails
        """
        try:
            cursor = self.conn.execute(
                "SELECT MAX(version) FROM schema_migrations"
            )
            version = cursor.fetchone()[0]
            return version or 0
        except Exception as e:
            logger.error(f"Failed to get current schema version: {e}")
            raise DatabaseSchemaError(f"Failed to get current schema version: {e}") from e
    
    def migrate(self, target_version: Optional[int] = None) -> None:
        """Migrate database schema to target version.
        
        Args:
            target_version: Target schema version (default: latest)
            
        Raises:
            DatabaseMigrationError: If migration fails
        """
        try:
            current_version = self.get_current_version()
            target_version = target_version or CURRENT_SCHEMA_VERSION
            
            logger.info(f"Current schema version: {current_version}")
            logger.info(f"Target schema version: {target_version}")
            
            if current_version == target_version:
                logger.info("Database schema is up to date")
                return
            
            with self.conn.transaction():
                if current_version < target_version:
                    # Migrate up
                    for migration in MIGRATIONS:
                        version = migration["version"]
                        if current_version < version <= target_version:
                            logger.info(f"Applying migration {version}: {migration['description']}")
                            
                            for statement in migration["up"]:
                                self.conn.execute(statement)
                            
                            self.conn.execute(
                                "INSERT INTO schema_migrations (version, description) VALUES (?, ?)",
                                (version, migration["description"])
                            )
                else:
                    # Migrate down
                    for migration in reversed(MIGRATIONS):
                        version = migration["version"]
                        if target_version < version <= current_version:
                            logger.info(f"Reverting migration {version}: {migration['description']}")
                            
                            for statement in migration["down"]:
                                self.conn.execute(statement)
                            
                            self.conn.execute(
                                "DELETE FROM schema_migrations WHERE version = ?",
                                (version,)
                            )
            
            logger.info(f"Database migrated successfully to version {target_version}")
        except Exception as e:
            logger.error(f"Migration failed: {e}")
            raise DatabaseMigrationError(f"Migration failed: {e}") from e
    
    def validate_schema(self) -> bool:
        """Validate database schema.
        
        Returns:
            True if schema is valid, False otherwise
            
        Raises:
            DatabaseSchemaError: If validation fails
        """
        try:
            # Get list of tables in database
            cursor = self.conn.execute(
                "SELECT name FROM sqlite_master WHERE type='table'"
            )
            tables = [row[0] for row in cursor.fetchall()]
            
            # Check if all required tables exist
            for table_name in SCHEMA.keys():
                if table_name not in tables:
                    logger.warning(f"Table {table_name} does not exist")
                    return False
            
            # Check schema version
            current_version = self.get_current_version()
            if current_version != CURRENT_SCHEMA_VERSION:
                logger.warning(f"Schema version mismatch: {current_version} != {CURRENT_SCHEMA_VERSION}")
                return False
            
            logger.info("Database schema is valid")
            return True
        except Exception as e:
            logger.error(f"Schema validation failed: {e}")
            raise DatabaseSchemaError(f"Schema validation failed: {e}") from e
