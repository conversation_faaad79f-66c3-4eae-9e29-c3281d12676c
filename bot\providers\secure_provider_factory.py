"""
Secure provider factory for VoicePal.

This module provides a secure factory for creating provider instances
with enhanced API key security.
"""

import os
import logging
from typing import Dict, Any, Optional, Type

from bot.core.key_manager import KeyManager
from bot.providers.provider_factory import ProviderFactory
from bot.providers.stt.base import BaseSTTProvider
from bot.providers.tts.base import BaseTTSProvider
from bot.providers.ai.base import BaseA<PERSON>rovider

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class SecureProviderFactory:
    """
    Secure factory for creating provider instances with enhanced API key security.
    
    This class extends the standard ProviderFactory to use the KeyManager
    for secure API key handling.
    """
    
    def __init__(self, key_manager: KeyManager):
        """
        Initialize the secure provider factory.
        
        Args:
            key_manager: KeyManager instance for secure key handling
        """
        self.key_manager = key_manager
        
    def create_stt_provider(self, provider_type: str, user_id: Optional[int] = None,
                           ip_address: Optional[str] = None, **kwargs) -> BaseSTTProvider:
        """
        Create a speech-to-text provider with secure key handling.
        
        Args:
            provider_type: Type of STT provider
            user_id: User ID for access logging
            ip_address: IP address for access logging
            **kwargs: Additional provider options
            
        Returns:
            BaseSTTProvider: Initialized STT provider
        """
        # Get API key securely
        api_key = self.key_manager.get_key(provider_type, user_id, ip_address)
        
        # If key not found in key manager, try environment or kwargs
        if not api_key:
            logger.warning(f"API key for {provider_type} not found in key manager, trying fallbacks")
            
            # Try environment variable
            env_var_name = f"{provider_type.upper()}_API_KEY"
            api_key = os.environ.get(env_var_name)
            
            # Try kwargs
            if not api_key and "api_key" in kwargs:
                api_key = kwargs["api_key"]
                
            # If key found in fallbacks, store it securely
            if api_key:
                logger.info(f"Storing API key for {provider_type} from fallback source")
                self.key_manager.store_key(provider_type, api_key)
        
        # Update kwargs with secure API key
        if api_key:
            kwargs["api_key"] = api_key
        
        # Create provider using standard factory
        return ProviderFactory.create_stt_provider(provider_type, **kwargs)
        
    def create_tts_provider(self, provider_type: str, user_id: Optional[int] = None,
                           ip_address: Optional[str] = None, **kwargs) -> BaseTTSProvider:
        """
        Create a text-to-speech provider with secure key handling.
        
        Args:
            provider_type: Type of TTS provider
            user_id: User ID for access logging
            ip_address: IP address for access logging
            **kwargs: Additional provider options
            
        Returns:
            BaseTTSProvider: Initialized TTS provider
        """
        # Get API key securely
        api_key = self.key_manager.get_key(provider_type, user_id, ip_address)
        
        # If key not found in key manager, try environment or kwargs
        if not api_key:
            logger.warning(f"API key for {provider_type} not found in key manager, trying fallbacks")
            
            # Try environment variable
            env_var_name = f"{provider_type.upper()}_API_KEY"
            api_key = os.environ.get(env_var_name)
            
            # Try kwargs
            if not api_key and "api_key" in kwargs:
                api_key = kwargs["api_key"]
                
            # If key found in fallbacks, store it securely
            if api_key:
                logger.info(f"Storing API key for {provider_type} from fallback source")
                self.key_manager.store_key(provider_type, api_key)
        
        # Update kwargs with secure API key
        if api_key:
            kwargs["api_key"] = api_key
        
        # Create provider using standard factory
        return ProviderFactory.create_tts_provider(provider_type, **kwargs)
        
    def create_ai_provider(self, provider_type: str, user_id: Optional[int] = None,
                          ip_address: Optional[str] = None, **kwargs) -> BaseAIProvider:
        """
        Create an AI provider with secure key handling.
        
        Args:
            provider_type: Type of AI provider
            user_id: User ID for access logging
            ip_address: IP address for access logging
            **kwargs: Additional provider options
            
        Returns:
            BaseAIProvider: Initialized AI provider
        """
        # Get API key securely
        api_key = self.key_manager.get_key(provider_type, user_id, ip_address)
        
        # If key not found in key manager, try environment or kwargs
        if not api_key:
            logger.warning(f"API key for {provider_type} not found in key manager, trying fallbacks")
            
            # Try environment variable
            env_var_name = f"{provider_type.upper()}_API_KEY"
            api_key = os.environ.get(env_var_name)
            
            # Try kwargs
            if not api_key and "api_key" in kwargs:
                api_key = kwargs["api_key"]
                
            # If key found in fallbacks, store it securely
            if api_key:
                logger.info(f"Storing API key for {provider_type} from fallback source")
                self.key_manager.store_key(provider_type, api_key)
        
        # Update kwargs with secure API key
        if api_key:
            kwargs["api_key"] = api_key
        
        # Create provider using standard factory
        return ProviderFactory.create_ai_provider(provider_type, **kwargs)
