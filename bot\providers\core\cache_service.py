"""
Caching service for VoicePal providers.

This module provides a centralized caching mechanism for providers to store
and retrieve data, reducing API calls and improving performance.
"""

import os
import logging
import hashlib
import json
from typing import Dict, Any, Optional, Union
from pathlib import Path
from datetime import datetime, timedelta

# Set up logging
logger = logging.getLogger(__name__)

class CacheService:
    """Centralized caching service for providers."""
    
    def __init__(self, cache_dir: Union[str, Path] = None, max_age: int = 3600):
        """Initialize cache service.
        
        Args:
            cache_dir: Directory for cache files
            max_age: Maximum cache age in seconds (default: 1 hour)
        """
        if cache_dir is None:
            cache_dir = Path("cache")
        elif isinstance(cache_dir, str):
            cache_dir = Path(cache_dir)
            
        self.cache_dir = cache_dir
        self.max_age = max_age
        self.cache = {}
        self.timestamps = {}
        
        # Create cache directory if it doesn't exist
        os.makedirs(self.cache_dir, exist_ok=True)
        
        logger.info(f"Cache service initialized with directory: {self.cache_dir}")
    
    def _generate_key(self, prefix: str, data: Any) -> str:
        """Generate a cache key.
        
        Args:
            prefix: Key prefix
            data: Data to hash
            
        Returns:
            Cache key
        """
        if isinstance(data, dict):
            data_str = json.dumps(data, sort_keys=True)
        elif not isinstance(data, str):
            data_str = str(data)
        else:
            data_str = data
            
        key = f"{prefix}_{hashlib.md5(data_str.encode()).hexdigest()}"
        return key
    
    def get(self, prefix: str, data: Any) -> Optional[Any]:
        """Get item from cache.
        
        Args:
            prefix: Key prefix
            data: Data to hash for key
            
        Returns:
            Cached item or None if not found or expired
        """
        key = self._generate_key(prefix, data)
        
        # Check memory cache first
        if key in self.cache:
            timestamp = self.timestamps.get(key, datetime.min)
            age = (datetime.now() - timestamp).total_seconds()
            
            if age < self.max_age:
                logger.debug(f"Cache hit (memory): {key}")
                return self.cache[key]
            else:
                # Expired
                del self.cache[key]
                del self.timestamps[key]
        
        # Check file cache
        cache_file = self.cache_dir / f"{key}.json"
        if cache_file.exists():
            try:
                # Check file age
                file_age = (datetime.now() - datetime.fromtimestamp(cache_file.stat().st_mtime)).total_seconds()
                if file_age < self.max_age:
                    with open(cache_file, "r") as f:
                        value = json.load(f)
                        
                    # Update memory cache
                    self.cache[key] = value
                    self.timestamps[key] = datetime.now()
                    
                    logger.debug(f"Cache hit (file): {key}")
                    return value
                else:
                    # Expired
                    cache_file.unlink(missing_ok=True)
            except Exception as e:
                logger.warning(f"Error reading cache file: {e}")
                cache_file.unlink(missing_ok=True)
        
        return None
    
    def set(self, prefix: str, data: Any, value: Any) -> None:
        """Set item in cache.
        
        Args:
            prefix: Key prefix
            data: Data to hash for key
            value: Value to cache
        """
        key = self._generate_key(prefix, data)
        
        # Update memory cache
        self.cache[key] = value
        self.timestamps[key] = datetime.now()
        
        # Update file cache
        try:
            cache_file = self.cache_dir / f"{key}.json"
            with open(cache_file, "w") as f:
                json.dump(value, f)
                
            logger.debug(f"Cache set: {key}")
        except Exception as e:
            logger.warning(f"Error writing cache file: {e}")
    
    def clear(self, prefix: Optional[str] = None, max_age: Optional[int] = None) -> int:
        """Clear expired cache items.
        
        Args:
            prefix: Optional prefix to clear only specific items
            max_age: Override default max age
            
        Returns:
            Number of items cleared
        """
        _max_age = max_age or self.max_age
        cleared_count = 0
        
        # Clear memory cache
        now = datetime.now()
        keys_to_delete = []
        
        for key, timestamp in self.timestamps.items():
            age = (now - timestamp).total_seconds()
            if (prefix is None or key.startswith(prefix)) and age >= _max_age:
                keys_to_delete.append(key)
        
        for key in keys_to_delete:
            del self.cache[key]
            del self.timestamps[key]
            cleared_count += 1
        
        # Clear file cache
        for cache_file in self.cache_dir.glob("*.json"):
            try:
                key = cache_file.stem
                if prefix is None or key.startswith(prefix):
                    file_age = (now - datetime.fromtimestamp(cache_file.stat().st_mtime)).total_seconds()
                    if file_age >= _max_age:
                        cache_file.unlink()
                        cleared_count += 1
            except Exception as e:
                logger.warning(f"Error clearing cache file: {e}")
        
        logger.info(f"Cleared {cleared_count} expired cache items")
        return cleared_count
    
    def delete(self, prefix: str, data: Any) -> bool:
        """Delete item from cache.
        
        Args:
            prefix: Key prefix
            data: Data to hash for key
            
        Returns:
            True if item was deleted, False otherwise
        """
        key = self._generate_key(prefix, data)
        
        # Delete from memory cache
        if key in self.cache:
            del self.cache[key]
            del self.timestamps[key]
        
        # Delete from file cache
        cache_file = self.cache_dir / f"{key}.json"
        if cache_file.exists():
            try:
                cache_file.unlink()
                logger.debug(f"Cache deleted: {key}")
                return True
            except Exception as e:
                logger.warning(f"Error deleting cache file: {e}")
                return False
        
        return False
