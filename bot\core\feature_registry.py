"""
Feature registry for VoicePal.

This module provides a registry for bot features.
"""

import logging
from typing import Dict, Any, Callable, List, Optional, Set

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class FeatureRegistry:
    """Registry for bot features."""
    
    def __init__(self):
        """Initialize the feature registry."""
        self.features = {}
        self.required_providers = {}
        self.enabled_features = set()
    
    def register_feature(self, feature_name: str, feature_handler: Callable, 
                        required_providers: List[str] = None) -> None:
        """
        Register a new feature.
        
        Args:
            feature_name: Feature name
            feature_handler: Feature handler function
            required_providers: List of required provider types
        """
        self.features[feature_name] = feature_handler
        self.required_providers[feature_name] = required_providers or []
        logger.info(f"Registered feature: {feature_name}")
    
    def enable_feature(self, feature_name: str) -> bool:
        """
        Enable a feature.
        
        Args:
            feature_name: Feature name
            
        Returns:
            bool: True if feature was enabled, False otherwise
        """
        if feature_name not in self.features:
            logger.warning(f"Feature not found: {feature_name}")
            return False
        
        self.enabled_features.add(feature_name)
        logger.info(f"Enabled feature: {feature_name}")
        return True
    
    def disable_feature(self, feature_name: str) -> bool:
        """
        Disable a feature.
        
        Args:
            feature_name: Feature name
            
        Returns:
            bool: True if feature was disabled, False otherwise
        """
        if feature_name not in self.features:
            logger.warning(f"Feature not found: {feature_name}")
            return False
        
        if feature_name in self.enabled_features:
            self.enabled_features.remove(feature_name)
            logger.info(f"Disabled feature: {feature_name}")
            return True
        
        return False
    
    def is_feature_enabled(self, feature_name: str) -> bool:
        """
        Check if a feature is enabled.
        
        Args:
            feature_name: Feature name
            
        Returns:
            bool: True if feature is enabled, False otherwise
        """
        return feature_name in self.enabled_features
    
    def is_feature_available(self, feature_name: str, available_providers: List[str]) -> bool:
        """
        Check if a feature can be enabled with available providers.
        
        Args:
            feature_name: Feature name
            available_providers: List of available provider types
            
        Returns:
            bool: True if feature can be enabled, False otherwise
        """
        if feature_name not in self.features:
            return False
        
        required = self.required_providers[feature_name]
        return all(provider in available_providers for provider in required)
    
    def get_feature_handler(self, feature_name: str) -> Optional[Callable]:
        """
        Get the handler for a feature.
        
        Args:
            feature_name: Feature name
            
        Returns:
            Callable: Feature handler or None if not found
        """
        return self.features.get(feature_name)
    
    def get_enabled_features(self) -> Set[str]:
        """
        Get all enabled features.
        
        Returns:
            Set of enabled feature names
        """
        return self.enabled_features.copy()
    
    def get_available_features(self, available_providers: List[str]) -> List[str]:
        """
        Get all available features based on available providers.
        
        Args:
            available_providers: List of available provider types
            
        Returns:
            List of available feature names
        """
        return [
            feature_name
            for feature_name in self.features
            if self.is_feature_available(feature_name, available_providers)
        ]
