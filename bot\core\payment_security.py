"""
Payment security monitoring for VoicePal.

This module provides specialized monitoring for payment operations
to detect and prevent fraud or abuse of the payment system.
"""

import logging
import json
import time
import asyncio
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
import sqlite3

from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes

from bot.core.security_monitor import (
    SEVERITY_INFO, SEVERITY_LOW, SEVERITY_MEDIUM, SEVERITY_HIGH, SEVERITY_CRITICAL,
    CATEGORY_PAYMENT
)

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# Payment fraud detection thresholds
DEFAULT_THRESHOLDS = {
    "max_daily_payments": 10,           # Maximum number of payments per day
    "max_daily_amount": 100,            # Maximum total payment amount per day
    "max_payment_attempts": 5,          # Maximum failed payment attempts before flagging
    "suspicious_time_window": 5,        # Minutes between payments to consider suspicious
    "max_payment_velocity": 3,          # Maximum payments per hour
    "max_different_payment_methods": 3, # Maximum different payment methods per day
    "min_account_age_days": 1,          # Minimum account age for payments
    "max_credits_per_payment": 1000,    # Maximum credits in a single payment
    "max_payment_amount": 100,          # Maximum payment amount in a single transaction
    "max_payment_frequency": 10         # Maximum payments per day
}

class PaymentSecurityMonitor:
    """
    Payment security monitoring for VoicePal.
    
    This class provides specialized monitoring for payment operations
    to detect and prevent fraud or abuse of the payment system.
    """
    
    def __init__(self, database, security_monitor, config_manager):
        """
        Initialize the payment security monitor.
        
        Args:
            database: Database instance
            security_monitor: SecurityMonitor instance
            config_manager: ConfigManager instance
        """
        self.database = database
        self.security_monitor = security_monitor
        self.config_manager = config_manager
        
        # Get payment security configuration
        self.config = self.config_manager.get_feature_config("payment_security") or {}
        
        # Get thresholds from config or use defaults
        self.thresholds = self.config.get("thresholds", DEFAULT_THRESHOLDS)
        
        # Initialize database tables
        self._setup_database()
        
        # Payment blacklist cache
        self.payment_blacklist = set()
        self._load_payment_blacklist()
        
        logger.info("Payment security monitor initialized")
    
    def _setup_database(self) -> None:
        """Create payment security tables if they don't exist."""
        try:
            cursor = self.database.conn.cursor()
            
            # Create payment_security_logs table
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS payment_security_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                user_id INTEGER NOT NULL,
                event_type TEXT NOT NULL,
                payment_method TEXT,
                payment_id TEXT,
                amount REAL,
                credits INTEGER,
                details TEXT,
                is_suspicious BOOLEAN DEFAULT 0,
                is_blocked BOOLEAN DEFAULT 0,
                resolution TEXT
            )
            ''')
            
            # Create payment_blacklist table
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS payment_blacklist (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                payment_method TEXT,
                reason TEXT NOT NULL,
                added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                added_by TEXT,
                expiry_date TIMESTAMP,
                is_permanent BOOLEAN DEFAULT 0,
                UNIQUE(user_id, payment_method)
            )
            ''')
            
            # Create payment_velocity table
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS payment_velocity (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                window_size INTEGER NOT NULL,
                payment_count INTEGER NOT NULL,
                total_amount REAL NOT NULL
            )
            ''')
            
            self.database.conn.commit()
            logger.info("Payment security tables created")
        except sqlite3.Error as e:
            logger.error(f"Error setting up payment security tables: {e}")
            raise
    
    def _load_payment_blacklist(self) -> None:
        """Load payment blacklist from database into memory."""
        try:
            cursor = self.database.conn.cursor()
            
            # Get active blacklist entries
            cursor.execute("""
                SELECT user_id, payment_method FROM payment_blacklist
                WHERE is_permanent = 1 OR expiry_date > ?
            """, (datetime.now().isoformat(),))
            
            blacklist = cursor.fetchall()
            
            # Add to in-memory cache
            self.payment_blacklist = set()
            for entry in blacklist:
                user_id = entry["user_id"]
                payment_method = entry["payment_method"]
                
                if payment_method:
                    self.payment_blacklist.add((user_id, payment_method))
                else:
                    # If payment_method is NULL, blacklist all methods for this user
                    self.payment_blacklist.add((user_id, None))
            
            logger.info(f"Loaded {len(self.payment_blacklist)} payment blacklist entries")
        except Exception as e:
            logger.error(f"Error loading payment blacklist: {e}")
    
    def is_blacklisted(self, user_id: int, payment_method: Optional[str] = None) -> bool:
        """
        Check if a user is blacklisted for payments.
        
        Args:
            user_id: User ID to check
            payment_method: Optional payment method to check
            
        Returns:
            bool: True if blacklisted, False otherwise
        """
        # Check for specific payment method blacklist
        if payment_method and (user_id, payment_method) in self.payment_blacklist:
            return True
        
        # Check for general user blacklist (all payment methods)
        if (user_id, None) in self.payment_blacklist:
            return True
        
        return False
    
    def add_to_blacklist(self, user_id: int, reason: str, 
                        payment_method: Optional[str] = None,
                        is_permanent: bool = False,
                        expiry_days: int = 30,
                        added_by: Optional[str] = None) -> bool:
        """
        Add a user to the payment blacklist.
        
        Args:
            user_id: User ID to blacklist
            reason: Reason for blacklisting
            payment_method: Optional specific payment method to blacklist
            is_permanent: Whether the blacklist is permanent
            expiry_days: Days until the blacklist expires (if not permanent)
            added_by: Who added the blacklist entry
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            cursor = self.database.conn.cursor()
            
            # Calculate expiry date
            expiry_date = None
            if not is_permanent:
                expiry_date = (datetime.now() + timedelta(days=expiry_days)).isoformat()
            
            # Insert or replace blacklist entry
            cursor.execute("""
                INSERT OR REPLACE INTO payment_blacklist
                (user_id, payment_method, reason, added_at, added_by, expiry_date, is_permanent)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                user_id, payment_method, reason, datetime.now().isoformat(),
                added_by, expiry_date, is_permanent
            ))
            
            self.database.conn.commit()
            
            # Update in-memory cache
            if payment_method:
                self.payment_blacklist.add((user_id, payment_method))
            else:
                self.payment_blacklist.add((user_id, None))
            
            # Log security event
            self.security_monitor.log_security_event(
                event_type="payment_blacklist_added",
                category=CATEGORY_PAYMENT,
                severity=SEVERITY_HIGH,
                user_id=user_id,
                description=f"User added to payment blacklist: {reason}",
                details={
                    "payment_method": payment_method,
                    "is_permanent": is_permanent,
                    "expiry_days": expiry_days if not is_permanent else None,
                    "added_by": added_by
                }
            )
            
            logger.info(f"Added user {user_id} to payment blacklist: {reason}")
            return True
        except Exception as e:
            logger.error(f"Error adding to payment blacklist: {e}")
            return False
    
    def remove_from_blacklist(self, user_id: int, payment_method: Optional[str] = None) -> bool:
        """
        Remove a user from the payment blacklist.
        
        Args:
            user_id: User ID to remove from blacklist
            payment_method: Optional specific payment method to remove
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            cursor = self.database.conn.cursor()
            
            if payment_method:
                # Remove specific payment method blacklist
                cursor.execute("""
                    DELETE FROM payment_blacklist
                    WHERE user_id = ? AND payment_method = ?
                """, (user_id, payment_method))
            else:
                # Remove all blacklist entries for this user
                cursor.execute("""
                    DELETE FROM payment_blacklist
                    WHERE user_id = ?
                """, (user_id,))
            
            self.database.conn.commit()
            
            # Update in-memory cache
            if payment_method:
                self.payment_blacklist.discard((user_id, payment_method))
            else:
                # Remove all entries for this user
                self.payment_blacklist = {
                    entry for entry in self.payment_blacklist
                    if entry[0] != user_id
                }
            
            # Log security event
            self.security_monitor.log_security_event(
                event_type="payment_blacklist_removed",
                category=CATEGORY_PAYMENT,
                severity=SEVERITY_MEDIUM,
                user_id=user_id,
                description=f"User removed from payment blacklist",
                details={"payment_method": payment_method}
            )
            
            logger.info(f"Removed user {user_id} from payment blacklist")
            return True
        except Exception as e:
            logger.error(f"Error removing from payment blacklist: {e}")
            return False
    
    def log_payment_attempt(self, user_id: int, payment_method: str,
                           payment_id: str, amount: float, credits: int,
                           is_successful: bool, details: Optional[Dict[str, Any]] = None) -> int:
        """
        Log a payment attempt for security monitoring.
        
        Args:
            user_id: User ID making the payment
            payment_method: Payment method used
            payment_id: Payment ID or transaction ID
            amount: Payment amount
            credits: Credits being purchased
            is_successful: Whether the payment was successful
            details: Additional payment details
            
        Returns:
            int: ID of the logged payment attempt
        """
        try:
            # Check if payment is suspicious
            is_suspicious, reason = self.check_payment_suspicious(
                user_id, payment_method, amount, credits
            )
            
            # Convert details to JSON
            details_json = json.dumps(details) if details else None
            
            # Determine event type
            event_type = "payment_success" if is_successful else "payment_failure"
            
            cursor = self.database.conn.cursor()
            
            # Insert payment security log
            cursor.execute("""
                INSERT INTO payment_security_logs
                (timestamp, user_id, event_type, payment_method, payment_id,
                 amount, credits, details, is_suspicious)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                datetime.now().isoformat(), user_id, event_type, payment_method,
                payment_id, amount, credits, details_json, is_suspicious
            ))
            
            self.database.conn.commit()
            log_id = cursor.lastrowid
            
            # Update payment velocity
            self._update_payment_velocity(user_id, amount)
            
            # Log security event if suspicious or failed
            if is_suspicious or not is_successful:
                severity = SEVERITY_HIGH if is_suspicious else SEVERITY_MEDIUM
                
                event_description = f"Suspicious payment detected: {reason}" if is_suspicious else "Payment failure"
                
                self.security_monitor.log_security_event(
                    event_type=event_type,
                    category=CATEGORY_PAYMENT,
                    severity=severity,
                    user_id=user_id,
                    description=event_description,
                    details={
                        "payment_method": payment_method,
                        "payment_id": payment_id,
                        "amount": amount,
                        "credits": credits,
                        "is_suspicious": is_suspicious,
                        "reason": reason if is_suspicious else None
                    }
                )
                
                # Create alert for suspicious payments
                if is_suspicious:
                    self.security_monitor.create_alert(
                        alert_type="suspicious_payment",
                        severity=SEVERITY_HIGH,
                        description=f"Suspicious payment detected for user {user_id}: {reason}",
                        details={
                            "user_id": user_id,
                            "payment_method": payment_method,
                            "payment_id": payment_id,
                            "amount": amount,
                            "credits": credits,
                            "reason": reason
                        }
                    )
            
            return log_id
        except Exception as e:
            logger.error(f"Error logging payment attempt: {e}")
            return -1
    
    def check_payment_suspicious(self, user_id: int, payment_method: str,
                               amount: float, credits: int) -> Tuple[bool, Optional[str]]:
        """
        Check if a payment attempt is suspicious.
        
        Args:
            user_id: User ID making the payment
            payment_method: Payment method used
            amount: Payment amount
            credits: Credits being purchased
            
        Returns:
            Tuple of (is_suspicious, reason)
        """
        try:
            # Check if user is blacklisted
            if self.is_blacklisted(user_id, payment_method):
                return True, "User is blacklisted for payments"
            
            # Check account age
            min_account_age_days = self.thresholds.get("min_account_age_days", 1)
            
            cursor = self.database.conn.cursor()
            cursor.execute("""
                SELECT created_at FROM users WHERE user_id = ?
            """, (user_id,))
            
            user_data = cursor.fetchone()
            if user_data:
                created_at = datetime.fromisoformat(user_data["created_at"])
                account_age = (datetime.now() - created_at).days
                
                if account_age < min_account_age_days:
                    return True, f"Account too new (age: {account_age} days)"
            
            # Check payment amount limits
            max_payment_amount = self.thresholds.get("max_payment_amount", 100)
            if amount > max_payment_amount:
                return True, f"Payment amount exceeds limit (${amount} > ${max_payment_amount})"
            
            max_credits_per_payment = self.thresholds.get("max_credits_per_payment", 1000)
            if credits > max_credits_per_payment:
                return True, f"Credits amount exceeds limit ({credits} > {max_credits_per_payment})"
            
            # Check daily payment limits
            today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0).isoformat()
            
            cursor.execute("""
                SELECT COUNT(*) as count, SUM(amount) as total_amount
                FROM payment_security_logs
                WHERE user_id = ? AND timestamp >= ? AND event_type = 'payment_success'
            """, (user_id, today_start))
            
            payment_stats = cursor.fetchone()
            
            if payment_stats:
                daily_count = payment_stats["count"] or 0
                daily_amount = payment_stats["total_amount"] or 0
                
                max_daily_payments = self.thresholds.get("max_daily_payments", 10)
                if daily_count >= max_daily_payments:
                    return True, f"Daily payment count exceeded ({daily_count} >= {max_daily_payments})"
                
                max_daily_amount = self.thresholds.get("max_daily_amount", 100)
                if daily_amount + amount > max_daily_amount:
                    return True, f"Daily payment amount exceeded (${daily_amount + amount} > ${max_daily_amount})"
            
            # Check payment velocity (payments per hour)
            one_hour_ago = (datetime.now() - timedelta(hours=1)).isoformat()
            
            cursor.execute("""
                SELECT COUNT(*) as count
                FROM payment_security_logs
                WHERE user_id = ? AND timestamp >= ? AND event_type = 'payment_success'
            """, (user_id, one_hour_ago))
            
            hourly_count = cursor.fetchone()["count"] or 0
            
            max_payment_velocity = self.thresholds.get("max_payment_velocity", 3)
            if hourly_count >= max_payment_velocity:
                return True, f"Payment velocity exceeded ({hourly_count} payments in last hour)"
            
            # Check payment method diversity
            cursor.execute("""
                SELECT COUNT(DISTINCT payment_method) as method_count
                FROM payment_security_logs
                WHERE user_id = ? AND timestamp >= ?
            """, (user_id, today_start))
            
            method_count = cursor.fetchone()["method_count"] or 0
            
            max_different_payment_methods = self.thresholds.get("max_different_payment_methods", 3)
            if method_count >= max_different_payment_methods:
                return True, f"Too many different payment methods used today ({method_count})"
            
            # Check for failed payment attempts
            cursor.execute("""
                SELECT COUNT(*) as count
                FROM payment_security_logs
                WHERE user_id = ? AND event_type = 'payment_failure'
                AND timestamp >= ?
            """, (user_id, (datetime.now() - timedelta(hours=24)).isoformat()))
            
            failure_count = cursor.fetchone()["count"] or 0
            
            max_payment_attempts = self.thresholds.get("max_payment_attempts", 5)
            if failure_count >= max_payment_attempts:
                return True, f"Too many failed payment attempts ({failure_count} in last 24h)"
            
            # Check time between payments
            cursor.execute("""
                SELECT timestamp
                FROM payment_security_logs
                WHERE user_id = ? AND event_type = 'payment_success'
                ORDER BY timestamp DESC
                LIMIT 1
            """, (user_id,))
            
            last_payment = cursor.fetchone()
            
            if last_payment:
                last_payment_time = datetime.fromisoformat(last_payment["timestamp"])
                minutes_since_last = (datetime.now() - last_payment_time).total_seconds() / 60
                
                suspicious_time_window = self.thresholds.get("suspicious_time_window", 5)
                if minutes_since_last < suspicious_time_window:
                    return True, f"Payments too close together ({minutes_since_last:.1f} minutes apart)"
            
            # No suspicious indicators found
            return False, None
        except Exception as e:
            logger.error(f"Error checking payment suspicion: {e}")
            return False, None
    
    def _update_payment_velocity(self, user_id: int, amount: float) -> None:
        """
        Update payment velocity metrics for a user.
        
        Args:
            user_id: User ID making the payment
            amount: Payment amount
        """
        try:
            cursor = self.database.conn.cursor()
            
            # Update velocity for different time windows
            for window_hours in [1, 6, 24]:
                window_start = (datetime.now() - timedelta(hours=window_hours)).isoformat()
                
                # Get payment count and total amount for this window
                cursor.execute("""
                    SELECT COUNT(*) as count, SUM(amount) as total_amount
                    FROM payment_security_logs
                    WHERE user_id = ? AND timestamp >= ? AND event_type = 'payment_success'
                """, (user_id, window_start))
                
                stats = cursor.fetchone()
                
                if stats:
                    payment_count = stats["count"] or 0
                    total_amount = stats["total_amount"] or 0
                    
                    # Insert or update velocity record
                    cursor.execute("""
                        INSERT OR REPLACE INTO payment_velocity
                        (user_id, timestamp, window_size, payment_count, total_amount)
                        VALUES (?, ?, ?, ?, ?)
                    """, (
                        user_id, datetime.now().isoformat(), window_hours,
                        payment_count, total_amount
                    ))
            
            self.database.conn.commit()
        except Exception as e:
            logger.error(f"Error updating payment velocity: {e}")
    
    def get_payment_security_stats(self, days: int = 7) -> Dict[str, Any]:
        """
        Get payment security statistics.
        
        Args:
            days: Number of days to include in statistics
            
        Returns:
            Dictionary of payment security statistics
        """
        try:
            cursor = self.database.conn.cursor()
            
            start_date = (datetime.now() - timedelta(days=days)).isoformat()
            
            # Get overall payment stats
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_payments,
                    SUM(CASE WHEN event_type = 'payment_success' THEN 1 ELSE 0 END) as successful_payments,
                    SUM(CASE WHEN event_type = 'payment_failure' THEN 1 ELSE 0 END) as failed_payments,
                    SUM(CASE WHEN is_suspicious = 1 THEN 1 ELSE 0 END) as suspicious_payments,
                    SUM(CASE WHEN event_type = 'payment_success' THEN amount ELSE 0 END) as total_amount,
                    COUNT(DISTINCT user_id) as unique_users
                FROM payment_security_logs
                WHERE timestamp >= ?
            """, (start_date,))
            
            stats = cursor.fetchone()
            
            # Get payment method breakdown
            cursor.execute("""
                SELECT 
                    payment_method,
                    COUNT(*) as count,
                    SUM(CASE WHEN event_type = 'payment_success' THEN 1 ELSE 0 END) as successful,
                    SUM(CASE WHEN event_type = 'payment_failure' THEN 1 ELSE 0 END) as failed
                FROM payment_security_logs
                WHERE timestamp >= ?
                GROUP BY payment_method
                ORDER BY count DESC
            """, (start_date,))
            
            payment_methods = cursor.fetchall()
            
            # Get daily payment counts
            cursor.execute("""
                SELECT 
                    strftime('%Y-%m-%d', timestamp) as day,
                    COUNT(*) as count,
                    SUM(CASE WHEN event_type = 'payment_success' THEN 1 ELSE 0 END) as successful,
                    SUM(CASE WHEN event_type = 'payment_failure' THEN 1 ELSE 0 END) as failed,
                    SUM(CASE WHEN is_suspicious = 1 THEN 1 ELSE 0 END) as suspicious
                FROM payment_security_logs
                WHERE timestamp >= ?
                GROUP BY day
                ORDER BY day
            """, (start_date,))
            
            daily_counts = cursor.fetchall()
            
            # Get blacklist count
            cursor.execute("""
                SELECT COUNT(*) as count
                FROM payment_blacklist
                WHERE is_permanent = 1 OR expiry_date > ?
            """, (datetime.now().isoformat(),))
            
            blacklist_count = cursor.fetchone()["count"]
            
            # Compile statistics
            return {
                "total_payments": stats["total_payments"] or 0,
                "successful_payments": stats["successful_payments"] or 0,
                "failed_payments": stats["failed_payments"] or 0,
                "suspicious_payments": stats["suspicious_payments"] or 0,
                "total_amount": stats["total_amount"] or 0,
                "unique_users": stats["unique_users"] or 0,
                "payment_methods": [dict(method) for method in payment_methods],
                "daily_counts": [dict(day) for day in daily_counts],
                "blacklist_count": blacklist_count,
                "days": days
            }
        except Exception as e:
            logger.error(f"Error getting payment security stats: {e}")
            return {}
