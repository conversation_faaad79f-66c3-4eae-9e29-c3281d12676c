"""
Feature flags system for VoicePal.

This module provides feature flag functionality for gradual rollouts and A/B testing.
"""

import logging
import json
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum

logger = logging.getLogger(__name__)

class FeatureFlagType(Enum):
    """Feature flag types."""
    BOOLEAN = "boolean"
    STRING = "string"
    NUMBER = "number"
    JSON = "json"

class RolloutStrategy(Enum):
    """Rollout strategies."""
    ALL_USERS = "all_users"
    PERCENTAGE = "percentage"
    USER_LIST = "user_list"
    USER_ATTRIBUTE = "user_attribute"
    GRADUAL = "gradual"

@dataclass
class FeatureFlag:
    """Feature flag definition."""
    flag_id: str
    name: str
    description: str
    flag_type: FeatureFlagType
    default_value: Any
    enabled: bool
    rollout_strategy: RolloutStrategy
    rollout_config: Dict[str, Any]
    created_at: datetime
    updated_at: datetime
    created_by: str

@dataclass
class UserContext:
    """User context for feature flag evaluation."""
    user_id: int
    attributes: Dict[str, Any]
    groups: List[str]

class FeatureFlagManager:
    """Feature flag management system."""
    
    def __init__(self, database, cache_manager=None, redis_client=None):
        """
        Initialize feature flag manager.
        
        Args:
            database: Database instance
            cache_manager: Cache manager for performance
            redis_client: Redis client for real-time updates
        """
        self.database = database
        self.cache_manager = cache_manager
        self.redis_client = redis_client
        
        # Initialize tables
        self._initialize_tables()
        
        logger.info("Feature flag manager initialized")
    
    def _initialize_tables(self):
        """Initialize feature flag tables."""
        try:
            # Feature flags table
            self.database.execute("""
                CREATE TABLE IF NOT EXISTS feature_flags (
                    flag_id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    description TEXT,
                    flag_type TEXT NOT NULL,
                    default_value TEXT NOT NULL,
                    enabled BOOLEAN DEFAULT 1,
                    rollout_strategy TEXT NOT NULL,
                    rollout_config TEXT,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    created_by TEXT
                )
            """)
            
            # Feature flag evaluations (for analytics)
            self.database.execute("""
                CREATE TABLE IF NOT EXISTS feature_flag_evaluations (
                    evaluation_id TEXT PRIMARY KEY,
                    flag_id TEXT NOT NULL,
                    user_id INTEGER,
                    value TEXT,
                    timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
                    user_context TEXT
                )
            """)
            
            # Create indexes
            self.database.execute("CREATE INDEX IF NOT EXISTS idx_evaluations_flag_user ON feature_flag_evaluations(flag_id, user_id)")
            self.database.execute("CREATE INDEX IF NOT EXISTS idx_evaluations_timestamp ON feature_flag_evaluations(timestamp)")
            
            self.database.commit()
            
        except Exception as e:
            logger.error(f"Failed to initialize feature flag tables: {e}")
    
    def create_flag(
        self,
        name: str,
        description: str,
        flag_type: FeatureFlagType,
        default_value: Any,
        rollout_strategy: RolloutStrategy = RolloutStrategy.ALL_USERS,
        rollout_config: Optional[Dict[str, Any]] = None,
        created_by: str = "system"
    ) -> str:
        """
        Create a new feature flag.
        
        Args:
            name: Flag name
            description: Flag description
            flag_type: Type of flag value
            default_value: Default value
            rollout_strategy: Rollout strategy
            rollout_config: Rollout configuration
            created_by: Creator identifier
            
        Returns:
            Flag ID
        """
        try:
            flag_id = f"flag_{name.lower().replace(' ', '_')}_{int(datetime.utcnow().timestamp())}"
            
            # Serialize default value
            serialized_value = self._serialize_value(default_value, flag_type)
            
            flag = FeatureFlag(
                flag_id=flag_id,
                name=name,
                description=description,
                flag_type=flag_type,
                default_value=default_value,
                enabled=True,
                rollout_strategy=rollout_strategy,
                rollout_config=rollout_config or {},
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow(),
                created_by=created_by
            )
            
            # Store in database
            self.database.execute("""
                INSERT INTO feature_flags (
                    flag_id, name, description, flag_type, default_value,
                    enabled, rollout_strategy, rollout_config, created_by
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                flag.flag_id,
                flag.name,
                flag.description,
                flag.flag_type.value,
                serialized_value,
                flag.enabled,
                flag.rollout_strategy.value,
                json.dumps(flag.rollout_config),
                flag.created_by
            ))
            
            self.database.commit()
            
            # Clear cache
            if self.cache_manager:
                self.cache_manager.delete(f"feature_flag_{flag_id}")
            
            logger.info(f"Created feature flag: {flag_id}")
            return flag_id
            
        except Exception as e:
            logger.error(f"Failed to create feature flag: {e}")
            raise
    
    def update_flag(
        self,
        flag_id: str,
        enabled: Optional[bool] = None,
        default_value: Optional[Any] = None,
        rollout_strategy: Optional[RolloutStrategy] = None,
        rollout_config: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Update feature flag.
        
        Args:
            flag_id: Flag ID
            enabled: Whether flag is enabled
            default_value: New default value
            rollout_strategy: New rollout strategy
            rollout_config: New rollout configuration
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Get current flag
            flag = self.get_flag(flag_id)
            if not flag:
                return False
            
            # Build update query
            updates = []
            values = []
            
            if enabled is not None:
                updates.append("enabled = ?")
                values.append(enabled)
            
            if default_value is not None:
                serialized_value = self._serialize_value(default_value, flag.flag_type)
                updates.append("default_value = ?")
                values.append(serialized_value)
            
            if rollout_strategy is not None:
                updates.append("rollout_strategy = ?")
                values.append(rollout_strategy.value)
            
            if rollout_config is not None:
                updates.append("rollout_config = ?")
                values.append(json.dumps(rollout_config))
            
            if updates:
                updates.append("updated_at = ?")
                values.append(datetime.utcnow().isoformat())
                values.append(flag_id)
                
                self.database.execute(f"""
                    UPDATE feature_flags 
                    SET {', '.join(updates)}
                    WHERE flag_id = ?
                """, values)
                
                self.database.commit()
                
                # Clear cache
                if self.cache_manager:
                    self.cache_manager.delete(f"feature_flag_{flag_id}")
                
                # Notify via Redis
                if self.redis_client:
                    try:
                        self.redis_client.publish("feature_flag_updates", json.dumps({
                            "flag_id": flag_id,
                            "action": "updated",
                            "timestamp": datetime.utcnow().isoformat()
                        }))
                    except Exception as e:
                        logger.warning(f"Failed to publish flag update: {e}")
                
                logger.info(f"Updated feature flag: {flag_id}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to update feature flag {flag_id}: {e}")
            return False
    
    def evaluate_flag(
        self,
        flag_id: str,
        user_context: Optional[UserContext] = None,
        default_value: Optional[Any] = None
    ) -> Any:
        """
        Evaluate feature flag for a user.
        
        Args:
            flag_id: Flag ID
            user_context: User context for evaluation
            default_value: Fallback default value
            
        Returns:
            Flag value
        """
        try:
            # Get flag
            flag = self.get_flag(flag_id)
            if not flag:
                return default_value
            
            # Check if flag is enabled
            if not flag.enabled:
                return self._deserialize_value(flag.default_value, flag.flag_type)
            
            # Evaluate based on rollout strategy
            value = self._evaluate_rollout(flag, user_context)
            
            # Log evaluation for analytics
            if user_context:
                self._log_evaluation(flag_id, user_context.user_id, value, user_context)
            
            return value
            
        except Exception as e:
            logger.error(f"Failed to evaluate flag {flag_id}: {e}")
            return default_value
    
    def _evaluate_rollout(self, flag: FeatureFlag, user_context: Optional[UserContext]) -> Any:
        """Evaluate rollout strategy."""
        try:
            default_value = self._deserialize_value(flag.default_value, flag.flag_type)
            
            if flag.rollout_strategy == RolloutStrategy.ALL_USERS:
                return default_value
            
            if not user_context:
                return default_value
            
            if flag.rollout_strategy == RolloutStrategy.PERCENTAGE:
                percentage = flag.rollout_config.get("percentage", 0)
                # Use user ID for consistent hashing
                hash_value = hash(f"{flag.flag_id}_{user_context.user_id}") % 100
                if hash_value < percentage:
                    return default_value
                else:
                    return self._get_fallback_value(flag)
            
            elif flag.rollout_strategy == RolloutStrategy.USER_LIST:
                user_list = flag.rollout_config.get("user_ids", [])
                if user_context.user_id in user_list:
                    return default_value
                else:
                    return self._get_fallback_value(flag)
            
            elif flag.rollout_strategy == RolloutStrategy.USER_ATTRIBUTE:
                attribute_name = flag.rollout_config.get("attribute_name")
                attribute_value = flag.rollout_config.get("attribute_value")
                
                if (attribute_name and 
                    user_context.attributes.get(attribute_name) == attribute_value):
                    return default_value
                else:
                    return self._get_fallback_value(flag)
            
            elif flag.rollout_strategy == RolloutStrategy.GRADUAL:
                # Gradual rollout based on time
                start_time = datetime.fromisoformat(flag.rollout_config.get("start_time", flag.created_at.isoformat()))
                duration_hours = flag.rollout_config.get("duration_hours", 24)
                
                elapsed_hours = (datetime.utcnow() - start_time).total_seconds() / 3600
                rollout_percentage = min(100, (elapsed_hours / duration_hours) * 100)
                
                hash_value = hash(f"{flag.flag_id}_{user_context.user_id}") % 100
                if hash_value < rollout_percentage:
                    return default_value
                else:
                    return self._get_fallback_value(flag)
            
            return default_value
            
        except Exception as e:
            logger.error(f"Failed to evaluate rollout: {e}")
            return self._deserialize_value(flag.default_value, flag.flag_type)
    
    def _get_fallback_value(self, flag: FeatureFlag) -> Any:
        """Get fallback value for flag."""
        fallback = flag.rollout_config.get("fallback_value")
        if fallback is not None:
            return self._deserialize_value(fallback, flag.flag_type)
        
        # Return opposite for boolean flags
        if flag.flag_type == FeatureFlagType.BOOLEAN:
            default_bool = self._deserialize_value(flag.default_value, flag.flag_type)
            return not default_bool
        
        return self._deserialize_value(flag.default_value, flag.flag_type)
    
    def _serialize_value(self, value: Any, flag_type: FeatureFlagType) -> str:
        """Serialize value for storage."""
        if flag_type == FeatureFlagType.JSON:
            return json.dumps(value)
        else:
            return str(value)
    
    def _deserialize_value(self, value: str, flag_type: FeatureFlagType) -> Any:
        """Deserialize value from storage."""
        try:
            if flag_type == FeatureFlagType.BOOLEAN:
                return value.lower() in ('true', '1', 'yes', 'on')
            elif flag_type == FeatureFlagType.NUMBER:
                try:
                    return int(value)
                except ValueError:
                    return float(value)
            elif flag_type == FeatureFlagType.JSON:
                return json.loads(value)
            else:  # STRING
                return value
        except Exception as e:
            logger.error(f"Failed to deserialize value: {e}")
            return value
    
    def _log_evaluation(
        self,
        flag_id: str,
        user_id: int,
        value: Any,
        user_context: UserContext
    ):
        """Log flag evaluation for analytics."""
        try:
            evaluation_id = f"eval_{flag_id}_{user_id}_{int(datetime.utcnow().timestamp() * 1000)}"
            
            self.database.execute("""
                INSERT INTO feature_flag_evaluations (
                    evaluation_id, flag_id, user_id, value, user_context
                ) VALUES (?, ?, ?, ?, ?)
            """, (
                evaluation_id,
                flag_id,
                user_id,
                json.dumps(value),
                json.dumps(asdict(user_context))
            ))
            
            self.database.commit()
            
        except Exception as e:
            logger.error(f"Failed to log evaluation: {e}")
    
    def get_flag(self, flag_id: str) -> Optional[FeatureFlag]:
        """
        Get feature flag by ID.
        
        Args:
            flag_id: Flag ID
            
        Returns:
            FeatureFlag object or None
        """
        try:
            # Check cache first
            if self.cache_manager:
                cached_flag = self.cache_manager.get(f"feature_flag_{flag_id}")
                if cached_flag:
                    return FeatureFlag(**cached_flag)
            
            # Get from database
            flag_data = self.database.execute("""
                SELECT * FROM feature_flags WHERE flag_id = ?
            """, (flag_id,)).fetchone()
            
            if not flag_data:
                return None
            
            flag = FeatureFlag(
                flag_id=flag_data['flag_id'],
                name=flag_data['name'],
                description=flag_data['description'],
                flag_type=FeatureFlagType(flag_data['flag_type']),
                default_value=flag_data['default_value'],
                enabled=bool(flag_data['enabled']),
                rollout_strategy=RolloutStrategy(flag_data['rollout_strategy']),
                rollout_config=json.loads(flag_data['rollout_config']) if flag_data['rollout_config'] else {},
                created_at=datetime.fromisoformat(flag_data['created_at']),
                updated_at=datetime.fromisoformat(flag_data['updated_at']),
                created_by=flag_data['created_by']
            )
            
            # Cache the result
            if self.cache_manager:
                self.cache_manager.set(f"feature_flag_{flag_id}", asdict(flag), ttl=3600)
            
            return flag
            
        except Exception as e:
            logger.error(f"Failed to get flag {flag_id}: {e}")
            return None
    
    def list_flags(self, enabled_only: bool = False) -> List[FeatureFlag]:
        """
        List all feature flags.
        
        Args:
            enabled_only: Whether to return only enabled flags
            
        Returns:
            List of feature flags
        """
        try:
            if enabled_only:
                flags_data = self.database.execute("""
                    SELECT flag_id FROM feature_flags WHERE enabled = 1
                    ORDER BY created_at DESC
                """).fetchall()
            else:
                flags_data = self.database.execute("""
                    SELECT flag_id FROM feature_flags
                    ORDER BY created_at DESC
                """).fetchall()
            
            flags = []
            for flag_data in flags_data:
                flag = self.get_flag(flag_data['flag_id'])
                if flag:
                    flags.append(flag)
            
            return flags
            
        except Exception as e:
            logger.error(f"Failed to list flags: {e}")
            return []
    
    def delete_flag(self, flag_id: str) -> bool:
        """
        Delete feature flag.
        
        Args:
            flag_id: Flag ID
            
        Returns:
            True if successful, False otherwise
        """
        try:
            self.database.execute("DELETE FROM feature_flags WHERE flag_id = ?", (flag_id,))
            self.database.execute("DELETE FROM feature_flag_evaluations WHERE flag_id = ?", (flag_id,))
            self.database.commit()
            
            # Clear cache
            if self.cache_manager:
                self.cache_manager.delete(f"feature_flag_{flag_id}")
            
            logger.info(f"Deleted feature flag: {flag_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete flag {flag_id}: {e}")
            return False
    
    def get_flag_analytics(self, flag_id: str, days: int = 7) -> Dict[str, Any]:
        """
        Get analytics for a feature flag.
        
        Args:
            flag_id: Flag ID
            days: Number of days to analyze
            
        Returns:
            Analytics data
        """
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days)
            
            # Get evaluation counts
            evaluations = self.database.execute("""
                SELECT value, COUNT(*) as count, COUNT(DISTINCT user_id) as unique_users
                FROM feature_flag_evaluations
                WHERE flag_id = ? AND timestamp >= ?
                GROUP BY value
            """, (flag_id, cutoff_date.isoformat())).fetchall()
            
            # Get daily trends
            daily_trends = self.database.execute("""
                SELECT DATE(timestamp) as date, COUNT(*) as evaluations
                FROM feature_flag_evaluations
                WHERE flag_id = ? AND timestamp >= ?
                GROUP BY DATE(timestamp)
                ORDER BY date
            """, (flag_id, cutoff_date.isoformat())).fetchall()
            
            return {
                "flag_id": flag_id,
                "period_days": days,
                "evaluations": [dict(eval) for eval in evaluations],
                "daily_trends": [dict(trend) for trend in daily_trends],
                "total_evaluations": sum(eval['count'] for eval in evaluations),
                "unique_users": sum(eval['unique_users'] for eval in evaluations)
            }
            
        except Exception as e:
            logger.error(f"Failed to get flag analytics: {e}")
            return {}
    
    # Convenience methods for common flag types
    
    def is_feature_enabled(self, flag_name: str, user_context: Optional[UserContext] = None) -> bool:
        """Check if a boolean feature flag is enabled."""
        return bool(self.evaluate_flag(flag_name, user_context, False))
    
    def get_feature_value(self, flag_name: str, user_context: Optional[UserContext] = None, default: Any = None) -> Any:
        """Get feature flag value with default."""
        return self.evaluate_flag(flag_name, user_context, default)
