"""
Core database components for VoicePal.

This package provides the core database functionality for VoicePal.
"""

from bot.database.core.exceptions import (
    DatabaseError,
    DatabaseConnectionError,
    DatabaseQueryError,
    DatabaseIntegrityError,
    DatabaseMigrationError,
    DatabaseSchemaError,
    DatabaseTransactionError,
    DatabaseTimeoutError,
    DatabaseNotFoundError,
    DatabaseDuplicateError,
    InsufficientCreditsError
)
from bot.database.core.connection import DatabaseConnection
from bot.database.core.utils import (
    dict_to_sql_params,
    build_insert_query,
    build_update_query,
    build_select_query,
    row_to_dict,
    rows_to_list
)
from bot.database.core.schema import (
    SCHEMA_DEFINITIONS,
    SCHEMA_INDEXES,
    SchemaManager
)
from bot.database.core.migrations import (
    Migration,
    MigrationManager
)

__all__ = [
    'DatabaseError',
    'DatabaseConnectionError',
    'DatabaseQueryError',
    'DatabaseIntegrityError',
    'DatabaseMigrationError',
    'DatabaseSchemaError',
    'DatabaseTransactionError',
    'DatabaseTimeoutError',
    'DatabaseNotFoundError',
    'DatabaseDuplicateError',
    'InsufficientCreditsError',
    'DatabaseConnection',
    'dict_to_sql_params',
    'build_insert_query',
    'build_update_query',
    'build_select_query',
    'row_to_dict',
    'rows_to_list',
    'SCHEMA_DEFINITIONS',
    'SCHEMA_INDEXES',
    'SchemaManager',
    'Migration',
    'MigrationManager'
]
