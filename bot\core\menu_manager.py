"""
Simplified Menu Manager for VoicePal.

This module provides a centralized menu management system with consistent
keyboard layouts and menu button logic.
"""

import logging
from typing import Dict, Any, Optional, List
from telegram import InlineKeyboardButton, InlineKeyboardMarkup, MenuButtonCommands

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class MenuManager:
    """Centralized menu manager for consistent UI across the bot."""

    def __init__(self, database, config: Dict[str, Any] = None):
        """
        Initialize the menu manager.

        Args:
            database: Database instance
            config: Configuration dictionary
        """
        self.database = database
        self.config = config or {}

        # Menu configuration
        self.show_persistent_menu = self.config.get("show_persistent_menu", True)
        self.menu_frequency = self.config.get("menu_frequency", 1)  # Show menu every N messages

        logger.info("Menu manager initialized")

    def get_main_menu_keyboard(self) -> InlineKeyboardMarkup:
        """
        Get the main menu keyboard.

        Returns:
            InlineKeyboardMarkup with main menu options
        """
        keyboard = [
            [
                InlineKeyboardButton("💬 Start Chat", callback_data="start_conversation")
            ],
            [
                InlineKeyboardButton("👤 My Profile", callback_data="show_profile"),
                InlineKeyboardButton("🎤 Voice Settings", callback_data="show_voice_settings")
            ],
            [
                InlineKeyboardButton("💰 Credits & Payments", callback_data="buy_credits"),
                InlineKeyboardButton("📊 Mood Diary", callback_data="show_mood_diary")
            ],
            [
                InlineKeyboardButton("⚙️ Settings", callback_data="show_settings"),
                InlineKeyboardButton("❓ Help", callback_data="show_help")
            ]
        ]
        return InlineKeyboardMarkup(keyboard)

    def get_persistent_menu_button(self) -> InlineKeyboardMarkup:
        """
        Get a persistent menu button for navigation.

        Returns:
            InlineKeyboardMarkup with a single menu button
        """
        keyboard = [
            [InlineKeyboardButton("📋 Menu", callback_data="back_to_main")]
        ]
        return InlineKeyboardMarkup(keyboard)

    def get_profile_keyboard(self, user_id: int) -> InlineKeyboardMarkup:
        """
        Get profile keyboard.

        Args:
            user_id: User ID

        Returns:
            InlineKeyboardMarkup with profile options
        """
        try:
            user_credits = self.database.get_user_credits(user_id)
        except Exception as e:
            logger.error(f"Error getting user credits: {e}")
            user_credits = 0

        keyboard = [
            [
                InlineKeyboardButton(f"💰 Credits: {user_credits}", callback_data="show_credits")
            ],
            [
                InlineKeyboardButton("💳 Buy Credits", callback_data="buy_credits"),
                InlineKeyboardButton("🎁 Free Credits", callback_data="free_credits")
            ],
            [
                InlineKeyboardButton("👤 Change Personality", callback_data="show_personality"),
                InlineKeyboardButton("🎤 Voice Settings", callback_data="show_voice_settings")
            ],
            [
                InlineKeyboardButton("🔙 Back to Menu", callback_data="back_to_main")
            ]
        ]
        return InlineKeyboardMarkup(keyboard)

    def get_credits_keyboard(self) -> InlineKeyboardMarkup:
        """
        Get credits keyboard.

        Returns:
            InlineKeyboardMarkup with credit options
        """
        keyboard = [
            [
                InlineKeyboardButton("💳 Buy More Credits", callback_data="buy_credits")
            ],
            [
                InlineKeyboardButton("🎁 Get Free Credits", callback_data="free_credits")
            ],
            [
                InlineKeyboardButton("📊 Usage History", callback_data="show_usage_history")
            ],
            [
                InlineKeyboardButton("🔙 Back to Profile", callback_data="show_profile")
            ]
        ]
        return InlineKeyboardMarkup(keyboard)

    def get_settings_keyboard(self) -> InlineKeyboardMarkup:
        """
        Get settings keyboard.

        Returns:
            InlineKeyboardMarkup with settings options
        """
        keyboard = [
            [
                InlineKeyboardButton("👤 Personality", callback_data="show_personality"),
                InlineKeyboardButton("🌐 Language", callback_data="show_language_settings")
            ],
            [
                InlineKeyboardButton("🔐 Privacy & Data", callback_data="show_privacy_settings"),
                InlineKeyboardButton("🔔 Notifications", callback_data="show_notification_settings")
            ],
            [
                InlineKeyboardButton("🗑️ Clear My Data", callback_data="show_clear_data")
            ],
            [
                InlineKeyboardButton("🔙 Back to Menu", callback_data="back_to_main")
            ]
        ]
        return InlineKeyboardMarkup(keyboard)

    def get_voice_settings_keyboard(self) -> InlineKeyboardMarkup:
        """
        Get voice settings keyboard.

        Returns:
            InlineKeyboardMarkup with voice settings options
        """
        # Use the new create_menu_keyboard method for consistent styling
        buttons = [
            [
                {"text": "🎙️ Deepgram Voices", "callback_data": "show_deepgram_voices"}
            ],
            [
                {"text": "⚡ Speech Speed", "callback_data": "show_speech_speed"},
                {"text": "🎵 Voice Tone", "callback_data": "show_voice_tone"}
            ]
        ]

        back_button = {"text": "🔙 Back to Settings", "callback_data": "show_settings"}

        return self.create_menu_keyboard(buttons, back_button)

    def get_info_keyboard(self) -> InlineKeyboardMarkup:
        """
        Get info keyboard.

        Returns:
            InlineKeyboardMarkup with info options
        """
        keyboard = [
            [
                InlineKeyboardButton("📖 About VoicePal", callback_data="show_about"),
                InlineKeyboardButton("🆕 What's New", callback_data="show_whats_new")
            ],
            [
                InlineKeyboardButton("📜 Terms of Service", callback_data="show_terms"),
                InlineKeyboardButton("🔒 Privacy Policy", callback_data="show_privacy")
            ],
            [
                InlineKeyboardButton("🆘 Support", callback_data="show_support"),
                InlineKeyboardButton("📝 Feedback", callback_data="show_feedback")
            ],
            [
                InlineKeyboardButton("🔙 Back to Menu", callback_data="back_to_main")
            ]
        ]
        return InlineKeyboardMarkup(keyboard)

    def get_help_keyboard(self) -> InlineKeyboardMarkup:
        """
        Get help keyboard.

        Returns:
            InlineKeyboardMarkup with help options
        """
        keyboard = [
            [
                InlineKeyboardButton("🚀 Getting Started", callback_data="help_getting_started"),
                InlineKeyboardButton("💬 How to Chat", callback_data="help_chat")
            ],
            [
                InlineKeyboardButton("💰 Credits & Payments", callback_data="help_credits"),
                InlineKeyboardButton("🎤 Voice Features", callback_data="help_voice")
            ],
            [
                InlineKeyboardButton("⚙️ Settings Guide", callback_data="help_settings"),
                InlineKeyboardButton("🐛 Troubleshooting", callback_data="help_troubleshooting")
            ],
            [
                InlineKeyboardButton("🔙 Back to Menu", callback_data="back_to_main")
            ]
        ]
        return InlineKeyboardMarkup(keyboard)

    def get_mood_diary_keyboard(self) -> InlineKeyboardMarkup:
        """
        Get mood diary keyboard.

        Returns:
            InlineKeyboardMarkup with mood diary options
        """
        keyboard = [
            [
                InlineKeyboardButton("📝 Add Mood Entry", callback_data="add_mood_entry"),
                InlineKeyboardButton("📊 View Mood History", callback_data="view_mood_history")
            ],
            [
                InlineKeyboardButton("📈 Mood Trends", callback_data="mood_trends"),
                InlineKeyboardButton("💡 Mood Insights", callback_data="mood_insights")
            ],
            [
                InlineKeyboardButton("🔙 Back to Menu", callback_data="back_to_main")
            ]
        ]
        return InlineKeyboardMarkup(keyboard)

    def should_show_menu(self, message_count: int, is_first_message: bool = False) -> bool:
        """
        Determine if menu should be shown based on configuration.

        Args:
            message_count: Current message count for the user
            is_first_message: Whether this is the first message

        Returns:
            bool: True if menu should be shown
        """
        # Always show menu on first message
        if is_first_message:
            return True

        # Show menu if persistent menu is enabled
        if self.show_persistent_menu:
            return True

        # Show menu based on frequency setting
        if self.menu_frequency > 0 and message_count % self.menu_frequency == 0:
            return True

        return False

    def get_menu_button_config(self) -> MenuButtonCommands:
        """
        Get menu button configuration for Telegram.

        Returns:
            MenuButtonCommands configuration
        """
        return MenuButtonCommands()

    def get_back_button(self, callback_data: str = "back_to_main", text: str = "🔙 Back") -> InlineKeyboardMarkup:
        """
        Get a simple back button.

        Args:
            callback_data: Callback data for the back button
            text: Text to display on the button

        Returns:
            InlineKeyboardMarkup with back button
        """
        keyboard = [
            [InlineKeyboardButton(text, callback_data=callback_data)]
        ]
        return InlineKeyboardMarkup(keyboard)

    def create_button(self, text: str, callback_data: str) -> InlineKeyboardButton:
        """
        Create a standard button with consistent styling.

        Args:
            text: Text to display on the button
            callback_data: Callback data for the button

        Returns:
            InlineKeyboardButton with standard styling
        """
        return InlineKeyboardButton(text, callback_data=callback_data)

    def create_menu_keyboard(self, buttons: List[List[Dict[str, str]]],
                            back_button: Optional[Dict[str, str]] = None) -> InlineKeyboardMarkup:
        """
        Create a menu keyboard with consistent styling.

        Args:
            buttons: List of button rows, each containing a list of button dictionaries with 'text' and 'callback_data'
            back_button: Optional back button dictionary with 'text' and 'callback_data'

        Returns:
            InlineKeyboardMarkup with standard styling
        """
        keyboard = []

        # Add menu buttons
        for row in buttons:
            keyboard_row = []
            for button in row:
                keyboard_row.append(self.create_button(button['text'], button['callback_data']))
            keyboard.append(keyboard_row)

        # Add back button if provided
        if back_button:
            keyboard.append([self.create_button(back_button['text'], back_button['callback_data'])])

        return InlineKeyboardMarkup(keyboard)

    def get_personality_keyboard(self) -> InlineKeyboardMarkup:
        """
        Get personality selection keyboard.

        Returns:
            InlineKeyboardMarkup with personality options
        """
        keyboard = [
            [InlineKeyboardButton("😊 Friendly", callback_data="set_personality_friendly")],
            [InlineKeyboardButton("😄 Witty", callback_data="set_personality_witty")],
            [InlineKeyboardButton("😌 Calm", callback_data="set_personality_calm")],
            [InlineKeyboardButton("💪 Motivational", callback_data="set_personality_motivational")],
            [InlineKeyboardButton("🤔 Thoughtful", callback_data="set_personality_thoughtful")],
            [InlineKeyboardButton("🔙 Back to Menu", callback_data="back_to_main")]
        ]
        return InlineKeyboardMarkup(keyboard)
