"""
Provider factory for VoicePal.

This module provides a factory for creating provider instances.
"""

import logging
import importlib
from typing import Dict, Any, Optional, Type, TypeVar, Generic, Union, List, Callable

from bot.providers.core.provider import Provider
from bot.providers.core.exceptions import (
    ProviderFactoryError,
    ProviderNotFoundError,
    ProviderConfigError,
    ProviderInitializationError
)

# Set up logging
logger = logging.getLogger(__name__)

# Type variable for provider configuration
T = TypeVar('T')
P = TypeVar('P', bound=Provider)

class ProviderFactory(Generic[P, T]):
    """Factory for creating provider instances.
    
    This class provides methods for creating and managing provider instances.
    """
    
    def __init__(self, provider_type: str, default_provider: Optional[str] = None):
        """Initialize provider factory.
        
        Args:
            provider_type: Provider type
            default_provider: Default provider name
        """
        self.provider_type = provider_type
        self.default_provider = default_provider
        self.providers: Dict[str, Type[P]] = {}
        self.instances: Dict[str, P] = {}
    
    def register_provider(self, name: str, provider_class: Type[P]) -> None:
        """Register provider class.
        
        Args:
            name: Provider name
            provider_class: Provider class
            
        Raises:
            ProviderFactoryError: If provider is already registered
        """
        if name in self.providers:
            raise ProviderFactoryError(f"Provider '{name}' is already registered")
        
        self.providers[name] = provider_class
        logger.debug(f"Registered provider '{name}' of type '{self.provider_type}'")
    
    def register_provider_from_module(self, name: str, module_path: str, class_name: str) -> None:
        """Register provider class from module.
        
        Args:
            name: Provider name
            module_path: Module path
            class_name: Provider class name
            
        Raises:
            ProviderFactoryError: If provider is already registered or module/class not found
        """
        try:
            module = importlib.import_module(module_path)
            provider_class = getattr(module, class_name)
            self.register_provider(name, provider_class)
        except ImportError as e:
            raise ProviderFactoryError(f"Failed to import module '{module_path}': {e}") from e
        except AttributeError as e:
            raise ProviderFactoryError(f"Failed to get class '{class_name}' from module '{module_path}': {e}") from e
    
    def unregister_provider(self, name: str) -> None:
        """Unregister provider class.
        
        Args:
            name: Provider name
            
        Raises:
            ProviderNotFoundError: If provider is not registered
        """
        if name not in self.providers:
            raise ProviderNotFoundError(f"Provider '{name}' is not registered")
        
        # Shutdown instance if it exists
        if name in self.instances:
            self.instances[name].shutdown()
            del self.instances[name]
        
        del self.providers[name]
        logger.debug(f"Unregistered provider '{name}' of type '{self.provider_type}'")
    
    def get_provider_class(self, name: Optional[str] = None) -> Type[P]:
        """Get provider class.
        
        Args:
            name: Provider name (default: default provider)
            
        Returns:
            Provider class
            
        Raises:
            ProviderNotFoundError: If provider is not registered
        """
        provider_name = name or self.default_provider
        
        if not provider_name:
            raise ProviderNotFoundError(f"No provider name specified and no default provider set for type '{self.provider_type}'")
        
        if provider_name not in self.providers:
            raise ProviderNotFoundError(f"Provider '{provider_name}' of type '{self.provider_type}' is not registered")
        
        return self.providers[provider_name]
    
    def create_provider(self, config: T, name: Optional[str] = None, initialize: bool = True) -> P:
        """Create provider instance.
        
        Args:
            config: Provider configuration
            name: Provider name (default: default provider)
            initialize: Whether to initialize provider
            
        Returns:
            Provider instance
            
        Raises:
            ProviderNotFoundError: If provider is not registered
            ProviderConfigError: If provider configuration is invalid
            ProviderInitializationError: If provider initialization fails
        """
        provider_class = self.get_provider_class(name)
        provider_name = name or self.default_provider
        
        try:
            provider = provider_class(config)
            
            if initialize:
                provider.initialize()
            
            logger.debug(f"Created provider instance '{provider_name}' of type '{self.provider_type}'")
            return provider
        except ProviderConfigError:
            # Re-raise without wrapping
            raise
        except ProviderInitializationError:
            # Re-raise without wrapping
            raise
        except Exception as e:
            logger.error(f"Failed to create provider instance '{provider_name}' of type '{self.provider_type}': {e}")
            raise ProviderFactoryError(f"Failed to create provider instance '{provider_name}' of type '{self.provider_type}': {e}") from e
    
    def get_or_create_provider(self, config: T, name: Optional[str] = None, initialize: bool = True) -> P:
        """Get existing provider instance or create a new one.
        
        Args:
            config: Provider configuration
            name: Provider name (default: default provider)
            initialize: Whether to initialize provider
            
        Returns:
            Provider instance
            
        Raises:
            ProviderNotFoundError: If provider is not registered
            ProviderConfigError: If provider configuration is invalid
            ProviderInitializationError: If provider initialization fails
        """
        provider_name = name or self.default_provider
        
        if not provider_name:
            raise ProviderNotFoundError(f"No provider name specified and no default provider set for type '{self.provider_type}'")
        
        if provider_name in self.instances:
            return self.instances[provider_name]
        
        provider = self.create_provider(config, provider_name, initialize)
        self.instances[provider_name] = provider
        
        return provider
    
    def get_provider(self, name: Optional[str] = None) -> P:
        """Get existing provider instance.
        
        Args:
            name: Provider name (default: default provider)
            
        Returns:
            Provider instance
            
        Raises:
            ProviderNotFoundError: If provider instance does not exist
        """
        provider_name = name or self.default_provider
        
        if not provider_name:
            raise ProviderNotFoundError(f"No provider name specified and no default provider set for type '{self.provider_type}'")
        
        if provider_name not in self.instances:
            raise ProviderNotFoundError(f"Provider instance '{provider_name}' of type '{self.provider_type}' does not exist")
        
        return self.instances[provider_name]
    
    def shutdown_provider(self, name: Optional[str] = None) -> None:
        """Shutdown provider instance.
        
        Args:
            name: Provider name (default: default provider)
            
        Raises:
            ProviderNotFoundError: If provider instance does not exist
        """
        provider_name = name or self.default_provider
        
        if not provider_name:
            raise ProviderNotFoundError(f"No provider name specified and no default provider set for type '{self.provider_type}'")
        
        if provider_name not in self.instances:
            raise ProviderNotFoundError(f"Provider instance '{provider_name}' of type '{self.provider_type}' does not exist")
        
        try:
            self.instances[provider_name].shutdown()
            del self.instances[provider_name]
            logger.debug(f"Shutdown provider instance '{provider_name}' of type '{self.provider_type}'")
        except Exception as e:
            logger.error(f"Failed to shutdown provider instance '{provider_name}' of type '{self.provider_type}': {e}")
            raise ProviderFactoryError(f"Failed to shutdown provider instance '{provider_name}' of type '{self.provider_type}': {e}") from e
    
    def shutdown_all(self) -> None:
        """Shutdown all provider instances."""
        for name in list(self.instances.keys()):
            try:
                self.shutdown_provider(name)
            except Exception as e:
                logger.error(f"Failed to shutdown provider instance '{name}' of type '{self.provider_type}': {e}")
    
    def get_registered_providers(self) -> List[str]:
        """Get list of registered provider names.
        
        Returns:
            List of registered provider names
        """
        return list(self.providers.keys())
    
    def get_active_providers(self) -> List[str]:
        """Get list of active provider instance names.
        
        Returns:
            List of active provider instance names
        """
        return list(self.instances.keys())
    
    def __del__(self):
        """Destructor."""
        self.shutdown_all()
