"""
<PERSON>ript to update requirements.txt file to remove outdated dependencies.

This script updates the requirements.txt file to remove outdated dependencies
and ensure compatibility between packages.
"""

import os
import sys
import re
import shutil
import logging
from typing import Dict, List, Set, Tuple, Any
import subprocess

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# Define outdated dependencies to be removed or updated
OUTDATED_DEPENDENCIES = {
    # Remove these dependencies
    "asyncio==3.4.3": None,  # Built-in module, no need to specify
    "reflex==0.3.6": None,  # Commented out due to compatibility issues
    
    # Update these dependencies
    "google-genai==0.3.0": "google-genai==0.4.0",
    "httpx==0.25.0": "httpx==0.27.0",
    "pydantic>=1.10.0,<2.0.0": "pydantic>=2.0.0,<3.0.0",
    "fastapi==0.96.1": "fastapi==0.109.2",
    "uvicorn==0.21.0": "uvicorn==0.27.1",
}

# Define dependencies to be added
NEW_DEPENDENCIES = [
    "pytest==7.4.3",  # For testing
    "pytest-asyncio==0.23.2",  # For async tests
    "coverage==7.3.2",  # For test coverage
]

def backup_file(file_path: str) -> str:
    """
    Create a backup of a file.
    
    Args:
        file_path: Path to the file
        
    Returns:
        Path to the backup file
    """
    backup_path = f"{file_path}.bak"
    try:
        shutil.copy2(file_path, backup_path)
        logger.info(f"Created backup: {backup_path}")
        return backup_path
    except Exception as e:
        logger.error(f"Error creating backup of {file_path}: {e}")
        return ""

def parse_requirements(file_path: str) -> List[str]:
    """
    Parse requirements.txt file.
    
    Args:
        file_path: Path to the requirements.txt file
        
    Returns:
        List of requirements
    """
    requirements = []
    
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith("#"):
                    requirements.append(line)
    except Exception as e:
        logger.error(f"Error parsing requirements file: {e}")
    
    return requirements

def update_requirements(file_path: str) -> bool:
    """
    Update requirements.txt file.
    
    Args:
        file_path: Path to the requirements.txt file
        
    Returns:
        bool: True if file was updated, False otherwise
    """
    try:
        # Parse requirements
        requirements = parse_requirements(file_path)
        logger.info(f"Found {len(requirements)} requirements in {file_path}")
        
        # Create backup
        backup_file(file_path)
        
        # Update requirements
        updated_requirements = []
        for req in requirements:
            # Check if requirement is outdated
            for outdated, updated in OUTDATED_DEPENDENCIES.items():
                if req.startswith(outdated.split("==")[0]) and (outdated in req or "==" in outdated):
                    if updated:
                        # Replace with updated version
                        req = updated
                    else:
                        # Remove requirement
                        req = None
                    break
            
            if req:
                updated_requirements.append(req)
        
        # Add new dependencies
        for dep in NEW_DEPENDENCIES:
            if not any(dep.split("==")[0] == req.split("==")[0] for req in updated_requirements):
                updated_requirements.append(dep)
        
        # Sort requirements
        updated_requirements.sort()
        
        # Write updated requirements
        with open(file_path, "w", encoding="utf-8") as f:
            # Add header
            f.write("# Updated requirements.txt\n")
            f.write("# Generated by update_requirements.py\n\n")
            
            # Group requirements by category
            categories = {
                "Core dependencies": [req for req in updated_requirements if any(x in req.lower() for x in ["python-telegram-bot", "python-dotenv", "requests", "httpx", "pydantic"])],
                "Voice processing": [req for req in updated_requirements if any(x in req.lower() for x in ["deepgram", "gtts", "elevenlabs", "soundfile", "ffmpeg"])],
                "AI providers": [req for req in updated_requirements if any(x in req.lower() for x in ["google-genai", "huggingface", "groq", "langchain"])],
                "Web framework": [req for req in updated_requirements if any(x in req.lower() for x in ["fastapi", "uvicorn", "aiohttp"])],
                "ML libraries": [req for req in updated_requirements if any(x in req.lower() for x in ["numpy", "transformers", "torch", "gradio"])],
                "Database": [req for req in updated_requirements if any(x in req.lower() for x in ["sqlalchemy", "sqlite", "postgres"])],
                "Testing": [req for req in updated_requirements if any(x in req.lower() for x in ["pytest", "coverage"])],
                "Utilities": []
            }
            
            # Add remaining requirements to Utilities
            for req in updated_requirements:
                if not any(req in category for category in categories.values()):
                    categories["Utilities"].append(req)
            
            # Write requirements by category
            for category, reqs in categories.items():
                if reqs:
                    f.write(f"# {category}\n")
                    for req in reqs:
                        f.write(f"{req}\n")
                    f.write("\n")
        
        logger.info(f"Updated requirements.txt with {len(updated_requirements)} requirements")
        return True
    except Exception as e:
        logger.error(f"Error updating requirements.txt: {e}")
        return False

def check_dependency_conflicts() -> bool:
    """
    Check for dependency conflicts.
    
    Returns:
        bool: True if no conflicts found, False otherwise
    """
    try:
        # Run pip check
        result = subprocess.run(["pip", "check"], capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info("No dependency conflicts found")
            return True
        else:
            logger.warning(f"Dependency conflicts found:\n{result.stdout}")
            return False
    except Exception as e:
        logger.error(f"Error checking dependencies: {e}")
        return False

def main():
    """Main function."""
    logger.info("Starting requirements.txt update")
    
    # Update requirements.txt
    if update_requirements("requirements.txt"):
        logger.info("requirements.txt updated successfully")
    else:
        logger.error("Failed to update requirements.txt")
        return 1
    
    # Check for dependency conflicts
    if check_dependency_conflicts():
        logger.info("No dependency conflicts found")
    else:
        logger.warning("Dependency conflicts found. Manual review required.")
    
    logger.info("Update completed successfully")
    return 0

if __name__ == "__main__":
    sys.exit(main())
