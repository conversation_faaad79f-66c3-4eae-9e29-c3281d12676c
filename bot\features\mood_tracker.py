"""
Mood tracker for VoicePal.

This module tracks and analyzes user mood, providing summaries, visualizations, and reports.
"""

import logging
import json
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from collections import Counter, defaultdict
import calendar

from bot.providers.stt_provider_interface import STTProviderInterface
from bot.providers.ai_provider_interface import AIProviderInterface

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class MoodTracker:
    """Tracks and analyzes user mood."""

    def __init__(self, database, stt_provider: STTProviderInterface,
                ai_provider: AIProviderInterface, config: Dict[str, Any] = None):
        """
        Initialize the mood tracker.

        Args:
            database: Database instance
            stt_provider: STT provider instance
            ai_provider: AI provider instance
            config: Configuration dictionary
        """
        self.database = database
        self.stt_provider = stt_provider
        self.ai_provider = ai_provider
        self.config = config or {}

        # Default configuration
        self.analysis_frequency = self.config.get("analysis_frequency", 7)  # days

    async def analyze_voice_mood(self, user_id: int, audio_file_path: str) -> Dict[str, Any]:
        """
        Analyze mood from voice.

        Args:
            user_id: User ID
            audio_file_path: Path to audio file

        Returns:
            Dict containing sentiment information
        """
        try:
            mood_data = {"sentiment": "neutral", "confidence": 0.0, "source": "voice"}
            transcript = None

            # Check if STT provider supports sentiment analysis
            if self.stt_provider.supports_feature("sentiment_analysis"):
                transcription_result = await self.stt_provider.transcribe_with_sentiment(audio_file_path)

                # Extract transcript if available
                if "transcript" in transcription_result:
                    transcript = transcription_result["transcript"]
                    mood_data["message_text"] = transcript

                # Extract sentiment if available
                if "sentiment" in transcription_result:
                    mood_data.update(transcription_result["sentiment"])

            # Fallback to AI provider for text-based sentiment analysis
            elif transcript and self.ai_provider.supports_feature("sentiment_analysis"):
                sentiment_result = self.ai_provider.analyze_sentiment(transcript)
                if sentiment_result:
                    mood_data.update(sentiment_result)

            # Store mood data
            self.add_mood_entry(user_id, mood_data)

            return mood_data
        except Exception as e:
            logger.error(f"Error analyzing voice mood for user {user_id}: {e}")
            return {"sentiment": "neutral", "confidence": 0.0, "source": "voice"}

    def analyze_text_mood(self, user_id: int, text: str) -> Dict[str, Any]:
        """
        Analyze mood from text.

        Args:
            user_id: User ID
            text: Text to analyze

        Returns:
            Dict containing sentiment information
        """
        try:
            mood_data = {"sentiment": "neutral", "confidence": 0.0, "source": "text", "message_text": text}

            # If AI provider supports sentiment analysis, use it
            if self.ai_provider.supports_feature("sentiment_analysis"):
                sentiment_result = self.ai_provider.analyze_sentiment(text)
                # Update mood_data with sentiment analysis results
                if sentiment_result:
                    mood_data.update(sentiment_result)

            # Store mood data
            self.add_mood_entry(user_id, mood_data)

            return mood_data
        except Exception as e:
            logger.error(f"Error analyzing text mood for user {user_id}: {e}")
            return {"sentiment": "neutral", "confidence": 0.0, "source": "text"}

    def add_mood_entry(self, user_id: int, mood_data: Dict[str, Any]) -> bool:
        """
        Add a mood entry.

        Args:
            user_id: User ID
            mood_data: Mood data

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Add mood entry to database
            self.database.add_mood_entry(user_id, mood_data)
            logger.info(f"Added mood entry for user {user_id}")
            return True
        except Exception as e:
            logger.error(f"Error adding mood entry for user {user_id}: {e}")
            return False

    def get_mood_history(self, user_id: int, days: int = None) -> List[Dict[str, Any]]:
        """
        Get mood history.

        Args:
            user_id: User ID
            days: Number of days to look back

        Returns:
            List of mood entries
        """
        try:
            # Use configured days if not specified
            if days is None:
                days = self.analysis_frequency

            # Get mood history from database
            mood_history = self.database.get_mood_history(user_id, days)
            return mood_history or []
        except Exception as e:
            logger.error(f"Error getting mood history for user {user_id}: {e}")
            return []

    def get_mood_summary(self, user_id: int) -> Dict[str, Any]:
        """
        Get a summary of user's mood patterns.

        Args:
            user_id: User ID

        Returns:
            Dict containing mood summary
        """
        try:
            # Get mood history
            mood_history = self.get_mood_history(user_id)

            # If AI provider supports mood analysis, use it
            if self.ai_provider.supports_feature("mood_analysis"):
                return self.ai_provider.analyze_mood_patterns(mood_history)

            # Otherwise, do basic analysis
            return self._basic_mood_analysis(mood_history)
        except Exception as e:
            logger.error(f"Error getting mood summary for user {user_id}: {e}")
            return {
                "dominant_mood": "neutral",
                "mood_stability": "stable",
                "mood_trend": "neutral"
            }

    def _basic_mood_analysis(self, mood_history: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Perform basic mood analysis.

        Args:
            mood_history: List of mood entries

        Returns:
            Dict containing mood summary
        """
        if not mood_history:
            return {
                "dominant_mood": "neutral",
                "mood_stability": "stable",
                "mood_trend": "neutral"
            }

        # Count sentiments
        sentiment_counts = Counter()
        for entry in mood_history:
            sentiment = entry.get("sentiment", "neutral")
            sentiment_counts[sentiment] += 1

        # Get dominant mood
        dominant_mood = sentiment_counts.most_common(1)[0][0]

        # Calculate mood stability
        unique_sentiments = len(sentiment_counts)
        if unique_sentiments == 1:
            mood_stability = "very stable"
        elif unique_sentiments == 2:
            mood_stability = "stable"
        elif unique_sentiments == 3:
            mood_stability = "somewhat unstable"
        else:
            mood_stability = "unstable"

        # Calculate mood trend
        if len(mood_history) >= 3:
            recent_sentiments = [entry.get("sentiment", "neutral") for entry in mood_history[-3:]]
            if all(s == "positive" for s in recent_sentiments):
                mood_trend = "improving"
            elif all(s == "negative" for s in recent_sentiments):
                mood_trend = "deteriorating"
            else:
                mood_trend = "fluctuating"
        else:
            mood_trend = "neutral"

        return {
            "dominant_mood": dominant_mood,
            "mood_stability": mood_stability,
            "mood_trend": mood_trend,
            "sentiment_distribution": dict(sentiment_counts)
        }

    def get_daily_mood_summary(self, user_id: int) -> Dict[str, Any]:
        """
        Get a summary of user's mood for today.

        Args:
            user_id: User ID

        Returns:
            Dict containing today's mood summary
        """
        try:
            # Get mood history for today
            mood_history = self.database.get_mood_history(user_id, days=1)

            if not mood_history:
                return {
                    "status": "no_data",
                    "message": "No mood data available for today."
                }

            # Count sentiments
            sentiment_counts = Counter()
            for entry in mood_history:
                sentiment = entry.get("sentiment", "neutral")
                sentiment_counts[sentiment] += 1

            # Get dominant mood
            dominant_mood = sentiment_counts.most_common(1)[0][0]

            # Calculate average confidence
            total_confidence = sum(entry.get("confidence", 0.0) for entry in mood_history)
            avg_confidence = total_confidence / len(mood_history) if mood_history else 0.0

            # Get most recent mood
            most_recent = mood_history[0] if mood_history else None
            most_recent_mood = most_recent.get("sentiment", "neutral") if most_recent else "neutral"
            most_recent_time = most_recent.get("created_at", "") if most_recent else ""

            # Get mood messages for context
            mood_messages = [
                entry.get("message_text", "") for entry in mood_history
                if entry.get("message_text")
            ]

            return {
                "status": "success",
                "date": datetime.now().strftime("%Y-%m-%d"),
                "entries_count": len(mood_history),
                "dominant_mood": dominant_mood,
                "most_recent_mood": most_recent_mood,
                "most_recent_time": most_recent_time,
                "average_confidence": avg_confidence,
                "sentiment_distribution": dict(sentiment_counts),
                "mood_messages": mood_messages[:5]  # Limit to 5 messages
            }
        except Exception as e:
            logger.error(f"Error getting daily mood summary for user {user_id}: {e}")
            return {
                "status": "error",
                "message": "Error retrieving mood data."
            }

    def get_weekly_mood_summary(self, user_id: int) -> Dict[str, Any]:
        """
        Get a summary of user's mood for the past week.

        Args:
            user_id: User ID

        Returns:
            Dict containing weekly mood summary
        """
        try:
            # Get mood history for the past week
            mood_history = self.database.get_mood_history(user_id, days=7)

            if not mood_history:
                return {
                    "status": "no_data",
                    "message": "No mood data available for the past week."
                }

            # Group by day
            daily_moods = defaultdict(list)
            for entry in mood_history:
                # Parse the created_at timestamp
                created_at = entry.get("created_at", "")
                if created_at:
                    try:
                        # Handle different datetime formats
                        if isinstance(created_at, str):
                            if "T" in created_at:
                                # ISO format
                                dt = datetime.fromisoformat(created_at.replace("Z", "+00:00"))
                            else:
                                # SQLite format
                                dt = datetime.strptime(created_at, "%Y-%m-%d %H:%M:%S")
                        else:
                            dt = created_at

                        day = dt.strftime("%Y-%m-%d")
                        daily_moods[day].append(entry)
                    except Exception as e:
                        logger.error(f"Error parsing date {created_at}: {e}")
                        continue

            # Calculate daily dominant moods
            daily_summary = {}
            for day, entries in daily_moods.items():
                # Count sentiments for this day
                day_sentiment_counts = Counter()
                for entry in entries:
                    sentiment = entry.get("sentiment", "neutral")
                    day_sentiment_counts[sentiment] += 1

                # Get dominant mood for this day
                dominant_mood = day_sentiment_counts.most_common(1)[0][0] if day_sentiment_counts else "neutral"

                # Add to summary
                daily_summary[day] = {
                    "dominant_mood": dominant_mood,
                    "entries_count": len(entries),
                    "sentiment_distribution": dict(day_sentiment_counts)
                }

            # Calculate overall weekly stats
            all_sentiments = Counter()
            for entries in daily_moods.values():
                for entry in entries:
                    sentiment = entry.get("sentiment", "neutral")
                    all_sentiments[sentiment] += 1

            # Get overall dominant mood
            overall_dominant_mood = all_sentiments.most_common(1)[0][0] if all_sentiments else "neutral"

            # Calculate mood trend
            days_list = sorted(daily_summary.keys())
            if len(days_list) >= 3:
                recent_days = days_list[-3:]
                recent_moods = [daily_summary[day]["dominant_mood"] for day in recent_days]

                if all(mood == "positive" for mood in recent_moods):
                    mood_trend = "improving"
                elif all(mood == "negative" for mood in recent_moods):
                    mood_trend = "deteriorating"
                else:
                    mood_trend = "fluctuating"
            else:
                mood_trend = "neutral"

            return {
                "status": "success",
                "period": f"{days_list[0]} to {days_list[-1]}" if days_list else "past week",
                "days_with_entries": len(daily_moods),
                "total_entries": len(mood_history),
                "overall_dominant_mood": overall_dominant_mood,
                "mood_trend": mood_trend,
                "overall_sentiment_distribution": dict(all_sentiments),
                "daily_summary": daily_summary
            }
        except Exception as e:
            logger.error(f"Error getting weekly mood summary for user {user_id}: {e}")
            return {
                "status": "error",
                "message": "Error retrieving weekly mood data."
            }

    def get_monthly_mood_summary(self, user_id: int) -> Dict[str, Any]:
        """
        Get a summary of user's mood for the past month.

        Args:
            user_id: User ID

        Returns:
            Dict containing monthly mood summary
        """
        try:
            # Get mood history for the past month
            mood_history = self.database.get_mood_history(user_id, days=30)

            if not mood_history:
                return {
                    "status": "no_data",
                    "message": "No mood data available for the past month."
                }

            # Group by week
            weekly_moods = defaultdict(list)
            for entry in mood_history:
                # Parse the created_at timestamp
                created_at = entry.get("created_at", "")
                if created_at:
                    try:
                        # Handle different datetime formats
                        if isinstance(created_at, str):
                            if "T" in created_at:
                                # ISO format
                                dt = datetime.fromisoformat(created_at.replace("Z", "+00:00"))
                            else:
                                # SQLite format
                                dt = datetime.strptime(created_at, "%Y-%m-%d %H:%M:%S")
                        else:
                            dt = created_at

                        # Get week number (1-5) within the month
                        week_of_month = (dt.day - 1) // 7 + 1
                        month_name = dt.strftime("%B")  # Full month name
                        week_key = f"{month_name} Week {week_of_month}"

                        weekly_moods[week_key].append(entry)
                    except Exception as e:
                        logger.error(f"Error parsing date {created_at}: {e}")
                        continue

            # Calculate weekly dominant moods
            weekly_summary = {}
            for week, entries in weekly_moods.items():
                # Count sentiments for this week
                week_sentiment_counts = Counter()
                for entry in entries:
                    sentiment = entry.get("sentiment", "neutral")
                    week_sentiment_counts[sentiment] += 1

                # Get dominant mood for this week
                dominant_mood = week_sentiment_counts.most_common(1)[0][0] if week_sentiment_counts else "neutral"

                # Add to summary
                weekly_summary[week] = {
                    "dominant_mood": dominant_mood,
                    "entries_count": len(entries),
                    "sentiment_distribution": dict(week_sentiment_counts)
                }

            # Calculate overall monthly stats
            all_sentiments = Counter()
            for entries in weekly_moods.values():
                for entry in entries:
                    sentiment = entry.get("sentiment", "neutral")
                    all_sentiments[sentiment] += 1

            # Get overall dominant mood
            overall_dominant_mood = all_sentiments.most_common(1)[0][0] if all_sentiments else "neutral"

            # Calculate mood stability
            unique_sentiments = len(all_sentiments)
            if unique_sentiments == 1:
                mood_stability = "very stable"
            elif unique_sentiments == 2:
                mood_stability = "stable"
            elif unique_sentiments == 3:
                mood_stability = "somewhat unstable"
            else:
                mood_stability = "unstable"

            # Get month name
            current_month = datetime.now().strftime("%B %Y")

            return {
                "status": "success",
                "period": current_month,
                "weeks_with_entries": len(weekly_moods),
                "total_entries": len(mood_history),
                "overall_dominant_mood": overall_dominant_mood,
                "mood_stability": mood_stability,
                "overall_sentiment_distribution": dict(all_sentiments),
                "weekly_summary": weekly_summary
            }
        except Exception as e:
            logger.error(f"Error getting monthly mood summary for user {user_id}: {e}")
            return {
                "status": "error",
                "message": "Error retrieving monthly mood data."
            }

    def generate_mood_insights(self, user_id: int) -> Dict[str, Any]:
        """
        Generate insights from user's mood history.

        Args:
            user_id: User ID

        Returns:
            Dict containing mood insights
        """
        try:
            # Get mood history for the past month
            mood_history = self.database.get_mood_history(user_id, days=30)

            if not mood_history:
                return {
                    "status": "no_data",
                    "message": "Not enough mood data to generate insights."
                }

            # If AI provider supports mood insights, use it
            if self.ai_provider.supports_feature("mood_insights") and len(mood_history) >= 5:
                # Extract messages for context
                messages = [entry.get("message_text", "") for entry in mood_history if entry.get("message_text")]

                # Get insights from AI
                insights = self.ai_provider.generate_mood_insights(mood_history, messages)
                if insights:
                    return {
                        "status": "success",
                        "insights": insights
                    }

            # Fallback to basic insights
            return self._generate_basic_insights(mood_history)
        except Exception as e:
            logger.error(f"Error generating mood insights for user {user_id}: {e}")
            return {
                "status": "error",
                "message": "Error generating mood insights."
            }

    def _generate_basic_insights(self, mood_history: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Generate basic insights from mood history.

        Args:
            mood_history: List of mood entries

        Returns:
            Dict containing basic mood insights
        """
        if len(mood_history) < 3:
            return {
                "status": "no_data",
                "message": "Not enough mood data to generate insights."
            }

        # Count sentiments
        sentiment_counts = Counter()
        for entry in mood_history:
            sentiment = entry.get("sentiment", "neutral")
            sentiment_counts[sentiment] += 1

        # Calculate percentages
        total = sum(sentiment_counts.values())
        sentiment_percentages = {k: round(v / total * 100) for k, v in sentiment_counts.items()}

        # Generate insights based on dominant mood
        insights = []
        dominant_mood = sentiment_counts.most_common(1)[0][0]

        if dominant_mood == "positive" and sentiment_percentages.get("positive", 0) > 60:
            insights.append("You've been feeling quite positive lately. That's great!")
        elif dominant_mood == "negative" and sentiment_percentages.get("negative", 0) > 60:
            insights.append("You've been feeling down lately. Remember to take care of yourself.")
        elif dominant_mood == "neutral" and sentiment_percentages.get("neutral", 0) > 60:
            insights.append("Your mood has been mostly neutral lately.")
        else:
            insights.append("Your mood has been varied lately.")

        # Add time-based insights
        if len(mood_history) >= 7:
            # Group by day of week
            day_moods = defaultdict(list)
            for entry in mood_history:
                created_at = entry.get("created_at", "")
                if created_at:
                    try:
                        # Handle different datetime formats
                        if isinstance(created_at, str):
                            if "T" in created_at:
                                # ISO format
                                dt = datetime.fromisoformat(created_at.replace("Z", "+00:00"))
                            else:
                                # SQLite format
                                dt = datetime.strptime(created_at, "%Y-%m-%d %H:%M:%S")
                        else:
                            dt = created_at

                        day_of_week = calendar.day_name[dt.weekday()]
                        day_moods[day_of_week].append(entry.get("sentiment", "neutral"))
                    except Exception:
                        continue

            # Find best and worst days
            day_sentiment_scores = {}
            for day, sentiments in day_moods.items():
                if len(sentiments) < 2:
                    continue

                # Calculate sentiment score
                score = 0
                for sentiment in sentiments:
                    if sentiment == "positive":
                        score += 1
                    elif sentiment == "negative":
                        score -= 1

                day_sentiment_scores[day] = score / len(sentiments)

            if day_sentiment_scores:
                best_day = max(day_sentiment_scores.items(), key=lambda x: x[1])
                worst_day = min(day_sentiment_scores.items(), key=lambda x: x[1])

                if best_day[1] > 0.3:
                    insights.append(f"You tend to feel most positive on {best_day[0]}s.")

                if worst_day[1] < -0.3:
                    insights.append(f"You tend to feel most negative on {worst_day[0]}s.")

        return {
            "status": "success",
            "sentiment_distribution": sentiment_percentages,
            "insights": insights
        }

    def format_mood_summary_text(self, summary: Dict[str, Any], summary_type: str = "basic") -> str:
        """
        Format mood summary as text.

        Args:
            summary: Mood summary dictionary
            summary_type: Type of summary (basic, daily, weekly, monthly, insights)

        Returns:
            str: Formatted text summary
        """
        if summary.get("status") == "error" or summary.get("status") == "no_data":
            return summary.get("message", "No mood data available.")

        if summary_type == "daily":
            return self._format_daily_summary_text(summary)
        elif summary_type == "weekly":
            return self._format_weekly_summary_text(summary)
        elif summary_type == "monthly":
            return self._format_monthly_summary_text(summary)
        elif summary_type == "insights":
            return self._format_insights_text(summary)
        else:
            # Basic summary
            text = "📊 *Your Mood Summary*\n\n"
            text += f"Dominant mood: {summary.get('dominant_mood', 'neutral')}\n"
            text += f"Mood stability: {summary.get('mood_stability', 'stable')}\n"
            text += f"Mood trend: {summary.get('mood_trend', 'neutral')}\n\n"

            # Add sentiment distribution if available
            if "sentiment_distribution" in summary:
                text += "*Sentiment Distribution:*\n"
                for sentiment, count in summary["sentiment_distribution"].items():
                    text += f"- {sentiment}: {count}\n"

            return text

    def _format_daily_summary_text(self, summary: Dict[str, Any]) -> str:
        """Format daily mood summary as text."""
        text = "📅 *Today's Mood Summary*\n\n"
        text += f"Date: {summary.get('date', 'Today')}\n"
        text += f"Entries: {summary.get('entries_count', 0)}\n"
        text += f"Dominant mood: {summary.get('dominant_mood', 'neutral')}\n"
        text += f"Most recent mood: {summary.get('most_recent_mood', 'neutral')}\n\n"

        # Add sentiment distribution
        if "sentiment_distribution" in summary:
            text += "*Sentiment Distribution:*\n"
            for sentiment, count in summary["sentiment_distribution"].items():
                text += f"- {sentiment}: {count}\n"

        # Add sample messages if available
        if summary.get("mood_messages"):
            text += "\n*Sample Messages:*\n"
            for i, msg in enumerate(summary["mood_messages"][:3], 1):
                if msg:
                    # Truncate long messages
                    if len(msg) > 50:
                        msg = msg[:47] + "..."
                    text += f"{i}. \"{msg}\"\n"

        return text

    def _format_weekly_summary_text(self, summary: Dict[str, Any]) -> str:
        """Format weekly mood summary as text."""
        text = "📊 *Weekly Mood Summary*\n\n"
        text += f"Period: {summary.get('period', 'Past week')}\n"
        text += f"Days with entries: {summary.get('days_with_entries', 0)}\n"
        text += f"Total entries: {summary.get('total_entries', 0)}\n"
        text += f"Overall dominant mood: {summary.get('overall_dominant_mood', 'neutral')}\n"
        text += f"Mood trend: {summary.get('mood_trend', 'neutral')}\n\n"

        # Add daily summary
        if "daily_summary" in summary:
            text += "*Daily Breakdown:*\n"
            for day, day_summary in list(summary["daily_summary"].items())[-5:]:  # Show last 5 days
                text += f"- {day}: {day_summary.get('dominant_mood', 'neutral')} ({day_summary.get('entries_count', 0)} entries)\n"

        return text

    def _format_monthly_summary_text(self, summary: Dict[str, Any]) -> str:
        """Format monthly mood summary as text."""
        text = "📈 *Monthly Mood Summary*\n\n"
        text += f"Period: {summary.get('period', 'Past month')}\n"
        text += f"Weeks with entries: {summary.get('weeks_with_entries', 0)}\n"
        text += f"Total entries: {summary.get('total_entries', 0)}\n"
        text += f"Overall dominant mood: {summary.get('overall_dominant_mood', 'neutral')}\n"
        text += f"Mood stability: {summary.get('mood_stability', 'stable')}\n\n"

        # Add weekly summary
        if "weekly_summary" in summary:
            text += "*Weekly Breakdown:*\n"
            for week, week_summary in summary["weekly_summary"].items():
                text += f"- {week}: {week_summary.get('dominant_mood', 'neutral')} ({week_summary.get('entries_count', 0)} entries)\n"

        return text

    def _format_insights_text(self, summary: Dict[str, Any]) -> str:
        """Format mood insights as text."""
        text = "🔍 *Mood Insights*\n\n"

        # Add insights
        if "insights" in summary and summary["insights"]:
            for insight in summary["insights"]:
                text += f"• {insight}\n"
        else:
            text += "Not enough data to generate meaningful insights yet.\n"

        # Add sentiment distribution if available
        if "sentiment_distribution" in summary:
            text += "\n*Overall Sentiment Distribution:*\n"
            for sentiment, percentage in summary["sentiment_distribution"].items():
                text += f"- {sentiment}: {percentage}%\n"

        return text
