"""
Key management handlers for VoicePal.

This module provides command handlers for managing API keys in the VoicePal bot.
"""

import logging
from typing import Dict, Any, Optional, Tuple, List
from datetime import datetime

from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes

from bot.core.key_manager import KeyManager
from bot.core.rate_limiter import rate_limit
from bot.core.security import extract_ip_info

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

def is_admin_user(bot_instance, user_id: int) -> bool:
    """
    Check if a user is an admin.
    
    Args:
        bot_instance: VoicePalBot instance
        user_id: User ID to check
        
    Returns:
        bool: True if user is an admin, False otherwise
    """
    admin_ids = bot_instance.config_manager.get_admin_user_ids()
    return user_id in admin_ids

@rate_limit(rate=0.1, max_tokens=3, action="key_command")
async def key_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """
    Handle the /key command (admin only).
    
    This command allows admins to manage API keys.
    
    Args:
        update: Telegram update
        context: Callback context
    """
    # Get bot instance
    bot_instance = context.application.bot_data.get("bot_instance")
    if not bot_instance:
        await update.message.reply_text("Bot instance not found.")
        return
    
    user_id = update.effective_user.id
    
    # Check if user is admin
    if not is_admin_user(bot_instance, user_id):
        await update.message.reply_text("You don't have permission to use this command.")
        return
    
    # Get IP address for logging
    ip_info = extract_ip_info(update)
    
    # Initialize key manager
    key_manager = KeyManager(bot_instance.database.conn)
    
    # Parse command arguments
    args = context.args
    
    if not args:
        # Show key management menu
        keyboard = [
            [
                InlineKeyboardButton("List Keys", callback_data="key_list"),
                InlineKeyboardButton("Access Logs", callback_data="key_logs")
            ],
            [
                InlineKeyboardButton("Add/Update Key", callback_data="key_add"),
                InlineKeyboardButton("Rotate Keys", callback_data="key_rotate")
            ]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await update.message.reply_text(
            "🔑 *API Key Management*\n\n"
            "Use this menu to manage API keys securely.\n\n"
            "• List Keys: View all stored API keys\n"
            "• Access Logs: View key access logs\n"
            "• Add/Update Key: Add or update an API key\n"
            "• Rotate Keys: Rotate API keys\n\n"
            "You can also use `/key [action] [service] [value]` directly.",
            reply_markup=reply_markup,
            parse_mode="Markdown"
        )
        return
    
    action = args[0].lower()
    
    if action == "list":
        # List all keys
        services = ["deepgram", "elevenlabs", "google_ai", "huggingface"]
        
        message = "🔑 *API Key Status*\n\n"
        
        for service in services:
            key_info = key_manager.get_key_info(service)
            
            if key_info:
                status = "✅ ACTIVE" if key_info['is_active'] else "❌ INACTIVE"
                if key_info['is_expired']:
                    status += " (EXPIRED)"
                    
                updated = datetime.fromisoformat(key_info['updated_at']).strftime('%Y-%m-%d')
                
                expires = "Never"
                if key_info['expires_at']:
                    expires = datetime.fromisoformat(key_info['expires_at']).strftime('%Y-%m-%d')
                
                message += f"*{service.upper()}*: {status}\n"
                message += f"Updated: {updated}\n"
                message += f"Expires: {expires}\n\n"
            else:
                message += f"*{service.upper()}*: ❓ NOT FOUND\n\n"
        
        await update.message.reply_text(message, parse_mode="Markdown")
        return
    
    elif action == "logs":
        # Show access logs
        service = args[1].lower() if len(args) > 1 else None
        limit = 10
        
        logs = key_manager.get_access_logs(service, limit)
        
        if not logs:
            await update.message.reply_text("No access logs found.")
            return
        
        message = "🔑 *API Key Access Logs*\n\n"
        
        for log in logs:
            timestamp = datetime.fromisoformat(log['timestamp']).strftime('%Y-%m-%d %H:%M:%S')
            status = "✅" if log['success'] else "❌"
            
            message += f"{timestamp} | {log['service'].upper()} | {log['access_type'].upper()} | {status}\n"
            if log['user_id']:
                message += f"User: {log['user_id']}\n"
            if log['ip_address']:
                message += f"IP: {log['ip_address']}\n"
            message += "\n"
        
        await update.message.reply_text(message, parse_mode="Markdown")
        return
    
    elif action == "add" or action == "update":
        # Add or update a key
        if len(args) < 3:
            await update.message.reply_text(
                "Usage: `/key add [service] [api_key]`\n\n"
                "Example: `/key add deepgram YOUR_API_KEY`",
                parse_mode="Markdown"
            )
            return
        
        service = args[1].lower()
        api_key = args[2]
        
        # Store the key
        success = key_manager.store_key(service, api_key)
        
        if success:
            await update.message.reply_text(f"✅ API key for {service} has been stored securely.")
        else:
            await update.message.reply_text(f"❌ Failed to store API key for {service}.")
        
        return
    
    elif action == "rotate":
        # Rotate a key
        if len(args) < 3:
            await update.message.reply_text(
                "Usage: `/key rotate [service] [new_api_key]`\n\n"
                "Example: `/key rotate deepgram NEW_API_KEY`",
                parse_mode="Markdown"
            )
            return
        
        service = args[1].lower()
        new_api_key = args[2]
        
        # Rotate the key
        success = key_manager.rotate_key(service, new_api_key, "manual_rotation")
        
        if success:
            await update.message.reply_text(f"✅ API key for {service} has been rotated successfully.")
        else:
            await update.message.reply_text(f"❌ Failed to rotate API key for {service}.")
        
        return
    
    elif action == "deactivate":
        # Deactivate a key
        if len(args) < 2:
            await update.message.reply_text(
                "Usage: `/key deactivate [service]`\n\n"
                "Example: `/key deactivate deepgram`",
                parse_mode="Markdown"
            )
            return
        
        service = args[1].lower()
        
        # Deactivate the key
        success = key_manager.deactivate_key(service)
        
        if success:
            await update.message.reply_text(f"✅ API key for {service} has been deactivated.")
        else:
            await update.message.reply_text(f"❌ Failed to deactivate API key for {service}.")
        
        return
    
    else:
        # Unknown action
        await update.message.reply_text(
            "Unknown action. Available actions: list, logs, add, rotate, deactivate."
        )
        return

async def handle_key_management_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> Optional[Tuple[str, InlineKeyboardMarkup]]:
    """
    Handle callback queries for key management.
    
    Args:
        update: Telegram update
        context: Callback context
        
    Returns:
        Optional[Tuple[str, InlineKeyboardMarkup]]: Response text and reply markup, or None if not handled
    """
    query = update.callback_query
    callback_data = query.data
    
    if not callback_data.startswith("key_"):
        return None
    
    # Get bot instance
    bot_instance = context.application.bot_data.get("bot_instance")
    if not bot_instance:
        await query.answer("Bot instance not found.")
        return None
    
    user_id = query.from_user.id
    
    # Check if user is admin
    if not is_admin_user(bot_instance, user_id):
        await query.answer("You don't have permission to use this feature.")
        return None
    
    # Initialize key manager
    key_manager = KeyManager(bot_instance.database.conn)
    
    action = callback_data.replace("key_", "")
    
    if action == "list":
        # List all keys
        services = ["deepgram", "elevenlabs", "google_ai", "huggingface"]
        
        message = "🔑 *API Key Status*\n\n"
        
        for service in services:
            key_info = key_manager.get_key_info(service)
            
            if key_info:
                status = "✅ ACTIVE" if key_info['is_active'] else "❌ INACTIVE"
                if key_info['is_expired']:
                    status += " (EXPIRED)"
                    
                updated = datetime.fromisoformat(key_info['updated_at']).strftime('%Y-%m-%d')
                
                expires = "Never"
                if key_info['expires_at']:
                    expires = datetime.fromisoformat(key_info['expires_at']).strftime('%Y-%m-%d')
                
                message += f"*{service.upper()}*: {status}\n"
                message += f"Updated: {updated}\n"
                message += f"Expires: {expires}\n\n"
            else:
                message += f"*{service.upper()}*: ❓ NOT FOUND\n\n"
        
        # Back button
        keyboard = [[InlineKeyboardButton("Back to Key Management", callback_data="key_menu")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        return message, reply_markup
    
    elif action == "logs":
        # Show access logs
        limit = 10
        
        logs = key_manager.get_access_logs(None, limit)
        
        if not logs:
            message = "No access logs found."
        else:
            message = "🔑 *API Key Access Logs*\n\n"
            
            for log in logs:
                timestamp = datetime.fromisoformat(log['timestamp']).strftime('%Y-%m-%d %H:%M:%S')
                status = "✅" if log['success'] else "❌"
                
                message += f"{timestamp} | {log['service'].upper()} | {log['access_type'].upper()} | {status}\n"
                if log['user_id']:
                    message += f"User: {log['user_id']}\n"
                if log['ip_address']:
                    message += f"IP: {log['ip_address']}\n"
                message += "\n"
        
        # Back button
        keyboard = [[InlineKeyboardButton("Back to Key Management", callback_data="key_menu")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        return message, reply_markup
    
    elif action == "add":
        # Show add key menu
        services = ["deepgram", "elevenlabs", "google_ai", "huggingface"]
        
        keyboard = []
        for service in services:
            keyboard.append([InlineKeyboardButton(f"Add {service.upper()} Key", callback_data=f"key_add_{service}")])
        
        keyboard.append([InlineKeyboardButton("Back to Key Management", callback_data="key_menu")])
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        message = (
            "🔑 *Add/Update API Key*\n\n"
            "Select a service to add or update its API key.\n\n"
            "You will need to use the command:\n"
            "`/key add [service] [api_key]`\n\n"
            "Example: `/key add deepgram YOUR_API_KEY`"
        )
        
        return message, reply_markup
    
    elif action == "rotate":
        # Show rotate key menu
        services = ["deepgram", "elevenlabs", "google_ai", "huggingface"]
        
        keyboard = []
        for service in services:
            keyboard.append([InlineKeyboardButton(f"Rotate {service.upper()} Key", callback_data=f"key_rotate_{service}")])
        
        keyboard.append([InlineKeyboardButton("Back to Key Management", callback_data="key_menu")])
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        message = (
            "🔑 *Rotate API Key*\n\n"
            "Select a service to rotate its API key.\n\n"
            "You will need to use the command:\n"
            "`/key rotate [service] [new_api_key]`\n\n"
            "Example: `/key rotate deepgram NEW_API_KEY`"
        )
        
        return message, reply_markup
    
    elif action == "menu":
        # Show key management menu
        keyboard = [
            [
                InlineKeyboardButton("List Keys", callback_data="key_list"),
                InlineKeyboardButton("Access Logs", callback_data="key_logs")
            ],
            [
                InlineKeyboardButton("Add/Update Key", callback_data="key_add"),
                InlineKeyboardButton("Rotate Keys", callback_data="key_rotate")
            ]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        message = (
            "🔑 *API Key Management*\n\n"
            "Use this menu to manage API keys securely.\n\n"
            "• List Keys: View all stored API keys\n"
            "• Access Logs: View key access logs\n"
            "• Add/Update Key: Add or update an API key\n"
            "• Rotate Keys: Rotate API keys\n\n"
            "You can also use `/key [action] [service] [value]` directly."
        )
        
        return message, reply_markup
    
    elif action.startswith("add_") or action.startswith("rotate_"):
        # Show instructions for adding or rotating a specific key
        parts = action.split("_")
        if len(parts) < 2:
            return None
        
        operation = parts[0]
        service = parts[1]
        
        if operation == "add":
            message = (
                f"🔑 *Add/Update {service.upper()} API Key*\n\n"
                f"To add or update the {service.upper()} API key, use the command:\n\n"
                f"`/key add {service} YOUR_API_KEY`\n\n"
                f"Replace YOUR_API_KEY with your actual API key."
            )
        else:  # rotate
            message = (
                f"🔑 *Rotate {service.upper()} API Key*\n\n"
                f"To rotate the {service.upper()} API key, use the command:\n\n"
                f"`/key rotate {service} NEW_API_KEY`\n\n"
                f"Replace NEW_API_KEY with your new API key."
            )
        
        # Back buttons
        keyboard = [
            [InlineKeyboardButton(f"Back to {'Add' if operation == 'add' else 'Rotate'} Menu", callback_data=f"key_{operation}")],
            [InlineKeyboardButton("Back to Key Management", callback_data="key_menu")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        return message, reply_markup
    
    return None
