# VoicePal Tests

This directory contains test modules for various components of the VoicePal bot.

## Test Files

### Original Tests

- `test_bot.py`: Tests for the main bot functionality
- `test_deepgram.py`: Tests for the Deepgram integration
- `test_elevenlabs.py`: Tests for the ElevenLabs TTS integration
- `test_full_flow.py`: End-to-end tests for the complete bot flow
- `test_google_ai_dia.py`: Tests for the Google AI and Dia integration
- `test_telegram.py`: Tests for the Telegram bot integration
- `test_tts_providers.py`: Tests for various TTS providers
- `test_voice_bot.py`: Tests for voice processing functionality
- `test_voice_personality.py`: Tests for voice personality features
- `test_keyboard_manager.py`: Tests for the keyboard manager
- `test_welcome_manager.py`: Tests for the welcome manager
- `test_mood_tracker.py`: Tests for the mood tracker
- `test_mood_entry.py`: Tests for the mood entry functionality

### New Tests (Added 2023)

- `test_database_core.py`: Tests for the database module, focusing on credit system
- `test_telegram_stars_payment_updated.py`: Tests for the updated Telegram Stars payment system
- `test_deepgram_tts_provider.py`: Tests for the improved Deepgram TTS provider
- `test_credit_system.py`: Specialized tests for the credit system functionality
- `test_deepgram_cache.py`: Specialized tests for the Deepgram TTS caching system

### Hierarchical Memory System Tests (Added 2024)

- `providers/test_redis_provider.py`: Unit tests for the Redis provider
- `providers/test_qdrant_provider.py`: Unit tests for the Qdrant vector database provider
- `utils/test_embedding_utils.py`: Unit tests for the embedding utilities
- `features/test_hierarchical_memory_manager.py`: Unit tests for the hierarchical memory manager
- `integration/test_memory_integration.py`: Integration tests for the memory system
- `e2e/test_bot_with_memory.py`: End-to-end tests for the bot with memory system
- `test_plan.md`: Comprehensive test plan for the hierarchical memory system

## Running Tests

### Running All Tests

To run all tests, use the `run_tests.py` script:

```bash
python tests/run_tests.py
```

Or use the unittest discover command:

```bash
python -m unittest discover tests
```

### Running Updated Tests Only

To run only the updated tests (database, payment, TTS), use the `run_updated_tests.py` script:

```bash
python tests/run_updated_tests.py
```

### Running Hierarchical Memory System Tests

The hierarchical memory system tests use pytest instead of unittest. To run these tests:

```bash
# Install pytest and required plugins
pip install pytest pytest-asyncio pytest-cov

# Run all hierarchical memory tests
pytest tests/providers/ tests/utils/ tests/features/ tests/integration/ tests/e2e/

# Run unit tests only
pytest tests/providers/ tests/utils/ tests/features/

# Run integration tests only
pytest tests/integration/

# Run end-to-end tests only
pytest tests/e2e/

# Run with coverage reporting
pytest --cov=bot.features.hierarchical_memory_manager --cov=bot.providers.memory --cov=bot.utils.embedding_utils
```

### Running Individual Tests

To run a specific test:

```bash
# For unittest-based tests
python -m unittest tests.test_database_core

# Or run the test file directly
python tests/test_database_core.py

# For pytest-based tests
pytest tests/providers/test_redis_provider.py

# To run a specific test function
pytest tests/providers/test_redis_provider.py::TestRedisProvider::test_initialization_with_config
```

## Adding New Tests

When adding new tests, please follow these guidelines:

### For Original and 2023 Tests

1. Create a new file named `test_<component>.py`
2. Use the `unittest` framework
3. Include docstrings for test classes and methods
4. Mock external dependencies when appropriate

### For Hierarchical Memory System Tests

1. Create a new file in the appropriate directory:
   - `tests/providers/` for provider tests
   - `tests/utils/` for utility tests
   - `tests/features/` for feature tests
   - `tests/integration/` for integration tests
   - `tests/e2e/` for end-to-end tests
2. Use the `pytest` framework
3. Include docstrings for test classes and methods
4. Use fixtures for test setup and teardown
5. Use `pytest.mark.asyncio` for async tests
6. Mock external dependencies using `unittest.mock` or `pytest-mock`

## Test Environment Setup for Hierarchical Memory Tests

### Prerequisites

- Python 3.10 or higher
- pytest
- pytest-asyncio
- pytest-cov (for coverage reporting)

### Installation

```bash
pip install pytest pytest-asyncio pytest-cov
```

### Environment Variables

Some tests may require environment variables to be set:

- `CONFIG_DIR`: Directory containing configuration files
- `DB_FILE`: Path to the database file
- `REDIS_URL`: URL for Redis (optional, tests will use in-memory Redis if not provided)
- `QDRANT_URL`: URL for Qdrant (optional, tests will use in-memory Qdrant if not provided)

## Mocking Dependencies

### Using pytest-mock

The hierarchical memory system tests use pytest-mock to mock dependencies:

```python
def test_example(mocker):
    mock_dependency = mocker.MagicMock()
    mock_dependency.method.return_value = "mocked_result"

    # Use the mock in your test
    result = component_under_test.use_dependency(mock_dependency)
    assert result == "mocked_result"
```

### Mocking Async Functions

For async functions, use `AsyncMock`:

```python
from unittest.mock import AsyncMock

def test_async_example(mocker):
    mock_dependency = mocker.MagicMock()
    mock_dependency.async_method = AsyncMock(return_value="mocked_result")

    # Use the mock in your test
    result = await component_under_test.use_async_dependency(mock_dependency)
    assert result == "mocked_result"
```

## Troubleshooting

If you encounter issues with the tests, please check:

1. You are using the correct test framework (unittest or pytest)
2. You have installed all required dependencies
3. Your environment variables are set correctly
4. You are using the correct mocking approach for the test framework
