"""
Google AI provider for VoicePal.

This module provides a provider for Google AI services.
"""

import os
import logging
import asyncio
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass, field

import google.generativeai as genai
from google.generativeai.types import GenerationConfig

from bot.providers.core.provider import AIProvider
from bot.providers.core.config import AIProviderConfig
from bot.providers.core.exceptions import (
    ProviderError,
    ProviderConfigError,
    ProviderAuthError,
    ProviderRateLimitError,
    ProviderTimeoutError,
    ProviderNotFoundError,
    ProviderValidationError,
    ProviderNotInitializedError
)

# Set up logging
logger = logging.getLogger(__name__)

@dataclass
class GoogleAIConfig(AIProviderConfig):
    """Configuration for Google AI provider."""
    
    api_key: str = ""
    model: str = "gemini-pro"
    max_tokens: int = 1000
    temperature: float = 0.7
    top_p: float = 1.0
    top_k: int = 40
    candidate_count: int = 1
    stop_sequences: List[str] = field(default_factory=list)
    safety_settings: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Post-initialization."""
        self.provider_type = "ai"
        self.provider_name = "google_ai"
    
    def validate(self) -> List[str]:
        """Validate configuration.
        
        Returns:
            List of validation error messages (empty if valid)
        """
        errors = super().validate()
        
        if self.top_k <= 0:
            errors.append("Top K must be positive")
        
        if self.candidate_count <= 0:
            errors.append("Candidate count must be positive")
        
        return errors

class GoogleAIProvider(AIProvider[GoogleAIConfig]):
    """Provider for Google AI services."""
    
    provider_type = "ai"
    provider_name = "google_ai"
    provider_version = "1.0.0"
    provider_description = "Provider for Google AI services"
    config_class = GoogleAIConfig
    
    def __init__(self, config: GoogleAIConfig):
        """Initialize provider.
        
        Args:
            config: Provider configuration
        """
        super().__init__(config)
        self.model = None
        self.embedding_model = None
        self.chat_histories = {}
        self.max_history_turns = 10
    
    def validate_config(self) -> None:
        """Validate provider configuration.
        
        Raises:
            ProviderConfigError: If configuration is invalid
        """
        errors = self.config.validate()
        if errors:
            error_message = "; ".join(errors)
            raise ProviderConfigError(f"Invalid configuration: {error_message}")
    
    def initialize(self) -> None:
        """Initialize provider.
        
        Raises:
            ProviderInitializationError: If initialization fails
        """
        try:
            # Configure API key
            genai.configure(api_key=self.config.api_key)
            
            # Get model
            self.model = genai.GenerativeModel(
                model_name=self.config.model,
                generation_config=GenerationConfig(
                    temperature=self.config.temperature,
                    top_p=self.config.top_p,
                    top_k=self.config.top_k,
                    max_output_tokens=self.config.max_tokens,
                    candidate_count=self.config.candidate_count,
                    stop_sequences=self.config.stop_sequences
                ),
                safety_settings=self.config.safety_settings
            )
            
            # Get embedding model
            self.embedding_model = genai.get_embedding_model("embedding-001")
            
            self.initialized = True
            logger.info(f"Initialized {self.provider_name} provider")
        except Exception as e:
            logger.error(f"Failed to initialize {self.provider_name} provider: {e}")
            raise ProviderInitializationError(f"Failed to initialize {self.provider_name} provider: {e}") from e
    
    def shutdown(self) -> None:
        """Shutdown provider.
        
        Raises:
            ProviderShutdownError: If shutdown fails
        """
        self.model = None
        self.embedding_model = None
        self.chat_histories = {}
        self.initialized = False
        logger.info(f"Shutdown {self.provider_name} provider")
    
    async def generate_text(self, prompt: str, **kwargs) -> str:
        """Generate text from prompt.
        
        Args:
            prompt: Text prompt
            **kwargs: Additional arguments
            
        Returns:
            Generated text
            
        Raises:
            ProviderError: If text generation fails
        """
        if not self.initialized:
            raise ProviderNotInitializedError(f"{self.provider_name} provider is not initialized")
        
        try:
            # Merge configuration with kwargs
            generation_config = {}
            for key in ["temperature", "top_p", "top_k", "max_output_tokens", "candidate_count", "stop_sequences"]:
                if key in kwargs:
                    generation_config[key] = kwargs[key]
            
            # Generate text
            response = await asyncio.to_thread(
                self.model.generate_content,
                prompt,
                generation_config=generation_config if generation_config else None
            )
            
            # Extract text from response
            if response.candidates:
                return response.text
            else:
                return ""
        except Exception as e:
            logger.error(f"Failed to generate text: {e}")
            
            # Map exceptions to provider exceptions
            if "API key not valid" in str(e):
                raise ProviderAuthError(f"Invalid API key: {e}") from e
            elif "quota exceeded" in str(e).lower() or "rate limit" in str(e).lower():
                raise ProviderRateLimitError(f"Rate limit exceeded: {e}") from e
            elif "timeout" in str(e).lower():
                raise ProviderTimeoutError(f"Request timed out: {e}") from e
            elif "not found" in str(e).lower():
                raise ProviderNotFoundError(f"Resource not found: {e}") from e
            elif "invalid" in str(e).lower():
                raise ProviderValidationError(f"Invalid request: {e}") from e
            else:
                raise ProviderError(f"Failed to generate text: {e}") from e
    
    async def generate_chat_response(self, messages: List[Dict[str, str]], **kwargs) -> Dict[str, Any]:
        """Generate chat response from messages.
        
        Args:
            messages: List of messages in the format {"role": "user|assistant|system", "content": "message"}
            **kwargs: Additional arguments
            
        Returns:
            Generated response
            
        Raises:
            ProviderError: If chat response generation fails
        """
        if not self.initialized:
            raise ProviderNotInitializedError(f"{self.provider_name} provider is not initialized")
        
        try:
            # Get user ID from kwargs
            user_id = kwargs.get("user_id", "default_user")
            
            # Initialize chat history for this user if it doesn't exist
            if user_id not in self.chat_histories:
                self.chat_histories[user_id] = []
            
            # Convert messages to Google AI format
            chat = self.model.start_chat(history=[])
            
            # Add messages to chat
            for message in messages:
                role = message["role"]
                content = message["content"]
                
                if role == "user":
                    chat.send_message(content)
                elif role == "assistant":
                    # Add assistant message to history
                    chat.history.append({"role": "model", "parts": [content]})
                elif role == "system":
                    # Add system message to history
                    chat.history.append({"role": "system", "parts": [content]})
            
            # Generate response
            response = await asyncio.to_thread(
                chat.send_message,
                messages[-1]["content"] if messages[-1]["role"] == "user" else ""
            )
            
            # Update chat history
            self.chat_histories[user_id].append({
                "user": messages[-1]["content"] if messages[-1]["role"] == "user" else "",
                "assistant": response.text
            })
            
            # Trim history if needed
            if len(self.chat_histories[user_id]) > self.max_history_turns:
                self.chat_histories[user_id] = self.chat_histories[user_id][-self.max_history_turns:]
            
            # Extract response
            return {
                "role": "assistant",
                "content": response.text
            }
        except Exception as e:
            logger.error(f"Failed to generate chat response: {e}")
            
            # Map exceptions to provider exceptions
            if "API key not valid" in str(e):
                raise ProviderAuthError(f"Invalid API key: {e}") from e
            elif "quota exceeded" in str(e).lower() or "rate limit" in str(e).lower():
                raise ProviderRateLimitError(f"Rate limit exceeded: {e}") from e
            elif "timeout" in str(e).lower():
                raise ProviderTimeoutError(f"Request timed out: {e}") from e
            elif "not found" in str(e).lower():
                raise ProviderNotFoundError(f"Resource not found: {e}") from e
            elif "invalid" in str(e).lower():
                raise ProviderValidationError(f"Invalid request: {e}") from e
            else:
                raise ProviderError(f"Failed to generate chat response: {e}") from e
    
    async def embed_text(self, text: str, **kwargs) -> List[float]:
        """Generate embeddings for text.
        
        Args:
            text: Text to embed
            **kwargs: Additional arguments
            
        Returns:
            Text embeddings
            
        Raises:
            ProviderError: If embedding generation fails
        """
        if not self.initialized:
            raise ProviderNotInitializedError(f"{self.provider_name} provider is not initialized")
        
        try:
            # Generate embeddings
            embedding = await asyncio.to_thread(
                self.embedding_model.embed_content,
                text,
                task_type="retrieval_document"
            )
            
            # Extract embeddings
            return embedding.embedding
        except Exception as e:
            logger.error(f"Failed to generate embeddings: {e}")
            
            # Map exceptions to provider exceptions
            if "API key not valid" in str(e):
                raise ProviderAuthError(f"Invalid API key: {e}") from e
            elif "quota exceeded" in str(e).lower() or "rate limit" in str(e).lower():
                raise ProviderRateLimitError(f"Rate limit exceeded: {e}") from e
            elif "timeout" in str(e).lower():
                raise ProviderTimeoutError(f"Request timed out: {e}") from e
            elif "not found" in str(e).lower():
                raise ProviderNotFoundError(f"Resource not found: {e}") from e
            elif "invalid" in str(e).lower():
                raise ProviderValidationError(f"Invalid request: {e}") from e
            else:
                raise ProviderError(f"Failed to generate embeddings: {e}") from e
