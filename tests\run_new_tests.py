"""
Test runner script for new VoicePal tests.

This script runs the new tests for the VoicePal bot,
focusing on the memory system, UI/button functionality, and TTS provider selection.
"""

import os
import sys
import unittest
import logging
import asyncio

# Add the project root directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

def create_test_suite():
    """Create a test suite containing all new tests."""
    test_suite = unittest.TestSuite()

    # Import test modules
    try:
        from test_enhanced_memory_system import TestEnhancedMemorySystem
        logger.info("Imported test_enhanced_memory_system")
        loader = unittest.TestLoader()
        test_suite.addTest(loader.loadTestsFromTestCase(TestEnhancedMemorySystem))
    except ImportError as e:
        logger.error(f"Error importing test_enhanced_memory_system: {e}")

    try:
        from test_ui_button_functionality import TestUIButtonFunctionality
        logger.info("Imported test_ui_button_functionality")
        loader = unittest.TestLoader()
        test_suite.addTest(loader.loadTestsFromTestCase(TestUIButtonFunctionality))
    except ImportError as e:
        logger.error(f"Error importing test_ui_button_functionality: {e}")

    try:
        from test_tts_provider_selection import TestTTSProviderSelection
        logger.info("Imported test_tts_provider_selection")
        loader = unittest.TestLoader()
        test_suite.addTest(loader.loadTestsFromTestCase(TestTTSProviderSelection))
    except ImportError as e:
        logger.error(f"Error importing test_tts_provider_selection: {e}")

    return test_suite

def run_async_tests(test_case):
    """Run async tests in a test case."""
    for method_name in dir(test_case):
        if method_name.startswith("test_") and asyncio.iscoroutinefunction(getattr(test_case, method_name)):
            logger.info(f"Running async test: {method_name}")
            loop = asyncio.get_event_loop()
            loop.run_until_complete(getattr(test_case, method_name)())

def run_tests():
    """Run all tests and return the result."""
    # Create test suite
    test_suite = create_test_suite()

    # Run tests
    test_runner = unittest.TextTestRunner(verbosity=2)
    result = test_runner.run(test_suite)

    # Return True if all tests passed, False otherwise
    return result.wasSuccessful()

if __name__ == "__main__":
    logger.info("Starting new VoicePal tests")

    # Run tests
    success = run_tests()

    # Exit with appropriate code
    if success:
        logger.info("All tests passed!")
        sys.exit(0)
    else:
        logger.error("Some tests failed!")
        sys.exit(1)
