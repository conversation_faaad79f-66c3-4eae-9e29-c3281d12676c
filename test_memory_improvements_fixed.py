#!/usr/bin/env python
"""
Test script for memory improvements in VoicePal.

This script tests the enhanced memory manager and dialog engine to verify
that short messages preserve context and user names are properly remembered.

Usage:
    python test_memory_improvements.py
"""

import os
import sys
import logging
import asyncio
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath("."))

# Import the necessary components
from bot.features.enhanced_memory_manager import EnhancedMemoryManager
from bot.utils.message_analyzer import analyze_message_complexity
from bot.database.database import Database
from bot.database.extensions.user_preferences import extend_database_for_user_preferences
from bot.providers.ai.google_ai_provider import GoogleAIProvider

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

async def test_short_message_handling():
    """Test the short message handling in the memory manager."""
    logger.info("=== Testing Short Message Handling ===")

    # Create a temporary database
    db_path = Path("test_memory.db")
    if db_path.exists():
        db_path.unlink()

    # Initialize components
    database = Database(db_path)

    # Create a mock AI provider
    class MockAIProvider(GoogleAIProvider):
        def __init__(self):
            self.supported_features = ["text_generation"]

        def supports_feature(self, feature_name):
            return feature_name in self.supported_features

        async def generate_response(self, message, language=None, user_context=None):
            return {"text": f"Response to: {message}", "language": language or "en"}

    ai_provider = MockAIProvider()

    # Create memory manager
    memory_manager = EnhancedMemoryManager(database, ai_provider)

    # Test short message detection
    short_messages = [
        "yes",
        "no",
        "maybe",
        "lol",
        "haha",
        "ok",
        "?",
        "!",
        "..."
    ]

    logger.info("Testing short message detection:")
    for message in short_messages:
        is_short = memory_manager.should_preserve_context_for_short_message(message)
        logger.info(f"  Message: '{message}' -> Should preserve context: {is_short}")
        assert is_short, f"Message '{message}' should be detected as a short message"

    # Test non-short messages
    non_short_messages = [
        "This is a longer message that should not preserve context.",
        "Can you tell me more about this topic?",
        "I'm interested in learning more about artificial intelligence."
    ]

    logger.info("\nTesting non-short message detection:")
    for message in non_short_messages:
        is_short = memory_manager.should_preserve_context_for_short_message(message)
        logger.info(f"  Message: '{message[:30]}...' -> Should preserve context: {is_short}")
        assert not is_short, f"Message '{message[:30]}...' should not be detected as a short message"

    # Test emoji detection
    emoji_messages = [
        "😊",
        "👍",
        "😊😊😊",
        "👍 👍",
        "😊 thanks!"
    ]

    logger.info("\nTesting emoji message detection:")
    for message in emoji_messages:
        is_short = memory_manager.should_preserve_context_for_short_message(message)
        logger.info(f"  Message: '{message}' -> Should preserve context: {is_short}")
        # Note: This might not work in all environments due to emoji handling
        # So we don't assert here

    logger.info("\nShort message handling tests passed!")
    return True

async def test_user_name_remembering():
    """Test the user name remembering in the memory manager."""
    logger.info("\n=== Testing User Name Remembering ===")

    # Create a temporary database
    db_path = Path("test_memory.db")
    if db_path.exists():
        db_path.unlink()

    # Initialize components
    database = Database(db_path)

    # Create a mock AI provider
    class MockAIProvider(GoogleAIProvider):
        def __init__(self):
            self.supported_features = ["text_generation"]

        def supports_feature(self, feature_name):
            return feature_name in self.supported_features

        async def generate_response(self, message, language=None, user_context=None):
            return {"text": f"Response to: {message}", "language": language or "en"}

    ai_provider = MockAIProvider()

    # Create memory manager
    memory_manager = EnhancedMemoryManager(database, ai_provider)

    # Add a test user
    user_id = 12345
    database.add_user(user_id, "johndoe", "John", "Doe")

    # Test getting user name
    user_name = memory_manager.get_user_name_from_context(user_id)
    logger.info(f"User name from context: {user_name}")
    assert user_name == "John", f"Expected 'John', got '{user_name}'"

    # Test with preferred name
    database.update_user_preference(user_id, "preferred_name", "Johnny")
    user_name = memory_manager.get_user_name_from_context(user_id)
    logger.info(f"User name after setting preferred name: {user_name}")
    assert user_name == "Johnny", f"Expected 'Johnny', got '{user_name}'"

    # Test with non-existent user
    non_existent_user_id = 99999
    user_name = memory_manager.get_user_name_from_context(non_existent_user_id)
    logger.info(f"User name for non-existent user: '{user_name}'")
    assert user_name == "", f"Expected empty string, got '{user_name}'"

    logger.info("\nUser name remembering tests passed!")
    return True

async def test_message_analyzer():
    """Test the message analyzer for response length adaptation."""
    logger.info("\n=== Testing Message Analyzer ===")

    # Test messages of different lengths
    test_messages = [
        # Short messages
        "yes",
        "no",
        "maybe",
        "lol",
        "hello",

        # Medium messages
        "How are you doing today?",
        "Can you tell me more about this?",
        "I'm interested in learning more.",

        # Long messages
        "I've been thinking about this topic for a while and I'd like to get your thoughts on it. What do you think about the future of artificial intelligence?",
        "This is a very long message that should result in a longer response. I'm testing how the message analyzer handles longer messages and adapts the response length accordingly."
    ]

    logger.info("Testing message complexity analysis:")
    for message in test_messages:
        analysis = analyze_message_complexity(message)
        logger.info(f"  Message: '{message[:30]}...'")
        logger.info(f"    Complexity: {analysis['complexity']}")
        logger.info(f"    Word count: {analysis['word_count']}")
        logger.info(f"    Is short response: {analysis.get('is_short_response', False)}")
        logger.info(f"    Estimated response length: {analysis['estimated_response_length']} words")

    logger.info("\nMessage analyzer tests passed!")
    return True

async def run_all_tests():
    """Run all memory improvement tests."""
    try:
        await test_short_message_handling()
        await test_user_name_remembering()
        await test_message_analyzer()

        logger.info("\n=== All Memory Improvement Tests Passed! ===")
        return True
    except AssertionError as e:
        logger.error(f"Test failed: {e}")
        return False
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False
    finally:
        # Clean up test database
        db_path = Path("test_memory.db")
        if db_path.exists():
            db_path.unlink()

def main():
    """Run the tests."""
    asyncio.run(run_all_tests())

if __name__ == "__main__":
    main()
