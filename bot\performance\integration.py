"""
Performance integration module for VoicePal.

This module integrates performance optimization features with the main bot.
"""

import logging
import async<PERSON>
from typing import Optional
from telegram.ext import CommandHandler

from bot.performance.optimization_manager import OptimizationManager

logger = logging.getLogger(__name__)

class PerformanceIntegration:
    """Performance integration for VoicePal bot."""
    
    def __init__(self, bot_instance, optimization_manager: OptimizationManager):
        """
        Initialize performance integration.
        
        Args:
            bot_instance: VoicePal bot instance
            optimization_manager: Optimization manager instance
        """
        self.bot = bot_instance
        self.optimization = optimization_manager
        
        # Add performance commands to bot
        self._add_performance_commands()
        
        logger.info("Performance integration initialized")
    
    def _add_performance_commands(self):
        """Add performance-related commands to the bot."""
        try:
            if hasattr(self.bot, 'application'):
                # Performance status command (admin only)
                self.bot.application.add_handler(CommandHandler("performance", self._performance_command))
                self.bot.application.add_handler(CommandHandler("perf_summary", self._performance_summary_command))
                self.bot.application.add_handler(CommandHandler("cache_stats", self._cache_stats_command))
                self.bot.application.add_handler(CommandHandler("cleanup", self._cleanup_command))
                
                logger.info("Performance commands added to bot")
        except Exception as e:
            logger.error(f"Failed to add performance commands: {e}")
    
    async def _performance_command(self, update, context):
        """Handle /performance command."""
        try:
            user_id = update.effective_user.id
            
            # Check if user is admin
            if hasattr(self.bot, 'config_manager'):
                telegram_config = self.bot.config_manager.get_telegram_config()
                admin_user_ids = telegram_config.get("admin_user_ids", [])
                
                if user_id not in admin_user_ids:
                    await update.message.reply_text("❌ This command is only available to administrators.")
                    return
            
            # Get optimization status
            status = self.optimization.get_optimization_status()
            
            status_text = "⚡ **Performance Status**\n\n"
            
            # Database pool status
            db_status = status['database_pool']
            if db_status['enabled']:
                db_stats = db_status['stats']
                status_text += f"🗄️ **Database Pool**: ✅ Enabled\n"
                status_text += f"   • Connections: {db_stats.get('active_connections', 0)}/{db_stats.get('total_connections', 0)}\n"
                status_text += f"   • Avg Wait: {db_stats.get('average_wait_time', 0):.3f}s\n"
            else:
                status_text += "🗄️ **Database Pool**: ❌ Disabled\n"
            
            # Redis status
            redis_status = status['redis']
            if redis_status['enabled']:
                if redis_status['available']:
                    redis_stats = redis_status['stats']
                    status_text += f"🔴 **Redis**: ✅ Available\n"
                    status_text += f"   • Hit Rate: {redis_stats.get('hit_rate', 0):.2%}\n"
                    status_text += f"   • Commands: {redis_stats.get('total_commands', 0)}\n"
                else:
                    status_text += "🔴 **Redis**: ❌ Unavailable\n"
            else:
                status_text += "🔴 **Redis**: ❌ Disabled\n"
            
            # Cache manager status
            cache_status = status['cache_manager']
            if cache_status['enabled']:
                cache_stats = cache_status['stats']
                status_text += f"💾 **Cache**: ✅ Enabled\n"
                status_text += f"   • Hit Rate: {cache_stats.get('hit_rate', 0):.2%}\n"
                status_text += f"   • Entries: {cache_stats.get('total_entries', 0)}\n"
            else:
                status_text += "💾 **Cache**: ❌ Disabled\n"
            
            # Async processor status
            async_status = status['async_processor']
            if async_status['enabled']:
                async_stats = async_status['stats']
                status_text += f"⚙️ **Async Processor**: ✅ Enabled\n"
                status_text += f"   • Tasks: {async_stats.get('completed_tasks', 0)}/{async_stats.get('total_tasks', 0)}\n"
                status_text += f"   • Avg Time: {async_stats.get('average_execution_time', 0):.3f}s\n"
            else:
                status_text += "⚙️ **Async Processor**: ❌ Disabled\n"
            
            await update.message.reply_text(status_text, parse_mode="Markdown")
            
        except Exception as e:
            logger.error(f"Performance command failed: {e}")
            await update.message.reply_text("❌ Failed to retrieve performance status.")
    
    async def _performance_summary_command(self, update, context):
        """Handle /perf_summary command."""
        try:
            user_id = update.effective_user.id
            
            # Check if user is admin
            if hasattr(self.bot, 'config_manager'):
                telegram_config = self.bot.config_manager.get_telegram_config()
                admin_user_ids = telegram_config.get("admin_user_ids", [])
                
                if user_id not in admin_user_ids:
                    await update.message.reply_text("❌ This command is only available to administrators.")
                    return
            
            # Get performance summary
            summary = self.optimization.get_performance_summary()
            
            summary_text = "📊 **Performance Summary**\n\n"
            
            # System metrics
            if 'system' in summary.get('components', {}):
                system = summary['components']['system']
                summary_text += f"🖥️ **System**:\n"
                summary_text += f"   • CPU: {system.get('avg_cpu_percent', 0):.1f}%\n"
                summary_text += f"   • Memory: {system.get('avg_memory_percent', 0):.1f}%\n"
                summary_text += f"   • Response Time: {system.get('avg_response_time', 0):.3f}s\n"
                summary_text += f"   • Requests/sec: {system.get('avg_requests_per_second', 0):.1f}\n"
                summary_text += f"   • Error Rate: {system.get('avg_error_rate', 0):.2%}\n\n"
            
            # Database metrics
            if 'database' in summary.get('components', {}):
                database = summary['components']['database']
                summary_text += f"🗄️ **Database**:\n"
                summary_text += f"   • Pool: {'✅' if database.get('connection_pool') else '❌'}\n"
                summary_text += f"   • Connections: {database.get('active_connections', 0)}\n"
                summary_text += f"   • Wait Time: {database.get('average_wait_time', 0):.3f}s\n\n"
            
            # Cache metrics
            if 'cache' in summary.get('components', {}):
                cache = summary['components']['cache']
                summary_text += f"💾 **Cache**:\n"
                summary_text += f"   • Hit Rate: {cache.get('hit_rate', 0):.2%}\n"
                summary_text += f"   • Entries: {cache.get('total_entries', 0)}\n"
                summary_text += f"   • Evictions: {cache.get('evictions', 0)}\n\n"
            
            await update.message.reply_text(summary_text, parse_mode="Markdown")
            
        except Exception as e:
            logger.error(f"Performance summary command failed: {e}")
            await update.message.reply_text("❌ Failed to retrieve performance summary.")
    
    async def _cache_stats_command(self, update, context):
        """Handle /cache_stats command."""
        try:
            user_id = update.effective_user.id
            
            # Check if user is admin
            if hasattr(self.bot, 'config_manager'):
                telegram_config = self.bot.config_manager.get_telegram_config()
                admin_user_ids = telegram_config.get("admin_user_ids", [])
                
                if user_id not in admin_user_ids:
                    await update.message.reply_text("❌ This command is only available to administrators.")
                    return
            
            cache_manager = self.optimization.get_cache_manager()
            if not cache_manager:
                await update.message.reply_text("❌ Cache manager is not available.")
                return
            
            # Get cache statistics
            stats = cache_manager.get_stats()
            
            stats_text = "💾 **Cache Statistics**\n\n"
            stats_text += f"📈 **Performance**:\n"
            stats_text += f"   • Hit Rate: {stats.hit_rate:.2%}\n"
            stats_text += f"   • Hits: {stats.hits:,}\n"
            stats_text += f"   • Misses: {stats.misses:,}\n\n"
            
            stats_text += f"📊 **Storage**:\n"
            stats_text += f"   • Total Entries: {stats.total_entries:,}\n"
            stats_text += f"   • Memory Usage: {stats.memory_usage / 1024:.1f} KB\n"
            stats_text += f"   • Evictions: {stats.evictions:,}\n"
            stats_text += f"   • Expired: {stats.expired_entries:,}\n"
            
            await update.message.reply_text(stats_text, parse_mode="Markdown")
            
        except Exception as e:
            logger.error(f"Cache stats command failed: {e}")
            await update.message.reply_text("❌ Failed to retrieve cache statistics.")
    
    async def _cleanup_command(self, update, context):
        """Handle /cleanup command."""
        try:
            user_id = update.effective_user.id
            
            # Check if user is admin
            if hasattr(self.bot, 'config_manager'):
                telegram_config = self.bot.config_manager.get_telegram_config()
                admin_user_ids = telegram_config.get("admin_user_ids", [])
                
                if user_id not in admin_user_ids:
                    await update.message.reply_text("❌ This command is only available to administrators.")
                    return
            
            await update.message.reply_text("🧹 Starting cleanup process...")
            
            # Perform cleanup
            self.optimization.cleanup_resources()
            
            await update.message.reply_text("✅ Cleanup completed successfully.")
            
        except Exception as e:
            logger.error(f"Cleanup command failed: {e}")
            await update.message.reply_text("❌ Cleanup failed.")
    
    def wrap_message_handler(self, original_handler):
        """
        Wrap message handler with performance monitoring.
        
        Args:
            original_handler: Original message handler function
            
        Returns:
            Wrapped handler function
        """
        async def wrapped_handler(update, context):
            import time
            start_time = time.time()
            success = True
            
            try:
                # Execute original handler
                result = await original_handler(update, context)
                return result
            except Exception as e:
                success = False
                raise e
            finally:
                # Record performance metrics
                response_time = time.time() - start_time
                self.optimization.record_request_performance(response_time, success)
        
        return wrapped_handler
    
    def wrap_voice_handler(self, original_handler):
        """
        Wrap voice handler with async processing.
        
        Args:
            original_handler: Original voice handler function
            
        Returns:
            Wrapped handler function
        """
        async def wrapped_handler(update, context):
            async_processor = self.optimization.get_async_processor()
            
            if async_processor:
                # Process voice asynchronously
                task_id = await async_processor.submit_task(
                    original_handler,
                    update,
                    context,
                    priority=async_processor.TaskPriority.HIGH
                )
                
                # Wait for completion
                return await async_processor.wait_for_task(task_id, timeout=30)
            else:
                # Fallback to synchronous processing
                return await original_handler(update, context)
        
        return wrapped_handler
    
    def optimize_database_queries(self, query_func):
        """
        Optimize database queries with connection pooling.
        
        Args:
            query_func: Function that executes database queries
            
        Returns:
            Optimized query function
        """
        def optimized_query(*args, **kwargs):
            # Use connection pool if available
            if self.optimization.database_pool:
                with self.optimization.get_database_connection() as conn:
                    return query_func(conn, *args, **kwargs)
            else:
                # Fallback to original function
                return query_func(*args, **kwargs)
        
        return optimized_query
    
    def cache_ai_responses(self, ai_func):
        """
        Cache AI responses for better performance.
        
        Args:
            ai_func: AI response generation function
            
        Returns:
            Cached AI function
        """
        async def cached_ai_func(prompt, *args, **kwargs):
            cache_manager = self.optimization.get_cache_manager()
            
            if cache_manager:
                # Try to get cached response
                cached_response = cache_manager.get_cached_ai_response(prompt)
                if cached_response:
                    return cached_response
                
                # Generate new response
                response = await ai_func(prompt, *args, **kwargs)
                
                # Cache the response
                cache_manager.cache_ai_response(prompt, response)
                
                return response
            else:
                # No caching available
                return await ai_func(prompt, *args, **kwargs)
        
        return cached_ai_func

def integrate_performance_with_bot(bot_instance, redis_config=None) -> Optional[PerformanceIntegration]:
    """
    Integrate performance optimization features with the VoicePal bot.
    
    Args:
        bot_instance: VoicePal bot instance
        redis_config: Redis configuration
        
    Returns:
        PerformanceIntegration instance if successful, None otherwise
    """
    try:
        # Get database path from bot
        database_path = getattr(bot_instance.database, 'db_file', 'voicepal.db')
        
        # Create optimization manager
        optimization_manager = OptimizationManager(
            database_path=database_path,
            redis_config=redis_config,
            enable_database_pool=True,
            enable_redis=redis_config is not None,
            enable_async_processing=True,
            enable_caching=True,
            enable_monitoring=True
        )
        
        # Create performance integration
        performance_integration = PerformanceIntegration(bot_instance, optimization_manager)
        
        # Store reference in bot instance
        bot_instance.optimization_manager = optimization_manager
        bot_instance.performance_integration = performance_integration
        
        # Register with feature registry if available
        if hasattr(bot_instance, 'feature_registry'):
            bot_instance.feature_registry.register_feature(
                "performance_optimization",
                True,
                "Comprehensive performance optimization including database pooling, caching, and async processing"
            )
        
        logger.info("Performance optimization successfully integrated with VoicePal bot")
        return performance_integration
        
    except Exception as e:
        logger.error(f"Failed to integrate performance optimization with bot: {e}")
        return None

async def start_performance_optimization(bot_instance):
    """
    Start performance optimization components.
    
    Args:
        bot_instance: VoicePal bot instance
    """
    try:
        if hasattr(bot_instance, 'optimization_manager'):
            await bot_instance.optimization_manager.start_optimization()
            logger.info("Performance optimization started")
    except Exception as e:
        logger.error(f"Failed to start performance optimization: {e}")

async def stop_performance_optimization(bot_instance):
    """
    Stop performance optimization components.
    
    Args:
        bot_instance: VoicePal bot instance
    """
    try:
        if hasattr(bot_instance, 'optimization_manager'):
            await bot_instance.optimization_manager.stop_optimization()
            logger.info("Performance optimization stopped")
    except Exception as e:
        logger.error(f"Failed to stop performance optimization: {e}")
