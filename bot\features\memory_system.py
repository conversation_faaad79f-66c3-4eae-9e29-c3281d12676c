"""
Memory system initialization for VoicePal.

This module provides functions to initialize and set up the enhanced memory system.
"""

import logging
from typing import Dict, Any, Optional

from bot.features.enhanced_memory_manager import EnhancedMemoryManager
from bot.core.enhanced_dialog_engine import EnhancedDialogEngine
from bot.database.extensions.memory import extend_database_for_memory

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

def initialize_memory_system(database, ai_provider, user_manager, config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Initialize the enhanced memory system.

    Args:
        database: Database instance
        ai_provider: AI provider instance
        user_manager: User manager instance
        config: Configuration dictionary

    Returns:
        Dict containing memory manager and dialog engine instances
    """
    try:
        # Extend database with memory-related methods
        extend_database_for_memory(database)

        # Create enhanced memory manager
        memory_manager = EnhancedMemoryManager(
            database=database,
            ai_provider=ai_provider,
            config=config,
            user_manager=user_manager  # Pass user_manager for user profile access
        )

        # Create enhanced dialog engine
        dialog_engine = EnhancedDialogEngine(
            ai_provider=ai_provider,
            memory_manager=memory_manager,
            user_manager=user_manager
        )

        logger.info("Enhanced memory system initialized successfully")

        return {
            "memory_manager": memory_manager,
            "dialog_engine": dialog_engine
        }
    except Exception as e:
        logger.error(f"Error initializing enhanced memory system: {e}")
        raise

def get_memory_config(config_manager) -> Dict[str, Any]:
    """
    Get memory configuration from config manager.

    Args:
        config_manager: Config manager instance

    Returns:
        Dict containing memory configuration
    """
    try:
        # Get memory configuration from config manager
        memory_config = config_manager.get_feature_config("memory") or {}

        # Set default values if not present
        if "conversation_memory" not in memory_config:
            memory_config["conversation_memory"] = 20  # Increased from 10 to 20

        if "summary_update_frequency" not in memory_config:
            memory_config["summary_update_frequency"] = 24

        if "token_limit" not in memory_config:
            memory_config["token_limit"] = 3000  # Increased from 2000 to 3000

        if "importance_threshold" not in memory_config:
            memory_config["importance_threshold"] = 0.5

        return memory_config
    except Exception as e:
        logger.error(f"Error getting memory configuration: {e}")
        return {
            "conversation_memory": 10,
            "summary_update_frequency": 24,
            "token_limit": 2000,
            "importance_threshold": 0.5
        }
