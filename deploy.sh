#!/bin/bash
# VoicePal Bot - Production Deployment Script
# ===========================================
# Automated deployment script for VoicePal bot

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Check if running on supported platform
check_platform() {
    log "🔍 Checking deployment platform..."
    
    if [ ! -z "$RENDER" ]; then
        PLATFORM="render"
        log "📡 Detected Render.com platform"
    elif [ ! -z "$HEROKU" ]; then
        PLATFORM="heroku"
        log "🟣 Detected Heroku platform"
    elif [ ! -z "$RAILWAY" ]; then
        PLATFORM="railway"
        log "🚂 Detected Railway platform"
    else
        PLATFORM="generic"
        log "🐳 Generic Docker deployment"
    fi
}

# Validate environment variables
validate_env() {
    log "🔐 Validating environment variables..."
    
    required_vars=(
        "TELEGRAM_TOKEN"
        "DEEPGRAM_API_KEY"
        "GOOGLE_AI_API_KEY"
    )
    
    missing_vars=()
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            missing_vars+=("$var")
        fi
    done
    
    if [ ${#missing_vars[@]} -ne 0 ]; then
        error "Missing required environment variables:"
        for var in "${missing_vars[@]}"; do
            error "  - $var"
        done
        exit 1
    fi
    
    success "✅ All required environment variables are set"
}

# Run health check
health_check() {
    log "🏥 Running health check..."
    
    if python run.py --health; then
        success "✅ Health check passed"
    else
        error "❌ Health check failed"
        exit 1
    fi
}

# Install dependencies
install_deps() {
    log "📦 Installing dependencies..."
    
    if [ -f "requirements.txt" ]; then
        pip install --no-cache-dir -r requirements.txt
        success "✅ Dependencies installed"
    else
        error "❌ requirements.txt not found"
        exit 1
    fi
}

# Database migration
migrate_db() {
    log "🗄️ Running database migrations..."
    
    python -c "
import sys
sys.path.insert(0, '.')
from bot.main import VoicePalBot
bot = VoicePalBot()
print('✅ Database initialized successfully')
bot.database.close()
"
    
    success "✅ Database migration completed"
}

# Start the bot
start_bot() {
    log "🚀 Starting VoicePal bot..."
    
    case $PLATFORM in
        "render"|"heroku"|"railway")
            log "📡 Starting in webhook mode for $PLATFORM"
            exec python run.py --webhook
            ;;
        *)
            log "📡 Starting in polling mode"
            exec python run.py
            ;;
    esac
}

# Main deployment function
deploy() {
    log "🎤 VoicePal Bot - Production Deployment"
    log "======================================"
    
    check_platform
    validate_env
    install_deps
    migrate_db
    health_check
    start_bot
}

# Handle script arguments
case "${1:-deploy}" in
    "deploy")
        deploy
        ;;
    "health")
        health_check
        ;;
    "migrate")
        migrate_db
        ;;
    "install")
        install_deps
        ;;
    *)
        echo "Usage: $0 [deploy|health|migrate|install]"
        echo ""
        echo "Commands:"
        echo "  deploy   - Full deployment (default)"
        echo "  health   - Run health check only"
        echo "  migrate  - Run database migration only"
        echo "  install  - Install dependencies only"
        exit 1
        ;;
esac
