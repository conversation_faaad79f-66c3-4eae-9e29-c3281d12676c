# VoicePal Test Instructions

This document provides instructions for testing the Google AI and Dia TTS integration in VoicePal.

## Prerequisites

1. Make sure you have all the required API keys:
   - `GOOGLE_AI_API_KEY`: For Google AI text generation
   - `HF_API_TOKEN`: For Hugging Face Dia TTS
   - `DEEPGRAM_API_KEY`: For speech-to-text conversion

2. Update your `.env` file with these keys:

```
# API Keys
DEEPGRAM_API_KEY=your_deepgram_api_key_here
GOOGLE_AI_API_KEY=your_google_ai_api_key_here
HF_API_TOKEN=*************************************

# AI and TTS Configuration
AI_PROVIDER=google_ai  # simple or google_ai
TTS_PROVIDER=dia       # google or dia
```

3. Install the required dependencies:

```bash
pip install -r requirements.txt
```

## Test Scripts

### 1. Basic Component Tests

The `test_google_ai_dia.py` script allows you to test individual components:

```bash
# Test Google AI with Google TTS
python test_google_ai_dia.py --ai google_ai --tts google

# Test simple AI with Dia TTS
python test_google_ai_dia.py --ai simple --tts dia

# Test Google AI with Dia TTS (full upgrade)
python test_google_ai_dia.py --ai google_ai --tts dia
```

### 2. Full Flow Tests

The `test_full_flow.py` script demonstrates the complete flow:

```bash
# Test Audio to Audio flow with Dia TTS
python test_full_flow.py --mode audio

# Test Text to Text flow with Google AI
python test_full_flow.py --mode text
```

## What to Expect

### Audio to Audio Flow

1. The script creates test audio files with sample phrases
2. Transcribes the audio using Deepgram
3. Generates a response using the AI
4. Converts the response to speech using Dia TTS
5. Plays the audio response (if supported by your system)

### Text to Text Flow

1. The script sends sample text phrases to Google AI
2. Displays the AI's responses

## Troubleshooting

If you encounter issues:

1. Check that all API keys are correctly set in your `.env` file
2. Verify that all dependencies are installed
3. Look for error messages in the console output

For Dia TTS issues:
- The Hugging Face API may have rate limits
- Try using the `--tts google` option as a fallback

For Google AI issues:
- The Google AI API may have rate limits
- Try using the `--ai simple` option as a fallback

## Next Steps

After testing, you can:

1. Integrate these components into your main VoicePal bot
2. Adjust the configuration in your production environment
3. Monitor performance and user feedback
