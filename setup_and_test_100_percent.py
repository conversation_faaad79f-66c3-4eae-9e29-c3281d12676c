#!/usr/bin/env python3
"""
Setup Environment and Run 100% Production Tests

This script:
1. Loads API keys from config.json
2. Sets up environment variables
3. Runs comprehensive production tests
4. Validates deployment readiness
"""

import os
import sys
import json
import asyncio
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def load_config_and_set_env():
    """Load configuration and set environment variables."""
    logger.info("📋 Loading configuration from config.json...")
    
    try:
        with open('config.json', 'r') as f:
            config = json.load(f)
        
        # Set environment variables from config
        env_vars_set = []
        
        # Telegram configuration
        if 'telegram' in config:
            telegram_config = config['telegram']
            if 'token' in telegram_config:
                os.environ['BOT_TOKEN'] = telegram_config['token']
                env_vars_set.append('BOT_TOKEN')
            
            if 'payment_provider_token' in telegram_config:
                os.environ['PAYMENT_PROVIDER_TOKEN'] = telegram_config['payment_provider_token']
                env_vars_set.append('PAYMENT_PROVIDER_TOKEN')
        
        # Provider API keys
        if 'providers' in config:
            providers = config['providers']
            
            # Deepgram
            if 'stt' in providers and providers['stt'].get('type') == 'deepgram':
                os.environ['DEEPGRAM_API_KEY'] = providers['stt']['api_key']
                env_vars_set.append('DEEPGRAM_API_KEY')
            
            if 'tts' in providers and providers['tts'].get('type') == 'deepgram':
                os.environ['DEEPGRAM_API_KEY'] = providers['tts']['api_key']
                env_vars_set.append('DEEPGRAM_API_KEY (TTS)')
            
            # Google AI
            if 'ai' in providers and providers['ai'].get('type') == 'google_ai':
                os.environ['GOOGLE_AI_API_KEY'] = providers['ai']['api_key']
                env_vars_set.append('GOOGLE_AI_API_KEY')
        
        # Check for ElevenLabs in environment (seems to be working)
        if os.getenv('ELEVENLABS_API_KEY'):
            env_vars_set.append('ELEVENLABS_API_KEY (from env)')
        
        # Set additional environment variables for production
        os.environ['ENVIRONMENT'] = 'production'
        os.environ['ENABLE_MEMORY'] = 'true'
        os.environ['ENABLE_MOOD_TRACKING'] = 'true'
        os.environ['ENABLE_PERSONALIZATION'] = 'true'
        os.environ['PAYMENT_PROVIDER'] = 'telegram_stars'
        
        logger.info(f"✅ Set {len(env_vars_set)} environment variables:")
        for var in env_vars_set:
            logger.info(f"   • {var}")
        
        return True, env_vars_set
        
    except FileNotFoundError:
        logger.error("❌ config.json not found")
        return False, []
    except json.JSONDecodeError as e:
        logger.error(f"❌ Invalid JSON in config.json: {e}")
        return False, []
    except Exception as e:
        logger.error(f"❌ Error loading config: {e}")
        return False, []

def check_critical_apis():
    """Check if critical APIs are available."""
    logger.info("🔍 Checking critical API availability...")
    
    critical_apis = {
        'BOT_TOKEN': 'Telegram Bot',
        'DEEPGRAM_API_KEY': 'Voice Processing',
        'GOOGLE_AI_API_KEY': 'AI Responses'
    }
    
    missing = []
    available = []
    
    for api, description in critical_apis.items():
        if os.getenv(api):
            available.append(f"{api} ({description})")
        else:
            missing.append(f"{api} ({description})")
    
    logger.info(f"✅ Available APIs: {len(available)}")
    for api in available:
        logger.info(f"   • {api}")
    
    if missing:
        logger.warning(f"⚠️ Missing APIs: {len(missing)}")
        for api in missing:
            logger.warning(f"   • {api}")
    
    return len(missing) == 0, missing

async def run_comprehensive_tests():
    """Run the comprehensive production test suite."""
    logger.info("🚀 Running 100% Production Test Suite...")
    
    try:
        # Import and run the production test suite
        from production_100_percent_test import Production100PercentTestSuite
        
        suite = Production100PercentTestSuite()
        report = await suite.run_all_tests()
        
        return report
        
    except ImportError as e:
        logger.error(f"❌ Cannot import test suite: {e}")
        return None
    except Exception as e:
        logger.error(f"❌ Error running tests: {e}")
        return None

def analyze_deployment_readiness(report):
    """Analyze deployment readiness based on test results."""
    if not report:
        return False, "Test suite failed to run"
    
    success_rate = float(report.get('success_rate', '0%').replace('%', ''))
    critical_failures = len(report.get('critical_failures', []))
    
    if critical_failures == 0 and success_rate >= 95:
        return True, f"✅ READY FOR DEPLOYMENT - {success_rate}% success rate"
    elif critical_failures == 0 and success_rate >= 85:
        return True, f"⚠️ MOSTLY READY - {success_rate}% success rate, minor issues"
    elif critical_failures <= 2:
        return False, f"🔧 NEEDS FIXES - {critical_failures} critical failures"
    else:
        return False, f"❌ NOT READY - {critical_failures} critical failures"

def generate_deployment_instructions(ready, message, report):
    """Generate deployment instructions."""
    instructions = []
    
    instructions.append("🚀 DEPLOYMENT INSTRUCTIONS")
    instructions.append("=" * 50)
    instructions.append(f"Status: {message}")
    instructions.append("")
    
    if ready:
        instructions.extend([
            "✅ YOUR BOT IS READY FOR DEPLOYMENT!",
            "",
            "📋 DEPLOYMENT CHECKLIST:",
            "1. ✅ API keys configured and validated",
            "2. ✅ Voice processing working",
            "3. ✅ Payment system ready",
            "4. ✅ All tests passing",
            "",
            "🚀 DEPLOY TO RENDER:",
            "1. Push your code to GitHub",
            "2. Connect Render to your GitHub repo",
            "3. Set environment variables in Render dashboard:",
        ])
        
        # Add environment variables to set in Render
        env_vars = [
            "BOT_TOKEN",
            "DEEPGRAM_API_KEY", 
            "GOOGLE_AI_API_KEY",
            "ELEVENLABS_API_KEY",
            "PAYMENT_PROVIDER_TOKEN"
        ]
        
        for var in env_vars:
            value = os.getenv(var, 'your_value_here')
            if value and not value.startswith('your_'):
                instructions.append(f"   • {var}={value[:20]}...")
            else:
                instructions.append(f"   • {var}=<your_value_here>")
        
        instructions.extend([
            "",
            "4. Deploy and test webhook functionality",
            "5. Monitor logs for any issues",
            "",
            "💰 MONETIZATION READY:",
            "• Telegram Stars payments configured",
            "• Credit system operational", 
            "• Voice processing optimized",
            "• User experience tested"
        ])
    else:
        instructions.extend([
            "❌ DEPLOYMENT BLOCKED - Issues need to be resolved",
            "",
            "🔧 REQUIRED FIXES:"
        ])
        
        if report and report.get('critical_failures'):
            for failure in report['critical_failures']:
                instructions.append(f"   • {failure}")
        
        instructions.extend([
            "",
            "📋 NEXT STEPS:",
            "1. Fix critical issues listed above",
            "2. Run this script again to verify fixes",
            "3. Ensure all API keys are valid",
            "4. Test bot functionality locally first"
        ])
    
    return "\n".join(instructions)

async def main():
    """Main function."""
    print("🎯 MoneyMule Bot - 100% Production Readiness Setup & Test")
    print("=" * 60)
    
    # Step 1: Load configuration
    config_loaded, env_vars = load_config_and_set_env()
    if not config_loaded:
        print("❌ Failed to load configuration. Please check config.json")
        return
    
    # Step 2: Check critical APIs
    apis_ready, missing_apis = check_critical_apis()
    if not apis_ready:
        print(f"\n❌ Missing critical APIs: {missing_apis}")
        print("Please ensure all required API keys are configured.")
        return
    
    # Step 3: Run comprehensive tests
    print("\n🧪 Running comprehensive production tests...")
    report = await run_comprehensive_tests()
    
    if not report:
        print("❌ Failed to run test suite")
        return
    
    # Step 4: Analyze results
    ready, message = analyze_deployment_readiness(report)
    
    # Step 5: Generate instructions
    instructions = generate_deployment_instructions(ready, message, report)
    
    # Print results
    print("\n" + "=" * 60)
    print("FINAL RESULTS")
    print("=" * 60)
    print(f"Overall Status: {report.get('overall_status', 'Unknown')}")
    print(f"Success Rate: {report.get('success_rate', 'Unknown')}")
    print(f"Recommendation: {report.get('recommendation', 'Unknown')}")
    
    # Print API keys status
    if 'api_keys_status' in report:
        print(f"\n🔑 API KEYS STATUS:")
        for key, status in report['api_keys_status'].items():
            status_icon = "✅" if status == "VALID" else "❌" if status == "INVALID" else "⚠️"
            print(f"  {status_icon} {key}: {status}")
    
    print(f"\n{instructions}")
    
    # Save comprehensive report
    final_report = {
        'deployment_ready': ready,
        'deployment_message': message,
        'environment_variables_set': env_vars,
        'test_results': report,
        'instructions': instructions
    }
    
    with open('final_deployment_report.json', 'w') as f:
        json.dump(final_report, f, indent=2)
    
    print(f"\n📄 Complete report saved to: final_deployment_report.json")
    
    if ready:
        print("\n🎉 CONGRATULATIONS! Your bot is 100% ready for production!")
        print("💰 You can now deploy and start making money!")
    else:
        print(f"\n🔧 Please fix the issues above and run again.")

if __name__ == "__main__":
    asyncio.run(main())
