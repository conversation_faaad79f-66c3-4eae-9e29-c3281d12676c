"""
Memory repositories for VoicePal.

This module provides the memory-related repositories for the VoicePal database.
"""

import logging
from typing import Dict, List, Any, Optional, Union, Tuple, Set
from datetime import datetime

from bot.database.core.connection import DatabaseConnection
from bot.database.core.exceptions import (
    DatabaseError,
    DatabaseNotFoundError,
    DatabaseDuplicateError
)
from bot.database.repositories.repository import Repository
from bot.database.models.memory import Memory, MemoryTag

# Set up logging
logger = logging.getLogger(__name__)

class MemoryRepository(Repository[Memory]):
    """Repository for Memory model."""
    
    _model_class = Memory
    
    def find_by_user(self, user_id: str, limit: Optional[int] = None, offset: Optional[int] = None) -> List[Memory]:
        """Find memories by user ID.
        
        Args:
            user_id: User ID
            limit: Maximum number of memories to return
            offset: Offset for pagination
            
        Returns:
            List of memories
            
        Raises:
            DatabaseError: If query fails
        """
        return self.find_all(
            where={"user_id": user_id},
            order_by="importance DESC, last_accessed DESC",
            limit=limit,
            offset=offset
        )
    
    def find_by_importance(self, user_id: str, min_importance: int = 1, max_importance: int = 10) -> List[Memory]:
        """Find memories by importance.
        
        Args:
            user_id: User ID
            min_importance: Minimum importance
            max_importance: Maximum importance
            
        Returns:
            List of memories
            
        Raises:
            DatabaseError: If query fails
        """
        try:
            query = f"""
                SELECT * FROM {self._model_class.get_table_name()}
                WHERE user_id = ? AND importance BETWEEN ? AND ?
                ORDER BY importance DESC, last_accessed DESC
            """
            
            cursor = self.connection.execute(query, (user_id, min_importance, max_importance))
            rows = cursor.fetchall()
            
            # Convert rows to model instances
            memories = []
            for row in cursor:
                data = dict(row)
                memory = self._model_class.from_dict(data)
                memories.append(memory)
            
            return memories
        except Exception as e:
            logger.error(f"Failed to find memories by importance: {e}")
            raise DatabaseError(f"Failed to find memories by importance: {e}") from e
    
    def find_by_content(self, user_id: str, search_term: str) -> List[Memory]:
        """Find memories by content.
        
        Args:
            user_id: User ID
            search_term: Search term
            
        Returns:
            List of memories
            
        Raises:
            DatabaseError: If query fails
        """
        try:
            query = f"""
                SELECT * FROM {self._model_class.get_table_name()}
                WHERE user_id = ? AND content LIKE ?
                ORDER BY importance DESC, last_accessed DESC
            """
            
            cursor = self.connection.execute(query, (user_id, f"%{search_term}%"))
            rows = cursor.fetchall()
            
            # Convert rows to model instances
            memories = []
            for row in cursor:
                data = dict(row)
                memory = self._model_class.from_dict(data)
                memories.append(memory)
            
            return memories
        except Exception as e:
            logger.error(f"Failed to find memories by content: {e}")
            raise DatabaseError(f"Failed to find memories by content: {e}") from e
    
    def find_by_tag(self, user_id: str, tag: str) -> List[Memory]:
        """Find memories by tag.
        
        Args:
            user_id: User ID
            tag: Tag name
            
        Returns:
            List of memories
            
        Raises:
            DatabaseError: If query fails
        """
        try:
            query = f"""
                SELECT m.* FROM {self._model_class.get_table_name()} m
                JOIN memory_tags t ON m.memory_id = t.memory_id
                WHERE m.user_id = ? AND t.tag = ?
                ORDER BY m.importance DESC, m.last_accessed DESC
            """
            
            cursor = self.connection.execute(query, (user_id, tag))
            rows = cursor.fetchall()
            
            # Convert rows to model instances
            memories = []
            for row in cursor:
                data = dict(row)
                memory = self._model_class.from_dict(data)
                memories.append(memory)
            
            return memories
        except Exception as e:
            logger.error(f"Failed to find memories by tag: {e}")
            raise DatabaseError(f"Failed to find memories by tag: {e}") from e
    
    def create_memory(self, user_id: str, content: str, importance: int = 1, tags: Optional[List[str]] = None) -> Memory:
        """Create a memory.
        
        Args:
            user_id: User ID
            content: Memory content
            importance: Memory importance
            tags: Memory tags
            
        Returns:
            Created memory
            
        Raises:
            DatabaseError: If creation fails
        """
        try:
            with self.transaction():
                # Create memory
                memory = Memory(
                    user_id=user_id,
                    content=content,
                    importance=importance,
                    last_accessed=datetime.now().isoformat()
                )
                
                memory = self.create(memory)
                
                # Add tags
                if tags:
                    tag_repo = MemoryTagRepository(self.connection)
                    for tag in tags:
                        tag_repo.create(MemoryTag(
                            memory_id=memory.memory_id,
                            tag=tag
                        ))
                
                return memory
        except Exception as e:
            logger.error(f"Failed to create memory: {e}")
            raise DatabaseError(f"Failed to create memory: {e}") from e
    
    def update_content(self, memory_id: str, content: str) -> Optional[Memory]:
        """Update memory content.
        
        Args:
            memory_id: Memory ID
            content: New content
            
        Returns:
            Updated memory or None if not found
            
        Raises:
            DatabaseError: If update fails
        """
        try:
            memory = self.find_by_id(memory_id)
            if not memory:
                return None
            
            memory.update_content(content)
            return self.update(memory)
        except Exception as e:
            logger.error(f"Failed to update memory content: {e}")
            raise DatabaseError(f"Failed to update memory content: {e}") from e
    
    def update_importance(self, memory_id: str, importance: int) -> Optional[Memory]:
        """Update memory importance.
        
        Args:
            memory_id: Memory ID
            importance: New importance
            
        Returns:
            Updated memory or None if not found
            
        Raises:
            DatabaseError: If update fails
            ValueError: If importance is not between 1 and 10
        """
        if not isinstance(importance, int) or importance < 1 or importance > 10:
            raise ValueError(f"Importance must be an integer between 1 and 10: {importance}")
        
        try:
            memory = self.find_by_id(memory_id)
            if not memory:
                return None
            
            memory.update_importance(importance)
            return self.update(memory)
        except ValueError:
            # Re-raise without wrapping
            raise
        except Exception as e:
            logger.error(f"Failed to update memory importance: {e}")
            raise DatabaseError(f"Failed to update memory importance: {e}") from e
    
    def access_memory(self, memory_id: str) -> Optional[Memory]:
        """Update memory last access timestamp.
        
        Args:
            memory_id: Memory ID
            
        Returns:
            Updated memory or None if not found
            
        Raises:
            DatabaseError: If update fails
        """
        try:
            memory = self.find_by_id(memory_id)
            if not memory:
                return None
            
            memory.access()
            return self.update(memory)
        except Exception as e:
            logger.error(f"Failed to update memory last access: {e}")
            raise DatabaseError(f"Failed to update memory last access: {e}") from e

class MemoryTagRepository(Repository[MemoryTag]):
    """Repository for MemoryTag model."""
    
    _model_class = MemoryTag
    
    def find_by_memory(self, memory_id: str) -> List[MemoryTag]:
        """Find tags by memory ID.
        
        Args:
            memory_id: Memory ID
            
        Returns:
            List of tags
            
        Raises:
            DatabaseError: If query fails
        """
        return self.find_all({"memory_id": memory_id})
    
    def find_by_tag(self, tag: str) -> List[MemoryTag]:
        """Find memory tags by tag name.
        
        Args:
            tag: Tag name
            
        Returns:
            List of memory tags
            
        Raises:
            DatabaseError: If query fails
        """
        return self.find_all({"tag": tag})
    
    def get_memory_tags(self, memory_id: str) -> List[str]:
        """Get tags for a memory.
        
        Args:
            memory_id: Memory ID
            
        Returns:
            List of tag names
            
        Raises:
            DatabaseError: If query fails
        """
        try:
            tags = self.find_by_memory(memory_id)
            return [tag.tag for tag in tags]
        except Exception as e:
            logger.error(f"Failed to get memory tags: {e}")
            raise DatabaseError(f"Failed to get memory tags: {e}") from e
    
    def get_user_tags(self, user_id: str) -> List[str]:
        """Get all tags for a user.
        
        Args:
            user_id: User ID
            
        Returns:
            List of tag names
            
        Raises:
            DatabaseError: If query fails
        """
        try:
            query = f"""
                SELECT DISTINCT t.tag FROM {self._model_class.get_table_name()} t
                JOIN memories m ON t.memory_id = m.memory_id
                WHERE m.user_id = ?
                ORDER BY t.tag
            """
            
            cursor = self.connection.execute(query, (user_id,))
            rows = cursor.fetchall()
            
            return [row["tag"] for row in rows]
        except Exception as e:
            logger.error(f"Failed to get user tags: {e}")
            raise DatabaseError(f"Failed to get user tags: {e}") from e
    
    def add_tag(self, memory_id: str, tag: str) -> MemoryTag:
        """Add a tag to a memory.
        
        Args:
            memory_id: Memory ID
            tag: Tag name
            
        Returns:
            Created memory tag
            
        Raises:
            DatabaseError: If creation fails
        """
        try:
            # Check if tag already exists
            existing = self.find_one({"memory_id": memory_id, "tag": tag})
            if existing:
                return existing
            
            # Create new tag
            memory_tag = MemoryTag(
                memory_id=memory_id,
                tag=tag
            )
            
            return self.create(memory_tag)
        except Exception as e:
            logger.error(f"Failed to add tag: {e}")
            raise DatabaseError(f"Failed to add tag: {e}") from e
    
    def remove_tag(self, memory_id: str, tag: str) -> bool:
        """Remove a tag from a memory.
        
        Args:
            memory_id: Memory ID
            tag: Tag name
            
        Returns:
            True if tag was removed, False otherwise
            
        Raises:
            DatabaseError: If deletion fails
        """
        try:
            memory_tag = self.find_one({"memory_id": memory_id, "tag": tag})
            if not memory_tag:
                return False
            
            return self.delete(memory_tag)
        except Exception as e:
            logger.error(f"Failed to remove tag: {e}")
            raise DatabaseError(f"Failed to remove tag: {e}") from e
