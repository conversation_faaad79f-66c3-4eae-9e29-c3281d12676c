"""
Sentiment analyzer for VoicePal.

This module provides sentiment analysis capabilities using Deepgram's API.
"""

import logging
import os
import tempfile
from typing import Dict, Any, Optional, List, Union
from pathlib import Path
import asyncio
import json

from bot.providers.stt.deepgram_provider import DeepgramProvider

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class SentimentAnalyzer:
    """Analyzes sentiment from voice and text using Deepgram."""
    
    # Sentiment mapping for standardization
    SENTIMENT_MAPPING = {
        "positive": ["positive", "happy", "excited", "joyful", "pleased", "content", "satisfied"],
        "negative": ["negative", "sad", "angry", "upset", "frustrated", "disappointed", "anxious"],
        "neutral": ["neutral", "calm", "indifferent", "balanced"]
    }
    
    # Emotion mapping for more detailed analysis
    EMOTION_MAPPING = {
        "happy": {"sentiment": "positive", "intensity": 0.8},
        "excited": {"sentiment": "positive", "intensity": 0.9},
        "joyful": {"sentiment": "positive", "intensity": 0.85},
        "content": {"sentiment": "positive", "intensity": 0.7},
        "pleased": {"sentiment": "positive", "intensity": 0.75},
        "satisfied": {"sentiment": "positive", "intensity": 0.7},
        "calm": {"sentiment": "neutral", "intensity": 0.6},
        "indifferent": {"sentiment": "neutral", "intensity": 0.5},
        "balanced": {"sentiment": "neutral", "intensity": 0.5},
        "sad": {"sentiment": "negative", "intensity": 0.7},
        "angry": {"sentiment": "negative", "intensity": 0.9},
        "upset": {"sentiment": "negative", "intensity": 0.8},
        "frustrated": {"sentiment": "negative", "intensity": 0.8},
        "disappointed": {"sentiment": "negative", "intensity": 0.7},
        "anxious": {"sentiment": "negative", "intensity": 0.8}
    }
    
    def __init__(self, deepgram_provider: Optional[DeepgramProvider] = None, 
                ai_provider = None, database = None, config: Dict[str, Any] = None):
        """
        Initialize the sentiment analyzer.
        
        Args:
            deepgram_provider: Deepgram provider instance
            ai_provider: AI provider instance for fallback
            database: Database instance for storing sentiment data
            config: Configuration dictionary
        """
        self.deepgram_provider = deepgram_provider
        self.ai_provider = ai_provider
        self.database = database
        self.config = config or {}
        
        # Initialize Deepgram provider if not provided
        if not self.deepgram_provider:
            api_key = self.config.get("deepgram_api_key") or os.getenv("DEEPGRAM_API_KEY")
            if api_key:
                self.deepgram_provider = DeepgramProvider(api_key=api_key)
                logger.info("Initialized Deepgram provider for sentiment analysis")
            else:
                logger.warning("No Deepgram API key provided. Sentiment analysis will be limited.")
    
    async def analyze_voice_sentiment(self, audio_file_path: Union[str, Path], 
                                    user_id: Optional[int] = None) -> Dict[str, Any]:
        """
        Analyze sentiment from voice.
        
        Args:
            audio_file_path: Path to audio file
            user_id: User ID for storing sentiment data
            
        Returns:
            Dict containing sentiment information
        """
        try:
            # Default sentiment data
            sentiment_data = {
                "sentiment": "neutral",
                "confidence": 0.5,
                "source": "voice",
                "emotions": {},
                "transcript": ""
            }
            
            # Check if Deepgram provider is available and supports sentiment analysis
            if self.deepgram_provider and self.deepgram_provider.supports_feature("sentiment_analysis"):
                # Transcribe with sentiment analysis
                result = await self.deepgram_provider.transcribe_with_sentiment(audio_file_path)
                
                # Extract transcript
                if "transcript" in result and result["transcript"]:
                    sentiment_data["transcript"] = result["transcript"]
                
                # Extract sentiment
                if "sentiment" in result and result["sentiment"]:
                    sentiment = result["sentiment"].get("sentiment", "neutral")
                    confidence = result["sentiment"].get("confidence", 0.5)
                    
                    # Standardize sentiment
                    sentiment_data["sentiment"] = self._standardize_sentiment(sentiment)
                    sentiment_data["confidence"] = confidence
                    
                    # Extract emotions if available
                    if "emotions" in result["sentiment"]:
                        sentiment_data["emotions"] = result["sentiment"]["emotions"]
                    else:
                        # Generate emotions based on sentiment
                        sentiment_data["emotions"] = self._generate_emotions(sentiment, confidence)
                
                # Store sentiment data if user_id is provided
                if user_id and self.database:
                    self._store_sentiment_data(user_id, sentiment_data)
                
                return sentiment_data
            
            # Fallback to AI provider if available
            elif self.ai_provider and sentiment_data["transcript"] and self.ai_provider.supports_feature("sentiment_analysis"):
                text_sentiment = await self.analyze_text_sentiment(sentiment_data["transcript"], user_id)
                
                # Update sentiment data with text sentiment
                sentiment_data.update({
                    "sentiment": text_sentiment["sentiment"],
                    "confidence": text_sentiment["confidence"],
                    "emotions": text_sentiment.get("emotions", {})
                })
                
                return sentiment_data
            
            # Return default sentiment data
            return sentiment_data
        except Exception as e:
            logger.error(f"Error analyzing voice sentiment: {e}")
            return {
                "sentiment": "neutral",
                "confidence": 0.5,
                "source": "voice",
                "emotions": {},
                "transcript": ""
            }
    
    async def analyze_text_sentiment(self, text: str, user_id: Optional[int] = None) -> Dict[str, Any]:
        """
        Analyze sentiment from text.
        
        Args:
            text: Text to analyze
            user_id: User ID for storing sentiment data
            
        Returns:
            Dict containing sentiment information
        """
        try:
            # Default sentiment data
            sentiment_data = {
                "sentiment": "neutral",
                "confidence": 0.5,
                "source": "text",
                "emotions": {},
                "transcript": text
            }
            
            # Check if AI provider is available and supports sentiment analysis
            if self.ai_provider and self.ai_provider.supports_feature("sentiment_analysis"):
                # Analyze sentiment
                result = self.ai_provider.analyze_sentiment(text)
                
                # Extract sentiment
                if result:
                    sentiment = result.get("sentiment", "neutral")
                    confidence = result.get("confidence", 0.5)
                    
                    # Standardize sentiment
                    sentiment_data["sentiment"] = self._standardize_sentiment(sentiment)
                    sentiment_data["confidence"] = confidence
                    
                    # Extract emotions if available
                    if "emotions" in result:
                        sentiment_data["emotions"] = result["emotions"]
                    else:
                        # Generate emotions based on sentiment
                        sentiment_data["emotions"] = self._generate_emotions(sentiment, confidence)
            
            # Store sentiment data if user_id is provided
            if user_id and self.database:
                self._store_sentiment_data(user_id, sentiment_data)
            
            return sentiment_data
        except Exception as e:
            logger.error(f"Error analyzing text sentiment: {e}")
            return {
                "sentiment": "neutral",
                "confidence": 0.5,
                "source": "text",
                "emotions": {},
                "transcript": text
            }
    
    def _standardize_sentiment(self, sentiment: str) -> str:
        """
        Standardize sentiment to positive, negative, or neutral.
        
        Args:
            sentiment: Raw sentiment
            
        Returns:
            str: Standardized sentiment
        """
        sentiment = sentiment.lower()
        
        # Check if sentiment is already standardized
        if sentiment in ["positive", "negative", "neutral"]:
            return sentiment
        
        # Check if sentiment is in mapping
        for standard, variations in self.SENTIMENT_MAPPING.items():
            if sentiment in variations:
                return standard
        
        # Default to neutral
        return "neutral"
    
    def _generate_emotions(self, sentiment: str, confidence: float) -> Dict[str, float]:
        """
        Generate emotions based on sentiment and confidence.
        
        Args:
            sentiment: Standardized sentiment
            confidence: Confidence score
            
        Returns:
            Dict containing emotions and their scores
        """
        emotions = {}
        
        # Generate emotions based on sentiment
        if sentiment == "positive":
            emotions["happy"] = confidence * 0.8
            emotions["excited"] = confidence * 0.6
            emotions["content"] = confidence * 0.7
        elif sentiment == "negative":
            emotions["sad"] = confidence * 0.7
            emotions["angry"] = confidence * 0.5
            emotions["frustrated"] = confidence * 0.6
        else:  # neutral
            emotions["calm"] = confidence * 0.8
            emotions["indifferent"] = confidence * 0.7
        
        return emotions
    
    def _store_sentiment_data(self, user_id: int, sentiment_data: Dict[str, Any]) -> None:
        """
        Store sentiment data in database.
        
        Args:
            user_id: User ID
            sentiment_data: Sentiment data
        """
        try:
            # Check if database is available
            if not self.database:
                return
            
            # Check if database has add_mood_entry method
            if not hasattr(self.database, "add_mood_entry"):
                logger.warning("Database does not have add_mood_entry method")
                return
            
            # Prepare mood data
            mood_data = {
                "sentiment": sentiment_data["sentiment"],
                "confidence": sentiment_data["confidence"],
                "source": sentiment_data["source"],
                "message_text": sentiment_data.get("transcript", ""),
                "emotions": json.dumps(sentiment_data.get("emotions", {}))
            }
            
            # Add mood entry
            self.database.add_mood_entry(user_id, mood_data)
            logger.info(f"Stored sentiment data for user {user_id}")
        except Exception as e:
            logger.error(f"Error storing sentiment data for user {user_id}: {e}")
    
    def get_response_adjustment(self, sentiment_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get response adjustment based on sentiment.
        
        Args:
            sentiment_data: Sentiment data
            
        Returns:
            Dict containing response adjustment information
        """
        try:
            # Default adjustment
            adjustment = {
                "tone": "neutral",
                "empathy_level": "medium",
                "response_length": "medium",
                "formality": "medium"
            }
            
            # Adjust based on sentiment
            sentiment = sentiment_data.get("sentiment", "neutral")
            confidence = sentiment_data.get("confidence", 0.5)
            emotions = sentiment_data.get("emotions", {})
            
            # Adjust tone based on sentiment
            if sentiment == "positive":
                adjustment["tone"] = "positive"
                adjustment["empathy_level"] = "medium"
            elif sentiment == "negative":
                adjustment["tone"] = "empathetic"
                adjustment["empathy_level"] = "high"
            
            # Adjust based on specific emotions
            if emotions:
                # Find dominant emotion
                dominant_emotion = max(emotions.items(), key=lambda x: x[1])[0]
                
                # Adjust based on dominant emotion
                if dominant_emotion in ["sad", "disappointed"]:
                    adjustment["tone"] = "supportive"
                    adjustment["empathy_level"] = "high"
                    adjustment["response_length"] = "long"
                elif dominant_emotion in ["angry", "frustrated"]:
                    adjustment["tone"] = "calm"
                    adjustment["empathy_level"] = "high"
                    adjustment["formality"] = "medium"
                elif dominant_emotion in ["happy", "excited"]:
                    adjustment["tone"] = "enthusiastic"
                    adjustment["empathy_level"] = "medium"
                    adjustment["formality"] = "low"
            
            return adjustment
        except Exception as e:
            logger.error(f"Error getting response adjustment: {e}")
            return {
                "tone": "neutral",
                "empathy_level": "medium",
                "response_length": "medium",
                "formality": "medium"
            }
