#!/usr/bin/env python3
"""
Setup script for testing dependencies.

This script installs all necessary testing and code quality tools.
"""

import subprocess
import sys
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def install_package(package_name):
    """Install a package using pip."""
    try:
        logger.info(f"Installing {package_name}...")
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', package_name])
        logger.info(f"✅ {package_name} installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ Failed to install {package_name}: {e}")
        return False

def main():
    """Install all testing dependencies."""
    logger.info("🔧 Setting up testing environment...")
    
    # Core testing packages
    testing_packages = [
        'pytest>=7.0.0',
        'pytest-asyncio>=0.21.0',
        'pytest-cov>=4.0.0',
        'pytest-mock>=3.10.0',
        'coverage>=7.0.0'
    ]
    
    # Code quality packages
    quality_packages = [
        'flake8>=6.0.0',
        'pylint>=2.17.0',
        'mypy>=1.0.0',
        'black>=23.0.0',
        'isort>=5.12.0'
    ]
    
    # Security packages
    security_packages = [
        'bandit>=1.7.0',
        'safety>=2.3.0'
    ]
    
    # Performance monitoring
    performance_packages = [
        'psutil>=5.9.0',
        'memory-profiler>=0.60.0'
    ]
    
    all_packages = testing_packages + quality_packages + security_packages + performance_packages
    
    failed_packages = []
    
    for package in all_packages:
        if not install_package(package):
            failed_packages.append(package)
    
    if failed_packages:
        logger.error(f"❌ Failed to install: {failed_packages}")
        logger.info("You can try installing them manually:")
        for package in failed_packages:
            print(f"  pip install {package}")
    else:
        logger.info("✅ All testing dependencies installed successfully!")
    
    logger.info("\n🚀 You can now run the production tests with:")
    logger.info("  python run_production_tests.py")

if __name__ == "__main__":
    main()
