"""
Cleanup script for VoicePal.

This script cleans up redundant files and renames the consolidated main file.
"""

import os
import shutil
import logging

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

def main():
    """Main function to clean up redundant files."""
    try:
        # Backup the original main.py file
        if os.path.exists("bot/main.py") and not os.path.exists("bot/main.py.original"):
            shutil.copy2("bot/main.py", "bot/main.py.original")
            logger.info("Backed up original main.py to main.py.original")
        
        # Replace main.py with the consolidated version
        if os.path.exists("bot/main_consolidated.py"):
            shutil.copy2("bot/main_consolidated.py", "bot/main.py")
            logger.info("Replaced main.py with consolidated version")
        
        # Files to remove
        files_to_remove = [
            "bot/main.py.bak",
            "bot/main_new.py",
            "bot/main_rest.py",
            "bot/main_rest2.py",
            "bot/main_rest3.py",
            "bot/main_rest4.py",
            "bot/main_consolidated.py",
            "bot/main_consolidated_part2.py",
            "bot/main_consolidated_part3.py",
            "bot/main_consolidated_part4.py",
            "test_implementation.py",
            "test_config.json",
            ".env.test"
        ]
        
        # Remove files
        for file_path in files_to_remove:
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"Removed {file_path}")
        
        logger.info("Cleanup completed successfully")
    except Exception as e:
        logger.error(f"Error during cleanup: {e}")

if __name__ == "__main__":
    main()
