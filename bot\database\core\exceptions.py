"""
Database exceptions for VoicePal.

This module defines a hierarchy of database-related exceptions to provide
consistent error handling throughout the database layer.
"""

class DatabaseError(Exception):
    """Base class for all database exceptions."""
    pass

class DatabaseConnectionError(DatabaseError):
    """Raised when database connection fails."""
    pass

class DatabaseQueryError(DatabaseError):
    """Raised when a query fails to execute."""
    pass

class DatabaseIntegrityError(DatabaseError):
    """Raised when a database constraint is violated."""
    pass

class DatabaseMigrationError(DatabaseError):
    """Raised when a database migration fails."""
    pass

class InsufficientCreditsError(DatabaseError):
    """Raised when a user has insufficient credits."""
    pass

class DatabaseSchemaError(DatabaseError):
    """Raised when there's an issue with the database schema."""
    pass

class DatabaseTransactionError(DatabaseError):
    """Raised when a database transaction fails."""
    pass

class DatabaseTimeoutError(DatabaseError):
    """Raised when a database operation times out."""
    pass

class DatabaseNotFoundError(DatabaseError):
    """Raised when a requested record is not found."""
    pass

class DatabaseDuplicateError(DatabaseError):
    """Raised when a duplicate record is detected."""
    pass
