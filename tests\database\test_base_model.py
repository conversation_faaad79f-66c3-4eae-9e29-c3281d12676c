"""
Tests for base model.

This module tests the BaseModel class.
"""

import pytest
import json
from datetime import datetime

from bot.database.models.base_model import BaseModel

class TestModel(BaseModel):
    """Test model class."""
    
    _table_name = "test_models"
    _primary_key = "test_id"
    _json_columns = ["data"]
    _hidden_columns = ["secret"]

def test_init():
    """Test model initialization."""
    # Test with attributes
    model = TestModel(test_id="123", name="Test", data={"key": "value"}, secret="hidden")
    assert model.test_id == "123"
    assert model.name == "Test"
    assert model.data == {"key": "value"}
    assert model.secret == "hidden"
    
    # Test with default primary key
    model = TestModel(name="Test")
    assert hasattr(model, "test_id")
    assert model.test_id is not None
    assert model.name == "Test"
    
    # Test with default timestamps
    model = TestModel()
    assert hasattr(model, "created_at")
    assert hasattr(model, "updated_at")

def test_from_dict():
    """Test creating model from dictionary."""
    data = {
        "test_id": "123",
        "name": "Test",
        "data": json.dumps({"key": "value"}),
        "secret": "hidden"
    }
    
    model = TestModel.from_dict(data)
    assert model.test_id == "123"
    assert model.name == "Test"
    assert model.data == {"key": "value"}
    assert model.secret == "hidden"
    
    # Test with invalid JSON
    data["data"] = "invalid json"
    model = TestModel.from_dict(data)
    assert model.data == "invalid json"

def test_to_dict():
    """Test converting model to dictionary."""
    model = TestModel(test_id="123", name="Test", data={"key": "value"}, secret="hidden")
    
    # Test without hidden columns
    data = model.to_dict()
    assert data["test_id"] == "123"
    assert data["name"] == "Test"
    assert data["data"] == json.dumps({"key": "value"})
    assert "secret" not in data
    
    # Test with hidden columns
    data = model.to_dict(include_hidden=True)
    assert data["test_id"] == "123"
    assert data["name"] == "Test"
    assert data["data"] == json.dumps({"key": "value"})
    assert data["secret"] == "hidden"

def test_str_repr():
    """Test string representation."""
    model = TestModel(test_id="123", name="Test")
    
    # Test __str__
    assert str(model) == "TestModel(test_id=123)"
    
    # Test __repr__
    assert "TestModel" in repr(model)
    assert "test_id='123'" in repr(model)
    assert "name='Test'" in repr(model)

def test_equality():
    """Test equality comparison."""
    model1 = TestModel(test_id="123", name="Test")
    model2 = TestModel(test_id="123", name="Different")
    model3 = TestModel(test_id="456", name="Test")
    
    # Same primary key should be equal
    assert model1 == model2
    
    # Different primary key should not be equal
    assert model1 != model3
    
    # Different types should not be equal
    assert model1 != "not a model"

def test_hash():
    """Test hash function."""
    model1 = TestModel(test_id="123", name="Test")
    model2 = TestModel(test_id="123", name="Different")
    model3 = TestModel(test_id="456", name="Test")
    
    # Same primary key should have same hash
    assert hash(model1) == hash(model2)
    
    # Different primary key should have different hash
    assert hash(model1) != hash(model3)

def test_update():
    """Test updating model attributes."""
    model = TestModel(test_id="123", name="Test", updated_at="old_timestamp")
    
    # Update attributes
    model.update(name="Updated", data={"new": "value"})
    
    assert model.name == "Updated"
    assert model.data == {"new": "value"}
    assert model.updated_at != "old_timestamp"

def test_get_table_name():
    """Test getting table name."""
    assert TestModel.get_table_name() == "test_models"
    
    # Test with missing table name
    class InvalidModel(BaseModel):
        pass
    
    with pytest.raises(ValueError):
        InvalidModel.get_table_name()

def test_get_primary_key():
    """Test getting primary key."""
    assert TestModel.get_primary_key() == "test_id"
    
    # Test with default primary key
    class DefaultModel(BaseModel):
        _table_name = "default_models"
    
    assert DefaultModel.get_primary_key() == "id"
