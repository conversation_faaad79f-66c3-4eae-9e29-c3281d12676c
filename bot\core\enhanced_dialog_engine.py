"""
Enhanced dialog engine for VoicePal.

This module provides an improved implementation of the dialog engine
with better conversation context handling and memory integration.
"""

import logging
from typing import Dict, Any, Optional, List
from datetime import datetime

from bot.providers.ai_provider_interface import AIProviderInterface
from bot.features.enhanced_memory_manager import EnhancedMemoryManager
from bot.utils.message_analyzer import analyze_message_complexity, get_response_guidance

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class EnhancedDialogEngine:
    """Enhanced dialog engine with improved conversation handling."""

    def __init__(self, ai_provider: AIProviderInterface, memory_manager: EnhancedMemoryManager, user_manager):
        """
        Initialize the enhanced dialog engine.

        Args:
            ai_provider: AI provider instance
            memory_manager: Enhanced memory manager instance
            user_manager: User manager instance
        """
        self.ai_provider = ai_provider
        self.memory_manager = memory_manager
        self.user_manager = user_manager

    async def process_message(self, user_id: int, message: str,
                             language: Optional[str] = None,
                             is_voice: bool = False,
                             user_context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Process a user message and generate a response with enhanced context.

        Args:
            user_id: User ID
            message: User message
            language: Detected language
            is_voice: Whether the message was voice-based
            user_context: Optional user context (if not provided, will be fetched)

        Returns:
            Dict containing response text, language, etc.
        """
        try:
            # Get enhanced user context if not provided
            if user_context is None:
                user_context = self.memory_manager.get_conversation_context(user_id)
                logger.info(f"Fetched conversation context for user {user_id}")

            # Extract user data for personalization
            user_data = user_context.get("user_data", {})
            preferences = user_context.get("preferences", {})

            # Get user's name for personalized responses using the enhanced method
            user_name = self.memory_manager.get_user_name_from_context(user_id) or "there"

            # Set personality based on user preferences
            personality = preferences.get("personality", "friendly")
            self.ai_provider.set_personality(personality)
            logger.info(f"Set personality to {personality} for user {user_id}")

            # Analyze message complexity to adapt response length
            message_analysis = analyze_message_complexity(message)
            response_guidance = get_response_guidance(message_analysis)

            # Add message analysis to user context
            if "context" not in user_context:
                user_context["context"] = {}
            user_context["context"]["message_analysis"] = message_analysis
            user_context["context"]["response_guidance"] = response_guidance
            user_context["context"]["user_name"] = user_name

            # Add user data to context for better personalization
            user_context["user_data"] = user_data

            # Add mood history to context if available
            mood_history = user_context.get("mood_history", [])
            if mood_history:
                user_context["context"]["recent_mood"] = mood_history[-1] if mood_history else None

            # Log message analysis
            logger.info(f"Message analysis for user {user_id}: {message_analysis}")

            # Format conversation history for AI context
            formatted_conversations = user_context.get("conversations", [])
            logger.info(f"Using {len(formatted_conversations)} conversations for context")

            # Ensure we're not losing important context due to short messages
            if len(message) < 20 and formatted_conversations:
                logger.info(f"Short message detected, ensuring context preservation for user {user_id}")

            # Generate response using AI provider with enhanced context
            response = await self.ai_provider.generate_response(
                message=message,
                language=language,
                user_context=user_context
            )

            # Update conversation history in database
            credits_used = 3 if is_voice else 1  # Use more credits for voice messages
            conversation_id = self.user_manager.add_conversation_entry(
                user_id=user_id,
                message=message,
                response=response["text"],
                is_voice=is_voice,
                credits_used=credits_used
            )

            # Analyze conversation importance and update if needed
            if conversation_id:
                await self._analyze_conversation_importance(user_id, conversation_id, message, response["text"])
                logger.info(f"Analyzed importance for conversation {conversation_id}")

            # Check if this is a short message that should preserve context
            if self.memory_manager.should_preserve_context_for_short_message(message):
                logger.info(f"Short message detected for user {user_id}, preserving context")
                # Don't clear the cache for short messages to maintain conversation flow
            else:
                # Clear the conversation cache to ensure fresh data next time
                # This ensures that the next message will include this conversation in the history
                self.memory_manager.clear_conversation_cache(user_id)
                logger.info(f"Cleared conversation cache for user {user_id}")

            # Check if we should update the user summary
            if self._should_update_summary(user_id, user_context):
                await self._update_user_summary(user_id)
                logger.info(f"Updated summary for user {user_id}")

            return response
        except Exception as e:
            logger.error(f"Error processing message for user {user_id}: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return {
                "text": "I'm sorry, I couldn't process your message. Please try again.",
                "language": language or "en"
            }

    async def _analyze_conversation_importance(self, user_id: int, conversation_id: int,
                                              message: str, response: str) -> None:
        """
        Analyze conversation importance and update database.

        Args:
            user_id: User ID
            conversation_id: Conversation ID
            message: User message
            response: Bot response
        """
        try:
            # Check if conversation_id is valid
            if not conversation_id:
                logger.warning(f"Invalid conversation ID for user {user_id}, skipping importance analysis")
                return

            # Simple heuristic for importance - can be replaced with more sophisticated analysis
            importance_score = 0.5  # Default importance

            # Longer messages might be more important
            if len(message) > 100:
                importance_score += 0.1

            # Questions might be more important
            if "?" in message:
                importance_score += 0.1

            # Messages with certain keywords might be more important
            important_keywords = ["help", "problem", "issue", "need", "want", "important", "urgent"]
            if any(keyword in message.lower() for keyword in important_keywords):
                importance_score += 0.2

            # Cap importance score at 1.0
            importance_score = min(importance_score, 1.0)

            # Update importance in database
            success = self.user_manager.database.set_conversation_importance(conversation_id, importance_score)
            if success:
                logger.info(f"Set importance score {importance_score} for conversation {conversation_id}")
            else:
                logger.warning(f"Failed to set importance score for conversation {conversation_id}")

            # If AI provider supports topic extraction, extract topics
            if self.ai_provider.supports_feature("topic_extraction"):
                topics = await self._extract_topics(message, response)
                for topic in topics:
                    success = self.user_manager.database.add_conversation_topic(conversation_id, topic)
                    if success:
                        logger.info(f"Added topic '{topic}' to conversation {conversation_id}")
                    else:
                        logger.warning(f"Failed to add topic '{topic}' to conversation {conversation_id}")
        except Exception as e:
            logger.error(f"Error analyzing conversation importance for user {user_id}: {e}")
            import traceback
            logger.error(traceback.format_exc())

    async def _extract_topics(self, message: str, response: str) -> List[str]:
        """
        Extract topics from a conversation.

        Args:
            message: User message
            response: Bot response

        Returns:
            List of topics
        """
        # This is a placeholder for a more sophisticated implementation
        # In a real implementation, we would use the AI provider to extract topics
        topics = []

        # Simple keyword-based topic extraction
        combined_text = f"{message} {response}".lower()

        topic_keywords = {
            "weather": ["weather", "temperature", "rain", "sunny", "forecast"],
            "health": ["health", "doctor", "sick", "illness", "medicine"],
            "technology": ["computer", "phone", "tech", "software", "app"],
            "food": ["food", "eat", "restaurant", "recipe", "cooking"],
            "travel": ["travel", "trip", "vacation", "flight", "hotel"],
            "work": ["work", "job", "career", "office", "business"],
            "family": ["family", "parent", "child", "mother", "father"],
            "entertainment": ["movie", "music", "game", "play", "watch"]
        }

        for topic, keywords in topic_keywords.items():
            if any(keyword in combined_text for keyword in keywords):
                topics.append(topic)

        return topics[:3]  # Limit to 3 topics

    def _should_update_summary(self, user_id: int, user_context: Dict[str, Any]) -> bool:
        """
        Check if we should update the user summary.

        Args:
            user_id: User ID
            user_context: User context

        Returns:
            bool: True if we should update the summary, False otherwise
        """
        # Get conversation count
        total_conversations = user_context.get("total_conversations", 0)

        # If we have at least 5 conversations and no summary, update
        if total_conversations >= 5 and not user_context.get("summary"):
            return True

        # Get last summary update time
        last_update = self.user_manager.database.get_user_summary_update_time(user_id)

        # If no last update time, update summary
        if not last_update:
            return True

        # Check if we have enough new conversations since last update
        conversation_count = self.user_manager.database.get_conversation_count_since(user_id, last_update)

        # Update if we have at least 5 new conversations
        return conversation_count >= 5

    async def _update_user_summary(self, user_id: int) -> None:
        """
        Update the user summary.

        Args:
            user_id: User ID
        """
        try:
            # Get conversation history
            conversations, _ = self.memory_manager._get_optimized_conversation_history(user_id)

            # If AI provider supports summarization, use it
            if self.ai_provider.supports_feature("summarization") and conversations:
                summary = self.ai_provider.summarize_conversation(conversations)
                self.user_manager.database.update_user_summary(user_id, summary)
                logger.info(f"Updated summary for user {user_id}")
        except Exception as e:
            logger.error(f"Error updating summary for user {user_id}: {e}")

    async def analyze_mood(self, user_id: int, text: str = None, audio_file_path: str = None) -> Dict[str, Any]:
        """
        Analyze the mood of a text or audio.

        Args:
            user_id: User ID
            text: Text to analyze
            audio_file_path: Path to audio file

        Returns:
            Dict containing mood information
        """
        try:
            mood_data = None

            # If we have audio and AI provider supports audio sentiment analysis
            if audio_file_path and self.ai_provider.supports_feature("audio_sentiment_analysis"):
                mood_data = await self.ai_provider.analyze_audio_sentiment(audio_file_path)

            # If we have text and AI provider supports text sentiment analysis
            elif text and self.ai_provider.supports_feature("sentiment_analysis"):
                mood_data = self.ai_provider.analyze_sentiment(text)

            # Store mood data if available
            if mood_data and hasattr(self.user_manager.database, "add_mood_entry"):
                self.user_manager.database.add_mood_entry(user_id, mood_data)
                return mood_data

            # Default mood data
            return {
                "sentiment": "neutral",
                "confidence": 0.5,
                "source": "text" if text else "audio"
            }
        except Exception as e:
            logger.error(f"Error analyzing mood for user {user_id}: {e}")
            return {
                "sentiment": "neutral",
                "confidence": 0.5,
                "source": "text" if text else "audio"
            }
