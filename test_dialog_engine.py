"""
Test script to verify dialog engine initialization.
"""

import os
import sys
import logging
import asyncio
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.DEBUG
)
logger = logging.getLogger(__name__)

async def test_dialog_engine():
    """Test initializing the dialog engine."""
    logger.info("Testing dialog engine initialization...")

    try:
        # Import the necessary classes
        logger.info("Importing required classes...")
        try:
            from bot.core.initialization_manager import InitializationManager
            logger.info("Imported InitializationManager")
        except Exception as e:
            logger.error(f"Failed to import InitializationManager: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False

        try:
            from bot.core.enhanced_dialog_engine import EnhancedDialogEngine as DialogEngine
            logger.info("Imported EnhancedDialogEngine")
        except Exception as e:
            logger.error(f"Failed to import EnhancedDialogEngine: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False

        try:
            from bot.providers.ai.google_ai_provider import GoogleAIProvider
            logger.info("Imported GoogleAIProvider")
        except Exception as e:
            logger.error(f"Failed to import GoogleAIProvider: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False

        try:
            from bot.core.user_manager import UserManager
            logger.info("Imported UserManager")
        except Exception as e:
            logger.error(f"Failed to import UserManager: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False

        try:
            from bot.features.enhanced_memory_manager import EnhancedMemoryManager as MemoryManager
            logger.info("Imported MemoryManager (EnhancedMemoryManager)")
        except Exception as e:
            logger.error(f"Failed to import MemoryManager: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False

        try:
            from bot.database import Database
            logger.info("Imported Database")
        except Exception as e:
            logger.error(f"Failed to import Database: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False

        # Create a database instance
        logger.info("Creating database instance...")
        try:
            database = Database("voicepal.db")
            logger.info("Database instance created")
        except Exception as e:
            logger.error(f"Failed to create database instance: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False

        # Create a user manager instance
        logger.info("Creating user manager instance...")
        try:
            user_manager = UserManager(database)
            logger.info("User manager instance created")
        except Exception as e:
            logger.error(f"Failed to create user manager instance: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False

        # Create an AI provider instance
        logger.info("Creating AI provider instance...")
        try:
            # Get API key from environment variable
            import os
            api_key = os.environ.get("GOOGLE_AI_API_KEY", "dummy_api_key_for_testing")
            ai_provider = GoogleAIProvider(api_key=api_key)
            logger.info("AI provider instance created")
        except Exception as e:
            logger.error(f"Failed to create AI provider instance: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False

        # Create a memory manager instance
        logger.info("Creating memory manager instance...")
        try:
            memory_manager = MemoryManager(database=database, ai_provider=ai_provider, config={})
            logger.info("Memory manager instance created")
        except Exception as e:
            logger.error(f"Failed to create memory manager instance: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False

        # Create a dialog engine instance
        logger.info("Creating dialog engine instance...")
        try:
            dialog_engine = DialogEngine(
                ai_provider=ai_provider,
                memory_manager=memory_manager,
                user_manager=user_manager
            )
            logger.info("Dialog engine created successfully")
        except Exception as e:
            logger.error(f"Failed to create dialog engine instance: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False

        return True
    except Exception as e:
        logger.error(f"Error testing dialog engine: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

async def main():
    """Main function."""
    # Load environment variables
    load_dotenv()

    success = await test_dialog_engine()
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
