"""
Deepgram STT provider for VoicePal.

This module provides a provider for Deepgram STT services.
"""

import os
import logging
import asyncio
import json
from typing import Dict, Any, Optional, List, Union, BinaryIO
from dataclasses import dataclass, field
from io import BytesIO

from deepgram import (
    DeepgramClient,
    DeepgramClientOptions,
    PrerecordedOptions,
    FileSource,
    LiveOptions,
    LiveTranscriptionEvents
)

from bot.providers.core.provider import STTProvider
from bot.providers.core.config import STTProviderConfig
from bot.providers.core.exceptions import (
    ProviderError,
    ProviderConfigError,
    ProviderAuthError,
    ProviderRateLimitError,
    ProviderTimeoutError,
    ProviderNotFoundError,
    ProviderValidationError,
    ProviderNotInitializedError
)

# Set up logging
logger = logging.getLogger(__name__)

@dataclass
class DeepgramSTTConfig(STTProviderConfig):
    """Configuration for Deepgram STT provider."""
    
    api_key: str = ""
    language: str = "en"
    model: str = "nova-2"
    tier: str = "enhanced"
    smart_format: bool = True
    punctuate: bool = True
    diarize: bool = False
    detect_language: bool = False
    timeout: int = 30
    
    def __post_init__(self):
        """Post-initialization."""
        self.provider_type = "stt"
        self.provider_name = "deepgram_stt"
    
    def validate(self) -> List[str]:
        """Validate configuration.
        
        Returns:
            List of validation error messages (empty if valid)
        """
        errors = super().validate()
        
        # Validate model
        valid_models = ["nova-2", "nova", "enhanced"]
        if self.model not in valid_models:
            errors.append(f"Invalid model: {self.model}. Must be one of {valid_models}")
        
        # Validate tier
        valid_tiers = ["base", "enhanced", "nova"]
        if self.tier not in valid_tiers:
            errors.append(f"Invalid tier: {self.tier}. Must be one of {valid_tiers}")
        
        return errors

class DeepgramSTTProvider(STTProvider[DeepgramSTTConfig]):
    """Provider for Deepgram STT services."""
    
    provider_type = "stt"
    provider_name = "deepgram_stt"
    provider_version = "1.0.0"
    provider_description = "Provider for Deepgram STT services"
    config_class = DeepgramSTTConfig
    
    def __init__(self, config: DeepgramSTTConfig):
        """Initialize provider.
        
        Args:
            config: Provider configuration
        """
        super().__init__(config)
        self.client = None
    
    def validate_config(self) -> None:
        """Validate provider configuration.
        
        Raises:
            ProviderConfigError: If configuration is invalid
        """
        errors = self.config.validate()
        if errors:
            error_message = "; ".join(errors)
            raise ProviderConfigError(f"Invalid configuration: {error_message}")
    
    def initialize(self) -> None:
        """Initialize provider.
        
        Raises:
            ProviderInitializationError: If initialization fails
        """
        try:
            # Initialize Deepgram client
            options = DeepgramClientOptions(
                api_key=self.config.api_key,
                timeout=self.config.timeout
            )
            self.client = DeepgramClient(options)
            
            self.initialized = True
            logger.info(f"Initialized {self.provider_name} provider")
        except Exception as e:
            logger.error(f"Failed to initialize {self.provider_name} provider: {e}")
            raise ProviderInitializationError(f"Failed to initialize {self.provider_name} provider: {e}") from e
    
    def shutdown(self) -> None:
        """Shutdown provider.
        
        Raises:
            ProviderShutdownError: If shutdown fails
        """
        self.client = None
        self.initialized = False
        logger.info(f"Shutdown {self.provider_name} provider")
    
    async def speech_to_text(self, audio_data: bytes, **kwargs) -> Dict[str, Any]:
        """Convert speech to text.
        
        Args:
            audio_data: Audio data
            **kwargs: Additional arguments
            
        Returns:
            Transcription result
            
        Raises:
            ProviderError: If speech-to-text conversion fails
        """
        if not self.initialized:
            raise ProviderNotInitializedError(f"{self.provider_name} provider is not initialized")
        
        try:
            # Create file source from audio data
            source = FileSource(buffer=audio_data)
            
            # Prepare options
            options = PrerecordedOptions(
                model=kwargs.get("model", self.config.model),
                smart_format=kwargs.get("smart_format", self.config.smart_format),
                punctuate=kwargs.get("punctuate", self.config.punctuate),
                diarize=kwargs.get("diarize", self.config.diarize),
                language=kwargs.get("language", self.config.language),
                detect_language=kwargs.get("detect_language", self.config.detect_language),
                tier=kwargs.get("tier", self.config.tier)
            )
            
            # Add sentiment analysis if requested
            if kwargs.get("sentiment", False):
                options.sentiment = True
            
            # Add summarization if requested
            if kwargs.get("summarize", False):
                options.summarize = True
            
            # Add topics if requested
            if kwargs.get("topics", False):
                options.topics = True
            
            # Add utterances if requested
            if kwargs.get("utterances", False):
                options.utterances = True
            
            # Transcribe audio
            response = await self.client.transcription.prerecorded(source, options)
            
            # Extract transcription
            result = response.results.channels[0].alternatives[0]
            
            # Create response
            transcription = {
                "text": result.transcript,
                "confidence": result.confidence,
                "words": [
                    {
                        "word": word.word,
                        "start": word.start,
                        "end": word.end,
                        "confidence": word.confidence
                    }
                    for word in result.words
                ] if hasattr(result, "words") else []
            }
            
            # Add sentiment if available
            if hasattr(response.results, "sentiment") and response.results.sentiment:
                transcription["sentiment"] = {
                    "overall": response.results.sentiment.overall,
                    "segments": [
                        {
                            "text": segment.text,
                            "sentiment": segment.sentiment,
                            "start": segment.start,
                            "end": segment.end
                        }
                        for segment in response.results.sentiment.segments
                    ] if hasattr(response.results.sentiment, "segments") else []
                }
            
            # Add summary if available
            if hasattr(response.results, "summary") and response.results.summary:
                transcription["summary"] = response.results.summary.text
            
            # Add topics if available
            if hasattr(response.results, "topics") and response.results.topics:
                transcription["topics"] = [
                    {
                        "topic": topic.topic,
                        "confidence": topic.confidence
                    }
                    for topic in response.results.topics.topics
                ]
            
            # Add utterances if available
            if hasattr(response.results, "utterances") and response.results.utterances:
                transcription["utterances"] = [
                    {
                        "text": utterance.transcript,
                        "start": utterance.start,
                        "end": utterance.end,
                        "confidence": utterance.confidence
                    }
                    for utterance in response.results.utterances
                ]
            
            # Add language detection if available
            if hasattr(response.results, "detected_language") and response.results.detected_language:
                transcription["detected_language"] = response.results.detected_language
            
            return transcription
        except Exception as e:
            logger.error(f"Failed to convert speech to text: {e}")
            
            # Map exceptions to provider exceptions
            if "authentication" in str(e).lower() or "api key" in str(e).lower():
                raise ProviderAuthError(f"Invalid API key: {e}") from e
            elif "rate limit" in str(e).lower():
                raise ProviderRateLimitError(f"Rate limit exceeded: {e}") from e
            elif "timeout" in str(e).lower():
                raise ProviderTimeoutError(f"Request timed out: {e}") from e
            elif "not found" in str(e).lower():
                raise ProviderNotFoundError(f"Resource not found: {e}") from e
            elif "invalid" in str(e).lower():
                raise ProviderValidationError(f"Invalid request: {e}") from e
            else:
                raise ProviderError(f"Failed to convert speech to text: {e}") from e
    
    async def analyze_sentiment(self, text: str, **kwargs) -> Dict[str, Any]:
        """Analyze sentiment of text.
        
        Args:
            text: Text to analyze
            **kwargs: Additional arguments
            
        Returns:
            Sentiment analysis result
            
        Raises:
            ProviderError: If sentiment analysis fails
        """
        if not self.initialized:
            raise ProviderNotInitializedError(f"{self.provider_name} provider is not initialized")
        
        try:
            # For text sentiment analysis, we'll use the speech_to_text method with a synthesized audio
            # This is a workaround since Deepgram doesn't have a direct text sentiment analysis API
            
            # Use the TTS provider to convert text to speech
            tts_provider = kwargs.get("tts_provider")
            if not tts_provider:
                raise ProviderValidationError("TTS provider is required for text sentiment analysis")
            
            # Convert text to speech
            audio_data = await tts_provider.text_to_speech(text)
            
            # Analyze sentiment of speech
            transcription = await self.speech_to_text(
                audio_data,
                sentiment=True
            )
            
            # Extract sentiment
            if "sentiment" in transcription:
                return transcription["sentiment"]
            else:
                return {
                    "overall": "neutral",
                    "segments": []
                }
        except Exception as e:
            logger.error(f"Failed to analyze sentiment: {e}")
            
            # Re-raise provider exceptions
            if isinstance(e, ProviderError):
                raise
            
            # Map exceptions to provider exceptions
            if "authentication" in str(e).lower() or "api key" in str(e).lower():
                raise ProviderAuthError(f"Invalid API key: {e}") from e
            elif "rate limit" in str(e).lower():
                raise ProviderRateLimitError(f"Rate limit exceeded: {e}") from e
            elif "timeout" in str(e).lower():
                raise ProviderTimeoutError(f"Request timed out: {e}") from e
            elif "not found" in str(e).lower():
                raise ProviderNotFoundError(f"Resource not found: {e}") from e
            elif "invalid" in str(e).lower():
                raise ProviderValidationError(f"Invalid request: {e}") from e
            else:
                raise ProviderError(f"Failed to analyze sentiment: {e}") from e
