# VoicePal Requirements
# Fixed dependencies for compatibility and deployment

# Core dependencies
python-telegram-bot==20.6
python-dotenv==1.0.0
requests==2.31.0
# httpx pinned for compatibility with python-telegram-bot
httpx~=0.25.0

# Data handling
pydantic>=1.10.0,<2.0.0  # For compatibility with various libraries

# Memory enhancement dependencies
upstash-redis>=1.0.0,<2.0.0  # Redis client for Upstash
upstash-ratelimit>=0.1.0,<1.0.0  # Rate limiting for Upstash Redis
qdrant-client>=1.6.0,<2.0.0  # Vector database client
sentence-transformers>=2.2.2,<3.0.0  # For generating embeddings

# Voice processing
# Using compatible Deepgram SDK version
deepgram-sdk>=3.2.7,<4.0.0  # Compatible version with stable API
elevenlabs>=0.2.26,<2.0.0  # Compatible version
ffmpeg-python==0.2.0
gTTS==2.3.2
soundfile==0.12.1

# AI providers
google-generativeai>=0.3.0,<1.0.0
groq>=0.4.1,<1.0.0
huggingface-hub>=0.19.0,<1.0.0

# Security
cryptography>=41.0.0,<42.0.0  # Required for key encryption

# Web framework and HTTP
aiohttp>=3.9.0,<4.0.0
fastapi>=0.100.0,<1.0.0
uvicorn>=0.20.0,<1.0.0
tornado>=6.3.0,<7.0.0  # Required for python-telegram-bot webhooks

# API dependencies
prometheus-client>=0.19.0,<1.0.0  # Metrics collection
psutil>=5.9.0,<6.0.0  # System monitoring
starlette>=0.27.0,<1.0.0  # ASGI framework (included with FastAPI)

# Security dependencies
cryptography>=41.0.0,<42.0.0  # Encryption and cryptographic functions

# Performance dependencies
redis>=5.0.0,<6.0.0  # Redis client for caching and optimization

# Additional dependencies
brotli>=1.0.0,<2.0.0
chardet>=5.0.0,<6.0.0
simplejson>=3.19.0,<4.0.0
aiodns>=3.0.0,<4.0.0

# ML libraries (optional, only if needed)
numpy>=1.24.0,<2.0.0
torch>=2.0.0,<3.0.0; python_version >= "3.8"
transformers>=4.30.0,<5.0.0; python_version >= "3.8"

# Database
sqlalchemy>=1.4.0,<2.0.0

# Testing (dev dependencies)
pytest>=7.4.0,<8.0.0
pytest-asyncio>=0.23.0,<1.0.0
coverage>=7.3.0,<8.0.0

# Utilities
colorama>=0.4.0,<1.0.0
pytz>=2023.3
tqdm>=4.66.0,<5.0.0
