"""
Text-to-Speech provider interface for VoicePal.

This module defines the base TTSProviderInterface that all TTS providers must implement.
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, Optional, Any

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class TTSProviderInterface(ABC):
    """Interface for text-to-speech providers."""

    @abstractmethod
    def generate_speech(self, text: str, language: Optional[str] = None,
                       voice: Optional[str] = None, personality: Optional[str] = None,
                       **kwargs) -> Optional[str]:
        """
        Generate speech from text.

        Args:
            text: Text to convert to speech
            language: Language code
            voice: Voice identifier
            personality: Personality type to influence speech style
            **kwargs: Additional parameters

        Returns:
            Path to the generated audio file or None if generation failed
        """
        pass

    @abstractmethod
    def supports_feature(self, feature_name: str) -> bool:
        """
        Check if provider supports a specific feature.

        Args:
            feature_name: Name of the feature to check

        Returns:
            bool: True if the feature is supported, False otherwise
        """
        pass

    @abstractmethod
    def get_available_languages(self) -> Dict[str, str]:
        """
        Get available languages.

        Returns:
            Dictionary of language codes and names
        """
        pass

    @abstractmethod
    def get_available_voices(self, language: Optional[str] = None) -> Dict[str, str]:
        """
        Get available voices.

        Args:
            language: Language code to filter voices

        Returns:
            Dictionary of voice identifiers and names
        """
        pass

    async def stream_speech(self, text: str, language: Optional[str] = None,
                          voice: Optional[str] = None, personality: Optional[str] = None,
                          **kwargs) -> bool:
        """
        Stream audio directly without saving to a file.

        Args:
            text: Text to convert to speech
            language: Language code
            voice: Voice identifier
            personality: Personality type to influence speech style
            **kwargs: Additional parameters

        Returns:
            bool: True if streaming was successful, False otherwise
        """
        # Default implementation falls back to generate_speech
        file_path = self.generate_speech(text, language, voice, personality, **kwargs)
        if file_path:
            # Play the file using a subprocess
            try:
                import subprocess
                subprocess.run(["ffplay", "-autoexit", "-nodisp", file_path],
                              stdout=subprocess.DEVNULL,
                              stderr=subprocess.DEVNULL)
                return True
            except Exception as e:
                logger.error(f"Error playing audio file: {e}")
                return False
        return False
