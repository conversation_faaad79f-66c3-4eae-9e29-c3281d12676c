# Changelog

## [Unreleased] - 2023-05-23

### Fixed
- **Deepgram Transcription**: Fixed compatibility issues with different Deepgram SDK versions
  - Added support for both v2.x and v3.x/v4.x SDK versions
  - Improved error handling and fallback mechanisms
  - Fixed voice ID handling to work with all Deepgram voice models

- **Menu Navigation**: Fixed issues with menu buttons disappearing
  - Improved callback query handling to properly acknowledge queries
  - Fixed mood entry and mood followup callbacks
  - Added back buttons to all mood-related menus
  - Enhanced navigation router to properly use mood entry component

- **Audio Processing**: Fixed voice message handling
  - Updated voice ID handling to use correct format
  - Improved error handling in the audio processing pipeline
  - Added better logging for debugging audio issues
  - Fixed streaming functionality

- **Response Quality**: Improved conversation quality
  - Enhanced system prompts to better use user context
  - Improved user name recognition and usage
  - Fixed short message handling to maintain conversation flow
  - Added better context preservation for more natural conversations

### Added
- **Testing**: Added comprehensive test scripts
  - Added unit tests for core functionality
  - Added user interaction simulation tests
  - Added Deepgram-specific tests
  - Added file verification tests

## [0.1.0] - 2023-05-01

### Added
- Initial release of VoicePal
- Basic conversation functionality
- Voice message support
- Mood tracking
- Menu navigation
- Payment system integration
