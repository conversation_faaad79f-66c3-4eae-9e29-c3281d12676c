"""
Database migration script to fix payment schema issues.

This script updates the payment-related tables to ensure they have the correct
foreign key constraints and schema structure.
"""

import os
import sys
import logging
import sqlite3
from datetime import datetime

# Add parent directory to path to import bot modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import bot modules
from bot.database.core import Database
from bot.database.schema import SCHEMA_DEFINITIONS

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

def backup_database(db_path):
    """
    Create a backup of the database.
    
    Args:
        db_path: Path to the database file
        
    Returns:
        str: Path to the backup file
    """
    backup_path = f"{db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    try:
        # Copy the database file
        with open(db_path, 'rb') as src, open(backup_path, 'wb') as dst:
            dst.write(src.read())
        logger.info(f"Database backup created at {backup_path}")
        return backup_path
    except Exception as e:
        logger.error(f"Error creating database backup: {e}")
        raise

def fix_payment_schema(db_path):
    """
    Fix payment schema issues.
    
    Args:
        db_path: Path to the database file
    """
    try:
        # Create a backup first
        backup_path = backup_database(db_path)
        
        # Connect to the database
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # Enable foreign keys
        cursor.execute("PRAGMA foreign_keys = OFF")
        
        # Check if invoices table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='invoices'")
        invoices_exists = cursor.fetchone() is not None
        
        # Check if transactions table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='transactions'")
        transactions_exists = cursor.fetchone() is not None
        
        # Fix invoices table
        if invoices_exists:
            logger.info("Fixing invoices table...")
            
            # Rename the old table
            cursor.execute("ALTER TABLE invoices RENAME TO invoices_old")
            
            # Create the new table with correct schema
            cursor.execute("""
            CREATE TABLE invoices (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                package_id TEXT,
                payload TEXT UNIQUE,
                amount INTEGER,
                currency TEXT,
                status TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
            )
            """)
            
            # Copy data from old table to new table
            cursor.execute("""
            INSERT INTO invoices (id, user_id, package_id, payload, amount, currency, status, created_at, updated_at)
            SELECT id, user_id, package_id, payload, amount, currency, status, created_at, updated_at
            FROM invoices_old
            """)
            
            # Drop the old table
            cursor.execute("DROP TABLE invoices_old")
            
            logger.info("Invoices table fixed successfully")
        
        # Fix transactions table
        if transactions_exists:
            logger.info("Fixing transactions table...")
            
            # Check if source column exists
            cursor.execute("PRAGMA table_info(transactions)")
            columns = cursor.fetchall()
            has_source_column = any(col['name'] == 'source' for col in columns)
            
            # Rename the old table
            cursor.execute("ALTER TABLE transactions RENAME TO transactions_old")
            
            # Create the new table with correct schema
            cursor.execute("""
            CREATE TABLE transactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                amount INTEGER,
                credits INTEGER,
                transaction_id TEXT UNIQUE,
                status TEXT,
                source TEXT DEFAULT 'purchase',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
            )
            """)
            
            # Copy data from old table to new table
            if has_source_column:
                cursor.execute("""
                INSERT INTO transactions (id, user_id, amount, credits, transaction_id, status, source, created_at)
                SELECT id, user_id, amount, credits, transaction_id, status, source, created_at
                FROM transactions_old
                """)
            else:
                cursor.execute("""
                INSERT INTO transactions (id, user_id, amount, credits, transaction_id, status, created_at)
                SELECT id, user_id, amount, credits, transaction_id, status, created_at
                FROM transactions_old
                """)
            
            # Drop the old table
            cursor.execute("DROP TABLE transactions_old")
            
            logger.info("Transactions table fixed successfully")
        
        # Commit changes
        conn.commit()
        
        # Re-enable foreign keys
        cursor.execute("PRAGMA foreign_keys = ON")
        
        # Verify the changes
        cursor.execute("PRAGMA table_info(invoices)")
        logger.info(f"Invoices table schema: {cursor.fetchall()}")
        
        cursor.execute("PRAGMA table_info(transactions)")
        logger.info(f"Transactions table schema: {cursor.fetchall()}")
        
        # Close connection
        conn.close()
        
        logger.info("Payment schema fixed successfully")
    except Exception as e:
        logger.error(f"Error fixing payment schema: {e}")
        import traceback
        logger.error(traceback.format_exc())
        
        # Try to restore from backup if something went wrong
        if 'backup_path' in locals():
            try:
                logger.info(f"Attempting to restore from backup {backup_path}")
                with open(backup_path, 'rb') as src, open(db_path, 'wb') as dst:
                    dst.write(src.read())
                logger.info("Database restored from backup")
            except Exception as restore_error:
                logger.error(f"Error restoring database from backup: {restore_error}")
        
        raise

if __name__ == "__main__":
    # Get database path from command line or use default
    if len(sys.argv) > 1:
        db_path = sys.argv[1]
    else:
        db_path = "voicepal.db"
    
    logger.info(f"Fixing payment schema for database at {db_path}")
    fix_payment_schema(db_path)
