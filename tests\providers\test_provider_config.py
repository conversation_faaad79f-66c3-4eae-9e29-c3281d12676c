"""
Tests for the provider configuration.
"""

import os
import unittest
import tempfile
import json
import yaml
from typing import Dict, Any, Optional, List, Type
from unittest.mock import MagicMock, patch

from bot.providers.core.config import (
    ProviderConfig,
    AIProviderConfig,
    TTSProviderConfig,
    STTProviderConfig,
    PaymentProviderConfig,
    ConfigLoader
)
from bot.providers.core.exceptions import ProviderConfigError

class TestProviderConfig(unittest.TestCase):
    """Tests for the ProviderConfig class."""
    
    def test_provider_config_init(self):
        """Test ProviderConfig initialization."""
        # Initialize config
        config = ProviderConfig(provider_type="test", provider_name="test_provider")
        
        # Check config attributes
        self.assertEqual(config.provider_type, "test")
        self.assertEqual(config.provider_name, "test_provider")
        self.assertTrue(config.enabled)
    
    def test_provider_config_to_dict(self):
        """Test ProviderConfig.to_dict method."""
        # Initialize config
        config = ProviderConfig(provider_type="test", provider_name="test_provider", enabled=False)
        
        # Convert to dictionary
        config_dict = config.to_dict()
        
        # Check dictionary
        self.assertEqual(config_dict["provider_type"], "test")
        self.assertEqual(config_dict["provider_name"], "test_provider")
        self.assertFalse(config_dict["enabled"])
    
    def test_provider_config_from_dict(self):
        """Test ProviderConfig.from_dict method."""
        # Create config dictionary
        config_dict = {
            "provider_type": "test",
            "provider_name": "test_provider",
            "enabled": False
        }
        
        # Create config from dictionary
        config = ProviderConfig.from_dict(config_dict)
        
        # Check config attributes
        self.assertEqual(config.provider_type, "test")
        self.assertEqual(config.provider_name, "test_provider")
        self.assertFalse(config.enabled)
    
    def test_provider_config_validate(self):
        """Test ProviderConfig.validate method."""
        # Initialize config with valid values
        config = ProviderConfig(provider_type="test", provider_name="test_provider")
        
        # Validate config
        errors = config.validate()
        
        # Check that there are no errors
        self.assertEqual(errors, [])
        
        # Initialize config with invalid values
        config = ProviderConfig(provider_type="", provider_name="")
        
        # Validate config
        errors = config.validate()
        
        # Check that there are errors
        self.assertEqual(len(errors), 2)
        self.assertIn("Provider type is required", errors)
        self.assertIn("Provider name is required", errors)
    
    def test_provider_config_is_valid(self):
        """Test ProviderConfig.is_valid method."""
        # Initialize config with valid values
        config = ProviderConfig(provider_type="test", provider_name="test_provider")
        
        # Check that config is valid
        self.assertTrue(config.is_valid())
        
        # Initialize config with invalid values
        config = ProviderConfig(provider_type="", provider_name="")
        
        # Check that config is invalid
        self.assertFalse(config.is_valid())

class TestAIProviderConfig(unittest.TestCase):
    """Tests for the AIProviderConfig class."""
    
    def test_ai_provider_config_init(self):
        """Test AIProviderConfig initialization."""
        # Initialize config
        config = AIProviderConfig(provider_type="ai", provider_name="test_ai_provider")
        
        # Check config attributes
        self.assertEqual(config.provider_type, "ai")
        self.assertEqual(config.provider_name, "test_ai_provider")
        self.assertEqual(config.api_key, "")
        self.assertEqual(config.model, "")
        self.assertEqual(config.max_tokens, 1000)
        self.assertEqual(config.temperature, 0.7)
        self.assertEqual(config.top_p, 1.0)
        self.assertEqual(config.frequency_penalty, 0.0)
        self.assertEqual(config.presence_penalty, 0.0)
        self.assertEqual(config.timeout, 30)
    
    def test_ai_provider_config_validate(self):
        """Test AIProviderConfig.validate method."""
        # Initialize config with valid values
        config = AIProviderConfig(
            provider_type="ai",
            provider_name="test_ai_provider",
            api_key="test_api_key",
            model="test_model"
        )
        
        # Validate config
        errors = config.validate()
        
        # Check that there are no errors
        self.assertEqual(errors, [])
        
        # Initialize config with invalid values
        config = AIProviderConfig(
            provider_type="ai",
            provider_name="test_ai_provider",
            api_key="",
            model="",
            max_tokens=0,
            temperature=2.0,
            top_p=2.0,
            timeout=0
        )
        
        # Validate config
        errors = config.validate()
        
        # Check that there are errors
        self.assertEqual(len(errors), 6)
        self.assertIn("API key is required", errors)
        self.assertIn("Model is required", errors)
        self.assertIn("Max tokens must be positive", errors)
        self.assertIn("Temperature must be between 0 and 1", errors)
        self.assertIn("Top P must be between 0 and 1", errors)
        self.assertIn("Timeout must be positive", errors)

class TestTTSProviderConfig(unittest.TestCase):
    """Tests for the TTSProviderConfig class."""
    
    def test_tts_provider_config_init(self):
        """Test TTSProviderConfig initialization."""
        # Initialize config
        config = TTSProviderConfig(provider_type="tts", provider_name="test_tts_provider")
        
        # Check config attributes
        self.assertEqual(config.provider_type, "tts")
        self.assertEqual(config.provider_name, "test_tts_provider")
        self.assertEqual(config.api_key, "")
        self.assertEqual(config.voice_id, "")
        self.assertEqual(config.sample_rate, 24000)
        self.assertEqual(config.audio_format, "mp3")
        self.assertEqual(config.timeout, 30)
    
    def test_tts_provider_config_validate(self):
        """Test TTSProviderConfig.validate method."""
        # Initialize config with valid values
        config = TTSProviderConfig(
            provider_type="tts",
            provider_name="test_tts_provider",
            api_key="test_api_key",
            voice_id="test_voice_id"
        )
        
        # Validate config
        errors = config.validate()
        
        # Check that there are no errors
        self.assertEqual(errors, [])
        
        # Initialize config with invalid values
        config = TTSProviderConfig(
            provider_type="tts",
            provider_name="test_tts_provider",
            api_key="",
            voice_id="",
            sample_rate=0,
            audio_format="",
            timeout=0
        )
        
        # Validate config
        errors = config.validate()
        
        # Check that there are errors
        self.assertEqual(len(errors), 5)
        self.assertIn("API key is required", errors)
        self.assertIn("Voice ID is required", errors)
        self.assertIn("Sample rate must be positive", errors)
        self.assertIn("Audio format is required", errors)
        self.assertIn("Timeout must be positive", errors)

class TestConfigLoader(unittest.TestCase):
    """Tests for the ConfigLoader class."""
    
    def setUp(self):
        """Set up test case."""
        self.loader = ConfigLoader()
        
        # Create temporary directory for config files
        self.temp_dir = tempfile.TemporaryDirectory()
        self.config_dir = self.temp_dir.name
    
    def tearDown(self):
        """Tear down test case."""
        self.temp_dir.cleanup()
    
    def test_load_from_dict(self):
        """Test ConfigLoader.load_from_dict method."""
        # Create config dictionary
        config_dict = {
            "provider_type": "test",
            "provider_name": "test_provider",
            "enabled": False
        }
        
        # Load config from dictionary
        config = self.loader.load_from_dict(config_dict, ProviderConfig)
        
        # Check config attributes
        self.assertEqual(config.provider_type, "test")
        self.assertEqual(config.provider_name, "test_provider")
        self.assertFalse(config.enabled)
        
        # Try to load invalid config
        with self.assertRaises(ProviderConfigError):
            self.loader.load_from_dict({"provider_type": "", "provider_name": ""}, ProviderConfig)
    
    def test_load_from_json(self):
        """Test ConfigLoader.load_from_json method."""
        # Create JSON string
        json_str = '{"provider_type": "test", "provider_name": "test_provider", "enabled": false}'
        
        # Load config from JSON
        config = self.loader.load_from_json(json_str, ProviderConfig)
        
        # Check config attributes
        self.assertEqual(config.provider_type, "test")
        self.assertEqual(config.provider_name, "test_provider")
        self.assertFalse(config.enabled)
        
        # Try to load invalid JSON
        with self.assertRaises(ProviderConfigError):
            self.loader.load_from_json('{"provider_type": }', ProviderConfig)
    
    def test_load_from_yaml(self):
        """Test ConfigLoader.load_from_yaml method."""
        # Create YAML string
        yaml_str = """
        provider_type: test
        provider_name: test_provider
        enabled: false
        """
        
        # Load config from YAML
        config = self.loader.load_from_yaml(yaml_str, ProviderConfig)
        
        # Check config attributes
        self.assertEqual(config.provider_type, "test")
        self.assertEqual(config.provider_name, "test_provider")
        self.assertFalse(config.enabled)
        
        # Try to load invalid YAML
        with self.assertRaises(ProviderConfigError):
            self.loader.load_from_yaml('provider_type: "test\n', ProviderConfig)
    
    def test_load_from_file(self):
        """Test ConfigLoader.load_from_file method."""
        # Create JSON file
        json_path = os.path.join(self.config_dir, "config.json")
        with open(json_path, "w") as f:
            f.write('{"provider_type": "test", "provider_name": "test_provider", "enabled": false}')
        
        # Load config from JSON file
        config = self.loader.load_from_file(json_path, ProviderConfig)
        
        # Check config attributes
        self.assertEqual(config.provider_type, "test")
        self.assertEqual(config.provider_name, "test_provider")
        self.assertFalse(config.enabled)
        
        # Create YAML file
        yaml_path = os.path.join(self.config_dir, "config.yaml")
        with open(yaml_path, "w") as f:
            f.write("""
            provider_type: test
            provider_name: test_provider
            enabled: false
            """)
        
        # Load config from YAML file
        config = self.loader.load_from_file(yaml_path, ProviderConfig)
        
        # Check config attributes
        self.assertEqual(config.provider_type, "test")
        self.assertEqual(config.provider_name, "test_provider")
        self.assertFalse(config.enabled)
        
        # Try to load from non-existent file
        with self.assertRaises(ProviderConfigError):
            self.loader.load_from_file("non_existent_file.json", ProviderConfig)
        
        # Try to load from unsupported file format
        unsupported_path = os.path.join(self.config_dir, "config.txt")
        with open(unsupported_path, "w") as f:
            f.write("provider_type: test")
        
        with self.assertRaises(ProviderConfigError):
            self.loader.load_from_file(unsupported_path, ProviderConfig)

if __name__ == "__main__":
    unittest.main()
