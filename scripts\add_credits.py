"""
<PERSON><PERSON><PERSON> to add credits to a user in the VoicePal bot.

This script adds credits to a user in the database.

Usage:
    python add_credits.py <user_id> <credits>
"""

import os
import sys
import sqlite3
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def add_credits_to_user(user_id, credits=1000):
    """Add credits to user in database."""
    # Connect to database
    db_file = os.getenv("DATABASE_FILE", "voicepal.db")
    conn = sqlite3.connect(db_file)
    cursor = conn.cursor()
    
    # Check if user exists
    cursor.execute("SELECT * FROM users WHERE user_id = ?", (user_id,))
    user = cursor.fetchone()
    
    if user:
        # Get current credits
        cursor.execute("SELECT credits FROM users WHERE user_id = ?", (user_id,))
        current_credits = cursor.fetchone()[0]
        
        # Update credits
        new_credits = current_credits + credits
        cursor.execute("UPDATE users SET credits = ? WHERE user_id = ?", (new_credits, user_id))
        
        # Add transaction record
        cursor.execute(
            "INSERT INTO transactions (user_id, amount, credits, transaction_id, status) VALUES (?, ?, ?, ?, ?)",
            (user_id, 0, credits, "admin_adjustment", "completed")
        )
        
        conn.commit()
        print(f"Added {credits} credits to user {user_id}. New balance: {new_credits}")
    else:
        print(f"User {user_id} not found in database. Please start the bot first to create the user.")
    
    conn.close()

def main():
    """Main function."""
    if len(sys.argv) < 2:
        print("Usage: python add_credits.py <user_id> [<credits>]")
        return
    
    try:
        user_id = int(sys.argv[1])
        credits = int(sys.argv[2]) if len(sys.argv) > 2 else 1000
        
        # Add credits to user in database
        add_credits_to_user(user_id, credits)
        
    except ValueError:
        print("Error: User ID and credits must be numbers")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
