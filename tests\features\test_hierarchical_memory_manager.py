"""
Unit tests for the hierarchical memory manager.
"""

import pytest
import json
from datetime import datetime, timedelta
from unittest.mock import Magic<PERSON><PERSON>, patch, AsyncMock

from bot.features.hierarchical_memory_manager import HierarchicalMemoryManager


@pytest.fixture
def mock_database():
    """Create a mock database."""
    mock_db = MagicMock()
    
    # Mock get_user method
    mock_db.get_user.return_value = {
        "id": 123,
        "username": "test_user",
        "first_name": "Test",
        "last_name": "User",
        "preferred_name": "TestUser"
    }
    
    # Mock get_user_preferences method
    mock_db.get_user_preferences.return_value = {
        "language": "en",
        "tts_provider": "deepgram",
        "voice_id": "aura-thalia-en"
    }
    
    # Mock get_user_summary method
    mock_db.get_user_summary.return_value = "This is a test user who likes technology and music."
    
    # Mock get_conversations method
    mock_db.get_conversations.return_value = [
        {
            "id": 1,
            "user_id": 123,
            "message": "Hello, how are you?",
            "response": "I'm doing well, thank you for asking!",
            "is_voice": False,
            "created_at": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        },
        {
            "id": 2,
            "user_id": 123,
            "message": "What's the weather like today?",
            "response": "I don't have access to real-time weather data, but I can help you find that information.",
            "is_voice": False,
            "created_at": (datetime.now() - timedelta(minutes=5)).strftime('%Y-%m-%d %H:%M:%S')
        }
    ]
    
    # Mock get_important_conversations method
    mock_db.get_important_conversations.return_value = [
        {
            "id": 3,
            "user_id": 123,
            "message": "My name is TestUser",
            "response": "Nice to meet you, TestUser! How can I help you today?",
            "is_voice": False,
            "created_at": (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d %H:%M:%S'),
            "importance_score": 0.8
        }
    ]
    
    # Mock add_conversation method
    mock_db.add_conversation.return_value = 4
    
    # Mock extend_database_for_memory method
    mock_db.extend_database_for_memory = MagicMock(return_value=True)
    
    return mock_db


@pytest.fixture
def mock_ai_provider():
    """Create a mock AI provider."""
    mock_ai = MagicMock()
    return mock_ai


@pytest.fixture
def mock_user_manager():
    """Create a mock user manager."""
    mock_um = MagicMock()
    return mock_um


@pytest.fixture
def mock_redis_provider():
    """Create a mock Redis provider."""
    mock_redis = MagicMock()
    mock_redis.is_available.return_value = True
    mock_redis.get.return_value = None  # Default to cache miss
    mock_redis.set.return_value = True
    mock_redis.delete.return_value = True
    mock_redis.check_rate_limit.return_value = True
    return mock_redis


@pytest.fixture
def mock_qdrant_provider():
    """Create a mock Qdrant provider."""
    mock_qdrant = MagicMock()
    mock_qdrant.is_available.return_value = True
    mock_qdrant.search_memories.return_value = [
        {
            "id": "memory_1",
            "score": 0.95,
            "text": "User: What are your hobbies?\nAssistant: I enjoy chatting with people like you!",
            "metadata": {
                "conversation_id": 5,
                "message": "What are your hobbies?",
                "response": "I enjoy chatting with people like you!",
                "is_voice": False,
                "importance_score": 0.7,
                "created_at": (datetime.now() - timedelta(days=2)).isoformat()
            }
        }
    ]
    mock_qdrant.store_memory.return_value = True
    return mock_qdrant


@pytest.fixture
def mock_embedding_provider():
    """Create a mock embedding provider."""
    mock_embedding = MagicMock()
    mock_embedding.is_available.return_value = True
    mock_embedding.generate_embedding.return_value = [0.1] * 384
    return mock_embedding


@pytest.fixture
def memory_manager(mock_database, mock_ai_provider, mock_user_manager, 
                  mock_redis_provider, mock_qdrant_provider, mock_embedding_provider):
    """Create a HierarchicalMemoryManager with mocked dependencies."""
    with patch('bot.providers.memory.redis_provider.RedisProvider', return_value=mock_redis_provider), \
         patch('bot.providers.memory.qdrant_provider.QdrantProvider', return_value=mock_qdrant_provider), \
         patch('bot.utils.embedding_utils.EmbeddingProvider', return_value=mock_embedding_provider):
        
        manager = HierarchicalMemoryManager(
            database=mock_database,
            ai_provider=mock_ai_provider,
            config={
                "short_term_limit": 10,
                "medium_term_limit": 50,
                "long_term_threshold": 0.7,
                "cache_ttl_minutes": 30,
                "name_cache_ttl_minutes": 60
            },
            user_manager=mock_user_manager
        )
        
        # Set mocked providers directly
        manager.redis_provider = mock_redis_provider
        manager.qdrant_provider = mock_qdrant_provider
        manager.embedding_provider = mock_embedding_provider
        
        yield manager


class TestHierarchicalMemoryManager:
    """Test cases for the HierarchicalMemoryManager class."""

    def test_initialization(self, memory_manager, mock_database):
        """Test initialization."""
        assert memory_manager.database == mock_database
        assert memory_manager.short_term_limit == 10
        assert memory_manager.medium_term_limit == 50
        assert memory_manager.long_term_importance_threshold == 0.7
        assert memory_manager.cache_ttl_minutes == 30
        assert memory_manager.name_cache_ttl_minutes == 60
        mock_database.extend_database_for_memory.assert_called_once()

    def test_get_conversation_context_no_cache(self, memory_manager, mock_database, mock_redis_provider):
        """Test get_conversation_context method with no cache."""
        # Set up Redis to return None (cache miss)
        mock_redis_provider.get.return_value = None
        
        # Get conversation context
        context = memory_manager.get_conversation_context(123)
        
        # Verify the result
        assert "user_data" in context
        assert "preferences" in context
        assert "summary" in context
        assert "short_term_memory" in context
        assert "medium_term_memory" in context
        assert "long_term_memory" in context
        
        # Verify database calls
        mock_database.get_user.assert_called_with(123)
        mock_database.get_user_preferences.assert_called_with(123)
        mock_database.get_user_summary.assert_called_with(123)
        mock_database.get_conversations.assert_called_with(user_id=123, limit=10)
        mock_database.get_important_conversations.assert_called()
        
        # Verify Redis calls
        mock_redis_provider.get.assert_called_with("context:123")
        mock_redis_provider.set.assert_called()

    def test_get_conversation_context_with_cache(self, memory_manager, mock_database, mock_redis_provider):
        """Test get_conversation_context method with cache hit."""
        # Set up Redis to return cached context
        cached_context = {
            "user_data": {"id": 123, "username": "cached_user"},
            "preferences": {"language": "en"},
            "summary": "Cached summary",
            "short_term_memory": [],
            "medium_term_memory": [],
            "long_term_memory": []
        }
        mock_redis_provider.get.return_value = cached_context
        
        # Get conversation context
        context = memory_manager.get_conversation_context(123)
        
        # Verify the result
        assert context == cached_context
        
        # Verify Redis calls
        mock_redis_provider.get.assert_called_with("context:123")
        
        # Verify database calls (should not be called)
        mock_database.get_user.assert_not_called()
        mock_database.get_user_preferences.assert_not_called()
        mock_database.get_user_summary.assert_not_called()

    def test_get_conversation_context_with_local_cache(self, memory_manager, mock_database, mock_redis_provider):
        """Test get_conversation_context method with local cache."""
        # Set up Redis to return None (cache miss)
        mock_redis_provider.get.return_value = None
        
        # Set up local cache
        cached_context = {
            "user_data": {"id": 123, "username": "local_cached_user"},
            "preferences": {"language": "en"},
            "summary": "Local cached summary",
            "short_term_memory": [],
            "medium_term_memory": [],
            "long_term_memory": []
        }
        memory_manager.conversation_cache[123] = {
            "context": cached_context,
            "timestamp": datetime.now()
        }
        
        # Get conversation context
        context = memory_manager.get_conversation_context(123)
        
        # Verify the result
        assert context == cached_context
        
        # Verify Redis calls
        mock_redis_provider.get.assert_called_with("context:123")
        
        # Verify database calls (should not be called)
        mock_database.get_user.assert_not_called()
        mock_database.get_user_preferences.assert_not_called()
        mock_database.get_user_summary.assert_not_called()

    def test_get_conversation_context_with_expired_local_cache(self, memory_manager, mock_database, mock_redis_provider):
        """Test get_conversation_context method with expired local cache."""
        # Set up Redis to return None (cache miss)
        mock_redis_provider.get.return_value = None
        
        # Set up expired local cache
        cached_context = {
            "user_data": {"id": 123, "username": "expired_cached_user"},
            "preferences": {"language": "en"},
            "summary": "Expired cached summary",
            "short_term_memory": [],
            "medium_term_memory": [],
            "long_term_memory": []
        }
        memory_manager.conversation_cache[123] = {
            "context": cached_context,
            "timestamp": datetime.now() - timedelta(minutes=60)  # Expired
        }
        
        # Get conversation context
        context = memory_manager.get_conversation_context(123)
        
        # Verify the result is not from cache
        assert context != cached_context
        
        # Verify database calls (should be called due to expired cache)
        mock_database.get_user.assert_called_with(123)
        mock_database.get_user_preferences.assert_called_with(123)
        mock_database.get_user_summary.assert_called_with(123)

    def test_clear_conversation_cache(self, memory_manager, mock_redis_provider):
        """Test clear_conversation_cache method."""
        # Set up local cache
        memory_manager.conversation_cache[123] = {
            "context": {},
            "timestamp": datetime.now()
        }
        
        # Clear cache
        memory_manager.clear_conversation_cache(123)
        
        # Verify local cache is cleared
        assert 123 not in memory_manager.conversation_cache
        
        # Verify Redis call
        mock_redis_provider.delete.assert_called_with("context:123")

    def test_get_user_name_from_context(self, memory_manager, mock_database, mock_redis_provider):
        """Test get_user_name_from_context method."""
        # Set up Redis to return None (cache miss)
        mock_redis_provider.get.return_value = None
        
        # Get user name
        name = memory_manager.get_user_name_from_context(123)
        
        # Verify the result
        assert name == "TestUser"  # From preferred_name in mock_database
        
        # Verify Redis calls
        mock_redis_provider.get.assert_called_with("user_name:123")
        mock_redis_provider.set.assert_called()
        
        # Verify database calls
        mock_database.get_user.assert_called_with(123)

    def test_get_user_name_from_context_with_cache(self, memory_manager, mock_database, mock_redis_provider):
        """Test get_user_name_from_context method with cache hit."""
        # Set up Redis to return cached name
        mock_redis_provider.get.return_value = "CachedName"
        
        # Get user name
        name = memory_manager.get_user_name_from_context(123)
        
        # Verify the result
        assert name == "CachedName"
        
        # Verify Redis calls
        mock_redis_provider.get.assert_called_with("user_name:123")
        
        # Verify database calls (should not be called)
        mock_database.get_user.assert_not_called()

    @pytest.mark.asyncio
    async def test_store_conversation(self, memory_manager, mock_database, mock_qdrant_provider, mock_embedding_provider):
        """Test store_conversation method."""
        # Store conversation
        conversation_id = await memory_manager.store_conversation(
            user_id=123,
            message="Hello, how are you?",
            response="I'm doing well, thank you for asking!",
            is_voice=False
        )
        
        # Verify the result
        assert conversation_id == 4  # From mock_database.add_conversation
        
        # Verify database calls
        mock_database.add_conversation.assert_called_with(
            user_id=123,
            message="Hello, how are you?",
            response="I'm doing well, thank you for asking!",
            is_voice=False
        )
        
        # Verify embedding and Qdrant calls
        mock_embedding_provider.generate_embedding.assert_called_once()
        mock_qdrant_provider.store_memory.assert_called_once()

    @pytest.mark.asyncio
    async def test_store_conversation_with_importance(self, memory_manager, mock_database):
        """Test store_conversation method with importance score."""
        # Store conversation with importance score
        conversation_id = await memory_manager.store_conversation(
            user_id=123,
            message="My name is TestUser",
            response="Nice to meet you, TestUser!",
            is_voice=False,
            importance_score=0.9
        )
        
        # Verify the result
        assert conversation_id == 4  # From mock_database.add_conversation
        
        # Verify database calls
        mock_database.add_conversation.assert_called_with(
            user_id=123,
            message="My name is TestUser",
            response="Nice to meet you, TestUser!",
            is_voice=False
        )
        
        # Verify importance score is set
        assert hasattr(mock_database, 'set_conversation_importance')
        if hasattr(mock_database, 'set_conversation_importance'):
            mock_database.set_conversation_importance.assert_called_with(
                conversation_id=4,
                importance_score=0.9
            )

    @pytest.mark.asyncio
    async def test_search_relevant_memories(self, memory_manager, mock_qdrant_provider, mock_embedding_provider):
        """Test search_relevant_memories method."""
        # Search for relevant memories
        memories = await memory_manager.search_relevant_memories(
            user_id=123,
            query="What are your hobbies?",
            limit=5
        )
        
        # Verify the result
        assert len(memories) == 1
        assert memories[0]["id"] == "memory_1"
        assert memories[0]["score"] == 0.95
        
        # Verify embedding and Qdrant calls
        mock_embedding_provider.generate_embedding.assert_called_once()
        mock_qdrant_provider.search_memories.assert_called_once()

    @pytest.mark.asyncio
    async def test_search_relevant_memories_fallback(self, memory_manager, mock_qdrant_provider, mock_embedding_provider, mock_database):
        """Test search_relevant_memories method with fallback."""
        # Make Qdrant unavailable
        mock_qdrant_provider.is_available.return_value = False
        
        # Search for relevant memories
        memories = await memory_manager.search_relevant_memories(
            user_id=123,
            query="What are your hobbies?",
            limit=5
        )
        
        # Verify the result (should use fallback)
        assert isinstance(memories, list)
        
        # Verify database calls for fallback
        mock_database.get_conversations.assert_called()

    def test_should_preserve_context_for_short_message(self, memory_manager):
        """Test should_preserve_context_for_short_message method."""
        # Test with short message
        assert memory_manager.should_preserve_context_for_short_message("Hi") is True
        
        # Test with question
        assert memory_manager.should_preserve_context_for_short_message("What is your name?") is True
        
        # Test with follow-up indicator
        assert memory_manager.should_preserve_context_for_short_message("Can you explain that?") is True
        
        # Test with long message without follow-up indicators
        assert memory_manager.should_preserve_context_for_short_message(
            "This is a very long message that doesn't contain any follow-up indicators. " * 5
        ) is False

    def test_calculate_importance_score(self, memory_manager):
        """Test _calculate_importance_score method."""
        # Test with name information (important)
        score1 = memory_manager._calculate_importance_score(
            "My name is TestUser",
            "Nice to meet you, TestUser!"
        )
        assert score1 > 0.5
        
        # Test with preference information (important)
        score2 = memory_manager._calculate_importance_score(
            "I prefer classical music",
            "I'll remember that you like classical music."
        )
        assert score2 > 0.5
        
        # Test with casual conversation (less important)
        score3 = memory_manager._calculate_importance_score(
            "How's the weather?",
            "I don't have access to weather information."
        )
        assert score3 < score1
