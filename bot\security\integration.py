"""
Security integration module for VoicePal.

This module integrates security features with the main bot.
"""

import logging
from typing import Optional
from telegram.ext import CommandHandler

from bot.security.security_manager import SecurityManager
from bot.security.audit_logger import AuditEventType, AuditSeverity
from bot.security.data_protection import GDPR

logger = logging.getLogger(__name__)

class SecurityIntegration:
    """Security integration for VoicePal bot."""
    
    def __init__(self, bot_instance, security_manager: SecurityManager):
        """
        Initialize security integration.
        
        Args:
            bot_instance: VoicePal bot instance
            security_manager: Security manager instance
        """
        self.bot = bot_instance
        self.security = security_manager
        
        # Add security commands to bot
        self._add_security_commands()
        
        logger.info("Security integration initialized")
    
    def _add_security_commands(self):
        """Add security-related commands to the bot."""
        try:
            if hasattr(self.bot, 'application'):
                # GDPR compliance commands
                self.bot.application.add_handler(CommandHandler("privacy", self._privacy_command))
                self.bot.application.add_handler(CommandHandler("export_data", self._export_data_command))
                self.bot.application.add_handler(CommandHandler("delete_data", self._delete_data_command))
                self.bot.application.add_handler(CommandHandler("consent", self._consent_command))
                
                # Security status command (admin only)
                self.bot.application.add_handler(CommandHandler("security_status", self._security_status_command))
                
                logger.info("Security commands added to bot")
        except Exception as e:
            logger.error(f"Failed to add security commands: {e}")
    
    async def _privacy_command(self, update, context):
        """Handle /privacy command."""
        try:
            user_id = update.effective_user.id
            
            # Log privacy notice request
            self.security.log_user_action(
                event_type=AuditEventType.USER_LOGIN,
                user_id=user_id,
                description="Privacy notice requested",
                severity=AuditSeverity.LOW
            )
            
            privacy_notice = GDPR.generate_privacy_notice()
            await update.message.reply_text(privacy_notice, parse_mode="Markdown")
            
        except Exception as e:
            logger.error(f"Privacy command failed: {e}")
            await update.message.reply_text("❌ Failed to retrieve privacy notice.")
    
    async def _export_data_command(self, update, context):
        """Handle /export_data command."""
        try:
            user_id = update.effective_user.id
            
            # Check rate limit
            if not self.security.check_rate_limit(user_id, "data_export"):
                await update.message.reply_text(
                    "⏰ You've requested data export too frequently. Please try again later."
                )
                return
            
            await update.message.reply_text("📦 Preparing your data export...")
            
            # Export user data
            export_data = self.security.export_user_data(user_id)
            
            if export_data:
                # In a real implementation, you'd save this to a file and send it
                # For now, we'll just confirm the export
                await update.message.reply_text(
                    "✅ Your data has been exported successfully. "
                    "In a production environment, this would be sent as a downloadable file."
                )
            else:
                await update.message.reply_text("❌ Failed to export your data. Please try again later.")
            
        except Exception as e:
            logger.error(f"Export data command failed: {e}")
            await update.message.reply_text("❌ Failed to export your data.")
    
    async def _delete_data_command(self, update, context):
        """Handle /delete_data command."""
        try:
            user_id = update.effective_user.id
            
            # Check rate limit
            if not self.security.check_rate_limit(user_id, "data_deletion"):
                await update.message.reply_text(
                    "⏰ You've requested data deletion too frequently. Please try again later."
                )
                return
            
            # Get deletion type from command arguments
            deletion_type = "complete"
            if context.args and context.args[0].lower() in ["partial", "complete"]:
                deletion_type = context.args[0].lower()
            
            # Confirm deletion
            if deletion_type == "complete":
                confirmation_text = (
                    "⚠️ **WARNING**: This will permanently delete ALL your data including:\n"
                    "• All conversations and messages\n"
                    "• Voice recordings\n"
                    "• Preferences and settings\n"
                    "• Payment history\n"
                    "• Your account\n\n"
                    "This action CANNOT be undone!\n\n"
                    "Type 'CONFIRM DELETE' to proceed or anything else to cancel."
                )
            else:
                confirmation_text = (
                    "⚠️ This will delete your personal data but keep anonymized analytics:\n"
                    "• Personal information (name, username)\n"
                    "• Message content\n"
                    "• Voice recordings\n\n"
                    "Conversation metadata will be kept for analytics.\n\n"
                    "Type 'CONFIRM PARTIAL' to proceed or anything else to cancel."
                )
            
            await update.message.reply_text(confirmation_text, parse_mode="Markdown")
            
            # Store deletion request in context for confirmation
            context.user_data['pending_deletion'] = {
                'type': deletion_type,
                'user_id': user_id
            }
            
        except Exception as e:
            logger.error(f"Delete data command failed: {e}")
            await update.message.reply_text("❌ Failed to process deletion request.")
    
    async def _consent_command(self, update, context):
        """Handle /consent command."""
        try:
            user_id = update.effective_user.id
            
            # Log consent request
            self.security.log_user_action(
                event_type=AuditEventType.USER_LOGIN,
                user_id=user_id,
                description="Consent information requested",
                severity=AuditSeverity.LOW
            )
            
            consent_text = GDPR.generate_consent_text()
            await update.message.reply_text(consent_text)
            
        except Exception as e:
            logger.error(f"Consent command failed: {e}")
            await update.message.reply_text("❌ Failed to retrieve consent information.")
    
    async def _security_status_command(self, update, context):
        """Handle /security_status command (admin only)."""
        try:
            user_id = update.effective_user.id
            
            # Check if user is admin
            if hasattr(self.bot, 'config_manager'):
                telegram_config = self.bot.config_manager.get_telegram_config()
                admin_user_ids = telegram_config.get("admin_user_ids", [])
                
                if user_id not in admin_user_ids:
                    await update.message.reply_text("❌ This command is only available to administrators.")
                    return
            
            # Get security status
            status = self.security.get_security_status()
            
            status_text = "🔒 **Security Status**\n\n"
            
            # Encryption status
            if status['encryption']['enabled']:
                status_text += "🔐 Encryption: ✅ Enabled\n"
                if status['encryption']['field_encryption']:
                    status_text += "📝 Field Encryption: ✅ Enabled\n"
            else:
                status_text += "🔐 Encryption: ❌ Disabled\n"
            
            # Audit logging status
            if status['audit_logging']['enabled']:
                status_text += "📋 Audit Logging: ✅ Enabled\n"
            else:
                status_text += "📋 Audit Logging: ❌ Disabled\n"
            
            # Rate limiting status
            if status['rate_limiting']['enabled']:
                status_text += "⏱️ Rate Limiting: ✅ Enabled\n"
                if status['rate_limiting']['redis_available']:
                    status_text += "🔴 Redis: ✅ Available\n"
                else:
                    status_text += "🔴 Redis: ❌ Not Available (using memory)\n"
            else:
                status_text += "⏱️ Rate Limiting: ❌ Disabled\n"
            
            # Data protection status
            if status['data_protection']['enabled']:
                status_text += "🛡️ Data Protection: ✅ Enabled\n"
            else:
                status_text += "🛡️ Data Protection: ❌ Disabled\n"
            
            await update.message.reply_text(status_text, parse_mode="Markdown")
            
        except Exception as e:
            logger.error(f"Security status command failed: {e}")
            await update.message.reply_text("❌ Failed to retrieve security status.")
    
    def handle_message_security(self, user_id: int, message_content: str, ip_address: Optional[str] = None) -> bool:
        """
        Handle security checks for incoming messages.
        
        Args:
            user_id: User ID
            message_content: Message content
            ip_address: User's IP address
            
        Returns:
            True if message passes security checks, False otherwise
        """
        try:
            # Check rate limit
            if not self.security.check_rate_limit(user_id, "message", ip_address):
                return False
            
            # Validate input
            if not self.security.validate_user_input(message_content):
                return False
            
            # Log message
            self.security.log_user_action(
                event_type=AuditEventType.MESSAGE_RECEIVED,
                user_id=user_id,
                description="Message received",
                details={"message_length": len(message_content)},
                severity=AuditSeverity.LOW,
                ip_address=ip_address
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Message security check failed: {e}")
            return True  # Allow message if security check fails
    
    def handle_voice_security(self, user_id: int, voice_duration: float, ip_address: Optional[str] = None) -> bool:
        """
        Handle security checks for voice messages.
        
        Args:
            user_id: User ID
            voice_duration: Voice message duration in seconds
            ip_address: User's IP address
            
        Returns:
            True if voice message passes security checks, False otherwise
        """
        try:
            # Check rate limit
            if not self.security.check_rate_limit(user_id, "voice", ip_address):
                return False
            
            # Check voice duration limits
            max_duration = 300  # 5 minutes
            if voice_duration > max_duration:
                self.security.log_security_event(
                    event_type=AuditEventType.SECURITY_VIOLATION,
                    description="Voice message too long",
                    details={"duration": voice_duration, "max_allowed": max_duration},
                    user_id=user_id,
                    severity=AuditSeverity.MEDIUM,
                    ip_address=ip_address
                )
                return False
            
            # Log voice message
            self.security.log_user_action(
                event_type=AuditEventType.VOICE_PROCESSED,
                user_id=user_id,
                description="Voice message received",
                details={"duration": voice_duration},
                severity=AuditSeverity.LOW,
                ip_address=ip_address
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Voice security check failed: {e}")
            return True  # Allow voice if security check fails
    
    def handle_payment_security(self, user_id: int, amount: float, currency: str, ip_address: Optional[str] = None) -> bool:
        """
        Handle security checks for payments.
        
        Args:
            user_id: User ID
            amount: Payment amount
            currency: Payment currency
            ip_address: User's IP address
            
        Returns:
            True if payment passes security checks, False otherwise
        """
        try:
            # Check rate limit
            if not self.security.check_rate_limit(user_id, "payment", ip_address):
                return False
            
            # Log payment attempt
            self.security.log_user_action(
                event_type=AuditEventType.PAYMENT_INITIATED,
                user_id=user_id,
                description="Payment initiated",
                details={"amount": amount, "currency": currency},
                severity=AuditSeverity.MEDIUM,
                ip_address=ip_address
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Payment security check failed: {e}")
            return True  # Allow payment if security check fails

def integrate_security_with_bot(bot_instance, redis_client=None) -> Optional[SecurityIntegration]:
    """
    Integrate security features with the VoicePal bot.
    
    Args:
        bot_instance: VoicePal bot instance
        redis_client: Redis client for distributed features
        
    Returns:
        SecurityIntegration instance if successful, None otherwise
    """
    try:
        # Create security manager
        security_manager = SecurityManager(
            database=bot_instance.database,
            redis_client=redis_client,
            enable_encryption=True,
            enable_audit_logging=True,
            enable_rate_limiting=True
        )
        
        # Create security integration
        security_integration = SecurityIntegration(bot_instance, security_manager)
        
        # Store reference in bot instance
        bot_instance.security_manager = security_manager
        bot_instance.security_integration = security_integration
        
        # Register with feature registry if available
        if hasattr(bot_instance, 'feature_registry'):
            bot_instance.feature_registry.register_feature(
                "security",
                True,
                "Comprehensive security features including encryption, audit logging, and rate limiting"
            )
        
        logger.info("Security successfully integrated with VoicePal bot")
        return security_integration
        
    except Exception as e:
        logger.error(f"Failed to integrate security with bot: {e}")
        return None
