"""
Tests for the provider factory.
"""

import unittest
import asyncio
from typing import Dict, Any, Optional, List, Type
from unittest.mock import MagicMock, patch

from bot.providers.core.provider import Provider
from bot.providers.core.factory import ProviderFactory
from bot.providers.core.exceptions import (
    ProviderError,
    ProviderFactoryError,
    ProviderNotFoundError,
    ProviderConfigError,
    ProviderInitializationError
)

class TestProviderFactory(unittest.TestCase):
    """Tests for the ProviderFactory class."""
    
    def setUp(self):
        """Set up test case."""
        # Create a mock provider class
        class MockProvider(Provider):
            provider_type = "mock"
            provider_name = "mock"
            
            def validate_config(self):
                pass
            
            def initialize(self):
                self.initialized = True
            
            def shutdown(self):
                self.initialized = False
        
        self.MockProvider = MockProvider
        
        # Create a mock provider class with initialization error
        class MockProviderWithInitError(Provider):
            provider_type = "mock"
            provider_name = "mock_init_error"
            
            def validate_config(self):
                pass
            
            def initialize(self):
                raise ProviderInitializationError("Initialization error")
            
            def shutdown(self):
                self.initialized = False
        
        self.MockProviderWithInitError = MockProviderWithInitError
        
        # Create a mock provider class with config error
        class MockProviderWithConfigError(Provider):
            provider_type = "mock"
            provider_name = "mock_config_error"
            
            def validate_config(self):
                raise ProviderConfigError("Configuration error")
            
            def initialize(self):
                self.initialized = True
            
            def shutdown(self):
                self.initialized = False
        
        self.MockProviderWithConfigError = MockProviderWithConfigError
    
    def test_factory_init(self):
        """Test ProviderFactory initialization."""
        # Initialize factory
        factory = ProviderFactory("mock", "default_provider")
        
        # Check factory attributes
        self.assertEqual(factory.provider_type, "mock")
        self.assertEqual(factory.default_provider, "default_provider")
        self.assertEqual(factory.providers, {})
        self.assertEqual(factory.instances, {})
    
    def test_register_provider(self):
        """Test ProviderFactory.register_provider method."""
        # Initialize factory
        factory = ProviderFactory("mock")
        
        # Register provider
        factory.register_provider("mock_provider", self.MockProvider)
        
        # Check registered provider
        self.assertIn("mock_provider", factory.providers)
        self.assertEqual(factory.providers["mock_provider"], self.MockProvider)
        
        # Try to register the same provider again
        with self.assertRaises(ProviderFactoryError):
            factory.register_provider("mock_provider", self.MockProvider)
    
    def test_unregister_provider(self):
        """Test ProviderFactory.unregister_provider method."""
        # Initialize factory
        factory = ProviderFactory("mock")
        
        # Register provider
        factory.register_provider("mock_provider", self.MockProvider)
        
        # Unregister provider
        factory.unregister_provider("mock_provider")
        
        # Check unregistered provider
        self.assertNotIn("mock_provider", factory.providers)
        
        # Try to unregister a non-existent provider
        with self.assertRaises(ProviderNotFoundError):
            factory.unregister_provider("non_existent_provider")
    
    def test_get_provider_class(self):
        """Test ProviderFactory.get_provider_class method."""
        # Initialize factory
        factory = ProviderFactory("mock", "mock_provider")
        
        # Register provider
        factory.register_provider("mock_provider", self.MockProvider)
        
        # Get provider class
        provider_class = factory.get_provider_class()
        self.assertEqual(provider_class, self.MockProvider)
        
        # Get provider class with explicit name
        provider_class = factory.get_provider_class("mock_provider")
        self.assertEqual(provider_class, self.MockProvider)
        
        # Try to get a non-existent provider class
        with self.assertRaises(ProviderNotFoundError):
            factory.get_provider_class("non_existent_provider")
        
        # Try to get a provider class without default provider
        factory = ProviderFactory("mock")
        with self.assertRaises(ProviderNotFoundError):
            factory.get_provider_class()
    
    def test_create_provider(self):
        """Test ProviderFactory.create_provider method."""
        # Initialize factory
        factory = ProviderFactory("mock", "mock_provider")
        
        # Register provider
        factory.register_provider("mock_provider", self.MockProvider)
        
        # Create provider
        provider = factory.create_provider({})
        
        # Check provider
        self.assertIsInstance(provider, self.MockProvider)
        self.assertTrue(provider.initialized)
        
        # Create provider without initialization
        provider = factory.create_provider({}, initialize=False)
        
        # Check provider
        self.assertIsInstance(provider, self.MockProvider)
        self.assertFalse(provider.initialized)
        
        # Try to create a provider with initialization error
        factory.register_provider("mock_init_error", self.MockProviderWithInitError)
        with self.assertRaises(ProviderInitializationError):
            factory.create_provider({}, "mock_init_error")
        
        # Try to create a provider with config error
        factory.register_provider("mock_config_error", self.MockProviderWithConfigError)
        with self.assertRaises(ProviderConfigError):
            factory.create_provider({}, "mock_config_error")
    
    def test_get_or_create_provider(self):
        """Test ProviderFactory.get_or_create_provider method."""
        # Initialize factory
        factory = ProviderFactory("mock", "mock_provider")
        
        # Register provider
        factory.register_provider("mock_provider", self.MockProvider)
        
        # Get or create provider
        provider1 = factory.get_or_create_provider({})
        
        # Check provider
        self.assertIsInstance(provider1, self.MockProvider)
        self.assertTrue(provider1.initialized)
        
        # Get or create provider again
        provider2 = factory.get_or_create_provider({})
        
        # Check that the same provider instance is returned
        self.assertIs(provider2, provider1)
    
    def test_get_provider(self):
        """Test ProviderFactory.get_provider method."""
        # Initialize factory
        factory = ProviderFactory("mock", "mock_provider")
        
        # Register provider
        factory.register_provider("mock_provider", self.MockProvider)
        
        # Create provider
        provider = factory.create_provider({})
        
        # Get provider
        provider2 = factory.get_provider()
        
        # Check that the same provider instance is returned
        self.assertIs(provider2, provider)
        
        # Try to get a non-existent provider
        with self.assertRaises(ProviderNotFoundError):
            factory.get_provider("non_existent_provider")
    
    def test_shutdown_provider(self):
        """Test ProviderFactory.shutdown_provider method."""
        # Initialize factory
        factory = ProviderFactory("mock", "mock_provider")
        
        # Register provider
        factory.register_provider("mock_provider", self.MockProvider)
        
        # Create provider
        provider = factory.create_provider({})
        
        # Shutdown provider
        factory.shutdown_provider()
        
        # Check that provider is shutdown
        self.assertFalse(provider.initialized)
        
        # Check that provider instance is removed
        self.assertNotIn("mock_provider", factory.instances)
        
        # Try to shutdown a non-existent provider
        with self.assertRaises(ProviderNotFoundError):
            factory.shutdown_provider("non_existent_provider")
    
    def test_shutdown_all(self):
        """Test ProviderFactory.shutdown_all method."""
        # Initialize factory
        factory = ProviderFactory("mock")
        
        # Register providers
        factory.register_provider("mock_provider1", self.MockProvider)
        factory.register_provider("mock_provider2", self.MockProvider)
        
        # Create providers
        provider1 = factory.create_provider({}, "mock_provider1")
        provider2 = factory.create_provider({}, "mock_provider2")
        
        # Shutdown all providers
        factory.shutdown_all()
        
        # Check that providers are shutdown
        self.assertFalse(provider1.initialized)
        self.assertFalse(provider2.initialized)
        
        # Check that provider instances are removed
        self.assertEqual(factory.instances, {})
    
    def test_get_registered_providers(self):
        """Test ProviderFactory.get_registered_providers method."""
        # Initialize factory
        factory = ProviderFactory("mock")
        
        # Register providers
        factory.register_provider("mock_provider1", self.MockProvider)
        factory.register_provider("mock_provider2", self.MockProvider)
        
        # Get registered providers
        providers = factory.get_registered_providers()
        
        # Check registered providers
        self.assertEqual(set(providers), {"mock_provider1", "mock_provider2"})
    
    def test_get_active_providers(self):
        """Test ProviderFactory.get_active_providers method."""
        # Initialize factory
        factory = ProviderFactory("mock")
        
        # Register providers
        factory.register_provider("mock_provider1", self.MockProvider)
        factory.register_provider("mock_provider2", self.MockProvider)
        
        # Create providers
        factory.create_provider({}, "mock_provider1")
        
        # Get active providers
        providers = factory.get_active_providers()
        
        # Check active providers
        self.assertEqual(providers, ["mock_provider1"])

if __name__ == "__main__":
    unittest.main()
