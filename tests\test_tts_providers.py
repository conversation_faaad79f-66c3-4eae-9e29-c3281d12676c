"""
Test script for TTS providers.

This script tests the Google TTS provider implemented in the VoicePal bot.
It generates speech using the provider and saves the output to a file.

Usage:
    python test_tts_providers.py
"""

import os
import logging
import tempfile
from pathlib import Path
from bot.tts_providers import (
    TTSProviderFactory,
    GoogleTTSProvider
)

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# Test text
TEST_TEXT = "Hello, this is a test of the text-to-speech system. I hope you can hear me clearly."

def test_google_tts():
    """Test Google TTS provider."""
    logger.info("Testing Google TTS provider...")

    # Create provider
    provider = GoogleTTSProvider()

    # Get available languages
    languages = provider.get_available_languages()
    logger.info(f"Available languages: {len(languages)}")

    # Generate speech
    output_path = provider.generate_speech(TEST_TEXT)

    if output_path:
        # Copy to a permanent location
        output_dir = Path("test_output")
        output_dir.mkdir(exist_ok=True)

        permanent_path = output_dir / "google_tts_output.mp3"

        # Copy file
        with open(output_path, "rb") as src, open(permanent_path, "wb") as dst:
            dst.write(src.read())

        # Clean up temporary file
        provider.cleanup_temp_file(output_path)

        logger.info(f"Google TTS output saved to: {permanent_path}")
        return True
    else:
        logger.error("Failed to generate speech with Google TTS")
        return False



def test_factory():
    """Test the TTS provider factory."""
    logger.info("Testing TTS provider factory...")

    # Test creating Google TTS provider
    provider_type = TTSProviderFactory.GOOGLE_TTS
    try:
        tts_provider = TTSProviderFactory.create_provider(provider_type)
        logger.info(f"Successfully created provider: {provider_type}")
        return True
    except Exception as e:
        logger.error(f"Error creating provider {provider_type}: {e}")
        return False

def main():
    """Run all tests."""
    logger.info("Testing Google TTS provider...")

    # Create output directory
    output_dir = Path("test_output")
    output_dir.mkdir(exist_ok=True)

    # Test factory
    factory_result = test_factory()

    # Test Google TTS
    google_result = test_google_tts()

    # Print summary
    logger.info("\nTest Results:")
    logger.info(f"Factory: {'Success' if factory_result else 'Failed'}")
    logger.info(f"Google TTS: {'Success' if google_result else 'Failed'}")

    logger.info("\nOutput files are in the 'test_output' directory")

if __name__ == "__main__":
    main()
