"""
Async processor for VoicePal.

This module provides asynchronous processing for heavy operations.
"""

import asyncio
import logging
import time
from typing import Any, Callable, Dict, List, Optional, Union
from dataclasses import dataclass
from enum import Enum
from concurrent.futures import Thread<PERSON><PERSON><PERSON>xecutor, ProcessPoolExecutor
import threading
import queue

logger = logging.getLogger(__name__)

class TaskPriority(Enum):
    """Task priority levels."""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4

class TaskStatus(Enum):
    """Task status."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

@dataclass
class Task:
    """Async task definition."""
    id: str
    func: Callable
    args: tuple
    kwargs: dict
    priority: TaskPriority
    created_at: float
    status: TaskStatus = TaskStatus.PENDING
    result: Any = None
    error: Optional[Exception] = None
    started_at: Optional[float] = None
    completed_at: Optional[float] = None
    
    def __lt__(self, other):
        """Compare tasks by priority and creation time."""
        if self.priority.value != other.priority.value:
            return self.priority.value > other.priority.value
        return self.created_at < other.created_at

@dataclass
class ProcessorStats:
    """Async processor statistics."""
    total_tasks: int
    pending_tasks: int
    running_tasks: int
    completed_tasks: int
    failed_tasks: int
    cancelled_tasks: int
    average_execution_time: float
    worker_threads: int
    worker_processes: int

class AsyncProcessor:
    """Asynchronous processor for heavy operations."""
    
    def __init__(
        self,
        max_workers: int = 4,
        max_processes: int = 2,
        queue_size: int = 1000,
        enable_process_pool: bool = True
    ):
        """
        Initialize async processor.
        
        Args:
            max_workers: Maximum number of worker threads
            max_processes: Maximum number of worker processes
            queue_size: Maximum queue size
            enable_process_pool: Whether to enable process pool for CPU-intensive tasks
        """
        self.max_workers = max_workers
        self.max_processes = max_processes
        self.queue_size = queue_size
        self.enable_process_pool = enable_process_pool
        
        # Task management
        self.tasks: Dict[str, Task] = {}
        self.task_queue = asyncio.PriorityQueue(maxsize=queue_size)
        self.task_counter = 0
        
        # Executors
        self.thread_executor = ThreadPoolExecutor(max_workers=max_workers)
        self.process_executor = ProcessPoolExecutor(max_workers=max_processes) if enable_process_pool else None
        
        # Statistics
        self.stats = ProcessorStats(
            total_tasks=0,
            pending_tasks=0,
            running_tasks=0,
            completed_tasks=0,
            failed_tasks=0,
            cancelled_tasks=0,
            average_execution_time=0.0,
            worker_threads=max_workers,
            worker_processes=max_processes if enable_process_pool else 0
        )
        
        # Worker management
        self.workers: List[asyncio.Task] = []
        self.is_running = False
        
        logger.info(f"Async processor initialized: {max_workers} threads, {max_processes} processes")
    
    async def start(self):
        """Start the async processor."""
        if self.is_running:
            return
        
        self.is_running = True
        
        # Start worker tasks
        for i in range(self.max_workers):
            worker = asyncio.create_task(self._worker(f"worker-{i}"))
            self.workers.append(worker)
        
        logger.info(f"Async processor started with {len(self.workers)} workers")
    
    async def stop(self):
        """Stop the async processor."""
        if not self.is_running:
            return
        
        self.is_running = False
        
        # Cancel all workers
        for worker in self.workers:
            worker.cancel()
        
        # Wait for workers to finish
        await asyncio.gather(*self.workers, return_exceptions=True)
        
        # Shutdown executors
        self.thread_executor.shutdown(wait=True)
        if self.process_executor:
            self.process_executor.shutdown(wait=True)
        
        logger.info("Async processor stopped")
    
    async def _worker(self, worker_name: str):
        """Worker coroutine to process tasks."""
        logger.debug(f"Worker {worker_name} started")
        
        while self.is_running:
            try:
                # Get task from queue with timeout
                try:
                    priority, task = await asyncio.wait_for(
                        self.task_queue.get(),
                        timeout=1.0
                    )
                except asyncio.TimeoutError:
                    continue
                
                # Process the task
                await self._process_task(task, worker_name)
                
                # Mark task as done
                self.task_queue.task_done()
                
            except asyncio.CancelledError:
                logger.debug(f"Worker {worker_name} cancelled")
                break
            except Exception as e:
                logger.error(f"Worker {worker_name} error: {e}")
        
        logger.debug(f"Worker {worker_name} stopped")
    
    async def _process_task(self, task: Task, worker_name: str):
        """Process a single task."""
        try:
            # Update task status
            task.status = TaskStatus.RUNNING
            task.started_at = time.time()
            self.stats.running_tasks += 1
            self.stats.pending_tasks -= 1
            
            logger.debug(f"Worker {worker_name} processing task {task.id}")
            
            # Determine execution method
            if hasattr(task.func, '_cpu_intensive') and task.func._cpu_intensive and self.process_executor:
                # CPU-intensive task - use process pool
                loop = asyncio.get_event_loop()
                result = await loop.run_in_executor(
                    self.process_executor,
                    task.func,
                    *task.args,
                    **task.kwargs
                )
            elif asyncio.iscoroutinefunction(task.func):
                # Async function
                result = await task.func(*task.args, **task.kwargs)
            else:
                # Regular function - use thread pool
                loop = asyncio.get_event_loop()
                result = await loop.run_in_executor(
                    self.thread_executor,
                    task.func,
                    *task.args,
                    **task.kwargs
                )
            
            # Update task with result
            task.result = result
            task.status = TaskStatus.COMPLETED
            task.completed_at = time.time()
            
            # Update statistics
            self.stats.running_tasks -= 1
            self.stats.completed_tasks += 1
            
            execution_time = task.completed_at - task.started_at
            self._update_average_execution_time(execution_time)
            
            logger.debug(f"Task {task.id} completed in {execution_time:.3f}s")
            
        except Exception as e:
            # Update task with error
            task.error = e
            task.status = TaskStatus.FAILED
            task.completed_at = time.time()
            
            # Update statistics
            self.stats.running_tasks -= 1
            self.stats.failed_tasks += 1
            
            logger.error(f"Task {task.id} failed: {e}")
    
    def _update_average_execution_time(self, execution_time: float):
        """Update average execution time."""
        total_completed = self.stats.completed_tasks
        if total_completed == 1:
            self.stats.average_execution_time = execution_time
        else:
            self.stats.average_execution_time = (
                (self.stats.average_execution_time * (total_completed - 1) + execution_time) /
                total_completed
            )
    
    async def submit_task(
        self,
        func: Callable,
        *args,
        priority: TaskPriority = TaskPriority.NORMAL,
        task_id: Optional[str] = None,
        **kwargs
    ) -> str:
        """
        Submit a task for async processing.
        
        Args:
            func: Function to execute
            args: Function arguments
            priority: Task priority
            task_id: Optional task ID
            kwargs: Function keyword arguments
            
        Returns:
            Task ID
        """
        if not self.is_running:
            raise RuntimeError("Async processor is not running")
        
        # Generate task ID
        if not task_id:
            self.task_counter += 1
            task_id = f"task-{self.task_counter}"
        
        # Create task
        task = Task(
            id=task_id,
            func=func,
            args=args,
            kwargs=kwargs,
            priority=priority,
            created_at=time.time()
        )
        
        # Store task
        self.tasks[task_id] = task
        
        # Add to queue
        await self.task_queue.put((priority, task))
        
        # Update statistics
        self.stats.total_tasks += 1
        self.stats.pending_tasks += 1
        
        logger.debug(f"Task {task_id} submitted with priority {priority.name}")
        return task_id
    
    def get_task_status(self, task_id: str) -> Optional[TaskStatus]:
        """
        Get task status.
        
        Args:
            task_id: Task ID
            
        Returns:
            Task status or None if not found
        """
        task = self.tasks.get(task_id)
        return task.status if task else None
    
    def get_task_result(self, task_id: str) -> Any:
        """
        Get task result.
        
        Args:
            task_id: Task ID
            
        Returns:
            Task result or None if not completed
        """
        task = self.tasks.get(task_id)
        if task and task.status == TaskStatus.COMPLETED:
            return task.result
        return None
    
    def get_task_error(self, task_id: str) -> Optional[Exception]:
        """
        Get task error.
        
        Args:
            task_id: Task ID
            
        Returns:
            Task error or None if no error
        """
        task = self.tasks.get(task_id)
        if task and task.status == TaskStatus.FAILED:
            return task.error
        return None
    
    async def wait_for_task(self, task_id: str, timeout: Optional[float] = None) -> Any:
        """
        Wait for task completion.
        
        Args:
            task_id: Task ID
            timeout: Timeout in seconds
            
        Returns:
            Task result
            
        Raises:
            asyncio.TimeoutError: If timeout is reached
            Exception: If task failed
        """
        task = self.tasks.get(task_id)
        if not task:
            raise ValueError(f"Task {task_id} not found")
        
        start_time = time.time()
        
        while task.status in [TaskStatus.PENDING, TaskStatus.RUNNING]:
            if timeout and (time.time() - start_time) > timeout:
                raise asyncio.TimeoutError(f"Task {task_id} timed out")
            
            await asyncio.sleep(0.1)
        
        if task.status == TaskStatus.COMPLETED:
            return task.result
        elif task.status == TaskStatus.FAILED:
            raise task.error
        else:
            raise RuntimeError(f"Task {task_id} was cancelled")
    
    def cancel_task(self, task_id: str) -> bool:
        """
        Cancel a task.
        
        Args:
            task_id: Task ID
            
        Returns:
            True if cancelled, False otherwise
        """
        task = self.tasks.get(task_id)
        if task and task.status == TaskStatus.PENDING:
            task.status = TaskStatus.CANCELLED
            self.stats.pending_tasks -= 1
            self.stats.cancelled_tasks += 1
            return True
        return False
    
    def cleanup_completed_tasks(self, max_age: int = 3600):
        """
        Clean up completed tasks older than max_age.
        
        Args:
            max_age: Maximum age in seconds
        """
        current_time = time.time()
        tasks_to_remove = []
        
        for task_id, task in self.tasks.items():
            if (task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED] and
                task.completed_at and (current_time - task.completed_at) > max_age):
                tasks_to_remove.append(task_id)
        
        for task_id in tasks_to_remove:
            del self.tasks[task_id]
        
        logger.debug(f"Cleaned up {len(tasks_to_remove)} old tasks")
    
    def get_stats(self) -> ProcessorStats:
        """Get processor statistics."""
        # Update current counts
        self.stats.pending_tasks = sum(1 for task in self.tasks.values() if task.status == TaskStatus.PENDING)
        self.stats.running_tasks = sum(1 for task in self.tasks.values() if task.status == TaskStatus.RUNNING)
        
        return self.stats

# Decorator for marking CPU-intensive functions
def cpu_intensive(func):
    """Decorator to mark a function as CPU-intensive."""
    func._cpu_intensive = True
    return func

# Utility functions for common async operations
async def process_voice_async(processor: AsyncProcessor, voice_data: bytes, user_id: int) -> str:
    """Process voice data asynchronously."""
    task_id = await processor.submit_task(
        _process_voice_sync,
        voice_data,
        user_id,
        priority=TaskPriority.HIGH
    )
    return await processor.wait_for_task(task_id)

@cpu_intensive
def _process_voice_sync(voice_data: bytes, user_id: int) -> str:
    """Synchronous voice processing function."""
    # Placeholder for actual voice processing
    time.sleep(2)  # Simulate processing time
    return f"Processed voice for user {user_id}"

async def generate_ai_response_async(processor: AsyncProcessor, prompt: str, user_id: int) -> str:
    """Generate AI response asynchronously."""
    task_id = await processor.submit_task(
        _generate_ai_response_sync,
        prompt,
        user_id,
        priority=TaskPriority.NORMAL
    )
    return await processor.wait_for_task(task_id)

def _generate_ai_response_sync(prompt: str, user_id: int) -> str:
    """Synchronous AI response generation."""
    # Placeholder for actual AI processing
    time.sleep(1)  # Simulate processing time
    return f"AI response for user {user_id}: {prompt[:50]}..."
