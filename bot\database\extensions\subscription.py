"""
Database extension for subscription management.

This module extends the database with subscription-related tables and methods.
"""

import logging
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple, Union

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

def extend_database_for_subscription(database):
    """
    Extend the database with subscription-related tables and methods.

    Args:
        database: Database instance to extend
    """
    # Create subscriptions table
    database.cursor.execute("""
    CREATE TABLE IF NOT EXISTS subscriptions (
        id INTEGER PRIMARY KEY,
        user_id INTEGER NOT NULL,
        package_id TEXT NOT NULL,
        subscription_id TEXT NOT NULL,
        status TEXT NOT NULL,
        start_date TEXT NOT NULL,
        end_date TEXT,
        next_billing_date TEXT,
        credits_per_cycle INTEGER NOT NULL,
        price INTEGER NOT NULL,
        currency TEXT NOT NULL,
        interval TEXT NOT NULL,
        FOREIGN KEY (user_id) REFERENCES users(user_id)
    )
    """)

    # Create subscription_events table for tracking subscription lifecycle
    database.cursor.execute("""
    CREATE TABLE IF NOT EXISTS subscription_events (
        id INTEGER PRIMARY KEY,
        subscription_id INTEGER NOT NULL,
        event_type TEXT NOT NULL,
        event_date TEXT NOT NULL,
        details TEXT,
        FOREIGN KEY (subscription_id) REFERENCES subscriptions(id)
    )
    """)

    # Add methods to the database instance
    database.add_subscription = add_subscription.__get__(database)
    database.get_subscription = get_subscription.__get__(database)
    database.get_user_subscriptions = get_user_subscriptions.__get__(database)
    database.update_subscription_status = update_subscription_status.__get__(database)
    database.add_subscription_event = add_subscription_event.__get__(database)
    database.get_subscription_events = get_subscription_events.__get__(database)
    database.process_subscription_renewal = process_subscription_renewal.__get__(database)
    database.cancel_subscription = cancel_subscription.__get__(database)

    logger.info("Database extended with methods for subscription management")

def add_subscription(self, user_id: int, package_id: str, subscription_id: str,
                    status: str, start_date: str, end_date: Optional[str],
                    next_billing_date: str, credits_per_cycle: int,
                    price: int, currency: str, interval: str) -> int:
    """
    Add a new subscription to the database.

    Args:
        user_id: User ID
        package_id: Package ID
        subscription_id: Subscription ID from payment provider
        status: Subscription status (active, canceled, etc.)
        start_date: Start date of subscription
        end_date: End date of subscription (if any)
        next_billing_date: Next billing date
        credits_per_cycle: Credits per billing cycle
        price: Price in cents
        currency: Currency code
        interval: Billing interval (month, year, etc.)

    Returns:
        int: ID of the new subscription
    """
    try:
        self.cursor.execute(
            """
            INSERT INTO subscriptions (
                user_id, package_id, subscription_id, status, start_date,
                end_date, next_billing_date, credits_per_cycle, price, currency, interval
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """,
            (
                user_id, package_id, subscription_id, status, start_date,
                end_date, next_billing_date, credits_per_cycle, price, currency, interval
            )
        )
        self.conn.commit()
        subscription_id = self.cursor.lastrowid
        logger.info(f"Added subscription {subscription_id} for user {user_id}")
        return subscription_id
    except sqlite3.Error as e:
        logger.error(f"Error adding subscription: {e}")
        return 0

def get_subscription(self, subscription_id: int) -> Optional[Dict[str, Any]]:
    """
    Get subscription by ID.

    Args:
        subscription_id: Subscription ID

    Returns:
        Dict containing subscription details or None if not found
    """
    try:
        self.cursor.execute(
            "SELECT * FROM subscriptions WHERE id = ?",
            (subscription_id,)
        )
        subscription = self.cursor.fetchone()
        return dict(subscription) if subscription else None
    except sqlite3.Error as e:
        logger.error(f"Error getting subscription {subscription_id}: {e}")
        return None

def get_user_subscriptions(self, user_id: int, active_only: bool = False) -> List[Dict[str, Any]]:
    """
    Get all subscriptions for a user.

    Args:
        user_id: User ID
        active_only: If True, only return active subscriptions

    Returns:
        List of dictionaries containing subscription details
    """
    try:
        if active_only:
            self.cursor.execute(
                "SELECT * FROM subscriptions WHERE user_id = ? AND status = 'active'",
                (user_id,)
            )
        else:
            self.cursor.execute(
                "SELECT * FROM subscriptions WHERE user_id = ?",
                (user_id,)
            )
        subscriptions = self.cursor.fetchall()
        return [dict(subscription) for subscription in subscriptions]
    except sqlite3.Error as e:
        logger.error(f"Error getting subscriptions for user {user_id}: {e}")
        return []

def update_subscription_status(self, subscription_id: int, status: str,
                              end_date: Optional[str] = None,
                              next_billing_date: Optional[str] = None) -> bool:
    """
    Update subscription status.

    Args:
        subscription_id: Subscription ID
        status: New status
        end_date: End date (for canceled subscriptions)
        next_billing_date: Next billing date (for active subscriptions)

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        if end_date and next_billing_date:
            self.cursor.execute(
                """
                UPDATE subscriptions
                SET status = ?, end_date = ?, next_billing_date = ?
                WHERE id = ?
                """,
                (status, end_date, next_billing_date, subscription_id)
            )
        elif end_date:
            self.cursor.execute(
                "UPDATE subscriptions SET status = ?, end_date = ? WHERE id = ?",
                (status, end_date, subscription_id)
            )
        elif next_billing_date:
            self.cursor.execute(
                """
                UPDATE subscriptions
                SET status = ?, next_billing_date = ?
                WHERE id = ?
                """,
                (status, next_billing_date, subscription_id)
            )
        else:
            self.cursor.execute(
                "UPDATE subscriptions SET status = ? WHERE id = ?",
                (status, subscription_id)
            )
        self.conn.commit()
        logger.info(f"Updated subscription {subscription_id} status to {status}")
        return True
    except sqlite3.Error as e:
        logger.error(f"Error updating subscription {subscription_id}: {e}")
        return False

def add_subscription_event(self, subscription_id: int, event_type: str,
                          details: Optional[str] = None) -> int:
    """
    Add a subscription event.

    Args:
        subscription_id: Subscription ID
        event_type: Event type (created, renewed, canceled, etc.)
        details: Additional details (JSON string)

    Returns:
        int: ID of the new event
    """
    try:
        event_date = datetime.now().isoformat()
        self.cursor.execute(
            """
            INSERT INTO subscription_events (
                subscription_id, event_type, event_date, details
            ) VALUES (?, ?, ?, ?)
            """,
            (subscription_id, event_type, event_date, details)
        )
        self.conn.commit()
        event_id = self.cursor.lastrowid
        logger.info(f"Added subscription event {event_id} for subscription {subscription_id}")
        return event_id
    except sqlite3.Error as e:
        logger.error(f"Error adding subscription event: {e}")
        return 0

def get_subscription_events(self, subscription_id: int) -> List[Dict[str, Any]]:
    """
    Get all events for a subscription.

    Args:
        subscription_id: Subscription ID

    Returns:
        List of dictionaries containing event details
    """
    try:
        self.cursor.execute(
            "SELECT * FROM subscription_events WHERE subscription_id = ? ORDER BY event_date DESC",
            (subscription_id,)
        )
        events = self.cursor.fetchall()
        return [dict(event) for event in events]
    except sqlite3.Error as e:
        logger.error(f"Error getting events for subscription {subscription_id}: {e}")
        return []

def process_subscription_renewal(self, subscription_id: int) -> bool:
    """
    Process subscription renewal.

    Args:
        subscription_id: Subscription ID

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Get subscription details
        subscription = self.get_subscription(subscription_id)
        if not subscription:
            logger.error(f"Subscription {subscription_id} not found")
            return False

        # Check if subscription is active
        if subscription["status"] != "active":
            logger.error(f"Subscription {subscription_id} is not active")
            return False

        # Add credits to user
        user_id = subscription["user_id"]
        credits = subscription["credits_per_cycle"]
        self.add_credits(user_id, credits, source="subscription")

        # Update next billing date
        interval = subscription["interval"]
        next_billing_date = datetime.fromisoformat(subscription["next_billing_date"])

        if interval == "month":
            new_next_billing_date = next_billing_date + timedelta(days=30)
        elif interval == "year":
            new_next_billing_date = next_billing_date + timedelta(days=365)
        else:
            new_next_billing_date = next_billing_date + timedelta(days=30)

        # Update subscription with new next billing date
        self.cursor.execute(
            """
            UPDATE subscriptions
            SET next_billing_date = ?
            WHERE id = ?
            """,
            (new_next_billing_date.isoformat(), subscription_id)
        )
        self.conn.commit()

        # Update subscription status
        self.update_subscription_status(
            subscription_id,
            "active"
        )

        # Add renewal event
        self.add_subscription_event(
            subscription_id,
            "renewed",
            f"Added {credits} credits to user {user_id}"
        )

        logger.info(f"Processed renewal for subscription {subscription_id}")
        return True
    except Exception as e:
        logger.error(f"Error processing renewal for subscription {subscription_id}: {e}")
        return False

def cancel_subscription(self, subscription_id: int) -> bool:
    """
    Cancel a subscription.

    Args:
        subscription_id: Subscription ID

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Get subscription details
        subscription = self.get_subscription(subscription_id)
        if not subscription:
            logger.error(f"Subscription {subscription_id} not found")
            return False

        # Set end date to next billing date
        end_date = subscription["next_billing_date"]

        # Update subscription status
        self.update_subscription_status(
            subscription_id,
            "canceled",
            end_date=end_date
        )

        # Add cancellation event
        self.add_subscription_event(
            subscription_id,
            "canceled",
            f"Subscription canceled, effective on {end_date}"
        )

        logger.info(f"Canceled subscription {subscription_id}")
        return True
    except Exception as e:
        logger.error(f"Error canceling subscription {subscription_id}: {e}")
        return False
