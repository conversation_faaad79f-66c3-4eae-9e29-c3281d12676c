"""
Health check routes for VoicePal API.

This module provides health check endpoints for the VoicePal API.
"""

import time
import logging
import psutil
from datetime import datetime
from typing import Dict, Any

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

from api.app import get_bot_instance, get_config

logger = logging.getLogger(__name__)

router = APIRouter()

class HealthStatus(BaseModel):
    """Health status response model."""
    status: str
    timestamp: datetime
    uptime: float
    version: str = "1.0.0"

class DetailedHealthStatus(BaseModel):
    """Detailed health status response model."""
    status: str
    timestamp: datetime
    uptime: float
    version: str = "1.0.0"
    system: Dict[str, Any]
    bot: Dict[str, Any]
    database: Dict[str, Any]
    services: Dict[str, Any]

# Track startup time
startup_time = time.time()

@router.get("/", response_model=HealthStatus)
@router.get("/status", response_model=HealthStatus)
async def health_check():
    """
    Basic health check endpoint.
    
    Returns:
        Basic health status
    """
    return HealthStatus(
        status="healthy",
        timestamp=datetime.utcnow(),
        uptime=time.time() - startup_time
    )

@router.get("/ready", response_model=HealthStatus)
async def readiness_check():
    """
    Readiness check endpoint.
    
    Returns:
        Readiness status
        
    Raises:
        HTTPException: If service is not ready
    """
    try:
        # Check if bot instance is available
        bot = get_bot_instance()
        if not bot:
            raise HTTPException(status_code=503, detail="Bot instance not available")
        
        # Check if database is accessible
        if hasattr(bot, 'database') and bot.database:
            try:
                # Simple database check
                bot.database.execute("SELECT 1")
            except Exception as e:
                logger.error(f"Database check failed: {e}")
                raise HTTPException(status_code=503, detail="Database not accessible")
        
        return HealthStatus(
            status="ready",
            timestamp=datetime.utcnow(),
            uptime=time.time() - startup_time
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Readiness check failed: {e}")
        raise HTTPException(status_code=503, detail="Service not ready")

@router.get("/live", response_model=HealthStatus)
async def liveness_check():
    """
    Liveness check endpoint.
    
    Returns:
        Liveness status
    """
    return HealthStatus(
        status="alive",
        timestamp=datetime.utcnow(),
        uptime=time.time() - startup_time
    )

@router.get("/detailed", response_model=DetailedHealthStatus)
async def detailed_health_check():
    """
    Detailed health check endpoint.
    
    Returns:
        Detailed health status including system metrics
    """
    try:
        # Get system information
        system_info = _get_system_info()
        
        # Get bot information
        bot_info = _get_bot_info()
        
        # Get database information
        database_info = _get_database_info()
        
        # Get services information
        services_info = _get_services_info()
        
        return DetailedHealthStatus(
            status="healthy",
            timestamp=datetime.utcnow(),
            uptime=time.time() - startup_time,
            system=system_info,
            bot=bot_info,
            database=database_info,
            services=services_info
        )
        
    except Exception as e:
        logger.error(f"Detailed health check failed: {e}")
        raise HTTPException(status_code=500, detail="Health check failed")

def _get_system_info() -> Dict[str, Any]:
    """Get system information."""
    try:
        return {
            "cpu_percent": psutil.cpu_percent(interval=1),
            "memory_percent": psutil.virtual_memory().percent,
            "disk_percent": psutil.disk_usage('/').percent,
            "load_average": psutil.getloadavg() if hasattr(psutil, 'getloadavg') else None,
            "boot_time": psutil.boot_time(),
            "process_count": len(psutil.pids())
        }
    except Exception as e:
        logger.warning(f"Failed to get system info: {e}")
        return {"error": str(e)}

def _get_bot_info() -> Dict[str, Any]:
    """Get bot information."""
    try:
        bot = get_bot_instance()
        if not bot:
            return {"status": "not_available"}
        
        info = {
            "status": "running",
            "initialized": hasattr(bot, 'application') and bot.application is not None
        }
        
        # Add feature information if available
        if hasattr(bot, 'feature_registry'):
            info["features"] = {
                "enabled": list(bot.feature_registry.enabled_features),
                "total": len(bot.feature_registry.features)
            }
        
        return info
        
    except Exception as e:
        logger.warning(f"Failed to get bot info: {e}")
        return {"error": str(e)}

def _get_database_info() -> Dict[str, Any]:
    """Get database information."""
    try:
        bot = get_bot_instance()
        if not bot or not hasattr(bot, 'database'):
            return {"status": "not_available"}
        
        # Test database connection
        try:
            result = bot.database.execute("SELECT 1")
            connection_status = "connected"
        except Exception:
            connection_status = "disconnected"
        
        return {
            "status": connection_status,
            "type": "sqlite",
            "file": getattr(bot.database, 'db_file', 'unknown')
        }
        
    except Exception as e:
        logger.warning(f"Failed to get database info: {e}")
        return {"error": str(e)}

def _get_services_info() -> Dict[str, Any]:
    """Get services information."""
    try:
        bot = get_bot_instance()
        services = {}
        
        if bot:
            # Check AI provider
            if hasattr(bot, 'ai_provider') and bot.ai_provider:
                services["ai_provider"] = {
                    "status": "available",
                    "type": getattr(bot.ai_provider, 'provider_type', 'unknown')
                }
            
            # Check voice processor
            if hasattr(bot, 'voice_processor') and bot.voice_processor:
                services["voice_processor"] = {
                    "status": "available",
                    "tts_provider": getattr(bot.voice_processor, 'tts_provider_type', 'unknown')
                }
            
            # Check memory manager
            if hasattr(bot, 'memory_manager') and bot.memory_manager:
                services["memory_manager"] = {
                    "status": "available",
                    "type": type(bot.memory_manager).__name__
                }
        
        return services
        
    except Exception as e:
        logger.warning(f"Failed to get services info: {e}")
        return {"error": str(e)}
