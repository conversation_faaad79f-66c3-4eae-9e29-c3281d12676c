"""
Main FastAPI application for VoicePal.

This module provides the main FastAPI application with all endpoints.
"""

import logging
from contextlib import asynccontextmanager
from typing import Dict, Any, Optional

from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

# Import API endpoints
from bot.api.endpoints.health import router as health_router
from bot.api.endpoints.users import router as users_router
from bot.api.endpoints.analytics import router as analytics_router
from bot.api.endpoints.ab_tests import router as ab_tests_router
from bot.api.endpoints.feature_flags import router as feature_flags_router
from bot.api.endpoints.notifications import router as notifications_router
from bot.api.endpoints.websocket import router as websocket_router

# Import dependencies
from bot.api.dependencies import get_database_manager, get_analytics_manager, get_advanced_features_manager

logger = logging.getLogger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    logger.info("Starting VoicePal API...")
    yield
    # Shutdown
    logger.info("Shutting down VoicePal API...")

def create_app(config: Optional[Dict[str, Any]] = None) -> FastAPI:
    """
    Create and configure the FastAPI application.
    
    Args:
        config: Application configuration
        
    Returns:
        FastAPI application instance
    """
    # Default configuration
    if config is None:
        config = {
            'title': 'VoicePal API',
            'description': 'Enterprise AI Voice Companion API',
            'version': '2.0.0',
            'enable_docs': True,
            'enable_cors': True
        }
    
    # Create FastAPI app
    app = FastAPI(
        title=config.get('title', 'VoicePal API'),
        description=config.get('description', 'Enterprise AI Voice Companion API'),
        version=config.get('version', '2.0.0'),
        docs_url="/docs" if config.get('enable_docs', True) else None,
        redoc_url="/redoc" if config.get('enable_docs', True) else None,
        lifespan=lifespan
    )
    
    # Add CORS middleware
    if config.get('enable_cors', True):
        app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
    
    # Include routers
    app.include_router(health_router, tags=["Health"])
    app.include_router(users_router, prefix="/api/v1", tags=["Users"])
    app.include_router(analytics_router, prefix="/api/v1", tags=["Analytics"])
    app.include_router(ab_tests_router, prefix="/api/v1", tags=["A/B Testing"])
    app.include_router(feature_flags_router, prefix="/api/v1", tags=["Feature Flags"])
    app.include_router(notifications_router, prefix="/api/v1", tags=["Notifications"])
    app.include_router(websocket_router, tags=["WebSocket"])
    
    # Global exception handler
    @app.exception_handler(Exception)
    async def global_exception_handler(request, exc):
        logger.error(f"Global exception: {exc}")
        return JSONResponse(
            status_code=500,
            content={
                "error": {
                    "code": "INTERNAL_SERVER_ERROR",
                    "message": "An internal server error occurred",
                    "timestamp": "2024-01-01T00:00:00Z"
                }
            }
        )
    
    # Root endpoint
    @app.get("/")
    async def root():
        """Root endpoint."""
        return {
            "message": "VoicePal API",
            "version": config.get('version', '2.0.0'),
            "status": "running"
        }
    
    logger.info(f"VoicePal API created with config: {config}")
    return app

# Create default app instance
app = create_app()
