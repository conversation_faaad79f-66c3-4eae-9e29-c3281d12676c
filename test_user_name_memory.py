#!/usr/bin/env python
"""
Test script for user name memory in VoicePal.

This script tests the enhanced memory manager's ability to remember user names.

Usage:
    python test_user_name_memory.py
"""

import os
import sys
import logging
import asyncio
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath("."))

# Import the necessary components
from bot.features.enhanced_memory_manager import EnhancedMemoryManager
from bot.database.database import Database
from bot.database.extensions.user_preferences import extend_database_for_user_preferences
from bot.providers.ai.google_ai_provider import GoogleAIProvider

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

async def test_user_name_remembering():
    """Test the user name remembering in the memory manager."""
    logger.info("=== Testing User Name Remembering ===")
    
    # Create a temporary database
    db_path = Path("test_memory.db")
    if db_path.exists():
        db_path.unlink()
    
    # Initialize components
    database = Database(db_path)
    
    # Extend database with user preferences
    extend_database_for_user_preferences(database)
    
    # Create a mock AI provider
    class MockAIProvider(GoogleAIProvider):
        def __init__(self):
            self.supported_features = ["text_generation"]
        
        def supports_feature(self, feature_name):
            return feature_name in self.supported_features
        
        async def generate_response(self, message, language=None, user_context=None):
            return {"text": f"Response to: {message}", "language": language or "en"}
    
    ai_provider = MockAIProvider()
    
    # Create memory manager
    memory_manager = EnhancedMemoryManager(database, ai_provider)
    
    # Add a test user
    user_id = 12345
    database.add_user(user_id, "johndoe", "John", "Doe")
    
    # Test getting user name
    user_name = memory_manager.get_user_name_from_context(user_id)
    logger.info(f"User name from context: {user_name}")
    assert user_name == "John", f"Expected 'John', got '{user_name}'"
    
    # Test with preferred name
    database.update_user_preference(user_id, "preferred_name", "Johnny")
    user_name = memory_manager.get_user_name_from_context(user_id)
    logger.info(f"User name after setting preferred name: {user_name}")
    assert user_name == "Johnny", f"Expected 'Johnny', got '{user_name}'"
    
    # Test with non-existent user
    non_existent_user_id = 99999
    user_name = memory_manager.get_user_name_from_context(non_existent_user_id)
    logger.info(f"User name for non-existent user: '{user_name}'")
    assert user_name == "", f"Expected empty string, got '{user_name}'"
    
    logger.info("User name remembering tests passed!")
    return True

async def run_test():
    """Run the test."""
    try:
        await test_user_name_remembering()
        logger.info("\n=== All Tests Passed! ===")
        return True
    except AssertionError as e:
        logger.error(f"Test failed: {e}")
        return False
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False
    finally:
        # Clean up test database
        db_path = Path("test_memory.db")
        if db_path.exists():
            db_path.unlink()

def main():
    """Run the tests."""
    asyncio.run(run_test())

if __name__ == "__main__":
    main()
