"""
Validate that the test files are correctly structured.
"""

import os
import ast
import sys

def validate_test_file(file_path):
    """Validate that a test file is syntactically correct."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Parse the file to check for syntax errors
        ast.parse(content)
        
        # Check for test functions
        tree = ast.parse(content)
        test_functions = []
        test_classes = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef) and node.name.startswith('test_'):
                test_functions.append(node.name)
            elif isinstance(node, ast.ClassDef) and node.name.startswith('Test'):
                test_classes.append(node.name)
        
        return {
            'valid': True,
            'test_functions': test_functions,
            'test_classes': test_classes,
            'error': None
        }
    except SyntaxError as e:
        return {
            'valid': False,
            'test_functions': [],
            'test_classes': [],
            'error': f"Syntax error: {e}"
        }
    except Exception as e:
        return {
            'valid': False,
            'test_functions': [],
            'test_classes': [],
            'error': f"Error: {e}"
        }

def find_test_files():
    """Find all test files."""
    test_files = []
    
    # Look for test files in various locations
    test_locations = [
        'tests/providers',
        'tests/utils',
        'tests/features', 
        'tests/integration',
        'tests/e2e',
        'tests'
    ]
    
    for location in test_locations:
        if os.path.exists(location):
            for root, dirs, files in os.walk(location):
                for file in files:
                    if file.startswith('test_') and file.endswith('.py'):
                        test_files.append(os.path.join(root, file))
    
    return test_files

def main():
    """Main validation function."""
    print("Test File Validation")
    print("=" * 50)
    
    test_files = find_test_files()
    
    if not test_files:
        print("No test files found.")
        return False
    
    print(f"Found {len(test_files)} test files:")
    
    total_tests = 0
    valid_files = 0
    
    for test_file in test_files:
        print(f"\nValidating: {test_file}")
        result = validate_test_file(test_file)
        
        if result['valid']:
            valid_files += 1
            print(f"  ✅ Valid syntax")
            print(f"  📊 Test functions: {len(result['test_functions'])}")
            print(f"  📊 Test classes: {len(result['test_classes'])}")
            
            if result['test_functions']:
                print(f"  🔍 Functions: {', '.join(result['test_functions'][:3])}{'...' if len(result['test_functions']) > 3 else ''}")
            
            if result['test_classes']:
                print(f"  🔍 Classes: {', '.join(result['test_classes'])}")
            
            total_tests += len(result['test_functions'])
            # Count methods in test classes
            try:
                with open(test_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                tree = ast.parse(content)
                for node in ast.walk(tree):
                    if isinstance(node, ast.ClassDef) and node.name.startswith('Test'):
                        for item in node.body:
                            if isinstance(item, ast.FunctionDef) and item.name.startswith('test_'):
                                total_tests += 1
            except:
                pass
        else:
            print(f"  ❌ Invalid: {result['error']}")
    
    print(f"\n{'='*50}")
    print(f"Summary:")
    print(f"  📁 Total files: {len(test_files)}")
    print(f"  ✅ Valid files: {valid_files}")
    print(f"  ❌ Invalid files: {len(test_files) - valid_files}")
    print(f"  🧪 Total tests: {total_tests}")
    
    if valid_files == len(test_files):
        print("\n🎉 All test files are valid!")
        return True
    else:
        print(f"\n❌ {len(test_files) - valid_files} files have issues.")
        return False

if __name__ == "__main__":
    success = main()
    print(f"\nValidation {'PASSED' if success else 'FAILED'}")
    sys.exit(0 if success else 1)
