"""
Analytics manager for VoicePal.

This module coordinates all analytics features and provides a unified interface.
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta

from bot.analytics.conversation_analytics import ConversationAnalytics
from bot.analytics.user_analytics import UserAnalytics
from bot.analytics.business_analytics import BusinessAnalytics
from bot.analytics.event_tracker import EventTracker, EventType
from bot.analytics.metrics_collector import MetricsCollector

logger = logging.getLogger(__name__)

class AnalyticsManager:
    """Centralized analytics manager for VoicePal."""
    
    def __init__(
        self,
        database,
        cache_manager=None,
        redis_client=None,
        enable_event_tracking: bool = True,
        enable_metrics_collection: bool = True
    ):
        """
        Initialize analytics manager.
        
        Args:
            database: Database instance
            cache_manager: Cache manager for performance
            redis_client: Redis client for real-time features
            enable_event_tracking: Whether to enable event tracking
            enable_metrics_collection: Whether to enable metrics collection
        """
        self.database = database
        self.cache_manager = cache_manager
        self.redis_client = redis_client
        
        # Initialize analytics components
        self.conversation_analytics = ConversationAnalytics(database, cache_manager)
        self.user_analytics = UserAnalytics(database, cache_manager)
        self.business_analytics = BusinessAnalytics(database, cache_manager)
        
        # Optional components
        self.event_tracker = None
        self.metrics_collector = None
        
        if enable_event_tracking:
            self.event_tracker = EventTracker(database, redis_client)
        
        if enable_metrics_collection:
            self.metrics_collector = MetricsCollector(database, redis_client)
        
        # Configuration
        self.config = {
            "event_tracking_enabled": enable_event_tracking,
            "metrics_collection_enabled": enable_metrics_collection,
            "cache_enabled": cache_manager is not None,
            "redis_enabled": redis_client is not None
        }
        
        logger.info("Analytics manager initialized")
    
    def start_analytics(self):
        """Start analytics components."""
        try:
            if self.metrics_collector:
                self.metrics_collector.start_collection()
            
            logger.info("Analytics components started")
            
        except Exception as e:
            logger.error(f"Failed to start analytics components: {e}")
    
    def stop_analytics(self):
        """Stop analytics components."""
        try:
            if self.metrics_collector:
                self.metrics_collector.stop_collection()
            
            if self.event_tracker:
                self.event_tracker.flush_events()
            
            logger.info("Analytics components stopped")
            
        except Exception as e:
            logger.error(f"Failed to stop analytics components: {e}")
    
    def get_comprehensive_dashboard(self, days: int = 30) -> Dict[str, Any]:
        """
        Get comprehensive analytics dashboard.
        
        Args:
            days: Number of days to analyze
            
        Returns:
            Comprehensive dashboard data
        """
        try:
            dashboard = {
                "timestamp": datetime.utcnow().isoformat(),
                "period_days": days,
                "user_metrics": {},
                "conversation_metrics": {},
                "business_metrics": {},
                "system_metrics": {},
                "insights": []
            }
            
            # User analytics
            try:
                user_metrics = self.user_analytics.get_user_metrics(days)
                dashboard["user_metrics"] = {
                    "total_users": user_metrics.total_users,
                    "active_users_daily": user_metrics.active_users_daily,
                    "active_users_weekly": user_metrics.active_users_weekly,
                    "active_users_monthly": user_metrics.active_users_monthly,
                    "new_users_daily": user_metrics.new_users_daily,
                    "new_users_weekly": user_metrics.new_users_weekly,
                    "new_users_monthly": user_metrics.new_users_monthly,
                    "retention_rate_7d": user_metrics.retention_rate_7d,
                    "retention_rate_30d": user_metrics.retention_rate_30d,
                    "churn_rate": user_metrics.churn_rate,
                    "avg_session_duration": user_metrics.avg_session_duration,
                    "avg_messages_per_user": user_metrics.avg_messages_per_user
                }
            except Exception as e:
                logger.error(f"Failed to get user metrics: {e}")
                dashboard["user_metrics"] = {}
            
            # Conversation analytics
            try:
                conv_metrics = self.conversation_analytics.analyze_conversation_patterns(days=days)
                dashboard["conversation_metrics"] = {
                    "total_conversations": conv_metrics.total_conversations,
                    "avg_conversation_length": conv_metrics.avg_conversation_length,
                    "avg_messages_per_conversation": conv_metrics.avg_messages_per_conversation,
                    "avg_response_time": conv_metrics.avg_response_time,
                    "most_active_hours": conv_metrics.most_active_hours,
                    "conversation_topics": conv_metrics.conversation_topics,
                    "sentiment_distribution": conv_metrics.sentiment_distribution,
                    "user_engagement_score": conv_metrics.user_engagement_score
                }
            except Exception as e:
                logger.error(f"Failed to get conversation metrics: {e}")
                dashboard["conversation_metrics"] = {}
            
            # Business analytics
            try:
                business_metrics = self.business_analytics.get_revenue_metrics(days)
                cost_analysis = self.business_analytics.analyze_costs(days)
                
                dashboard["business_metrics"] = {
                    "total_revenue": business_metrics.total_revenue,
                    "monthly_recurring_revenue": business_metrics.monthly_recurring_revenue,
                    "average_revenue_per_user": business_metrics.average_revenue_per_user,
                    "customer_lifetime_value": business_metrics.customer_lifetime_value,
                    "conversion_rate": business_metrics.conversion_rate,
                    "churn_rate": business_metrics.churn_rate,
                    "revenue_growth_rate": business_metrics.revenue_growth_rate,
                    "payment_success_rate": business_metrics.payment_success_rate,
                    "total_costs": cost_analysis.total_costs,
                    "profit_margin": cost_analysis.profit_margin,
                    "cost_per_user": cost_analysis.cost_per_user
                }
            except Exception as e:
                logger.error(f"Failed to get business metrics: {e}")
                dashboard["business_metrics"] = {}
            
            # System metrics
            if self.metrics_collector:
                try:
                    dashboard["system_metrics"] = self.metrics_collector.get_dashboard_metrics()
                except Exception as e:
                    logger.error(f"Failed to get system metrics: {e}")
                    dashboard["system_metrics"] = {}
            
            # Generate insights
            try:
                insights = []
                
                # Conversation insights
                conv_insights = self.conversation_analytics.generate_conversation_insights(days=days)
                insights.extend([{
                    "type": insight.insight_type,
                    "title": insight.title,
                    "description": insight.description,
                    "confidence": insight.confidence,
                    "timestamp": insight.timestamp.isoformat()
                } for insight in conv_insights])
                
                # Business insights
                business_insights = self.business_analytics.generate_business_insights(days)
                insights.extend([{
                    "type": insight.insight_type,
                    "title": insight.title,
                    "description": insight.description,
                    "impact": insight.impact,
                    "recommendation": insight.recommendation,
                    "timestamp": insight.timestamp.isoformat()
                } for insight in business_insights])
                
                dashboard["insights"] = insights
                
            except Exception as e:
                logger.error(f"Failed to generate insights: {e}")
                dashboard["insights"] = []
            
            return dashboard
            
        except Exception as e:
            logger.error(f"Failed to get comprehensive dashboard: {e}")
            return {"error": "Failed to generate dashboard"}
    
    def track_user_action(
        self,
        event_type: EventType,
        user_id: int,
        properties: Optional[Dict[str, Any]] = None,
        session_id: Optional[str] = None
    ) -> str:
        """
        Track user action event.
        
        Args:
            event_type: Type of event
            user_id: User ID
            properties: Event properties
            session_id: Session ID
            
        Returns:
            Event ID
        """
        if self.event_tracker:
            return self.event_tracker.track_event(
                event_type=event_type,
                user_id=user_id,
                properties=properties,
                session_id=session_id
            )
        return ""
    
    def record_performance_metric(self, name: str, value: float, unit: str = "count"):
        """
        Record performance metric.
        
        Args:
            name: Metric name
            value: Metric value
            unit: Metric unit
        """
        if self.metrics_collector:
            self.metrics_collector.record_metric(name, value, unit=unit)
    
    def get_user_journey(self, user_id: int) -> Dict[str, Any]:
        """
        Get comprehensive user journey analysis.
        
        Args:
            user_id: User ID
            
        Returns:
            User journey data
        """
        try:
            journey_data = {
                "user_id": user_id,
                "analytics": {},
                "events": [],
                "metrics": {}
            }
            
            # User analytics
            journey = self.user_analytics.analyze_user_journey(user_id)
            journey_data["analytics"] = {
                "journey_stages": journey.journey_stages,
                "total_duration": journey.total_duration,
                "conversion_events": journey.conversion_events,
                "drop_off_points": journey.drop_off_points
            }
            
            # Conversation analytics for this user
            conv_metrics = self.conversation_analytics.analyze_conversation_patterns(user_id=user_id)
            journey_data["conversation_metrics"] = {
                "total_conversations": conv_metrics.total_conversations,
                "avg_conversation_length": conv_metrics.avg_conversation_length,
                "avg_messages_per_conversation": conv_metrics.avg_messages_per_conversation,
                "engagement_score": conv_metrics.user_engagement_score,
                "topics": conv_metrics.conversation_topics,
                "sentiment": conv_metrics.sentiment_distribution
            }
            
            # User events
            if self.event_tracker:
                events = self.event_tracker.get_user_events(user_id, days=30)
                journey_data["events"] = events[:50]  # Limit to last 50 events
            
            return journey_data
            
        except Exception as e:
            logger.error(f"Failed to get user journey: {e}")
            return {"error": "Failed to get user journey"}
    
    def get_analytics_summary(self) -> Dict[str, Any]:
        """
        Get analytics system summary.
        
        Returns:
            Analytics summary
        """
        try:
            summary = {
                "status": "active",
                "components": {
                    "conversation_analytics": True,
                    "user_analytics": True,
                    "business_analytics": True,
                    "event_tracker": self.event_tracker is not None,
                    "metrics_collector": self.metrics_collector is not None
                },
                "config": self.config,
                "statistics": {}
            }
            
            # Event tracking statistics
            if self.event_tracker:
                try:
                    event_summaries = self.event_tracker.get_event_summary(days=7)
                    summary["statistics"]["events_last_7_days"] = len(event_summaries)
                    summary["statistics"]["total_events"] = sum(s.count for s in event_summaries)
                except Exception as e:
                    logger.warning(f"Failed to get event statistics: {e}")
            
            # Metrics collection statistics
            if self.metrics_collector:
                try:
                    metric_names = self.metrics_collector.get_all_metric_names()
                    summary["statistics"]["total_metrics"] = len(metric_names)
                except Exception as e:
                    logger.warning(f"Failed to get metrics statistics: {e}")
            
            return summary
            
        except Exception as e:
            logger.error(f"Failed to get analytics summary: {e}")
            return {"status": "error", "error": str(e)}
    
    def generate_report(self, report_type: str, days: int = 30) -> Dict[str, Any]:
        """
        Generate analytics report.
        
        Args:
            report_type: Type of report (user, business, conversation, system)
            days: Number of days to analyze
            
        Returns:
            Generated report
        """
        try:
            report = {
                "report_type": report_type,
                "period_days": days,
                "generated_at": datetime.utcnow().isoformat(),
                "data": {}
            }
            
            if report_type == "user":
                # User analytics report
                user_metrics = self.user_analytics.get_user_metrics(days)
                user_segments = self.user_analytics.segment_users()
                cohort_analysis = self.user_analytics.get_user_cohort_analysis()
                
                report["data"] = {
                    "metrics": user_metrics.__dict__,
                    "segments": [segment.__dict__ for segment in user_segments],
                    "cohort_analysis": cohort_analysis
                }
                
            elif report_type == "business":
                # Business analytics report
                revenue_metrics = self.business_analytics.get_revenue_metrics(days)
                cost_analysis = self.business_analytics.analyze_costs(days)
                business_insights = self.business_analytics.generate_business_insights(days)
                revenue_forecast = self.business_analytics.get_revenue_forecast()
                
                report["data"] = {
                    "revenue_metrics": revenue_metrics.__dict__,
                    "cost_analysis": cost_analysis.__dict__,
                    "insights": [insight.__dict__ for insight in business_insights],
                    "revenue_forecast": revenue_forecast
                }
                
            elif report_type == "conversation":
                # Conversation analytics report
                conv_metrics = self.conversation_analytics.analyze_conversation_patterns(days=days)
                conv_insights = self.conversation_analytics.generate_conversation_insights(days=days)
                conv_trends = self.conversation_analytics.get_conversation_trends(days)
                
                report["data"] = {
                    "metrics": conv_metrics.__dict__,
                    "insights": [insight.__dict__ for insight in conv_insights],
                    "trends": conv_trends
                }
                
            elif report_type == "system" and self.metrics_collector:
                # System metrics report
                dashboard_metrics = self.metrics_collector.get_dashboard_metrics()
                
                # Get trends for key metrics
                key_metrics = ["system.cpu.percent", "system.memory.percent", "app.users.active_24h"]
                trends = {}
                for metric_name in key_metrics:
                    trends[metric_name] = self.metrics_collector.get_metric_trends(metric_name, hours=days*24)
                
                report["data"] = {
                    "current_metrics": dashboard_metrics,
                    "trends": trends
                }
            
            return report
            
        except Exception as e:
            logger.error(f"Failed to generate {report_type} report: {e}")
            return {"error": f"Failed to generate {report_type} report"}
    
    def cleanup_old_data(self, days: int = 90):
        """
        Clean up old analytics data.
        
        Args:
            days: Number of days to retain data
        """
        try:
            if self.event_tracker:
                self.event_tracker.cleanup_old_events(days)
            
            if self.metrics_collector:
                self.metrics_collector.cleanup_old_metrics()
            
            logger.info(f"Cleaned up analytics data older than {days} days")
            
        except Exception as e:
            logger.error(f"Failed to cleanup old analytics data: {e}")
    
    def export_analytics_data(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """
        Export analytics data for the specified period.
        
        Args:
            start_date: Start date for export
            end_date: End date for export
            
        Returns:
            Exported analytics data
        """
        try:
            export_data = {
                "export_period": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat()
                },
                "exported_at": datetime.utcnow().isoformat(),
                "data": {}
            }
            
            # Export events
            if self.event_tracker:
                # This would need to be implemented in EventTracker
                export_data["data"]["events"] = "Event export not implemented"
            
            # Export metrics
            if self.metrics_collector:
                # This would need to be implemented in MetricsCollector
                export_data["data"]["metrics"] = "Metrics export not implemented"
            
            return export_data
            
        except Exception as e:
            logger.error(f"Failed to export analytics data: {e}")
            return {"error": "Failed to export analytics data"}
