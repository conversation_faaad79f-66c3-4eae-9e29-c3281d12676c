"""
Tests for base repository.

This module tests the BaseRepository class.
"""

import pytest
import sqlite3
from unittest.mock import MagicMock, patch

from bot.database.models.base_model import BaseModel
from bot.database.repositories.base_repository import BaseRepository
from bot.database.core.exceptions import (
    DatabaseError,
    DatabaseNotFoundError,
    DatabaseDuplicateError
)

class TestModel(BaseModel):
    """Test model class."""
    
    _table_name = "test_models"
    _primary_key = "test_id"

class TestRepository(BaseRepository[TestModel]):
    """Test repository class."""
    
    _model_class = TestModel

@pytest.fixture
def test_repo(db_connection):
    """Create a test repository."""
    # Create test table
    db_connection.execute("""
        CREATE TABLE test_models (
            test_id TEXT PRIMARY KEY,
            name TEXT,
            value INTEGER
        )
    """)
    
    return TestRepository(db_connection)

def test_create(test_repo):
    """Test creating a record."""
    model = TestModel(test_id="123", name="Test", value=42)
    
    # Create record
    created = test_repo.create(model)
    
    assert created == model
    
    # Verify record was created
    cursor = test_repo.connection.execute(
        "SELECT * FROM test_models WHERE test_id = ?",
        ("123",)
    )
    row = cursor.fetchone()
    
    assert row is not None
    assert row["test_id"] == "123"
    assert row["name"] == "Test"
    assert row["value"] == 42
    
    # Test duplicate record
    with pytest.raises(DatabaseDuplicateError):
        test_repo.create(model)

def test_update(test_repo):
    """Test updating a record."""
    # Create record
    model = TestModel(test_id="123", name="Test", value=42)
    test_repo.create(model)
    
    # Update record
    model.name = "Updated"
    model.value = 99
    updated = test_repo.update(model)
    
    assert updated == model
    
    # Verify record was updated
    cursor = test_repo.connection.execute(
        "SELECT * FROM test_models WHERE test_id = ?",
        ("123",)
    )
    row = cursor.fetchone()
    
    assert row is not None
    assert row["name"] == "Updated"
    assert row["value"] == 99
    
    # Test non-existent record
    non_existent = TestModel(test_id="456", name="Non-existent")
    with pytest.raises(DatabaseNotFoundError):
        test_repo.update(non_existent)

def test_delete(test_repo):
    """Test deleting a record."""
    # Create record
    model = TestModel(test_id="123", name="Test", value=42)
    test_repo.create(model)
    
    # Delete by model
    deleted = test_repo.delete(model)
    assert deleted is True
    
    # Verify record was deleted
    cursor = test_repo.connection.execute(
        "SELECT COUNT(*) FROM test_models WHERE test_id = ?",
        ("123",)
    )
    count = cursor.fetchone()[0]
    assert count == 0
    
    # Test deleting by ID
    model = TestModel(test_id="456", name="Test", value=42)
    test_repo.create(model)
    
    deleted = test_repo.delete("456")
    assert deleted is True
    
    # Test deleting non-existent record
    deleted = test_repo.delete("789")
    assert deleted is False

def test_find_by_id(test_repo):
    """Test finding a record by ID."""
    # Create record
    model = TestModel(test_id="123", name="Test", value=42)
    test_repo.create(model)
    
    # Find by ID
    found = test_repo.find_by_id("123")
    
    assert found is not None
    assert found.test_id == "123"
    assert found.name == "Test"
    assert found.value == 42
    
    # Test non-existent record
    not_found = test_repo.find_by_id("456")
    assert not_found is None

def test_find_all(test_repo):
    """Test finding all records."""
    # Create records
    models = [
        TestModel(test_id="1", name="Test 1", value=10),
        TestModel(test_id="2", name="Test 2", value=20),
        TestModel(test_id="3", name="Test 3", value=30),
        TestModel(test_id="4", name="Test 4", value=40),
        TestModel(test_id="5", name="Test 5", value=50)
    ]
    
    for model in models:
        test_repo.create(model)
    
    # Find all
    all_models = test_repo.find_all()
    assert len(all_models) == 5
    
    # Find with where condition
    filtered = test_repo.find_all(where={"name": "Test 3"})
    assert len(filtered) == 1
    assert filtered[0].test_id == "3"
    
    # Find with order by
    ordered = test_repo.find_all(order_by="value DESC")
    assert len(ordered) == 5
    assert ordered[0].test_id == "5"
    assert ordered[-1].test_id == "1"
    
    # Find with limit
    limited = test_repo.find_all(limit=2)
    assert len(limited) == 2
    
    # Find with offset
    offset = test_repo.find_all(offset=2, limit=2)
    assert len(offset) == 2
    assert offset[0].test_id == "3"

def test_count(test_repo):
    """Test counting records."""
    # Create records
    models = [
        TestModel(test_id="1", name="Test A", value=10),
        TestModel(test_id="2", name="Test B", value=20),
        TestModel(test_id="3", name="Test A", value=30)
    ]
    
    for model in models:
        test_repo.create(model)
    
    # Count all
    count = test_repo.count()
    assert count == 3
    
    # Count with where condition
    filtered_count = test_repo.count(where={"name": "Test A"})
    assert filtered_count == 2

def test_exists(test_repo):
    """Test checking if a record exists."""
    # Create record
    model = TestModel(test_id="123", name="Test", value=42)
    test_repo.create(model)
    
    # Check exists
    exists = test_repo.exists(where={"test_id": "123"})
    assert exists is True
    
    # Check non-existent
    exists = test_repo.exists(where={"test_id": "456"})
    assert exists is False
    
    # Check with multiple conditions
    exists = test_repo.exists(where={"test_id": "123", "name": "Test"})
    assert exists is True
    
    exists = test_repo.exists(where={"test_id": "123", "name": "Wrong"})
    assert exists is False

def test_transaction(test_repo):
    """Test transaction management."""
    # Test successful transaction
    with test_repo.transaction():
        model1 = TestModel(test_id="1", name="Test 1")
        model2 = TestModel(test_id="2", name="Test 2")
        
        test_repo.create(model1)
        test_repo.create(model2)
    
    # Verify records were created
    count = test_repo.count()
    assert count == 2
    
    # Test failed transaction
    try:
        with test_repo.transaction():
            model3 = TestModel(test_id="3", name="Test 3")
            test_repo.create(model3)
            
            # This should fail and trigger a rollback
            raise ValueError("Test error")
    except ValueError:
        pass
    
    # Verify model3 was not created
    count = test_repo.count()
    assert count == 2
