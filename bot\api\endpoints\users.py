"""
User management endpoints for VoicePal API.
"""

import logging
from typing import List, Optional
from fastapi import APIRouter, HTTPException, Depends, Query
from pydantic import BaseModel

from bot.api.dependencies import get_database_manager

logger = logging.getLogger(__name__)
router = APIRouter()

class UserResponse(BaseModel):
    user_id: int
    username: str
    first_name: str
    credits: int
    created_at: str

class UserUpdate(BaseModel):
    credits: Optional[int] = None
    first_name: Optional[str] = None

@router.get("/users")
async def list_users(
    page: int = Query(1, ge=1),
    limit: int = Query(50, ge=1, le=100),
    db=Depends(get_database_manager)
):
    """List all users with pagination."""
    try:
        users = db.get_all_users()
        return {
            "users": users[:limit],
            "pagination": {
                "page": page,
                "limit": limit,
                "total": len(users),
                "pages": (len(users) + limit - 1) // limit
            }
        }
    except Exception as e:
        logger.error(f"Error listing users: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving users")

@router.get("/users/{user_id}")
async def get_user(user_id: int, db=Depends(get_database_manager)):
    """Get specific user by ID."""
    try:
        user = db.get_user(user_id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        return user
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting user {user_id}: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving user")

@router.put("/users/{user_id}")
async def update_user(
    user_id: int, 
    user_update: UserUpdate,
    db=Depends(get_database_manager)
):
    """Update user information."""
    try:
        success = db.update_user(user_id, user_update.dict(exclude_unset=True))
        if not success:
            raise HTTPException(status_code=404, detail="User not found")
        return {"message": "User updated successfully"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating user {user_id}: {e}")
        raise HTTPException(status_code=500, detail="Error updating user")

@router.delete("/users/{user_id}")
async def delete_user(user_id: int, db=Depends(get_database_manager)):
    """Delete user."""
    try:
        success = db.delete_user(user_id)
        if not success:
            raise HTTPException(status_code=404, detail="User not found")
        return {"message": "User deleted successfully"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting user {user_id}: {e}")
        raise HTTPException(status_code=500, detail="Error deleting user")
