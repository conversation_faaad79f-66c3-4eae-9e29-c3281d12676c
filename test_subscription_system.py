#!/usr/bin/env python
"""
Test script for subscription system.

This script tests the subscription system to verify that:
1. The subscription database schema is properly created
2. Subscriptions can be added, updated, and canceled
3. Subscription renewals work correctly

Usage:
    python test_subscription_system.py
"""

import os
import sys
import logging
import asyncio
import uuid
from pathlib import Path
from datetime import datetime, timed<PERSON><PERSON>

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath("."))

# Import the necessary components
from bot.database.database import Database
from bot.database.extensions.payment import extend_database_for_payment
from bot.database.extensions.subscription import extend_database_for_subscription

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class SubscriptionTester:
    """Class to test the subscription system."""

    def __init__(self):
        """Initialize the subscription tester."""
        # Create a temporary database
        self.db_path = Path("test_subscription.db")
        if self.db_path.exists():
            self.db_path.unlink()

        # Initialize database
        self.database = Database(self.db_path)

        # Extend database with payment and subscription methods
        extend_database_for_payment(self.database)
        extend_database_for_subscription(self.database)

        logger.info("Subscription tester initialized")

    async def cleanup(self):
        """Clean up resources."""
        try:
            # Close database connection
            self.database.conn.close()

            # Delete database file
            if self.db_path.exists():
                self.db_path.unlink()

            logger.info("Cleaned up resources")
        except Exception as e:
            logger.error(f"Error cleaning up: {e}")

    async def test_database_schema(self):
        """Test that the subscription database schema is properly created."""
        logger.info("=== Testing Subscription Database Schema ===")

        # Check that the subscriptions table exists
        self.database.cursor.execute(
            "SELECT name FROM sqlite_master WHERE type='table' AND name='subscriptions'"
        )
        result = self.database.cursor.fetchone()
        assert result is not None, "Expected subscriptions table to exist"

        # Check that the subscription_events table exists
        self.database.cursor.execute(
            "SELECT name FROM sqlite_master WHERE type='table' AND name='subscription_events'"
        )
        result = self.database.cursor.fetchone()
        assert result is not None, "Expected subscription_events table to exist"

        logger.info("Subscription database schema test passed!")
        return True

    async def test_add_subscription(self):
        """Test adding a subscription."""
        logger.info("\n=== Testing Add Subscription ===")

        # Add a test user
        user_id = 12345
        self.database.add_user(
            user_id=user_id,
            username="test_user",
            first_name="Test",
            last_name="User"
        )

        # Add a subscription
        start_date = datetime.now().isoformat()
        next_billing_date = (datetime.now() + timedelta(days=30)).isoformat()

        subscription_id = self.database.add_subscription(
            user_id=user_id,
            package_id="monthly",
            subscription_id=f"sub_{uuid.uuid4()}",
            status="active",
            start_date=start_date,
            end_date=None,
            next_billing_date=next_billing_date,
            credits_per_cycle=500,
            price=499,
            currency="EUR",
            interval="month"
        )

        assert subscription_id > 0, "Expected subscription ID to be positive"

        # Check that the subscription was added
        subscription = self.database.get_subscription(subscription_id)
        assert subscription is not None, "Expected subscription to exist"
        assert subscription["user_id"] == user_id, f"Expected user_id to be {user_id}, got {subscription['user_id']}"
        assert subscription["package_id"] == "monthly", f"Expected package_id to be 'monthly', got {subscription['package_id']}"
        assert subscription["status"] == "active", f"Expected status to be 'active', got {subscription['status']}"

        logger.info("Add subscription test passed!")
        return True

    async def test_get_user_subscriptions(self):
        """Test getting user subscriptions."""
        logger.info("\n=== Testing Get User Subscriptions ===")

        # Add another subscription for the same user
        user_id = 12345
        start_date = datetime.now().isoformat()
        next_billing_date = (datetime.now() + timedelta(days=365)).isoformat()

        subscription_id = self.database.add_subscription(
            user_id=user_id,
            package_id="yearly",
            subscription_id=f"sub_{uuid.uuid4()}",
            status="active",
            start_date=start_date,
            end_date=None,
            next_billing_date=next_billing_date,
            credits_per_cycle=7000,
            price=4999,
            currency="EUR",
            interval="year"
        )

        # Get all subscriptions for the user
        subscriptions = self.database.get_user_subscriptions(user_id)
        assert len(subscriptions) == 2, f"Expected 2 subscriptions, got {len(subscriptions)}"

        # Get active subscriptions for the user
        active_subscriptions = self.database.get_user_subscriptions(user_id, active_only=True)
        assert len(active_subscriptions) == 2, f"Expected 2 active subscriptions, got {len(active_subscriptions)}"

        logger.info("Get user subscriptions test passed!")
        return True

    async def test_update_subscription_status(self):
        """Test updating subscription status."""
        logger.info("\n=== Testing Update Subscription Status ===")

        # Get the first subscription
        user_id = 12345
        subscriptions = self.database.get_user_subscriptions(user_id)
        subscription_id = subscriptions[0]["id"]

        # Update subscription status
        end_date = (datetime.now() + timedelta(days=30)).isoformat()
        success = self.database.update_subscription_status(
            subscription_id=subscription_id,
            status="canceled",
            end_date=end_date
        )

        assert success, "Expected update_subscription_status to return True"

        # Check that the subscription status was updated
        subscription = self.database.get_subscription(subscription_id)
        assert subscription["status"] == "canceled", f"Expected status to be 'canceled', got {subscription['status']}"
        assert subscription["end_date"] == end_date, f"Expected end_date to be {end_date}, got {subscription['end_date']}"

        logger.info("Update subscription status test passed!")
        return True

    async def test_subscription_events(self):
        """Test subscription events."""
        logger.info("\n=== Testing Subscription Events ===")

        # Get the first subscription
        user_id = 12345
        subscriptions = self.database.get_user_subscriptions(user_id)
        subscription_id = subscriptions[0]["id"]

        # Add a subscription event
        event_id = self.database.add_subscription_event(
            subscription_id=subscription_id,
            event_type="canceled",
            details="Canceled by user"
        )

        assert event_id > 0, "Expected event ID to be positive"

        # Get subscription events
        events = self.database.get_subscription_events(subscription_id)
        assert len(events) == 1, f"Expected 1 event, got {len(events)}"
        assert events[0]["event_type"] == "canceled", f"Expected event_type to be 'canceled', got {events[0]['event_type']}"

        logger.info("Subscription events test passed!")
        return True

    async def test_subscription_renewal(self):
        """Test subscription renewal."""
        logger.info("\n=== Testing Subscription Renewal ===")

        # Get the second subscription (which should be active)
        user_id = 12345
        subscriptions = self.database.get_user_subscriptions(user_id, active_only=True)
        subscription_id = subscriptions[-1]["id"]

        # Get initial credits
        initial_credits = self.database.get_user_credits(user_id)

        # Get the original next billing date
        original_subscription = self.database.get_subscription(subscription_id)
        original_next_billing_date = original_subscription["next_billing_date"]

        # Process subscription renewal
        success = self.database.process_subscription_renewal(subscription_id)
        assert success, "Expected process_subscription_renewal to return True"

        # Check that credits were added
        new_credits = self.database.get_user_credits(user_id)
        subscription = self.database.get_subscription(subscription_id)
        expected_credits = initial_credits + subscription["credits_per_cycle"]
        assert new_credits == expected_credits, f"Expected {expected_credits} credits, got {new_credits}"

        # Get the updated subscription directly from the database to ensure we have the latest data
        self.database.cursor.execute(
            "SELECT * FROM subscriptions WHERE id = ?",
            (subscription_id,)
        )
        updated_subscription = dict(self.database.cursor.fetchone())

        # Check that next billing date was updated
        assert updated_subscription["next_billing_date"] != original_next_billing_date, \
            "Expected next_billing_date to be updated"

        # Check that a renewal event was added
        events = self.database.get_subscription_events(subscription_id)
        assert any(event["event_type"] == "renewed" for event in events), \
            "Expected a 'renewed' event to be added"

        logger.info("Subscription renewal test passed!")
        return True

    async def test_cancel_subscription(self):
        """Test canceling a subscription."""
        logger.info("\n=== Testing Cancel Subscription ===")

        # Get the second subscription (which should be active)
        user_id = 12345
        subscriptions = self.database.get_user_subscriptions(user_id, active_only=True)
        subscription_id = subscriptions[-1]["id"]

        # Cancel subscription
        success = self.database.cancel_subscription(subscription_id)
        assert success, "Expected cancel_subscription to return True"

        # Check that the subscription was canceled
        subscription = self.database.get_subscription(subscription_id)
        assert subscription["status"] == "canceled", f"Expected status to be 'canceled', got {subscription['status']}"
        assert subscription["end_date"] is not None, "Expected end_date to be set"

        # Check that a cancellation event was added
        events = self.database.get_subscription_events(subscription_id)
        assert any(event["event_type"] == "canceled" for event in events), \
            "Expected a 'canceled' event to be added"

        logger.info("Cancel subscription test passed!")
        return True

    async def run_all_tests(self):
        """Run all subscription tests."""
        try:
            await self.test_database_schema()
            await self.test_add_subscription()
            await self.test_get_user_subscriptions()
            await self.test_update_subscription_status()
            await self.test_subscription_events()
            await self.test_subscription_renewal()
            await self.test_cancel_subscription()

            logger.info("\n=== All Subscription Tests Passed! ===")
            return True
        except AssertionError as e:
            logger.error(f"Test failed: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False
        finally:
            await self.cleanup()

async def main():
    """Run the subscription tests."""
    tester = SubscriptionTester()
    await tester.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
