"""
Security monitoring integration for VoicePal.

This module integrates the security monitoring system with the VoicePal bot.
"""

import logging
import asyncio
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime, timedelta

from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes, CommandHandler, CallbackQueryHandler

from bot.core.security_monitor import (
    SecurityMonitor,
    SEVERITY_INFO, SEVERITY_LOW, SEVERITY_MEDIUM, SEVERITY_HIGH, SEVERITY_CRITICAL,
    CATEGORY_AUTH, CATEGORY_ACCESS, CATEGORY_PAYMENT, CATEGORY_ABUSE,
    CATEGORY_RATE_LIMIT, CATEGORY_API, CATEGORY_SYSTEM
)

from bot.features.security_dashboard import (
    show_security_dashboard,
    show_alerts_section,
    show_events_section,
    show_users_section,
    show_payments_section,
    show_metrics_section,
    handle_security_dashboard_callback
)

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

def integrate_security_monitoring(bot_instance):
    """
    Integrate security monitoring with the VoicePal bot.

    Args:
        bot_instance: VoicePalBot instance
    """
    # Initialize security monitor
    bot_instance.security_monitor = SecurityMonitor(
        database=bot_instance.database,
        config_manager=bot_instance.config_manager,
        bot=bot_instance.application.bot
    )

    # Register security command handlers
    bot_instance.application.add_handler(
        CommandHandler("security", security_command, filters=bot_instance.admin_filter)
    )

    # Register callback query handlers for security dashboard
    bot_instance.application.add_handler(
        CallbackQueryHandler(handle_security_dashboard_callback, pattern=r"^security_section_")
    )
    bot_instance.application.add_handler(
        CallbackQueryHandler(handle_security_dashboard_callback, pattern=r"^security_events_")
    )
    bot_instance.application.add_handler(
        CallbackQueryHandler(handle_resolve_alert_callback, pattern=r"^resolve_alert_")
    )

    # Set up periodic security checks
    asyncio.create_task(periodic_security_checks(bot_instance))

    # Monkey patch key methods to add security monitoring
    patch_methods(bot_instance)

    logger.info("Security monitoring integrated with VoicePal bot")

def patch_methods(bot_instance):
    """
    Patch key methods to add security monitoring.

    Args:
        bot_instance: VoicePalBot instance
    """
    # Store original methods
    original_handle_text = bot_instance.handle_text
    original_handle_voice = bot_instance.handle_voice
    original_handle_pre_checkout = bot_instance.handle_pre_checkout
    original_handle_successful_payment = bot_instance.handle_successful_payment
    original_error_handler = bot_instance.error_handler

    # Patch handle_text method
    async def patched_handle_text(update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Patched handle_text method with security monitoring."""
        user_id = update.effective_user.id

        # Log message event for monitoring
        bot_instance.security_monitor.log_security_event(
            event_type="text_message",
            category=CATEGORY_SYSTEM,
            severity=SEVERITY_INFO,
            user_id=user_id,
            description=f"Text message from user {user_id}",
            details={"message_length": len(update.message.text)}
        )

        # Call original method
        return await original_handle_text(update, context)

    # Patch handle_voice method
    async def patched_handle_voice(update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Patched handle_voice method with security monitoring."""
        user_id = update.effective_user.id

        # Log voice message event for monitoring
        bot_instance.security_monitor.log_security_event(
            event_type="voice_message",
            category=CATEGORY_SYSTEM,
            severity=SEVERITY_INFO,
            user_id=user_id,
            description=f"Voice message from user {user_id}",
            details={"duration": update.message.voice.duration}
        )

        # Call original method
        return await original_handle_voice(update, context)

    # Patch handle_pre_checkout method
    async def patched_handle_pre_checkout(update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Patched handle_pre_checkout method with security monitoring."""
        user_id = update.pre_checkout_query.from_user.id
        invoice_payload = update.pre_checkout_query.invoice_payload
        total_amount = update.pre_checkout_query.total_amount

        # Log payment event for monitoring
        bot_instance.security_monitor.log_security_event(
            event_type="payment_pre_checkout",
            category=CATEGORY_PAYMENT,
            severity=SEVERITY_INFO,
            user_id=user_id,
            description=f"Pre-checkout for user {user_id}",
            details={
                "invoice_payload": invoice_payload,
                "total_amount": total_amount,
                "currency": update.pre_checkout_query.currency
            }
        )

        # Call original method
        return await original_handle_pre_checkout(update, context)

    # Patch handle_successful_payment method
    async def patched_handle_successful_payment(update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Patched handle_successful_payment method with security monitoring."""
        user_id = update.effective_user.id
        invoice_payload = update.message.successful_payment.invoice_payload
        total_amount = update.message.successful_payment.total_amount

        # Log successful payment event for monitoring
        bot_instance.security_monitor.log_security_event(
            event_type="payment_successful",
            category=CATEGORY_PAYMENT,
            severity=SEVERITY_INFO,
            user_id=user_id,
            description=f"Successful payment from user {user_id}",
            details={
                "invoice_payload": invoice_payload,
                "total_amount": total_amount,
                "currency": update.message.successful_payment.currency,
                "telegram_payment_charge_id": update.message.successful_payment.telegram_payment_charge_id,
                "provider_payment_charge_id": update.message.successful_payment.provider_payment_charge_id
            }
        )

        # Call original method
        return await original_handle_successful_payment(update, context)

    # Patch error_handler method
    async def patched_error_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Patched error_handler method with security monitoring."""
        # Get error information
        error = context.error

        # Determine severity based on error type
        severity = SEVERITY_MEDIUM
        if isinstance(error, Exception):
            if "payment" in str(error).lower():
                severity = SEVERITY_HIGH
                category = CATEGORY_PAYMENT
            elif "permission" in str(error).lower() or "unauthorized" in str(error).lower():
                severity = SEVERITY_HIGH
                category = CATEGORY_ACCESS
            elif "rate limit" in str(error).lower():
                severity = SEVERITY_MEDIUM
                category = CATEGORY_RATE_LIMIT
            elif "api" in str(error).lower():
                severity = SEVERITY_MEDIUM
                category = CATEGORY_API
            else:
                category = CATEGORY_SYSTEM
        else:
            category = CATEGORY_SYSTEM

        # Get user ID if available
        user_id = None
        if update and update.effective_user:
            user_id = update.effective_user.id

        # Log error event for monitoring
        bot_instance.security_monitor.log_security_event(
            event_type="bot_error",
            category=category,
            severity=severity,
            user_id=user_id,
            description=f"Bot error: {str(error)[:100]}",
            details={"error_type": type(error).__name__}
        )

        # Call original method
        return await original_error_handler(update, context)

    # Replace methods with patched versions
    bot_instance.handle_text = patched_handle_text
    bot_instance.handle_voice = patched_handle_voice
    bot_instance.handle_pre_checkout = patched_handle_pre_checkout
    bot_instance.handle_successful_payment = patched_handle_successful_payment
    bot_instance.error_handler = patched_error_handler

async def periodic_security_checks(bot_instance):
    """
    Run periodic security checks.

    Args:
        bot_instance: VoicePalBot instance
    """
    while True:
        try:
            # Wait for 15 minutes between checks
            await asyncio.sleep(15 * 60)

            # Run security checks
            await check_suspicious_activity(bot_instance)
            await check_payment_patterns(bot_instance)
            await check_api_usage(bot_instance)

            # Log a heartbeat metric
            bot_instance.security_monitor.log_metric(
                metric_name="security_heartbeat",
                metric_value=1.0,
                details={"timestamp": datetime.now().isoformat()}
            )

        except Exception as e:
            logger.error(f"Error in periodic security checks: {e}")
            # Wait a bit before retrying
            await asyncio.sleep(60)

async def check_suspicious_activity(bot_instance):
    """
    Check for suspicious user activity.

    Args:
        bot_instance: VoicePalBot instance
    """
    try:
        # Get database cursor
        cursor = bot_instance.database.conn.cursor()

        # Check for users with multiple accounts (same device ID)
        cursor.execute("""
            SELECT device_id, COUNT(DISTINCT user_id) as user_count
            FROM users
            WHERE device_id IS NOT NULL
            GROUP BY device_id
            HAVING COUNT(DISTINCT user_id) > 1
        """)

        multiple_accounts = cursor.fetchall()
        for row in multiple_accounts:
            device_id = row['device_id']
            user_count = row['user_count']

            # Get the users with this device ID
            cursor.execute("""
                SELECT user_id, username, first_name, last_name, created_at
                FROM users
                WHERE device_id = ?
            """, (device_id,))

            users = cursor.fetchall()
            user_ids = [user['user_id'] for user in users]

            # Log suspicious activity
            bot_instance.security_monitor.log_security_event(
                event_type="multiple_accounts",
                category=CATEGORY_ABUSE,
                severity=SEVERITY_MEDIUM,
                description=f"Multiple accounts ({user_count}) using same device ID",
                details={
                    "device_id": device_id,
                    "user_ids": user_ids,
                    "user_count": user_count
                }
            )

            # Create alert if threshold exceeded
            if user_count >= bot_instance.security_monitor.alert_thresholds.get("multiple_accounts", 2):
                bot_instance.security_monitor.create_alert(
                    alert_type="multiple_accounts_detected",
                    severity=SEVERITY_HIGH,
                    description=f"Multiple accounts ({user_count}) detected using same device ID",
                    details={
                        "device_id": device_id,
                        "user_ids": user_ids,
                        "user_count": user_count
                    }
                )

        # Check for excessive credit usage
        one_hour_ago = (datetime.now() - timedelta(hours=1)).isoformat()
        cursor.execute("""
            SELECT user_id, SUM(amount) as credit_usage
            FROM transactions
            WHERE timestamp > ? AND status = 'completed' AND amount < 0
            GROUP BY user_id
            ORDER BY credit_usage DESC
            LIMIT 10
        """, (one_hour_ago,))

        high_usage = cursor.fetchall()
        for row in high_usage:
            user_id = row['user_id']
            credit_usage = abs(row['credit_usage'])

            # Check if usage exceeds threshold
            if credit_usage > bot_instance.security_monitor.alert_thresholds.get("credit_usage_rate", 50):
                bot_instance.security_monitor.log_security_event(
                    event_type="high_credit_usage",
                    category=CATEGORY_ABUSE,
                    severity=SEVERITY_MEDIUM,
                    user_id=user_id,
                    description=f"High credit usage by user {user_id}",
                    details={
                        "credit_usage": credit_usage,
                        "timeframe": "1 hour"
                    }
                )

    except Exception as e:
        logger.error(f"Error checking suspicious activity: {e}")

async def check_payment_patterns(bot_instance):
    """
    Check for suspicious payment patterns.

    Args:
        bot_instance: VoicePalBot instance
    """
    try:
        # Get database cursor
        cursor = bot_instance.database.conn.cursor()

        # Check for failed payments
        one_day_ago = (datetime.now() - timedelta(days=1)).isoformat()
        cursor.execute("""
            SELECT user_id, COUNT(*) as failure_count
            FROM transactions
            WHERE timestamp > ? AND status = 'failed' AND source = 'payment'
            GROUP BY user_id
            HAVING COUNT(*) >= ?
        """, (one_day_ago, bot_instance.security_monitor.alert_thresholds.get("payment_failures", 3)))

        failed_payments = cursor.fetchall()
        for row in failed_payments:
            user_id = row['user_id']
            failure_count = row['failure_count']

            bot_instance.security_monitor.log_security_event(
                event_type="multiple_payment_failures",
                category=CATEGORY_PAYMENT,
                severity=SEVERITY_MEDIUM,
                user_id=user_id,
                description=f"Multiple payment failures by user {user_id}",
                details={
                    "failure_count": failure_count,
                    "timeframe": "24 hours"
                }
            )

            # Create alert for high number of failures
            if failure_count >= bot_instance.security_monitor.alert_thresholds.get("payment_failures", 3) * 2:
                bot_instance.security_monitor.create_alert(
                    alert_type="excessive_payment_failures",
                    severity=SEVERITY_HIGH,
                    description=f"Excessive payment failures by user {user_id}",
                    details={
                        "user_id": user_id,
                        "failure_count": failure_count,
                        "timeframe": "24 hours"
                    }
                )

    except Exception as e:
        logger.error(f"Error checking payment patterns: {e}")

async def check_api_usage(bot_instance):
    """
    Check for suspicious API usage patterns.

    Args:
        bot_instance: VoicePalBot instance
    """
    try:
        # Get database cursor
        cursor = bot_instance.database.conn.cursor()

        # Check for excessive API key access
        one_day_ago = (datetime.now() - timedelta(days=1)).isoformat()

        # This assumes we have a key_access_logs table from the KeyManager
        cursor.execute("""
            SELECT service, user_id, COUNT(*) as access_count
            FROM key_access_logs
            WHERE timestamp > ?
            GROUP BY service, user_id
            ORDER BY access_count DESC
            LIMIT 10
        """, (one_day_ago,))

        api_usage = cursor.fetchall()
        for row in api_usage:
            service = row['service']
            user_id = row['user_id']
            access_count = row['access_count']

            # Check if usage exceeds threshold
            if access_count > bot_instance.security_monitor.alert_thresholds.get("api_key_access", 10):
                bot_instance.security_monitor.log_security_event(
                    event_type="high_api_usage",
                    category=CATEGORY_API,
                    severity=SEVERITY_MEDIUM,
                    user_id=user_id,
                    description=f"High API usage for service {service}",
                    details={
                        "service": service,
                        "access_count": access_count,
                        "timeframe": "24 hours"
                    }
                )

    except Exception as e:
        logger.error(f"Error checking API usage: {e}")

async def security_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """
    Handle the /security command (admin only).

    Args:
        update: Update object
        context: Context object
    """
    user_id = update.effective_user.id
    bot_instance = context.bot_data.get("bot_instance")

    if not bot_instance or not hasattr(bot_instance, "security_monitor"):
        await update.message.reply_text("Security monitoring is not available.")
        return

    # Check if user is admin
    admin_ids = bot_instance.config_manager.get_admin_user_ids()
    if user_id not in admin_ids:
        await update.message.reply_text("This command is only available to administrators.")
        return

    # Store bot instance in context for dashboard access
    context.bot_data["bot_instance"] = bot_instance

    # Show security dashboard
    await show_security_dashboard(update, context)



async def handle_resolve_alert_callback(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """
    Handle resolve alert callback queries.

    Args:
        update: Update object
        context: Context object
    """
    query = update.callback_query
    await query.answer()

    callback_data = query.data
    bot_instance = context.bot_data.get("bot_instance")

    if not bot_instance or not hasattr(bot_instance, "security_monitor"):
        await query.edit_message_text("Security monitoring is not available.")
        return

    if callback_data.startswith("resolve_alert_"):
        alert_id = int(callback_data.replace("resolve_alert_", ""))

        # Resolve the alert
        success = bot_instance.security_monitor.resolve_alert(alert_id)

        if success:
            await query.edit_message_text(
                f"Alert #{alert_id} has been marked as resolved.",
                reply_markup=InlineKeyboardMarkup([[
                    InlineKeyboardButton("Back to Dashboard", callback_data="security_dashboard")
                ]])
            )
        else:
            await query.edit_message_text(
                f"Failed to resolve alert #{alert_id}.",
                reply_markup=InlineKeyboardMarkup([[
                    InlineKeyboardButton("Back to Dashboard", callback_data="security_dashboard")
                ]])
            )
    else:
        await query.edit_message_text(
            "Unknown action.",
            reply_markup=InlineKeyboardMarkup([[
                InlineKeyboardButton("Back to Dashboard", callback_data="security_dashboard")
            ]])
        )

# Additional dashboard view functions will be implemented in the next task
