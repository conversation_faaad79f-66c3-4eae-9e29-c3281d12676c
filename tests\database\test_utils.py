"""
Tests for database utilities.

This module tests the database utility functions.
"""

import unittest
import sqlite3
from collections import namedtuple

from bot.database.core.utils import (
    build_insert_query,
    build_update_query,
    build_select_query,
    row_to_dict,
    rows_to_list
)

class TestDatabaseUtils(unittest.TestCase):
    """Test case for database utilities."""
    
    def test_build_insert_query(self):
        """Test building INSERT query."""
        # Test with dictionary
        data = {
            "id": 1,
            "name": "Test",
            "age": 30
        }
        
        query, params = build_insert_query("users", data)
        
        expected_query = "INSERT INTO users (id, name, age) VALUES (?, ?, ?)"
        expected_params = (1, "Test", 30)
        
        self.assertEqual(query, expected_query)
        self.assertEqual(params, expected_params)
        
        # Test with empty data
        with self.assertRaises(ValueError):
            build_insert_query("users", {})
    
    def test_build_update_query(self):
        """Test building UPDATE query."""
        # Test with dictionary
        data = {
            "name": "New Name",
            "age": 31
        }
        
        where = {
            "id": 1
        }
        
        query, params = build_update_query("users", data, where)
        
        expected_query = "UPDATE users SET name = ?, age = ? WHERE id = ?"
        expected_params = ("New Name", 31, 1)
        
        self.assertEqual(query, expected_query)
        self.assertEqual(params, expected_params)
        
        # Test with multiple where conditions
        where = {
            "id": 1,
            "name": "Test"
        }
        
        query, params = build_update_query("users", data, where)
        
        expected_query = "UPDATE users SET name = ?, age = ? WHERE id = ? AND name = ?"
        expected_params = ("New Name", 31, 1, "Test")
        
        self.assertEqual(query, expected_query)
        self.assertEqual(params, expected_params)
        
        # Test with empty data
        with self.assertRaises(ValueError):
            build_update_query("users", {}, where)
        
        # Test with empty where
        with self.assertRaises(ValueError):
            build_update_query("users", data, {})
    
    def test_build_select_query(self):
        """Test building SELECT query."""
        # Test basic query
        query, params = build_select_query("users")
        
        expected_query = "SELECT * FROM users"
        expected_params = ()
        
        self.assertEqual(query, expected_query)
        self.assertEqual(params, expected_params)
        
        # Test with columns
        query, params = build_select_query("users", columns=["id", "name"])
        
        expected_query = "SELECT id, name FROM users"
        expected_params = ()
        
        self.assertEqual(query, expected_query)
        self.assertEqual(params, expected_params)
        
        # Test with where
        where = {
            "id": 1,
            "name": "Test"
        }
        
        query, params = build_select_query("users", where=where)
        
        expected_query = "SELECT * FROM users WHERE id = ? AND name = ?"
        expected_params = (1, "Test")
        
        self.assertEqual(query, expected_query)
        self.assertEqual(params, expected_params)
        
        # Test with order by
        query, params = build_select_query("users", order_by="name ASC")
        
        expected_query = "SELECT * FROM users ORDER BY name ASC"
        expected_params = ()
        
        self.assertEqual(query, expected_query)
        self.assertEqual(params, expected_params)
        
        # Test with limit
        query, params = build_select_query("users", limit=10)
        
        expected_query = "SELECT * FROM users LIMIT 10"
        expected_params = ()
        
        self.assertEqual(query, expected_query)
        self.assertEqual(params, expected_params)
        
        # Test with offset
        query, params = build_select_query("users", offset=5)
        
        expected_query = "SELECT * FROM users OFFSET 5"
        expected_params = ()
        
        self.assertEqual(query, expected_query)
        self.assertEqual(params, expected_params)
        
        # Test with all options
        query, params = build_select_query(
            "users",
            columns=["id", "name"],
            where=where,
            order_by="name ASC",
            limit=10,
            offset=5
        )
        
        expected_query = "SELECT id, name FROM users WHERE id = ? AND name = ? ORDER BY name ASC LIMIT 10 OFFSET 5"
        expected_params = (1, "Test")
        
        self.assertEqual(query, expected_query)
        self.assertEqual(params, expected_params)
    
    def test_row_to_dict(self):
        """Test converting row to dictionary."""
        # Test with sqlite3.Row
        row = sqlite3.Row(
            cursor=None,
            data=(1, "Test", 30)
        )
        row.keys = lambda: ["id", "name", "age"]
        
        result = row_to_dict(row)
        
        expected = {
            "id": 1,
            "name": "Test",
            "age": 30
        }
        
        self.assertEqual(result, expected)
        
        # Test with namedtuple
        Row = namedtuple("Row", ["id", "name", "age"])
        row = Row(1, "Test", 30)
        
        result = row_to_dict(row)
        
        expected = {
            "id": 1,
            "name": "Test",
            "age": 30
        }
        
        self.assertEqual(result, expected)
        
        # Test with dictionary
        row = {
            "id": 1,
            "name": "Test",
            "age": 30
        }
        
        result = row_to_dict(row)
        
        expected = {
            "id": 1,
            "name": "Test",
            "age": 30
        }
        
        self.assertEqual(result, expected)
        
        # Test with unsupported type
        with self.assertRaises(ValueError):
            row_to_dict(123)
    
    def test_rows_to_list(self):
        """Test converting rows to list."""
        # Test with list of sqlite3.Row
        row1 = sqlite3.Row(
            cursor=None,
            data=(1, "Test 1", 30)
        )
        row1.keys = lambda: ["id", "name", "age"]
        
        row2 = sqlite3.Row(
            cursor=None,
            data=(2, "Test 2", 40)
        )
        row2.keys = lambda: ["id", "name", "age"]
        
        rows = [row1, row2]
        
        result = rows_to_list(rows)
        
        expected = [
            {
                "id": 1,
                "name": "Test 1",
                "age": 30
            },
            {
                "id": 2,
                "name": "Test 2",
                "age": 40
            }
        ]
        
        self.assertEqual(result, expected)
        
        # Test with empty list
        result = rows_to_list([])
        self.assertEqual(result, [])

if __name__ == "__main__":
    unittest.main()
