"""
Tests for the DatabaseManager class.

This module provides tests for the DatabaseManager class.
"""

import os
import unittest
import tempfile
from pathlib import Path

from bot.database.database_manager import DatabaseManager
from bot.database.core.exceptions import DatabaseError

class TestDatabaseManager(unittest.TestCase):
    """Test case for DatabaseManager."""
    
    def setUp(self):
        """Set up test environment."""
        # Create temporary directory for database
        self.temp_dir = tempfile.TemporaryDirectory()
        self.db_path = Path(self.temp_dir.name) / "test.db"
        
        # Initialize database
        self.db = DatabaseManager(self.db_path)
    
    def tearDown(self):
        """Clean up test environment."""
        # Close database connection
        self.db.close()
        
        # Remove temporary directory
        self.temp_dir.cleanup()
    
    def test_initialization(self):
        """Test database initialization."""
        # Check if database file exists
        self.assertTrue(self.db_path.exists())
        
        # Check if schema is valid
        self.assertTrue(self.db.validate_schema())
        
        # Check if current version is 1
        self.assertEqual(self.db.get_current_version(), 1)
    
    def test_repositories(self):
        """Test repository initialization."""
        # Check if repositories are initialized
        self.assertIsNotNone(self.db.users)
        self.assertIsNotNone(self.db.user_preferences)
        self.assertIsNotNone(self.db.user_stats)
        self.assertIsNotNone(self.db.conversations)
        self.assertIsNotNone(self.db.messages)
        self.assertIsNotNone(self.db.message_metadata)
        self.assertIsNotNone(self.db.transactions)
        self.assertIsNotNone(self.db.payment_packages)
        self.assertIsNotNone(self.db.subscriptions)
        self.assertIsNotNone(self.db.memories)
        self.assertIsNotNone(self.db.memory_tags)
        self.assertIsNotNone(self.db.voice_settings)
        self.assertIsNotNone(self.db.voice_recordings)
    
    def test_transaction(self):
        """Test database transaction."""
        # Create a user
        user_id = "test_user"
        username = "test_username"
        
        with self.db.transaction():
            self.db.users.create({
                "user_id": user_id,
                "username": username
            })
        
        # Check if user exists
        user = self.db.users.find_by_id(user_id)
        self.assertIsNotNone(user)
        self.assertEqual(user.user_id, user_id)
        self.assertEqual(user.username, username)
    
    def test_transaction_rollback(self):
        """Test transaction rollback."""
        # Create a user
        user_id = "test_user"
        username = "test_username"
        
        try:
            with self.db.transaction():
                self.db.users.create({
                    "user_id": user_id,
                    "username": username
                })
                
                # Raise an exception to trigger rollback
                raise ValueError("Test exception")
        except ValueError:
            pass
        
        # Check if user does not exist
        user = self.db.users.find_by_id(user_id)
        self.assertIsNone(user)
    
    def test_user_repository(self):
        """Test UserRepository."""
        # Create a user
        user_id = "test_user"
        username = "test_username"
        first_name = "Test"
        last_name = "User"
        
        user = self.db.users.create({
            "user_id": user_id,
            "username": username,
            "first_name": first_name,
            "last_name": last_name
        })
        
        # Check if user exists
        self.assertIsNotNone(user)
        self.assertEqual(user.user_id, user_id)
        self.assertEqual(user.username, username)
        self.assertEqual(user.first_name, first_name)
        self.assertEqual(user.last_name, last_name)
        
        # Find user by username
        user = self.db.users.find_by_username(username)
        self.assertIsNotNone(user)
        self.assertEqual(user.user_id, user_id)
        
        # Update user
        new_username = "new_username"
        user.username = new_username
        self.db.users.update(user)
        
        # Check if user is updated
        user = self.db.users.find_by_id(user_id)
        self.assertIsNotNone(user)
        self.assertEqual(user.username, new_username)
        
        # Delete user
        self.db.users.delete(user)
        
        # Check if user is deleted
        user = self.db.users.find_by_id(user_id)
        self.assertIsNone(user)
    
    def test_user_preference_repository(self):
        """Test UserPreferenceRepository."""
        # Create a user
        user_id = "test_user"
        self.db.users.create({
            "user_id": user_id,
            "username": "test_username"
        })
        
        # Set a preference
        key = "theme"
        value = "dark"
        preference = self.db.user_preferences.set_preference(user_id, key, value)
        
        # Check if preference exists
        self.assertIsNotNone(preference)
        self.assertEqual(preference.user_id, user_id)
        self.assertEqual(preference.key, key)
        self.assertEqual(preference.value, value)
        
        # Get preference
        value = self.db.user_preferences.get_preference(user_id, key)
        self.assertEqual(value, value)
        
        # Update preference
        new_value = "light"
        preference = self.db.user_preferences.set_preference(user_id, key, new_value)
        
        # Check if preference is updated
        self.assertEqual(preference.value, new_value)
        
        # Delete preference
        self.db.user_preferences.delete_preference(user_id, key)
        
        # Check if preference is deleted
        value = self.db.user_preferences.get_preference(user_id, key)
        self.assertIsNone(value)
    
    def test_conversation_repository(self):
        """Test ConversationRepository."""
        # Create a user
        user_id = "test_user"
        self.db.users.create({
            "user_id": user_id,
            "username": "test_username"
        })
        
        # Create a conversation
        conversation, created = self.db.conversations.get_or_create_active_conversation(user_id)
        
        # Check if conversation exists
        self.assertIsNotNone(conversation)
        self.assertEqual(conversation.user_id, user_id)
        self.assertTrue(created)
        
        # Add a message
        content = "Hello, world!"
        message = self.db.messages.add_message(
            conversation.conversation_id,
            user_id,
            content,
            "user"
        )
        
        # Check if message exists
        self.assertIsNotNone(message)
        self.assertEqual(message.conversation_id, conversation.conversation_id)
        self.assertEqual(message.user_id, user_id)
        self.assertEqual(message.content, content)
        self.assertEqual(message.role, "user")
        
        # Get conversation history
        history = self.db.messages.get_conversation_history(conversation.conversation_id)
        self.assertEqual(len(history), 1)
        self.assertEqual(history[0]["role"], "user")
        self.assertEqual(history[0]["content"], content)
        
        # Archive conversation
        self.db.conversations.archive_conversation(conversation.conversation_id)
        
        # Check if conversation is archived
        conversation = self.db.conversations.find_by_id(conversation.conversation_id)
        self.assertIsNotNone(conversation)
        self.assertFalse(conversation.is_active)
        
        # Create a new active conversation
        new_conversation, created = self.db.conversations.get_or_create_active_conversation(user_id)
        self.assertIsNotNone(new_conversation)
        self.assertNotEqual(new_conversation.conversation_id, conversation.conversation_id)
        self.assertTrue(created)

if __name__ == "__main__":
    unittest.main()
