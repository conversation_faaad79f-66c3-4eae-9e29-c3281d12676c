"""
User analytics for VoicePal.

This module provides analytics for user behavior and engagement patterns.
"""

import logging
import statistics
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from collections import defaultdict

logger = logging.getLogger(__name__)

@dataclass
class UserMetrics:
    """User metrics data structure."""
    total_users: int
    active_users_daily: int
    active_users_weekly: int
    active_users_monthly: int
    new_users_daily: int
    new_users_weekly: int
    new_users_monthly: int
    retention_rate_7d: float
    retention_rate_30d: float
    avg_session_duration: float
    avg_messages_per_user: float
    churn_rate: float

@dataclass
class UserSegment:
    """User segment data structure."""
    segment_name: str
    user_count: int
    characteristics: Dict[str, Any]
    avg_engagement: float
    avg_revenue: float

@dataclass
class UserJourney:
    """User journey data structure."""
    user_id: int
    journey_stages: List[Dict[str, Any]]
    total_duration: float
    conversion_events: List[str]
    drop_off_points: List[str]

class UserAnalytics:
    """Analytics for user behavior and engagement patterns."""
    
    def __init__(self, database, cache_manager=None):
        """
        Initialize user analytics.
        
        Args:
            database: Database instance
            cache_manager: Cache manager for performance
        """
        self.database = database
        self.cache_manager = cache_manager
        
        logger.info("User analytics initialized")
    
    def get_user_metrics(self, days: int = 30) -> UserMetrics:
        """
        Get comprehensive user metrics.
        
        Args:
            days: Number of days to analyze
            
        Returns:
            User metrics
        """
        try:
            # Check cache first
            cache_key = f"user_metrics_{days}"
            if self.cache_manager:
                cached_result = self.cache_manager.get(cache_key)
                if cached_result:
                    return UserMetrics(**cached_result)
            
            end_date = datetime.utcnow()
            start_date = end_date - timedelta(days=days)
            
            # Total users
            total_users = self.database.execute("SELECT COUNT(*) FROM users").fetchone()[0]
            
            # Active users (users with conversations in time period)
            active_users_daily = self._get_active_users(1)
            active_users_weekly = self._get_active_users(7)
            active_users_monthly = self._get_active_users(30)
            
            # New users
            new_users_daily = self._get_new_users(1)
            new_users_weekly = self._get_new_users(7)
            new_users_monthly = self._get_new_users(30)
            
            # Retention rates
            retention_rate_7d = self._calculate_retention_rate(7)
            retention_rate_30d = self._calculate_retention_rate(30)
            
            # Session metrics
            avg_session_duration = self._calculate_avg_session_duration(days)
            avg_messages_per_user = self._calculate_avg_messages_per_user(days)
            
            # Churn rate
            churn_rate = self._calculate_churn_rate(days)
            
            metrics = UserMetrics(
                total_users=total_users,
                active_users_daily=active_users_daily,
                active_users_weekly=active_users_weekly,
                active_users_monthly=active_users_monthly,
                new_users_daily=new_users_daily,
                new_users_weekly=new_users_weekly,
                new_users_monthly=new_users_monthly,
                retention_rate_7d=retention_rate_7d,
                retention_rate_30d=retention_rate_30d,
                avg_session_duration=avg_session_duration,
                avg_messages_per_user=avg_messages_per_user,
                churn_rate=churn_rate
            )
            
            # Cache result
            if self.cache_manager:
                self.cache_manager.set(cache_key, metrics.__dict__, ttl=1800)  # 30 minutes
            
            return metrics
            
        except Exception as e:
            logger.error(f"Failed to get user metrics: {e}")
            return UserMetrics(
                total_users=0, active_users_daily=0, active_users_weekly=0,
                active_users_monthly=0, new_users_daily=0, new_users_weekly=0,
                new_users_monthly=0, retention_rate_7d=0.0, retention_rate_30d=0.0,
                avg_session_duration=0.0, avg_messages_per_user=0.0, churn_rate=0.0
            )
    
    def _get_active_users(self, days: int) -> int:
        """Get number of active users in the last N days."""
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days)
            
            result = self.database.execute("""
                SELECT COUNT(DISTINCT c.user_id)
                FROM conversations c
                WHERE c.created_at >= ?
            """, (cutoff_date.isoformat(),)).fetchone()
            
            return result[0] if result else 0
            
        except Exception as e:
            logger.error(f"Failed to get active users: {e}")
            return 0
    
    def _get_new_users(self, days: int) -> int:
        """Get number of new users in the last N days."""
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days)
            
            result = self.database.execute("""
                SELECT COUNT(*)
                FROM users
                WHERE created_at >= ?
            """, (cutoff_date.isoformat(),)).fetchone()
            
            return result[0] if result else 0
            
        except Exception as e:
            logger.error(f"Failed to get new users: {e}")
            return 0
    
    def _calculate_retention_rate(self, days: int) -> float:
        """Calculate user retention rate."""
        try:
            # Get users who joined N days ago
            join_date = datetime.utcnow() - timedelta(days=days)
            join_start = join_date - timedelta(days=1)
            
            new_users = self.database.execute("""
                SELECT user_id FROM users
                WHERE created_at >= ? AND created_at < ?
            """, (join_start.isoformat(), join_date.isoformat())).fetchall()
            
            if not new_users:
                return 0.0
            
            # Check how many of those users are still active
            user_ids = [user['user_id'] for user in new_users]
            placeholders = ','.join(['?'] * len(user_ids))
            
            active_users = self.database.execute(f"""
                SELECT COUNT(DISTINCT user_id)
                FROM conversations
                WHERE user_id IN ({placeholders})
                AND created_at >= ?
            """, user_ids + [join_date.isoformat()]).fetchone()
            
            retention_rate = (active_users[0] / len(new_users)) if new_users else 0.0
            return round(retention_rate, 3)
            
        except Exception as e:
            logger.error(f"Failed to calculate retention rate: {e}")
            return 0.0
    
    def _calculate_avg_session_duration(self, days: int) -> float:
        """Calculate average session duration."""
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days)
            
            conversations = self.database.execute("""
                SELECT created_at, ended_at
                FROM conversations
                WHERE created_at >= ? AND ended_at IS NOT NULL
            """, (cutoff_date.isoformat(),)).fetchall()
            
            if not conversations:
                return 0.0
            
            durations = []
            for conv in conversations:
                start_time = datetime.fromisoformat(conv['created_at'])
                end_time = datetime.fromisoformat(conv['ended_at'])
                duration = (end_time - start_time).total_seconds() / 60  # minutes
                durations.append(duration)
            
            return round(statistics.mean(durations), 2) if durations else 0.0
            
        except Exception as e:
            logger.error(f"Failed to calculate avg session duration: {e}")
            return 0.0
    
    def _calculate_avg_messages_per_user(self, days: int) -> float:
        """Calculate average messages per user."""
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days)
            
            result = self.database.execute("""
                SELECT COUNT(m.message_id) / COUNT(DISTINCT c.user_id) as avg_messages
                FROM messages m
                JOIN conversations c ON m.conversation_id = c.conversation_id
                WHERE c.created_at >= ?
            """, (cutoff_date.isoformat(),)).fetchone()
            
            return round(result[0], 2) if result and result[0] else 0.0
            
        except Exception as e:
            logger.error(f"Failed to calculate avg messages per user: {e}")
            return 0.0
    
    def _calculate_churn_rate(self, days: int) -> float:
        """Calculate user churn rate."""
        try:
            # Users who were active in the previous period but not in current period
            current_period_start = datetime.utcnow() - timedelta(days=days)
            previous_period_start = current_period_start - timedelta(days=days)
            
            # Users active in previous period
            previous_active = self.database.execute("""
                SELECT DISTINCT user_id
                FROM conversations
                WHERE created_at >= ? AND created_at < ?
            """, (previous_period_start.isoformat(), current_period_start.isoformat())).fetchall()
            
            if not previous_active:
                return 0.0
            
            # Users active in current period
            current_active = self.database.execute("""
                SELECT DISTINCT user_id
                FROM conversations
                WHERE created_at >= ?
            """, (current_period_start.isoformat(),)).fetchall()
            
            previous_user_ids = {user['user_id'] for user in previous_active}
            current_user_ids = {user['user_id'] for user in current_active}
            
            churned_users = previous_user_ids - current_user_ids
            churn_rate = len(churned_users) / len(previous_user_ids)
            
            return round(churn_rate, 3)
            
        except Exception as e:
            logger.error(f"Failed to calculate churn rate: {e}")
            return 0.0
    
    def segment_users(self) -> List[UserSegment]:
        """
        Segment users based on behavior patterns.
        
        Returns:
            List of user segments
        """
        try:
            segments = []
            
            # High-value users (lots of conversations and payments)
            high_value_users = self.database.execute("""
                SELECT COUNT(DISTINCT u.user_id) as user_count,
                       AVG(u.credits) as avg_credits,
                       AVG(conv_count.conversations) as avg_conversations
                FROM users u
                JOIN (
                    SELECT user_id, COUNT(*) as conversations
                    FROM conversations
                    GROUP BY user_id
                    HAVING COUNT(*) >= 10
                ) conv_count ON u.user_id = conv_count.user_id
                WHERE u.credits >= 100
            """).fetchone()
            
            if high_value_users and high_value_users['user_count'] > 0:
                segments.append(UserSegment(
                    segment_name="High Value Users",
                    user_count=high_value_users['user_count'],
                    characteristics={
                        "avg_credits": high_value_users['avg_credits'],
                        "avg_conversations": high_value_users['avg_conversations'],
                        "criteria": "10+ conversations and 100+ credits"
                    },
                    avg_engagement=0.9,
                    avg_revenue=high_value_users['avg_credits'] * 0.01  # Assuming 1 credit = $0.01
                ))
            
            # Regular users
            regular_users = self.database.execute("""
                SELECT COUNT(DISTINCT u.user_id) as user_count,
                       AVG(u.credits) as avg_credits,
                       AVG(conv_count.conversations) as avg_conversations
                FROM users u
                JOIN (
                    SELECT user_id, COUNT(*) as conversations
                    FROM conversations
                    GROUP BY user_id
                    HAVING COUNT(*) BETWEEN 3 AND 9
                ) conv_count ON u.user_id = conv_count.user_id
            """).fetchone()
            
            if regular_users and regular_users['user_count'] > 0:
                segments.append(UserSegment(
                    segment_name="Regular Users",
                    user_count=regular_users['user_count'],
                    characteristics={
                        "avg_credits": regular_users['avg_credits'],
                        "avg_conversations": regular_users['avg_conversations'],
                        "criteria": "3-9 conversations"
                    },
                    avg_engagement=0.6,
                    avg_revenue=regular_users['avg_credits'] * 0.01
                ))
            
            # New/Trial users
            trial_users = self.database.execute("""
                SELECT COUNT(DISTINCT u.user_id) as user_count,
                       AVG(u.credits) as avg_credits,
                       AVG(COALESCE(conv_count.conversations, 0)) as avg_conversations
                FROM users u
                LEFT JOIN (
                    SELECT user_id, COUNT(*) as conversations
                    FROM conversations
                    GROUP BY user_id
                ) conv_count ON u.user_id = conv_count.user_id
                WHERE COALESCE(conv_count.conversations, 0) <= 2
            """).fetchone()
            
            if trial_users and trial_users['user_count'] > 0:
                segments.append(UserSegment(
                    segment_name="Trial Users",
                    user_count=trial_users['user_count'],
                    characteristics={
                        "avg_credits": trial_users['avg_credits'],
                        "avg_conversations": trial_users['avg_conversations'],
                        "criteria": "0-2 conversations"
                    },
                    avg_engagement=0.2,
                    avg_revenue=trial_users['avg_credits'] * 0.01
                ))
            
            return segments
            
        except Exception as e:
            logger.error(f"Failed to segment users: {e}")
            return []
    
    def analyze_user_journey(self, user_id: int) -> UserJourney:
        """
        Analyze individual user journey.
        
        Args:
            user_id: User ID to analyze
            
        Returns:
            User journey analysis
        """
        try:
            # Get user's conversations chronologically
            conversations = self.database.execute("""
                SELECT conversation_id, created_at, ended_at,
                       COUNT(m.message_id) as message_count
                FROM conversations c
                LEFT JOIN messages m ON c.conversation_id = m.conversation_id
                WHERE c.user_id = ?
                GROUP BY c.conversation_id
                ORDER BY c.created_at
            """, (user_id,)).fetchall()
            
            journey_stages = []
            total_duration = 0.0
            conversion_events = []
            drop_off_points = []
            
            for i, conv in enumerate(conversations):
                stage = {
                    "stage_number": i + 1,
                    "conversation_id": conv['conversation_id'],
                    "start_time": conv['created_at'],
                    "end_time": conv['ended_at'],
                    "message_count": conv['message_count']
                }
                
                # Calculate duration
                if conv['ended_at']:
                    start_time = datetime.fromisoformat(conv['created_at'])
                    end_time = datetime.fromisoformat(conv['ended_at'])
                    duration = (end_time - start_time).total_seconds() / 60
                    stage["duration_minutes"] = duration
                    total_duration += duration
                
                # Identify conversion events (long conversations)
                if conv['message_count'] >= 10:
                    conversion_events.append(f"Stage {i+1}: Extended conversation")
                
                # Identify potential drop-off points (short conversations)
                if conv['message_count'] <= 2:
                    drop_off_points.append(f"Stage {i+1}: Short conversation")
                
                journey_stages.append(stage)
            
            # Check for payment events
            payments = self.database.execute("""
                SELECT created_at, amount, status
                FROM transactions
                WHERE user_id = ?
                ORDER BY created_at
            """, (user_id,)).fetchall()
            
            for payment in payments:
                if payment['status'] == 'completed':
                    conversion_events.append(f"Payment: ${payment['amount']}")
            
            return UserJourney(
                user_id=user_id,
                journey_stages=journey_stages,
                total_duration=total_duration,
                conversion_events=conversion_events,
                drop_off_points=drop_off_points
            )
            
        except Exception as e:
            logger.error(f"Failed to analyze user journey: {e}")
            return UserJourney(
                user_id=user_id,
                journey_stages=[],
                total_duration=0.0,
                conversion_events=[],
                drop_off_points=[]
            )
    
    def get_user_cohort_analysis(self, weeks: int = 12) -> Dict[str, Any]:
        """
        Perform cohort analysis to understand user retention over time.
        
        Args:
            weeks: Number of weeks to analyze
            
        Returns:
            Cohort analysis data
        """
        try:
            cohort_data = {}
            end_date = datetime.utcnow()
            
            for week in range(weeks):
                cohort_start = end_date - timedelta(weeks=week+1)
                cohort_end = end_date - timedelta(weeks=week)
                
                # Users who joined in this cohort week
                cohort_users = self.database.execute("""
                    SELECT user_id FROM users
                    WHERE created_at >= ? AND created_at < ?
                """, (cohort_start.isoformat(), cohort_end.isoformat())).fetchall()
                
                if not cohort_users:
                    continue
                
                cohort_size = len(cohort_users)
                user_ids = [user['user_id'] for user in cohort_users]
                
                # Track retention for each subsequent week
                retention_data = []
                for retention_week in range(min(week + 1, 12)):  # Up to 12 weeks retention
                    retention_start = cohort_end + timedelta(weeks=retention_week)
                    retention_end = retention_start + timedelta(weeks=1)
                    
                    if retention_start > end_date:
                        break
                    
                    # Count active users in this retention week
                    placeholders = ','.join(['?'] * len(user_ids))
                    active_count = self.database.execute(f"""
                        SELECT COUNT(DISTINCT user_id)
                        FROM conversations
                        WHERE user_id IN ({placeholders})
                        AND created_at >= ? AND created_at < ?
                    """, user_ids + [retention_start.isoformat(), retention_end.isoformat()]).fetchone()[0]
                    
                    retention_rate = active_count / cohort_size if cohort_size > 0 else 0
                    retention_data.append({
                        "week": retention_week,
                        "active_users": active_count,
                        "retention_rate": retention_rate
                    })
                
                cohort_data[f"week_{week}"] = {
                    "cohort_start": cohort_start.strftime("%Y-%m-%d"),
                    "cohort_size": cohort_size,
                    "retention": retention_data
                }
            
            return cohort_data
            
        except Exception as e:
            logger.error(f"Failed to perform cohort analysis: {e}")
            return {}
