"""
Voice settings handlers for VoicePal.

This module provides handlers for voice settings commands.
"""

import logging
from typing import <PERSON><PERSON>, <PERSON><PERSON>

from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# Constants
BACK_BUTTON_TEXT = "🔙 Back to Voice Settings"
BOT_INSTANCE_ERROR = "Bot instance not found. Please try again later."

def _get_voice_name(voice_processor, voice_id: str) -> str:
    """
    Get the display name for a voice ID.

    Args:
        voice_processor: Voice processor instance
        voice_id: Voice ID

    Returns:
        str: Voice name or the original voice ID if name not found
    """
    if not hasattr(voice_processor.tts_provider, "get_available_voices"):
        return voice_id

    voices = voice_processor.tts_provider.get_available_voices()
    if voice_id in voices and "name" in voices[voice_id]:
        return voices[voice_id]["name"]

    return voice_id

async def tts_provider_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle the /tts_provider command."""
    # Get bot instance from context
    bot_instance = context.application.bot_data.get("bot_instance")
    if not bot_instance:
        await update.message.reply_text("Bot instance not found. Please try again later.")
        return

    # Create message
    message = (
        "🔊 *Voice Provider Settings*\n\n"
        "Deepgram is the primary voice provider for best performance and quality.\n"
        "Select from available Deepgram voices:"
    )

    # Create keyboard that directly shows Deepgram voices
    keyboard = [
        [InlineKeyboardButton("View Deepgram Voices", callback_data="show_deepgram_voices")],
        [InlineKeyboardButton(BACK_BUTTON_TEXT, callback_data="show_voice_settings")]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)

    await update.message.reply_text(message, reply_markup=reply_markup, parse_mode="Markdown")

async def deepgram_voices_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle the /deepgram_voices command."""
    # Get bot instance
    bot_instance = context.application.bot_data.get("bot_instance")
    if not bot_instance:
        # Handle both message and callback query cases
        if update.message:
            await update.message.reply_text(
                BOT_INSTANCE_ERROR,
                parse_mode="Markdown"
            )
        elif update.callback_query:
            await update.callback_query.answer(BOT_INSTANCE_ERROR)
        return

    # Use bot instance's voice processor
    voice_processor = bot_instance.voice_processor

    # Always set to Deepgram if not already using it
    if voice_processor.tts_provider_type != "deepgram":
        logger.info("Setting voice processor to Deepgram for voice selection")
        # Get Deepgram API key
        config_manager = bot_instance.config_manager
        key_manager = bot_instance.key_manager
        stt_config = config_manager.get_provider_config("stt")
        api_key = key_manager.get_key("deepgram", update.effective_user.id) or stt_config.get("api_key")

        if not api_key:
            error_message = "Deepgram API key not found. Please contact support."
            if update.message:
                await update.message.reply_text(error_message)
            elif update.callback_query:
                await update.callback_query.edit_message_text(
                    error_message,
                    reply_markup=InlineKeyboardMarkup([[
                        InlineKeyboardButton(BACK_BUTTON_TEXT, callback_data="show_voice_settings")
                    ]])
                )
            return

        # Set up Deepgram provider
        success = voice_processor.set_tts_provider("deepgram", api_key=api_key)
        if not success:
            error_message = "Failed to set up Deepgram TTS provider. Please try again later."
            if update.message:
                await update.message.reply_text(error_message)
            elif update.callback_query:
                await update.callback_query.edit_message_text(
                    error_message,
                    reply_markup=InlineKeyboardMarkup([[
                        InlineKeyboardButton(BACK_BUTTON_TEXT, callback_data="show_voice_settings")
                    ]])
                )
            return

    # Get available voices
    voices = voice_processor.tts_provider.get_available_voices()

    # Define recommended voices with descriptions
    recommended_voices = {
        "aura-thalia-en": {"name": "Thalia", "description": "Friendly, Female"},
        "aura-athena-en": {"name": "Athena", "description": "Professional, Female"},
        "aura-zeus-en": {"name": "Zeus", "description": "Authoritative, Male"},
        "aura-apollo-en": {"name": "Apollo", "description": "Friendly, Male"},
        "aura-stella-en": {"name": "Stella", "description": "Casual, Female"}
    }

    # Create message
    message = "🎙️ *Deepgram Voices*\n\nSelect a voice for your AI companion:\n\n"

    # Add recommended voices section
    message += "*✨ Recommended Voices:*\n"
    for voice_id, voice_info in recommended_voices.items():
        if voice_id in voices:
            message += f"• *{voice_info['name']}* - {voice_info['description']}\n"
    message += "\n"

    # Create keyboard for voice selection
    keyboard = []

    # Add recommended voices first
    for voice_id, voice_info in recommended_voices.items():
        if voice_id in voices:
            keyboard.append([
                InlineKeyboardButton(
                    f"{voice_info['name']} ({voice_info['description']})",
                    callback_data=f"set_voice_{voice_id}"
                )
            ])

    # Add divider
    keyboard.append([InlineKeyboardButton("───────────────", callback_data="noop")])

    # Add other voices if any
    other_voices = {k: v for k, v in voices.items() if k not in recommended_voices}
    if other_voices:
        for voice_id, voice_info in other_voices.items():
            if isinstance(voice_info, dict) and 'name' in voice_info and 'gender' in voice_info:
                keyboard.append([
                    InlineKeyboardButton(
                        f"{voice_info['name']} ({voice_info['gender'].capitalize()})",
                        callback_data=f"set_voice_{voice_id}"
                    )
                ])

    # Add back button
    keyboard.append([InlineKeyboardButton(BACK_BUTTON_TEXT, callback_data="show_voice_settings")])
    reply_markup = InlineKeyboardMarkup(keyboard)

    # Handle both message and callback query cases
    if update.message:
        await update.message.reply_text(message, reply_markup=reply_markup, parse_mode="Markdown")
    elif update.callback_query:
        await update.callback_query.edit_message_text(
            message,
            reply_markup=reply_markup,
            parse_mode="Markdown"
        )

async def elevenlabs_voices_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle the /elevenlabs_voices command."""
    # Get bot instance
    bot_instance = context.application.bot_data.get("bot_instance")
    if not bot_instance:
        # Handle both message and callback query cases
        if update.message:
            await update.message.reply_text(
                BOT_INSTANCE_ERROR,
                parse_mode="Markdown"
            )
        elif update.callback_query:
            await update.callback_query.answer(BOT_INSTANCE_ERROR)
        return

    # Use bot instance's voice processor
    voice_processor = bot_instance.voice_processor

    # Check if voice processor is using ElevenLabs
    if voice_processor.tts_provider_type != "elevenlabs":
        # Handle both message and callback query cases
        if update.message:
            await update.message.reply_text(
                "You are not currently using ElevenLabs TTS. Use /tts_provider to switch to ElevenLabs.",
                parse_mode="Markdown"
            )
        elif update.callback_query:
            await update.callback_query.edit_message_text(
                "You are not currently using ElevenLabs TTS. Please select a different provider first.",
                reply_markup=InlineKeyboardMarkup([[
                    InlineKeyboardButton("🔙 Back to Voice Settings", callback_data="show_voice_settings")
                ]]),
                parse_mode="Markdown"
            )
        return

    # Get available voices
    voices = voice_processor.tts_provider.get_available_voices()

    # Create message
    message = "🎙️ *Available ElevenLabs Voices*\n\n"

    # Add voices
    for voice_id, voice_info in voices.items():
        message += f"• {voice_id} - {voice_info.get('description', 'No description')}\n"

    # Create keyboard for voice selection
    keyboard = []
    for voice_id in voices.keys():
        keyboard.append([
            InlineKeyboardButton(voice_id, callback_data=f"set_voice_{voice_id}")
        ])

    # Add back button
    keyboard.append([InlineKeyboardButton(BACK_BUTTON_TEXT, callback_data="show_voice_settings")])
    reply_markup = InlineKeyboardMarkup(keyboard)

    # Handle both message and callback query cases
    if update.message:
        await update.message.reply_text(message, reply_markup=reply_markup, parse_mode="Markdown")
    elif update.callback_query:
        await update.callback_query.edit_message_text(
            message,
            reply_markup=reply_markup,
            parse_mode="Markdown"
        )

async def handle_voice_settings_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> Optional[Tuple[str, InlineKeyboardMarkup]]:
    """
    Handle voice settings callbacks.

    Args:
        update: Telegram update
        context: Callback context

    Returns:
        Optional tuple of (response_text, keyboard_markup)
    """
    query = update.callback_query
    user_id = query.from_user.id
    callback_data = query.data

    # Get bot instance
    bot_instance = context.application.bot_data.get("bot_instance")
    if not bot_instance:
        logger.error("Bot instance not found in context.application.bot_data")
        # Try to get it from context directly
        if hasattr(context, "bot_data") and "bot_instance" in context.bot_data:
            bot_instance = context.bot_data["bot_instance"]
            logger.info("Retrieved bot instance from context.bot_data")
        else:
            logger.error("Bot instance not found in context.bot_data either")
            return BOT_INSTANCE_ERROR, None

    # Use bot instance's database and voice processor
    database = bot_instance.database
    voice_processor = bot_instance.voice_processor

    # Log for debugging
    logger.info("Using database and voice processor for user %s", user_id)

    # Handle TTS provider selection
    if callback_data.startswith("set_tts_provider_"):
        # Always use Deepgram regardless of the requested provider
        provider_type = "deepgram"

        # Log the provider selection
        logger.info(f"Setting TTS provider to Deepgram for user {user_id}")

        # Get provider options from config manager
        config_manager = bot_instance.config_manager
        key_manager = bot_instance.key_manager

        try:
            # Get Deepgram API key
            stt_config = config_manager.get_provider_config("stt")
            api_key = key_manager.get_key("deepgram", user_id) or stt_config.get("api_key")

            if not api_key:
                return "Deepgram API key not found. Please contact support.", None

            # Set up provider options
            provider_options = {
                "api_key": api_key,
                "voice_id": "aura-thalia-en",  # Default voice
                "model_id": "aura-2"  # Latest model
            }

            # If we got the API key from config, store it in key manager
            if api_key and api_key == stt_config.get("api_key"):
                key_manager.store_key("deepgram", api_key)

            # Save user preference
            database.update_user_preference(user_id, "tts_provider", "deepgram")
            database.update_user_preference(user_id, "voice_id", provider_options["voice_id"])

            # Save button state if available
            if hasattr(bot_instance, 'button_state_manager') and bot_instance.button_state_manager:
                bot_instance.button_state_manager.save_button_state(
                    user_id=user_id,
                    menu_id="tts_provider",
                    state={"provider": "deepgram", "voice_id": provider_options["voice_id"]}
                )

            # Update voice processor
            success = voice_processor.set_tts_provider("deepgram", **provider_options)

            if success:
                # Create response and keyboard
                response = "Voice provider set to Deepgram!"
                keyboard = [
                    [InlineKeyboardButton("View Available Voices", callback_data="show_deepgram_voices")],
                    [InlineKeyboardButton(BACK_BUTTON_TEXT, callback_data="show_voice_settings")]
                ]
                return response, InlineKeyboardMarkup(keyboard)
            else:
                # Try Google TTS as a last resort
                logger.warning("Failed to set Deepgram TTS provider, falling back to Google TTS")
                success = voice_processor.set_tts_provider("google", {})

                if success:
                    database.update_user_preference(user_id, "tts_provider", "google")
                    return "Failed to set Deepgram TTS provider. Falling back to Google TTS.", InlineKeyboardMarkup([
                        [InlineKeyboardButton(BACK_BUTTON_TEXT, callback_data="show_voice_settings")]
                    ])
                else:
                    return "Failed to set any TTS provider. Please try again later.", None

        except Exception as e:
            logger.error(f"Error setting up Deepgram TTS provider: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return f"Error setting TTS provider: {str(e)[:100]}. Please try again.", None

    # Handle voice selection
    elif callback_data.startswith("set_voice_"):
        voice_id = callback_data.replace("set_voice_", "")

        try:
            # Get the current TTS provider type
            provider_type = voice_processor.tts_provider_type
            logger.info(f"Setting voice to {voice_id} for provider {provider_type}")

            # Update voice processor based on provider type
            if hasattr(voice_processor.tts_provider, "set_voice"):
                # Use set_voice method if available
                voice_processor.tts_provider.set_voice(voice_id)
                logger.info(f"Set voice using set_voice method: {voice_id}")
            else:
                # Directly update the voice_id attribute
                voice_processor.tts_provider.voice_id = voice_id
                logger.info(f"Set voice by updating voice_id attribute: {voice_id}")

            # Update user preference
            database.update_user_preference(user_id, "voice_id", voice_id)

            # Save button state if available
            if hasattr(bot_instance, 'button_state_manager') and bot_instance.button_state_manager:
                bot_instance.button_state_manager.save_button_state(
                    user_id=user_id,
                    menu_id="voice",
                    state={"provider": provider_type, "voice_id": voice_id}
                )

            # Get voice name
            voice_name = _get_voice_name(voice_processor, voice_id)

            # Create keyboard with back button
            keyboard = [
                [InlineKeyboardButton(BACK_BUTTON_TEXT, callback_data="show_voice_settings")]
            ]

            return f"Voice set to {voice_name}!", InlineKeyboardMarkup(keyboard)

        except Exception as e:
            logger.error(f"Error setting voice: {e}")
            import traceback
            logger.error(traceback.format_exc())

            # Create keyboard with back button
            keyboard = [
                [InlineKeyboardButton(BACK_BUTTON_TEXT, callback_data="show_voice_settings")]
            ]

            return f"Error setting voice: {str(e)[:100]}. Please try again.", InlineKeyboardMarkup(keyboard)

    # Handle show voices callbacks
    elif callback_data == "show_deepgram_voices":
        await deepgram_voices_command(update, context)
        return None, None
    elif callback_data == "show_elevenlabs_voices":
        await elevenlabs_voices_command(update, context)
        return None, None

    return None, None
