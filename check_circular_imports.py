"""
<PERSON><PERSON><PERSON> to check for circular imports in the project.

This script:
1. Scans all Python files in the project
2. Builds a dependency graph
3. Detects circular dependencies
4. Reports the findings
"""

import os
import re
from collections import defaultdict

# Define directories to ignore
IGNORE_DIRS = [
    '.git',
    'venv',
    '__pycache__',
    '.idea',
    '.vscode',
    'node_modules',
    'voicepal-env',
    'voicepal-env-new',
]

# Regular expressions for import statements
IMPORT_PATTERN = re.compile(r'^import\s+([^#\n]+)')
FROM_IMPORT_PATTERN = re.compile(r'^from\s+([^#\n]+)\s+import\s+([^#\n]+)')
COMMENT_PATTERN = re.compile(r'#.*$')

def extract_imports(file_path):
    """Extract import statements from a Python file."""
    imports = []
    
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
            
            # Process line by line
            lines = content.split('\n')
            for line in lines:
                # Remove comments
                line = COMMENT_PATTERN.sub('', line).strip()
                
                # Check for 'import' statements
                import_match = IMPORT_PATTERN.match(line)
                if import_match:
                    modules = import_match.group(1).strip()
                    for module in modules.split(','):
                        module = module.strip()
                        if module:
                            if ' as ' in module:
                                module_name = module.split(' as ')[0].strip()
                                imports.append(module_name)
                            else:
                                imports.append(module)
                
                # Check for 'from ... import' statements
                from_import_match = FROM_IMPORT_PATTERN.match(line)
                if from_import_match:
                    module = from_import_match.group(1).strip()
                    imports.append(module)
    
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
    
    return imports

def file_path_to_module_name(file_path):
    """Convert a file path to a module name."""
    # Remove .py extension
    if file_path.endswith('.py'):
        file_path = file_path[:-3]
    
    # Replace directory separators with dots
    module_name = file_path.replace(os.path.sep, '.')
    
    # Handle __init__.py files
    if module_name.endswith('.__init__'):
        module_name = module_name[:-9]
    
    return module_name

def find_python_files(base_dir):
    """Find all Python files in the project."""
    python_files = []
    
    for root, dirs, files in os.walk(base_dir):
        # Skip ignored directories
        dirs[:] = [d for d in dirs if d not in IGNORE_DIRS]
        
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                rel_path = os.path.relpath(file_path, base_dir)
                python_files.append(rel_path)
    
    return python_files

def build_dependency_graph(base_dir, python_files):
    """Build a dependency graph from import statements."""
    dependency_graph = defaultdict(set)
    
    for file_path in python_files:
        full_path = os.path.join(base_dir, file_path)
        module_name = file_path_to_module_name(file_path)
        
        imports = extract_imports(full_path)
        for imported_module in imports:
            # Only consider project imports
            if imported_module.startswith('bot.'):
                dependency_graph[module_name].add(imported_module)
    
    return dependency_graph

def find_circular_dependencies(dependency_graph):
    """Find circular dependencies in the dependency graph."""
    circular_deps = []
    
    def dfs(node, visited, path):
        visited.add(node)
        path.append(node)
        
        for neighbor in dependency_graph.get(node, []):
            if neighbor in path:
                # Found a cycle
                cycle = path[path.index(neighbor):] + [neighbor]
                circular_deps.append(cycle)
            elif neighbor not in visited:
                dfs(neighbor, visited, path)
        
        path.pop()
    
    for node in dependency_graph:
        visited = set()
        path = []
        dfs(node, visited, path)
    
    # Remove duplicates
    unique_circular_deps = []
    for cycle in circular_deps:
        # Sort cycle to normalize it
        sorted_cycle = sorted(cycle)
        if sorted_cycle not in unique_circular_deps:
            unique_circular_deps.append(sorted_cycle)
    
    return unique_circular_deps

def main():
    """Main function."""
    base_dir = os.path.dirname(os.path.abspath(__file__))
    print(f"Checking for circular imports in {base_dir}")
    
    # Find all Python files
    python_files = find_python_files(base_dir)
    print(f"Found {len(python_files)} Python files")
    
    # Build dependency graph
    dependency_graph = build_dependency_graph(base_dir, python_files)
    print(f"Built dependency graph with {len(dependency_graph)} modules")
    
    # Find circular dependencies
    circular_deps = find_circular_dependencies(dependency_graph)
    
    # Print results
    print("\n=== CIRCULAR DEPENDENCIES REPORT ===\n")
    
    if circular_deps:
        print(f"Found {len(circular_deps)} circular dependencies:")
        for i, cycle in enumerate(circular_deps, 1):
            print(f"{i}. {' -> '.join(cycle)}")
    else:
        print("No circular dependencies found")
    
    print("\n=== END OF REPORT ===")

if __name__ == "__main__":
    main()
