"""
Database extensions for VoicePal.

This module provides extensions to the Database class for various features.
"""

import logging
from typing import Dict, Any, Optional, List, Tuple

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

from bot.database.extensions.payment import extend_database_for_payment
from bot.database.extensions.sentiment import extend_database_for_sentiment
from bot.database.extensions.memory import extend_database_for_memory
from bot.database.extensions.user_preferences import extend_database_for_user_preferences
from bot.database.extensions.stats import extend_database_for_stats
from bot.database.extensions.verification import extend_database_for_verification
from bot.database.extensions.subscription import extend_database_for_subscription

def extend_database(database) -> None:
    """
    Extend the database with all extensions.

    Args:
        database: Database instance
    """
    # Extend database with all extensions
    extend_database_for_payment(database)
    extend_database_for_sentiment(database)
    extend_database_for_memory(database)
    extend_database_for_user_preferences(database)
    extend_database_for_stats(database)
    extend_database_for_verification(database)
    extend_database_for_subscription(database)

    logger.info("Database extended with all extensions")

__all__ = [
    'extend_database',
    'extend_database_for_payment',
    'extend_database_for_sentiment',
    'extend_database_for_memory',
    'extend_database_for_user_preferences',
    'extend_database_for_stats',
    'extend_database_for_verification',
    'extend_database_for_subscription'
]
