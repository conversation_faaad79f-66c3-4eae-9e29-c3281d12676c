"""
Test script for Telegram Stars payment system.

This script tests the Telegram Stars payment system functionality.
"""

import unittest
import asyncio
import logging
import os
import sys
from unittest.mock import MagicMock, patch
from datetime import datetime

# Add parent directory to path to import bot modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from bot.payment.telegram_stars_payment import TelegramStarsPayment
from bot.database.extensions.payment import extend_database_for_payment

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class TestTelegramStarsPayment(unittest.TestCase):
    """Test case for Telegram Stars payment system."""

    def setUp(self):
        """Set up test fixtures."""
        # Create mock database
        self.mock_db = MagicMock()
        self.mock_db.add_invoice.return_value = 1
        self.mock_db.update_invoice_status.return_value = True
        self.mock_db.add_credits.return_value = 100
        self.mock_db.add_transaction.return_value = 1
        self.mock_db.get_transaction.return_value = {
            "id": 1,
            "user_id": 123,
            "amount": 100,
            "credits": 100,
            "transaction_id": "test_transaction_id",
            "status": "completed",
            "created_at": "2023-01-01 12:00:00"
        }
        self.mock_db.deduct_credits.return_value = 0
        self.mock_db.update_transaction_status.return_value = True

        # Create payment system
        self.payment_system = TelegramStarsPayment(self.mock_db)

        # Create mock update and context
        self.mock_update = MagicMock()
        self.mock_update.callback_query.from_user.id = 123
        self.mock_update.effective_user.id = 123
        self.mock_update.message.successful_payment.invoice_payload = "credits-small-test"
        self.mock_update.message.successful_payment.telegram_payment_charge_id = "test_charge_id"
        self.mock_update.message.successful_payment.total_amount = 100

        self.mock_context = MagicMock()

    def test_get_credit_packages(self):
        """Test get_credit_packages method."""
        packages = self.payment_system.get_credit_packages()

        # Check that packages contain expected keys
        self.assertIn("small", packages)
        self.assertIn("medium", packages)
        self.assertIn("large", packages)

        # Check that small package has expected values
        small_package = packages["small"]
        self.assertEqual(small_package["credits"], 100)
        self.assertEqual(small_package["price"], 100)
        self.assertEqual(small_package["currency"], "XTR")

    def test_get_credit_package(self):
        """Test get_credit_package method."""
        # Test with valid package ID
        package = self.payment_system.get_credit_package("small")
        self.assertIsNotNone(package)
        self.assertEqual(package["id"], "small")

        # Test with invalid package ID
        package = self.payment_system.get_credit_package("invalid")
        self.assertIsNone(package)

    def test_get_payment_keyboard(self):
        """Test get_payment_keyboard method."""
        keyboard = self.payment_system.get_payment_keyboard()

        # Check that keyboard has expected structure
        self.assertIsNotNone(keyboard)
        self.assertGreater(len(keyboard.inline_keyboard), 0)

        # Check that first button is for small package
        first_button = keyboard.inline_keyboard[0][0]
        self.assertIn("Small Credit Package", first_button.text)
        self.assertIn("buy_credits_small", first_button.callback_data)

    async def test_start_payment(self):
        """Test start_payment method."""
        # Test with valid package ID
        await self.payment_system.start_payment(self.mock_update, self.mock_context, "small")

        # Check that invoice was added to database
        self.mock_db.add_invoice.assert_called_once()

        # Check that invoice was sent
        self.mock_context.bot.send_invoice.assert_called_once()

    async def test_precheckout_callback(self):
        """Test precheckout_callback method."""
        # Create mock pre-checkout query
        mock_query = MagicMock()
        mock_query.invoice_payload = "credits-small-test"
        mock_query.from_user.id = 123
        self.mock_update.pre_checkout_query = mock_query

        # Test with valid payload
        await self.payment_system.precheckout_callback(self.mock_update, self.mock_context)

        # Check that invoice status was updated
        self.mock_db.update_invoice_status.assert_called_once_with(
            payload="credits-small-test",
            status="pre_checkout"
        )

        # Check that query was answered
        mock_query.answer.assert_called_once_with(ok=True)

    async def test_successful_payment_callback(self):
        """Test successful_payment_callback method."""
        # Test with valid payment
        await self.payment_system.successful_payment_callback(self.mock_update, self.mock_context)

        # Check that credits were added
        self.mock_db.add_credits.assert_called_once_with(123, 100)

        # Check that transaction was added
        self.mock_db.add_transaction.assert_called_once()

        # Check that invoice status was updated
        self.mock_db.update_invoice_status.assert_called_once_with(
            payload="credits-small-test",
            status="completed"
        )

        # Check that message was sent
        self.mock_update.message.reply_text.assert_called_once()

    async def test_refund_payment(self):
        """Test refund_payment method."""
        # Test with valid transaction
        result = await self.payment_system.refund_payment(123, "test_transaction_id")

        # Check that result is True
        self.assertTrue(result)

        # Check that credits were deducted
        self.mock_db.deduct_credits.assert_called_once_with(123, 100)

        # Check that refund transaction was added
        self.mock_db.add_transaction.assert_called_once()

        # Check that original transaction status was updated
        self.mock_db.update_transaction_status.assert_called_once_with(
            transaction_id="test_transaction_id",
            status="refunded"
        )

def run_tests():
    """Run the tests."""
    unittest.main()

if __name__ == "__main__":
    run_tests()
