"""
Sentiment integration for VoicePal.

This module provides functions to integrate the sentiment analyzer into the main bot.
"""

import logging
from typing import Dict, Any, Optional

from bot.features.sentiment_analyzer import SentimentAnalyzer
from bot.database.extensions.sentiment import extend_database_for_sentiment

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

def integrate_sentiment_analyzer(bot_instance) -> bool:
    """
    Integrate sentiment analyzer into the bot.

    Args:
        bot_instance: VoicePalBot instance

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Extend database with sentiment methods
        extend_database_for_sentiment(bot_instance.database)

        # Create sentiment analyzer
        sentiment_analyzer = SentimentAnalyzer(
            deepgram_provider=bot_instance.stt_provider,
            ai_provider=bot_instance.ai_provider,
            database=bot_instance.database,
            config=bot_instance.config_manager.get_feature_config("sentiment")
        )

        # Add sentiment analyzer to bot
        bot_instance.sentiment_analyzer = sentiment_analyzer

        # Update feature registry to indicate sentiment analysis is enabled
        if hasattr(bot_instance, 'feature_registry'):
            bot_instance.feature_registry.register_feature(
                "sentiment_analysis",
                True,
                "Sentiment analysis for detecting user emotions"
            )

        # Update bot handlers for sentiment analysis
        _update_bot_handlers_for_sentiment(bot_instance)

        logger.info("Sentiment analyzer integrated successfully")
        return True
    except Exception as e:
        logger.error(f"Error integrating sentiment analyzer: {e}")
        return False

def _update_bot_handlers_for_sentiment(bot_instance) -> bool:
    """
    Update bot handlers to use sentiment analyzer.

    Args:
        bot_instance: VoicePalBot instance

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Store original handle_voice method
        original_handle_voice = bot_instance.handle_voice

        # Create new handle_voice method
        async def enhanced_handle_voice(update, context):
            """Enhanced voice message handler with sentiment analysis."""
            # Call original handler first to ensure proper execution
            await original_handle_voice(update, context)

        # Replace the method
        bot_instance.handle_voice = enhanced_handle_voice

        # Store original handle_text method
        original_handle_text = bot_instance.handle_text

        # Create new handle_text method
        async def enhanced_handle_text(update, context):
            """Enhanced text message handler with sentiment analysis."""
            # Call original handler first to ensure proper execution
            await original_handle_text(update, context)

        # Replace the method
        bot_instance.handle_text = enhanced_handle_text

        # Store original _handle_personalized_response method
        original_handle_personalized_response = bot_instance._handle_personalized_response

        # Create new _handle_personalized_response method
        async def enhanced_handle_personalized_response(user_id, message, **kwargs):
            """Enhanced personalized response handler with sentiment adjustment."""
            # Get sentiment adjustment from kwargs
            sentiment = kwargs.pop("sentiment", None)
            response_adjustment = kwargs.pop("response_adjustment", None)

            # Add sentiment to user context if available
            if sentiment or response_adjustment:
                user_context = kwargs.get("user_context", {})

                if sentiment:
                    user_context["sentiment"] = sentiment

                if response_adjustment:
                    user_context["response_adjustment"] = response_adjustment

                kwargs["user_context"] = user_context

            # Call original handler
            return await original_handle_personalized_response(user_id, message, **kwargs)

        # Replace the method
        bot_instance._handle_personalized_response = enhanced_handle_personalized_response

        logger.info("Bot handlers updated for sentiment analysis")
        return True
    except Exception as e:
        logger.error(f"Error updating bot handlers for sentiment: {e}")
        return False

def add_sentiment_response_templates(database) -> bool:
    """
    Add sentiment response templates to database.

    Args:
        database: Database instance

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Add positive sentiment templates
        database.add_sentiment_response_template(
            sentiment="positive",
            response_template="I'm glad to hear you're feeling positive! {response}",
            tone="enthusiastic",
            empathy_level="medium"
        )

        database.add_sentiment_response_template(
            sentiment="positive",
            response_template="That sounds wonderful! {response}",
            tone="enthusiastic",
            empathy_level="medium"
        )

        database.add_sentiment_response_template(
            sentiment="positive",
            response_template="I can sense your positive energy! {response}",
            tone="enthusiastic",
            empathy_level="medium"
        )

        # Add negative sentiment templates
        database.add_sentiment_response_template(
            sentiment="negative",
            response_template="I'm sorry to hear that you're feeling down. {response}",
            tone="empathetic",
            empathy_level="high"
        )

        database.add_sentiment_response_template(
            sentiment="negative",
            response_template="I understand this might be difficult for you. {response}",
            tone="supportive",
            empathy_level="high"
        )

        database.add_sentiment_response_template(
            sentiment="negative",
            response_template="It sounds like you're going through a tough time. {response}",
            tone="empathetic",
            empathy_level="high"
        )

        # Add neutral sentiment templates
        database.add_sentiment_response_template(
            sentiment="neutral",
            response_template="{response}",
            tone="neutral",
            empathy_level="medium"
        )

        database.add_sentiment_response_template(
            sentiment="neutral",
            response_template="I see. {response}",
            tone="neutral",
            empathy_level="medium"
        )

        database.add_sentiment_response_template(
            sentiment="neutral",
            response_template="Understood. {response}",
            tone="neutral",
            empathy_level="medium"
        )

        logger.info("Added sentiment response templates to database")
        return True
    except Exception as e:
        logger.error(f"Error adding sentiment response templates: {e}")
        return False

def format_response_with_sentiment(response: str, sentiment_data: Dict[str, Any],
                                 database) -> str:
    """
    Format response with sentiment.

    Args:
        response: Original response
        sentiment_data: Sentiment data
        database: Database instance

    Returns:
        str: Formatted response
    """
    try:
        # Get sentiment
        sentiment = sentiment_data.get("sentiment", {}).get("sentiment", "neutral")

        # Get response adjustment
        response_adjustment = sentiment_data.get("response_adjustment", {})
        tone = response_adjustment.get("tone", "neutral")
        empathy_level = response_adjustment.get("empathy_level", "medium")

        # Get response template
        template = database.get_sentiment_response_template(sentiment, tone, empathy_level)

        # If no template found, use default
        if not template:
            template = "{response}"

        # Format response
        formatted_response = template.format(response=response)

        return formatted_response
    except Exception as e:
        logger.error(f"Error formatting response with sentiment: {e}")
        return response
