"""
Test script for ElevenLabs TTS provider.
"""

import os
import logging
from dotenv import load_dotenv
from bot.elevenlabs_tts_provider import ElevenLabsTTSProvider

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

def test_elevenlabs_tts():
    """Test ElevenLabs TTS provider."""
    # Get API key from environment
    api_key = os.getenv("ELEVENLABS_API_KEY")
    voice_id = os.getenv("ELEVENLABS_VOICE_ID", "Bella")
    model_id = os.getenv("ELEVENLABS_MODEL_ID", "eleven_multilingual_v2")
    
    # Create provider
    provider = ElevenLabsTTSProvider(
        api_key=api_key,
        voice_id=voice_id,
        model_id=model_id,
        use_cache=True
    )
    
    # Test English
    logger.info("Testing English...")
    english_text = "Hello, this is a test of the ElevenLabs TTS provider."
    english_path = provider.generate_speech(
        text=english_text,
        language="en",
        personality="calm"
    )
    logger.info(f"English audio generated: {english_path}")
    
    # Test Italian
    logger.info("Testing Italian...")
    italian_text = "Ciao, questo è un test del provider ElevenLabs TTS."
    italian_path = provider.generate_speech(
        text=italian_text,
        language="it",
        personality="calm"
    )
    logger.info(f"Italian audio generated: {italian_path}")
    
    # Test Spanish
    logger.info("Testing Spanish...")
    spanish_text = "Hola, esta es una prueba del proveedor ElevenLabs TTS."
    spanish_path = provider.generate_speech(
        text=spanish_text,
        language="es",
        personality="calm"
    )
    logger.info(f"Spanish audio generated: {spanish_path}")
    
    # Test French
    logger.info("Testing French...")
    french_text = "Bonjour, ceci est un test du fournisseur ElevenLabs TTS."
    french_path = provider.generate_speech(
        text=french_text,
        language="fr",
        personality="calm"
    )
    logger.info(f"French audio generated: {french_path}")
    
    # Test German
    logger.info("Testing German...")
    german_text = "Hallo, dies ist ein Test des ElevenLabs TTS-Providers."
    german_path = provider.generate_speech(
        text=german_text,
        language="de",
        personality="calm"
    )
    logger.info(f"German audio generated: {german_path}")
    
    logger.info("All tests completed.")

if __name__ == "__main__":
    test_elevenlabs_tts()
