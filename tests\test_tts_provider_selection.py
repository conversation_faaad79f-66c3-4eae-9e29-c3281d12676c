"""
Test script for TTS provider selection.

This script tests the TTS provider selection functionality, including:
1. TTS provider initialization
2. Provider switching
3. Voice selection
4. Fallback mechanisms
"""

import unittest
import os
import sys
import logging
import asyncio
import tempfile
from unittest.mock import MagicMock, patch, AsyncMock
from datetime import datetime

# Add parent directory to path to import bot modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from telegram import Update, InlineKeyboardMarkup, InlineKeyboardButton, User, Message, Chat, CallbackQuery
from telegram.ext import ContextTypes

from bot.database import Database
from bot.providers.voice.processor import VoiceProcessor
from bot.providers.tts.deepgram_provider import DeepgramTTSProvider
from bot.providers.tts.elevenlabs_provider import ElevenLabsTTSProvider
from bot.providers.tts.google_provider import GoogleTTSProvider
from bot.handlers.voice_settings_handlers import handle_voice_settings_callback

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class TestTTSProviderSelection(unittest.TestCase):
    """Test case for TTS provider selection."""

    def setUp(self):
        """Set up test environment."""
        # Create in-memory database
        self.database = Database(":memory:")
        
        # Create mock config manager
        self.config_manager = MagicMock()
        self.config_manager.get_provider_config.return_value = {
            "api_key": "test_api_key",
            "voice_id": "test_voice",
            "model_id": "test_model"
        }
        
        # Create mock key manager
        self.key_manager = MagicMock()
        self.key_manager.get_key.return_value = "test_api_key"
        
        # Create voice processor
        self.voice_processor = VoiceProcessor(
            deepgram_api_key="test_api_key",
            default_language="en",
            tts_provider="google",
            tts_provider_options={}
        )
        
        # Add test user
        self.test_user_id = 123456789
        self.database.add_user(
            user_id=self.test_user_id,
            username="test_user",
            first_name="Test",
            last_name="User"
        )
        
        # Create mock update and context
        self.mock_user = MagicMock(spec=User)
        self.mock_user.id = self.test_user_id
        self.mock_user.username = "test_user"
        self.mock_user.first_name = "Test"
        self.mock_user.last_name = "User"
        
        self.mock_chat = MagicMock(spec=Chat)
        self.mock_chat.id = self.test_user_id
        
        self.mock_message = MagicMock(spec=Message)
        self.mock_message.chat = self.mock_chat
        self.mock_message.from_user = self.mock_user
        self.mock_message.reply_text = AsyncMock()
        
        self.mock_callback_query = MagicMock(spec=CallbackQuery)
        self.mock_callback_query.from_user = self.mock_user
        self.mock_callback_query.message = self.mock_message
        self.mock_callback_query.data = "set_tts_provider_google"
        self.mock_callback_query.answer = AsyncMock()
        
        self.mock_update = MagicMock(spec=Update)
        self.mock_update.effective_user = self.mock_user
        self.mock_update.effective_chat = self.mock_chat
        self.mock_update.message = self.mock_message
        self.mock_update.callback_query = self.mock_callback_query
        
        self.mock_context = MagicMock(spec=ContextTypes.DEFAULT_TYPE)
        self.mock_context.bot = MagicMock()
        self.mock_context.bot.send_message = AsyncMock()
        
        # Create mock bot instance
        self.mock_bot_instance = MagicMock()
        self.mock_bot_instance.database = self.database
        self.mock_bot_instance.config_manager = self.config_manager
        self.mock_bot_instance.key_manager = self.key_manager
        self.mock_bot_instance.voice_processor = self.voice_processor
    
    def test_voice_processor_initialization(self):
        """Test voice processor initialization."""
        # Check that voice processor is initialized
        self.assertIsNotNone(self.voice_processor)
        
        # Check that voice processor has the correct attributes
        self.assertEqual(self.voice_processor.deepgram_api_key, "test_api_key")
        self.assertEqual(self.voice_processor.default_language, "en")
        self.assertEqual(self.voice_processor.tts_provider_type, "google")
    
    def test_set_tts_provider(self):
        """Test set_tts_provider method."""
        # Set TTS provider to Deepgram
        success = self.voice_processor.set_tts_provider(
            provider_type="deepgram",
            api_key="test_api_key",
            voice_id="aura-2-thalia-en",
            model_id="aura-2"
        )
        
        # Check that provider was set successfully
        self.assertTrue(success)
        self.assertEqual(self.voice_processor.tts_provider_type, "deepgram")
        self.assertIsInstance(self.voice_processor.tts_provider, DeepgramTTSProvider)
        
        # Set TTS provider to ElevenLabs
        success = self.voice_processor.set_tts_provider(
            provider_type="elevenlabs",
            api_key="test_api_key",
            voice_id="Bella",
            model_id="eleven_multilingual_v2"
        )
        
        # Check that provider was set successfully
        self.assertTrue(success)
        self.assertEqual(self.voice_processor.tts_provider_type, "elevenlabs")
        self.assertIsInstance(self.voice_processor.tts_provider, ElevenLabsTTSProvider)
        
        # Set TTS provider to Google
        success = self.voice_processor.set_tts_provider(
            provider_type="google"
        )
        
        # Check that provider was set successfully
        self.assertTrue(success)
        self.assertEqual(self.voice_processor.tts_provider_type, "google")
        self.assertIsInstance(self.voice_processor.tts_provider, GoogleTTSProvider)
    
    def test_fallback_to_google_tts(self):
        """Test fallback to Google TTS."""
        # Create ElevenLabs provider with mock methods
        provider = ElevenLabsTTSProvider(api_key="test_api_key")
        
        # Mock generate method to raise an exception
        provider.generate = MagicMock(side_effect=Exception("Test error"))
        
        # Create a mock for the _fallback_to_google_tts method
        provider._fallback_to_google_tts = MagicMock(return_value="test_output.mp3")
        
        # Generate speech (should fall back to Google TTS)
        output_path = provider.generate_speech("Test text")
        
        # Check that fallback was called
        provider._fallback_to_google_tts.assert_called_once()
        
        # Check that output path is returned
        self.assertEqual(output_path, "test_output.mp3")
    
    @patch('bot.handlers.voice_settings_handlers.handle_voice_settings_callback')
    async def test_handle_voice_settings_callback(self, mock_handler):
        """Test handle_voice_settings_callback function."""
        # Set up mock handler
        mock_handler.return_value = ("TTS provider set to Google!", InlineKeyboardMarkup([[InlineKeyboardButton("Back", callback_data="show_voice_settings")]]))
        
        # Call handler
        result = await handle_voice_settings_callback(self.mock_update, self.mock_context)
        
        # Check that handler was called
        mock_handler.assert_called_once()
        
        # Check that result is returned
        self.assertIsNotNone(result)
        self.assertEqual(result[0], "TTS provider set to Google!")
        self.assertIsInstance(result[1], InlineKeyboardMarkup)
    
    @patch('bot.providers.tts.elevenlabs_provider.ElevenLabsTTSProvider.generate_speech')
    def test_elevenlabs_provider_with_fallback(self, mock_generate_speech):
        """Test ElevenLabs provider with fallback."""
        # Set up mock to simulate failure
        mock_generate_speech.side_effect = Exception("Test error")
        
        # Create provider
        provider = ElevenLabsTTSProvider(api_key="test_api_key")
        
        # Create a real fallback method
        def fallback_to_google_tts(text, language=None):
            # Create a temporary file
            with tempfile.NamedTemporaryFile(suffix=".mp3", delete=False) as temp_file:
                temp_path = temp_file.name
                # Write some dummy data
                temp_file.write(b"dummy audio data")
                return temp_path
        
        # Replace the fallback method
        provider._fallback_to_google_tts = fallback_to_google_tts
        
        # Generate speech (should fall back to Google TTS)
        output_path = provider.generate_speech("Test text")
        
        # Check that output path is returned
        self.assertIsNotNone(output_path)
        self.assertTrue(os.path.exists(output_path))
        
        # Clean up
        if os.path.exists(output_path):
            os.remove(output_path)

def run_async_test(test_func):
    """Run an async test function."""
    loop = asyncio.get_event_loop()
    loop.run_until_complete(test_func())

if __name__ == "__main__":
    # Create test suite
    suite = unittest.TestSuite()
    
    # Add test cases
    test_case = TestTTSProviderSelection()
    suite.addTest(test_case)
    
    # Add async tests
    for method_name in dir(test_case):
        if method_name.startswith("test_") and asyncio.iscoroutinefunction(getattr(test_case, method_name)):
            run_async_test(getattr(test_case, method_name))
    
    # Run tests
    unittest.TextTestRunner(verbosity=2).run(suite)
