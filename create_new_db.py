#!/usr/bin/env python3
"""
Create a new database for VoicePal.
"""

import os
import sys
import shutil
import datetime

def create_new_db() -> None:
    """
    Create a new database by running the bot with a new database file.
    """
    # Backup existing database
    if os.path.exists("voicepal.db"):
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = f"voicepal.db.backup_{timestamp}"
        shutil.copy2("voicepal.db", backup_path)
        print(f"Database backed up to: {backup_path}")

        # Remove old database if it exists
        if os.path.exists("voicepal.db.old"):
            os.remove("voicepal.db.old")
            print("Removed existing voicepal.db.old")

        # Rename existing database
        os.rename("voicepal.db", "voicepal.db.old")
        print("Existing database renamed to voicepal.db.old")

    # Create a simple script to initialize the database
    with open("init_db.py", "w") as f:
        f.write("""
import sys
import os

# Add project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from bot.database.schema import initialize_database, run_migrations

# Initialize database
conn = initialize_database("voicepal.db")

# Run migrations
run_migrations(conn)

# Close connection
conn.close()

print("Database initialized successfully")
""")

    # Run the script
    os.system("python init_db.py")

    # Copy data from old database
    if os.path.exists("voicepal.db.old"):
        print("\nCopying data from old database...")

        # Create a script to copy data
        with open("copy_data.py", "w") as f:
            f.write("""
import sqlite3
import sys
import os

# Connect to old database
conn_old = sqlite3.connect("voicepal.db.old")
conn_old.row_factory = sqlite3.Row
cursor_old = conn_old.cursor()

# Connect to new database
conn_new = sqlite3.connect("voicepal.db")
conn_new.row_factory = sqlite3.Row
cursor_new = conn_new.cursor()

# Get list of tables
cursor_old.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
tables = cursor_old.fetchall()

# Copy data for each table
for table in tables:
    table_name = table['name']

    # Check if table exists in new database
    cursor_new.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'")
    if not cursor_new.fetchone():
        print(f"Table {table_name} does not exist in new database, skipping")
        continue

    # Get column names for old table
    cursor_old.execute(f"PRAGMA table_info({table_name})")
    old_columns = cursor_old.fetchall()
    old_column_names = [col['name'] for col in old_columns]

    # Get column names for new table
    cursor_new.execute(f"PRAGMA table_info({table_name})")
    new_columns = cursor_new.fetchall()
    new_column_names = [col['name'] for col in new_columns]

    # Find common columns
    common_columns = [col for col in old_column_names if col in new_column_names]

    if not common_columns:
        print(f"No common columns found for table {table_name}, skipping")
        continue

    # Get data from old table
    cursor_old.execute(f"SELECT * FROM {table_name}")
    rows = cursor_old.fetchall()

    if not rows:
        print(f"No data found in table {table_name}, skipping")
        continue

    # Insert data into new table
    columns_str = ", ".join(common_columns)
    placeholders = ", ".join(["?" for _ in common_columns])

    for row in rows:
        values = [row[col] for col in common_columns]
        try:
            cursor_new.execute(f"INSERT INTO {table_name} ({columns_str}) VALUES ({placeholders})", values)
        except sqlite3.IntegrityError:
            print(f"Integrity error inserting into {table_name}, skipping row")

    conn_new.commit()
    print(f"Copied {len(rows)} rows to table {table_name}")

# Close connections
conn_old.close()
conn_new.close()

print("Data copy completed")
""")

        # Run the script
        os.system("python copy_data.py")

    # Clean up
    if os.path.exists("init_db.py"):
        os.remove("init_db.py")

    if os.path.exists("copy_data.py"):
        os.remove("copy_data.py")

    print("\nNew database created successfully")

def main():
    """Main entry point."""
    create_new_db()

if __name__ == "__main__":
    main()
