#!/usr/bin/env python3
"""
Comprehensive test runner for VoicePal.

This script runs all tests with proper categorization and reporting.
"""

import os
import sys
import subprocess
import time
import argparse
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class TestRunner:
    """Comprehensive test runner for VoicePal."""
    
    def __init__(self):
        self.project_root = project_root
        self.test_results = {}
        
    def run_command(self, command, description):
        """Run a command and capture results."""
        print(f"\n{'='*60}")
        print(f"Running: {description}")
        print(f"Command: {' '.join(command)}")
        print(f"{'='*60}")
        
        start_time = time.time()
        
        try:
            result = subprocess.run(
                command,
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=300  # 5 minutes timeout
            )
            
            end_time = time.time()
            duration = end_time - start_time
            
            success = result.returncode == 0
            
            self.test_results[description] = {
                'success': success,
                'duration': duration,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'returncode': result.returncode
            }
            
            if success:
                print(f"✅ {description} - PASSED ({duration:.2f}s)")
            else:
                print(f"❌ {description} - FAILED ({duration:.2f}s)")
                print(f"Error output:\n{result.stderr}")
            
            # Always show some output for context
            if result.stdout:
                lines = result.stdout.split('\n')
                if len(lines) > 20:
                    print("Output (last 20 lines):")
                    print('\n'.join(lines[-20:]))
                else:
                    print("Output:")
                    print(result.stdout)
            
            return success
            
        except subprocess.TimeoutExpired:
            print(f"❌ {description} - TIMEOUT (>300s)")
            self.test_results[description] = {
                'success': False,
                'duration': 300,
                'stdout': '',
                'stderr': 'Test timed out',
                'returncode': -1
            }
            return False
        except Exception as e:
            print(f"❌ {description} - ERROR: {e}")
            self.test_results[description] = {
                'success': False,
                'duration': 0,
                'stdout': '',
                'stderr': str(e),
                'returncode': -1
            }
            return False
    
    def run_unit_tests(self):
        """Run unit tests."""
        return self.run_command(
            ['python', '-m', 'pytest', 'tests/unit/', '-v', '--tb=short'],
            "Unit Tests"
        )
    
    def run_integration_tests(self):
        """Run integration tests."""
        return self.run_command(
            ['python', '-m', 'pytest', 'tests/integration/', '-v', '--tb=short'],
            "Integration Tests"
        )
    
    def run_performance_tests(self):
        """Run performance tests."""
        return self.run_command(
            ['python', '-m', 'pytest', 'tests/performance/', '-v', '--tb=short', '-m', 'not slow'],
            "Performance Tests (Fast)"
        )
    
    def run_slow_performance_tests(self):
        """Run slow performance tests."""
        return self.run_command(
            ['python', '-m', 'pytest', 'tests/performance/', '-v', '--tb=short', '-m', 'slow'],
            "Performance Tests (Slow)"
        )
    
    def run_coverage_tests(self):
        """Run tests with coverage."""
        return self.run_command(
            ['python', '-m', 'pytest', '--cov=bot', '--cov-report=html', '--cov-report=term-missing'],
            "Coverage Analysis"
        )
    
    def run_linting(self):
        """Run code linting."""
        success = True
        
        # Run flake8
        if not self.run_command(
            ['python', '-m', 'flake8', 'bot/', '--max-line-length=120', '--ignore=E203,W503'],
            "Flake8 Linting"
        ):
            success = False
        
        # Run black check
        if not self.run_command(
            ['python', '-m', 'black', '--check', 'bot/'],
            "Black Code Formatting Check"
        ):
            success = False
        
        # Run isort check
        if not self.run_command(
            ['python', '-m', 'isort', '--check-only', 'bot/'],
            "Import Sorting Check"
        ):
            success = False
        
        return success
    
    def run_type_checking(self):
        """Run type checking."""
        return self.run_command(
            ['python', '-m', 'mypy', 'bot/', '--ignore-missing-imports'],
            "Type Checking (MyPy)"
        )
    
    def run_security_checks(self):
        """Run security checks."""
        success = True
        
        # Run bandit security check
        if not self.run_command(
            ['python', '-m', 'bandit', '-r', 'bot/', '-f', 'json'],
            "Security Check (Bandit)"
        ):
            success = False
        
        # Run safety check for dependencies
        if not self.run_command(
            ['python', '-m', 'safety', 'check'],
            "Dependency Security Check (Safety)"
        ):
            success = False
        
        return success
    
    def check_dependencies(self):
        """Check if all required dependencies are installed."""
        required_packages = [
            'pytest',
            'pytest-cov',
            'pytest-asyncio',
            'flake8',
            'black',
            'isort',
            'mypy',
            'bandit',
            'safety'
        ]
        
        missing_packages = []
        
        for package in required_packages:
            try:
                __import__(package.replace('-', '_'))
            except ImportError:
                missing_packages.append(package)
        
        if missing_packages:
            print(f"❌ Missing required packages: {', '.join(missing_packages)}")
            print("Install them with: pip install " + ' '.join(missing_packages))
            return False
        
        print("✅ All required packages are installed")
        return True
    
    def generate_report(self):
        """Generate test report."""
        print(f"\n{'='*80}")
        print("TEST EXECUTION SUMMARY")
        print(f"{'='*80}")
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result['success'])
        failed_tests = total_tests - passed_tests
        total_duration = sum(result['duration'] for result in self.test_results.values())
        
        print(f"Total test suites: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Total duration: {total_duration:.2f}s")
        print(f"Success rate: {(passed_tests/total_tests)*100:.1f}%")
        
        print(f"\n{'='*80}")
        print("DETAILED RESULTS")
        print(f"{'='*80}")
        
        for test_name, result in self.test_results.items():
            status = "✅ PASS" if result['success'] else "❌ FAIL"
            print(f"{status} {test_name} ({result['duration']:.2f}s)")
            
            if not result['success'] and result['stderr']:
                print(f"    Error: {result['stderr'][:200]}...")
        
        # Generate HTML report if coverage was run
        coverage_html_path = self.project_root / "htmlcov" / "index.html"
        if coverage_html_path.exists():
            print(f"\n📊 Coverage report available at: {coverage_html_path}")
        
        return passed_tests == total_tests
    
    def run_all_tests(self, include_slow=False, include_linting=True, include_security=True):
        """Run all tests."""
        print("🚀 Starting comprehensive test suite for VoicePal")
        
        # Check dependencies first
        if not self.check_dependencies():
            return False
        
        all_passed = True
        
        # Core tests
        if not self.run_unit_tests():
            all_passed = False
        
        if not self.run_integration_tests():
            all_passed = False
        
        if not self.run_performance_tests():
            all_passed = False
        
        if include_slow:
            if not self.run_slow_performance_tests():
                all_passed = False
        
        # Code quality checks
        if include_linting:
            if not self.run_linting():
                all_passed = False
            
            if not self.run_type_checking():
                all_passed = False
        
        # Security checks
        if include_security:
            if not self.run_security_checks():
                all_passed = False
        
        # Coverage analysis (run last as it's comprehensive)
        if not self.run_coverage_tests():
            all_passed = False
        
        # Generate final report
        final_success = self.generate_report()
        
        return final_success and all_passed


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Run comprehensive tests for VoicePal")
    parser.add_argument('--unit', action='store_true', help='Run only unit tests')
    parser.add_argument('--integration', action='store_true', help='Run only integration tests')
    parser.add_argument('--performance', action='store_true', help='Run only performance tests')
    parser.add_argument('--coverage', action='store_true', help='Run only coverage analysis')
    parser.add_argument('--lint', action='store_true', help='Run only linting checks')
    parser.add_argument('--security', action='store_true', help='Run only security checks')
    parser.add_argument('--slow', action='store_true', help='Include slow tests')
    parser.add_argument('--no-lint', action='store_true', help='Skip linting checks')
    parser.add_argument('--no-security', action='store_true', help='Skip security checks')
    
    args = parser.parse_args()
    
    runner = TestRunner()
    
    # Run specific test categories if requested
    if args.unit:
        success = runner.run_unit_tests()
    elif args.integration:
        success = runner.run_integration_tests()
    elif args.performance:
        success = runner.run_performance_tests()
        if args.slow:
            success = success and runner.run_slow_performance_tests()
    elif args.coverage:
        success = runner.run_coverage_tests()
    elif args.lint:
        success = runner.run_linting()
    elif args.security:
        success = runner.run_security_checks()
    else:
        # Run all tests
        success = runner.run_all_tests(
            include_slow=args.slow,
            include_linting=not args.no_lint,
            include_security=not args.no_security
        )
    
    if success:
        print("\n🎉 All tests passed!")
        sys.exit(0)
    else:
        print("\n💥 Some tests failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
