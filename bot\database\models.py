"""
Database models for VoicePal.

This module provides the database models for VoicePal.
"""

# Import all models from the models package
from bot.database.models.model import Model
from bot.database.models.user import User, UserPreference, UserStat
from bot.database.models.conversation import Conversation, Message, MessageMetadata
from bot.database.models.payment import Transaction, PaymentPackage, Subscription
from bot.database.models.memory import Memory, MemoryTag
from bot.database.models.voice import VoiceSetting, VoiceRecording

__all__ = [
    'Model',
    'User',
    'UserPreference',
    'UserStat',
    'Conversation',
    'Message',
    'MessageMetadata',
    'Transaction',
    'PaymentPackage',
    'Subscription',
    'Memory',
    'MemoryTag',
    'VoiceSetting',
    'VoiceRecording'
]
