"""
Authentication middleware for VoicePal API.

This module provides authentication middleware for the VoicePal API.
"""

import logging
from typing import Optional
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.requests import Request
from starlette.responses import Response, JSONResponse
from starlette.status import HTTP_401_UNAUTHORIZED, HTTP_403_FORBIDDEN

from api.config import APIConfig

logger = logging.getLogger(__name__)

class AuthMiddleware(BaseHTTPMiddleware):
    """Authentication middleware for API endpoints."""
    
    def __init__(self, app, config: APIConfig):
        """
        Initialize authentication middleware.
        
        Args:
            app: ASGI application
            config: API configuration
        """
        super().__init__(app)
        self.config = config
        
        # Public endpoints that don't require authentication
        self.public_endpoints = {
            "/health",
            "/health/",
            "/health/status",
            "/health/ready",
            "/docs",
            "/redoc",
            "/openapi.json"
        }
        
        # Admin endpoints that require admin authentication
        self.admin_endpoints = {
            "/admin",
            "/admin/",
            "/admin/users",
            "/admin/stats",
            "/admin/config",
            "/admin/logs"
        }
    
    async def dispatch(self, request: Request, call_next) -> Response:
        """
        Process the request and apply authentication.
        
        Args:
            request: HTTP request
            call_next: Next middleware/handler
            
        Returns:
            HTTP response
        """
        path = request.url.path
        
        # Skip authentication for public endpoints
        if self._is_public_endpoint(path):
            return await call_next(request)
        
        # Check authentication
        auth_result = await self._authenticate_request(request)
        
        if not auth_result["authenticated"]:
            return JSONResponse(
                status_code=HTTP_401_UNAUTHORIZED,
                content={
                    "error": "Authentication required",
                    "message": auth_result.get("message", "Invalid or missing API key")
                }
            )
        
        # Check admin access for admin endpoints
        if self._is_admin_endpoint(path):
            if not auth_result.get("is_admin", False):
                return JSONResponse(
                    status_code=HTTP_403_FORBIDDEN,
                    content={
                        "error": "Admin access required",
                        "message": "This endpoint requires admin privileges"
                    }
                )
        
        # Add user info to request state
        request.state.user_id = auth_result.get("user_id")
        request.state.is_admin = auth_result.get("is_admin", False)
        request.state.api_key_type = auth_result.get("api_key_type")
        
        return await call_next(request)
    
    def _is_public_endpoint(self, path: str) -> bool:
        """
        Check if an endpoint is public.
        
        Args:
            path: Request path
            
        Returns:
            True if endpoint is public, False otherwise
        """
        # Exact match
        if path in self.public_endpoints:
            return True
        
        # Prefix match for health endpoints
        if path.startswith("/health"):
            return True
        
        # Documentation endpoints
        if path in ["/docs", "/redoc", "/openapi.json"]:
            return True
        
        return False
    
    def _is_admin_endpoint(self, path: str) -> bool:
        """
        Check if an endpoint requires admin access.
        
        Args:
            path: Request path
            
        Returns:
            True if endpoint requires admin access, False otherwise
        """
        return path.startswith("/admin")
    
    async def _authenticate_request(self, request: Request) -> dict:
        """
        Authenticate the request.
        
        Args:
            request: HTTP request
            
        Returns:
            Authentication result dictionary
        """
        # Get API key from header or query parameter
        api_key = self._extract_api_key(request)
        
        if not api_key:
            return {
                "authenticated": False,
                "message": "API key required"
            }
        
        # Check admin API key
        if self.config.admin_api_key and api_key == self.config.admin_api_key:
            return {
                "authenticated": True,
                "is_admin": True,
                "api_key_type": "admin",
                "user_id": None
            }
        
        # Check regular API key
        if self.config.api_key and api_key == self.config.api_key:
            return {
                "authenticated": True,
                "is_admin": False,
                "api_key_type": "regular",
                "user_id": None
            }
        
        return {
            "authenticated": False,
            "message": "Invalid API key"
        }
    
    def _extract_api_key(self, request: Request) -> Optional[str]:
        """
        Extract API key from request.
        
        Args:
            request: HTTP request
            
        Returns:
            API key if found, None otherwise
        """
        # Check Authorization header
        auth_header = request.headers.get("Authorization")
        if auth_header:
            if auth_header.startswith("Bearer "):
                return auth_header[7:]  # Remove "Bearer " prefix
            elif auth_header.startswith("ApiKey "):
                return auth_header[7:]  # Remove "ApiKey " prefix
        
        # Check X-API-Key header
        api_key_header = request.headers.get("X-API-Key")
        if api_key_header:
            return api_key_header
        
        # Check query parameter
        api_key_param = request.query_params.get("api_key")
        if api_key_param:
            return api_key_param
        
        return None
