# VoicePal Utility Scripts

This directory contains utility scripts for managing and maintaining the VoicePal bot.

## Script Files

- `add_admin.py`: Adds an admin user to the database
- `add_credits.py`: Adds credits to a user's account
- `check_providers.py`: Checks the status of various service providers
- `cleanup.py`: Cleans up temporary files and directories
- `fix_providers.py`: Fixes issues with service providers
- `install_dependencies.py`: Installs required dependencies
- `load_env.py`: Loads environment variables from .env file
- `update_imports.py`: Updates import statements in the codebase
- `verify_implementation.py`: Verifies the implementation of various components

## Running Scripts

To run a script:

```bash
python -m scripts.script_name [arguments]
```

For example:

```bash
python -m scripts.add_credits --user_id ********* --credits 100
```

## Adding New Scripts

When adding new scripts, please follow these guidelines:

1. Create a new file named `descriptive_name.py`
2. Include a docstring at the top explaining the purpose of the script
3. Add command-line argument parsing using `argparse`
4. Add the script to this README.md file
