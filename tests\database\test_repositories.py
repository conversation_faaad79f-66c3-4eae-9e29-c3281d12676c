"""
Tests for database repositories.

This module tests the database repository classes.
"""

import unittest
import tempfile
from pathlib import Path
from datetime import datetime, timedelta

from bot.database.database_manager import DatabaseManager
from bot.database.core.exceptions import (
    DatabaseError,
    DatabaseNotFoundError,
    DatabaseDuplicateError
)
from bot.database.models import (
    User,
    UserPreference,
    UserStat,
    Conversation,
    Message,
    MessageMetadata,
    Transaction,
    PaymentPackage,
    Subscription,
    Memory,
    MemoryTag,
    VoiceSetting,
    VoiceRecording
)

class TestDatabaseRepositories(unittest.TestCase):
    """Test case for database repositories."""
    
    def setUp(self):
        """Set up test environment."""
        # Create temporary directory for database
        self.temp_dir = tempfile.TemporaryDirectory()
        self.db_path = Path(self.temp_dir.name) / "test.db"
        
        # Initialize database
        self.db = DatabaseManager(self.db_path)
        
        # Create test user
        self.user_id = "test_user"
        self.user = User(
            user_id=self.user_id,
            username="test_username",
            first_name="Test",
            last_name="User"
        )
        self.db.users.create(self.user)
    
    def tearDown(self):
        """Clean up test environment."""
        # Close database connection
        self.db.close()
        
        # Remove temporary directory
        self.temp_dir.cleanup()
    
    def test_user_repository(self):
        """Test UserRepository."""
        # Find user by ID
        user = self.db.users.find_by_id(self.user_id)
        self.assertIsNotNone(user)
        self.assertEqual(user.user_id, self.user_id)
        
        # Find user by username
        user = self.db.users.find_by_username("test_username")
        self.assertIsNotNone(user)
        self.assertEqual(user.user_id, self.user_id)
        
        # Find active users
        users = self.db.users.find_active_users()
        self.assertEqual(len(users), 1)
        self.assertEqual(users[0].user_id, self.user_id)
        
        # Find users with credits
        users = self.db.users.find_users_with_credits(min_credits=50)
        self.assertEqual(len(users), 1)
        self.assertEqual(users[0].user_id, self.user_id)
        
        # Update last interaction
        self.db.users.update_last_interaction(self.user_id)
        user = self.db.users.find_by_id(self.user_id)
        self.assertIsNotNone(user.last_interaction)
        
        # Add credits
        initial_credits = user.credits
        added_credits = 50
        self.db.users.add_credits(self.user_id, added_credits)
        user = self.db.users.find_by_id(self.user_id)
        self.assertEqual(user.credits, initial_credits + added_credits)
        
        # Use credits
        used_credits = 20
        self.db.users.use_credits(self.user_id, used_credits)
        user = self.db.users.find_by_id(self.user_id)
        self.assertEqual(user.credits, initial_credits + added_credits - used_credits)
        
        # Update user
        new_username = "new_username"
        user.username = new_username
        self.db.users.update(user)
        
        user = self.db.users.find_by_id(self.user_id)
        self.assertEqual(user.username, new_username)
        
        # Delete user
        self.db.users.delete(self.user_id)
        user = self.db.users.find_by_id(self.user_id)
        self.assertIsNone(user)
    
    def test_user_preference_repository(self):
        """Test UserPreferenceRepository."""
        # Set preference
        theme_key = "theme"
        theme_value = "dark"
        
        preference = self.db.user_preferences.set_preference(self.user_id, theme_key, theme_value)
        self.assertIsNotNone(preference)
        self.assertEqual(preference.user_id, self.user_id)
        self.assertEqual(preference.key, theme_key)
        self.assertEqual(preference.value, theme_value)
        
        # Get preference
        value = self.db.user_preferences.get_preference(self.user_id, theme_key)
        self.assertEqual(value, theme_value)
        
        # Find by user and key
        preference = self.db.user_preferences.find_by_user_and_key(self.user_id, theme_key)
        self.assertIsNotNone(preference)
        self.assertEqual(preference.value, theme_value)
        
        # Find by user
        preferences = self.db.user_preferences.find_by_user(self.user_id)
        self.assertEqual(len(preferences), 1)
        self.assertEqual(preferences[0].key, theme_key)
        self.assertEqual(preferences[0].value, theme_value)
        
        # Update preference
        new_value = "light"
        preference = self.db.user_preferences.set_preference(self.user_id, theme_key, new_value)
        self.assertEqual(preference.value, new_value)
        
        # Delete preference
        self.db.user_preferences.delete_preference(self.user_id, theme_key)
        value = self.db.user_preferences.get_preference(self.user_id, theme_key)
        self.assertIsNone(value)
    
    def test_user_stat_repository(self):
        """Test UserStatRepository."""
        # Increment stat
        messages_key = "messages_sent"
        messages_value = 5
        
        stat = self.db.user_stats.increment_stat(self.user_id, messages_key, messages_value)
        self.assertIsNotNone(stat)
        self.assertEqual(stat.user_id, self.user_id)
        self.assertEqual(stat.key, messages_key)
        self.assertEqual(stat.value, messages_value)
        
        # Get stat
        value = self.db.user_stats.get_stat(self.user_id, messages_key)
        self.assertEqual(value, messages_value)
        
        # Find by user and key
        stat = self.db.user_stats.find_by_user_and_key(self.user_id, messages_key)
        self.assertIsNotNone(stat)
        self.assertEqual(stat.value, messages_value)
        
        # Find by user
        stats = self.db.user_stats.find_by_user(self.user_id)
        self.assertEqual(len(stats), 1)
        self.assertEqual(stats[0].key, messages_key)
        self.assertEqual(stats[0].value, messages_value)
        
        # Increment again
        self.db.user_stats.increment_stat(self.user_id, messages_key, 2)
        value = self.db.user_stats.get_stat(self.user_id, messages_key)
        self.assertEqual(value, messages_value + 2)
        
        # Reset stat
        self.db.user_stats.reset_stat(self.user_id, messages_key)
        value = self.db.user_stats.get_stat(self.user_id, messages_key)
        self.assertEqual(value, 0)
    
    def test_conversation_repository(self):
        """Test ConversationRepository."""
        # Create conversation
        conversation, created = self.db.conversations.get_or_create_active_conversation(self.user_id)
        self.assertIsNotNone(conversation)
        self.assertEqual(conversation.user_id, self.user_id)
        self.assertTrue(created)
        
        # Find by user
        conversations = self.db.conversations.find_by_user(self.user_id)
        self.assertEqual(len(conversations), 1)
        self.assertEqual(conversations[0].conversation_id, conversation.conversation_id)
        
        # Find active by user
        active_conversations = self.db.conversations.find_active_by_user(self.user_id)
        self.assertEqual(len(active_conversations), 1)
        self.assertEqual(active_conversations[0].conversation_id, conversation.conversation_id)
        
        # Update title
        new_title = "Test Conversation"
        self.db.conversations.update_title(conversation.conversation_id, new_title)
        
        conversation = self.db.conversations.find_by_id(conversation.conversation_id)
        self.assertEqual(conversation.title, new_title)
        
        # Archive conversation
        self.db.conversations.archive_conversation(conversation.conversation_id)
        
        conversation = self.db.conversations.find_by_id(conversation.conversation_id)
        self.assertFalse(conversation.is_active)
        
        # Activate conversation
        self.db.conversations.activate_conversation(conversation.conversation_id)
        
        conversation = self.db.conversations.find_by_id(conversation.conversation_id)
        self.assertTrue(conversation.is_active)
    
    def test_message_repository(self):
        """Test MessageRepository."""
        # Create conversation
        conversation, _ = self.db.conversations.get_or_create_active_conversation(self.user_id)
        
        # Add messages
        user_message = "Hello, bot!"
        bot_message = "Hello, human!"
        
        user_msg = self.db.messages.add_message(
            conversation.conversation_id,
            self.user_id,
            user_message,
            Message.ROLE_USER
        )
        
        bot_msg = self.db.messages.add_message(
            conversation.conversation_id,
            self.user_id,
            bot_message,
            Message.ROLE_ASSISTANT
        )
        
        # Find by conversation
        messages = self.db.messages.find_by_conversation(conversation.conversation_id)
        self.assertEqual(len(messages), 2)
        self.assertEqual(messages[0].content, user_message)
        self.assertEqual(messages[1].content, bot_message)
        
        # Find by user
        messages = self.db.messages.find_by_user(self.user_id)
        self.assertEqual(len(messages), 2)
        
        # Find by role
        user_messages = self.db.messages.find_by_role(conversation.conversation_id, Message.ROLE_USER)
        self.assertEqual(len(user_messages), 1)
        self.assertEqual(user_messages[0].content, user_message)
        
        bot_messages = self.db.messages.find_by_role(conversation.conversation_id, Message.ROLE_ASSISTANT)
        self.assertEqual(len(bot_messages), 1)
        self.assertEqual(bot_messages[0].content, bot_message)
        
        # Get conversation history
        history = self.db.messages.get_conversation_history(conversation.conversation_id)
        self.assertEqual(len(history), 2)
        self.assertEqual(history[0]["role"], Message.ROLE_USER)
        self.assertEqual(history[0]["content"], user_message)
        self.assertEqual(history[1]["role"], Message.ROLE_ASSISTANT)
        self.assertEqual(history[1]["content"], bot_message)
    
    def test_message_metadata_repository(self):
        """Test MessageMetadataRepository."""
        # Create conversation and message
        conversation, _ = self.db.conversations.get_or_create_active_conversation(self.user_id)
        
        message = self.db.messages.add_message(
            conversation.conversation_id,
            self.user_id,
            "Hello, bot!",
            Message.ROLE_USER
        )
        
        # Set metadata
        sentiment_key = "sentiment"
        sentiment_value = "positive"
        
        metadata = self.db.message_metadata.set_metadata(message.message_id, sentiment_key, sentiment_value)
        self.assertIsNotNone(metadata)
        self.assertEqual(metadata.message_id, message.message_id)
        self.assertEqual(metadata.key, sentiment_key)
        self.assertEqual(metadata.value, sentiment_value)
        
        # Get metadata
        value = self.db.message_metadata.get_metadata(message.message_id, sentiment_key)
        self.assertEqual(value, sentiment_value)
        
        # Find by message
        metadata_list = self.db.message_metadata.find_by_message(message.message_id)
        self.assertEqual(len(metadata_list), 1)
        self.assertEqual(metadata_list[0].key, sentiment_key)
        self.assertEqual(metadata_list[0].value, sentiment_value)
        
        # Find by message and key
        metadata = self.db.message_metadata.find_by_message_and_key(message.message_id, sentiment_key)
        self.assertIsNotNone(metadata)
        self.assertEqual(metadata.value, sentiment_value)
        
        # Update metadata
        new_value = "negative"
        metadata = self.db.message_metadata.set_metadata(message.message_id, sentiment_key, new_value)
        self.assertEqual(metadata.value, new_value)
        
        # Delete metadata
        self.db.message_metadata.delete_metadata(message.message_id, sentiment_key)
        value = self.db.message_metadata.get_metadata(message.message_id, sentiment_key)
        self.assertIsNone(value)
    
    def test_transaction_repository(self):
        """Test TransactionRepository."""
        # Create purchase transaction
        amount = 100
        provider = "stripe"
        
        transaction = self.db.transactions.create_purchase_transaction(
            self.user_id,
            amount,
            provider
        )
        
        self.assertIsNotNone(transaction)
        self.assertEqual(transaction.user_id, self.user_id)
        self.assertEqual(transaction.amount, amount)
        self.assertEqual(transaction.type, Transaction.TYPE_PURCHASE)
        self.assertEqual(transaction.status, Transaction.STATUS_PENDING)
        self.assertEqual(transaction.provider, provider)
        
        # Find by user
        transactions = self.db.transactions.find_by_user(self.user_id)
        self.assertEqual(len(transactions), 1)
        self.assertEqual(transactions[0].transaction_id, transaction.transaction_id)
        
        # Find by status
        pending_transactions = self.db.transactions.find_by_status(Transaction.STATUS_PENDING)
        self.assertEqual(len(pending_transactions), 1)
        self.assertEqual(pending_transactions[0].transaction_id, transaction.transaction_id)
        
        # Complete transaction
        provider_transaction_id = "txn_123456"
        self.db.transactions.complete_transaction(transaction.transaction_id, provider_transaction_id)
        
        transaction = self.db.transactions.find_by_id(transaction.transaction_id)
        self.assertEqual(transaction.status, Transaction.STATUS_COMPLETED)
        self.assertEqual(transaction.provider_transaction_id, provider_transaction_id)
        
        # Find by provider transaction ID
        transaction = self.db.transactions.find_by_provider_transaction_id(provider_transaction_id)
        self.assertIsNotNone(transaction)
        self.assertEqual(transaction.status, Transaction.STATUS_COMPLETED)
        
        # Create credit transaction
        credit_amount = 50
        credit_transaction = self.db.transactions.create_credit_transaction(
            self.user_id,
            credit_amount
        )
        
        self.assertIsNotNone(credit_transaction)
        self.assertEqual(credit_transaction.user_id, self.user_id)
        self.assertEqual(credit_transaction.amount, credit_amount)
        self.assertEqual(credit_transaction.type, Transaction.TYPE_CREDIT)
        self.assertEqual(credit_transaction.status, Transaction.STATUS_COMPLETED)
        
        # Create debit transaction
        debit_amount = 20
        debit_transaction = self.db.transactions.create_debit_transaction(
            self.user_id,
            debit_amount
        )
        
        self.assertIsNotNone(debit_transaction)
        self.assertEqual(debit_transaction.user_id, self.user_id)
        self.assertEqual(debit_transaction.amount, debit_amount)
        self.assertEqual(debit_transaction.type, Transaction.TYPE_DEBIT)
        self.assertEqual(debit_transaction.status, Transaction.STATUS_COMPLETED)
        
        # Fail transaction
        self.db.transactions.fail_transaction(debit_transaction.transaction_id)
        
        debit_transaction = self.db.transactions.find_by_id(debit_transaction.transaction_id)
        self.assertEqual(debit_transaction.status, Transaction.STATUS_FAILED)

if __name__ == "__main__":
    unittest.main()
