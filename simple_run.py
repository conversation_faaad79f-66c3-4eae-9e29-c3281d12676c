"""
Simple script to run the VoicePal bot.
"""

import os
import logging
import asyncio
from dotenv import load_dotenv
from telegram.ext import Application

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

async def main():
    """Run the VoicePal bot."""
    # Load environment variables
    load_dotenv()
    
    # Get the Telegram token
    token = os.getenv("TELEGRAM_TOKEN")
    if not token:
        logger.error("TELEGRAM_TOKEN not found in environment variables")
        return
    
    logger.info(f"Starting VoicePal bot with token: {token}")
    
    # Import the bot module
    from bot.main import VoicePalBot
    
    # Create and run the bot
    bot = VoicePalBot()
    await bot.run()
    
    # Keep the script running
    try:
        logger.info("Bot is running. Press Ctrl+C to stop.")
        while True:
            await asyncio.sleep(1)
    except KeyboardInterrupt:
        logger.info("Bo<PERSON> stopped by user")

if __name__ == "__main__":
    asyncio.run(main())
