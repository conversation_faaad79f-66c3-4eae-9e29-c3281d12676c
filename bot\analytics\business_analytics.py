"""
Business analytics for VoicePal.

This module provides business intelligence and revenue analytics.
"""

import logging
import statistics
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from collections import defaultdict

logger = logging.getLogger(__name__)

@dataclass
class RevenueMetrics:
    """Revenue metrics data structure."""
    total_revenue: float
    monthly_recurring_revenue: float
    average_revenue_per_user: float
    customer_lifetime_value: float
    conversion_rate: float
    churn_rate: float
    revenue_growth_rate: float
    payment_success_rate: float

@dataclass
class BusinessInsight:
    """Business insight data structure."""
    insight_type: str
    title: str
    description: str
    impact: str  # high, medium, low
    recommendation: str
    value: Any
    timestamp: datetime

@dataclass
class CostAnalysis:
    """Cost analysis data structure."""
    ai_api_costs: float
    voice_processing_costs: float
    infrastructure_costs: float
    total_costs: float
    cost_per_user: float
    cost_per_conversation: float
    profit_margin: float

class BusinessAnalytics:
    """Business intelligence and revenue analytics."""

    def __init__(self, database, cache_manager=None):
        """
        Initialize business analytics.

        Args:
            database: Database instance
            cache_manager: Cache manager for performance
        """
        self.database = database
        self.cache_manager = cache_manager

        # Cost assumptions (should be configurable)
        self.cost_assumptions = {
            "ai_cost_per_1k_tokens": 0.002,  # $0.002 per 1K tokens
            "voice_cost_per_minute": 0.01,   # $0.01 per minute
            "infrastructure_cost_per_user_per_month": 0.50,  # $0.50 per user per month
            "credit_value": 0.01  # $0.01 per credit
        }

        logger.info("Business analytics initialized")

    def get_revenue_metrics(self, days: int = 30) -> RevenueMetrics:
        """
        Get comprehensive revenue metrics.

        Args:
            days: Number of days to analyze

        Returns:
            Revenue metrics
        """
        try:
            # Check cache first
            cache_key = f"revenue_metrics_{days}"
            if self.cache_manager:
                cached_result = self.cache_manager.get(cache_key)
                if cached_result:
                    return RevenueMetrics(**cached_result)

            end_date = datetime.utcnow()
            start_date = end_date - timedelta(days=days)

            # Total revenue
            total_revenue = self._calculate_total_revenue(start_date, end_date)

            # Monthly recurring revenue (MRR)
            mrr = self._calculate_mrr()

            # Average revenue per user (ARPU)
            arpu = self._calculate_arpu(start_date, end_date)

            # Customer lifetime value (CLV)
            clv = self._calculate_clv()

            # Conversion rate
            conversion_rate = self._calculate_conversion_rate(start_date, end_date)

            # Churn rate
            churn_rate = self._calculate_churn_rate(days)

            # Revenue growth rate
            revenue_growth_rate = self._calculate_revenue_growth_rate(days)

            # Payment success rate
            payment_success_rate = self._calculate_payment_success_rate(start_date, end_date)

            metrics = RevenueMetrics(
                total_revenue=total_revenue,
                monthly_recurring_revenue=mrr,
                average_revenue_per_user=arpu,
                customer_lifetime_value=clv,
                conversion_rate=conversion_rate,
                churn_rate=churn_rate,
                revenue_growth_rate=revenue_growth_rate,
                payment_success_rate=payment_success_rate
            )

            # Cache result
            if self.cache_manager:
                self.cache_manager.set(cache_key, metrics.__dict__, ttl=3600)

            return metrics

        except Exception as e:
            logger.error(f"Failed to get revenue metrics: {e}")
            return RevenueMetrics(
                total_revenue=0.0, monthly_recurring_revenue=0.0,
                average_revenue_per_user=0.0, customer_lifetime_value=0.0,
                conversion_rate=0.0, churn_rate=0.0, revenue_growth_rate=0.0,
                payment_success_rate=0.0
            )

    def _calculate_total_revenue(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate total revenue in the period."""
        try:
            result = self.database.execute("""
                SELECT SUM(amount) FROM transactions
                WHERE status = 'completed'
                AND created_at >= ? AND created_at <= ?
            """, (start_date.isoformat(), end_date.isoformat())).fetchone()

            return float(result[0]) if result and result[0] else 0.0

        except Exception as e:
            logger.error(f"Failed to calculate total revenue: {e}")
            return 0.0

    def _calculate_mrr(self) -> float:
        """Calculate Monthly Recurring Revenue."""
        try:
            # Get active subscriptions
            result = self.database.execute("""
                SELECT SUM(amount) FROM subscriptions
                WHERE status = 'active'
                AND billing_cycle = 'monthly'
            """).fetchone()

            monthly_subscriptions = float(result[0]) if result and result[0] else 0.0

            # Convert other billing cycles to monthly equivalent
            yearly_result = self.database.execute("""
                SELECT SUM(amount) FROM subscriptions
                WHERE status = 'active'
                AND billing_cycle = 'yearly'
            """).fetchone()

            yearly_subscriptions = float(yearly_result[0]) if yearly_result and yearly_result[0] else 0.0
            monthly_from_yearly = yearly_subscriptions / 12

            return monthly_subscriptions + monthly_from_yearly

        except Exception as e:
            logger.error(f"Failed to calculate MRR: {e}")
            return 0.0

    def _calculate_arpu(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate Average Revenue Per User."""
        try:
            # Total revenue
            total_revenue = self._calculate_total_revenue(start_date, end_date)

            # Active users in period
            active_users = self.database.execute("""
                SELECT COUNT(DISTINCT user_id) FROM conversations
                WHERE created_at >= ? AND created_at <= ?
            """, (start_date.isoformat(), end_date.isoformat())).fetchone()[0]

            return total_revenue / active_users if active_users > 0 else 0.0

        except Exception as e:
            logger.error(f"Failed to calculate ARPU: {e}")
            return 0.0

    def _calculate_clv(self) -> float:
        """Calculate Customer Lifetime Value."""
        try:
            # Simplified CLV calculation: ARPU / Churn Rate
            arpu_monthly = self._calculate_arpu(
                datetime.utcnow() - timedelta(days=30),
                datetime.utcnow()
            )

            churn_rate = self._calculate_churn_rate(30)

            if churn_rate > 0:
                return arpu_monthly / churn_rate
            else:
                return arpu_monthly * 12  # Assume 12 months if no churn

        except Exception as e:
            logger.error(f"Failed to calculate CLV: {e}")
            return 0.0

    def _calculate_conversion_rate(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate conversion rate from trial to paid."""
        try:
            # New users in period
            new_users = self.database.execute("""
                SELECT COUNT(*) FROM users
                WHERE created_at >= ? AND created_at <= ?
            """, (start_date.isoformat(), end_date.isoformat())).fetchone()[0]

            # Users who made payments
            paying_users = self.database.execute("""
                SELECT COUNT(DISTINCT t.user_id) FROM transactions t
                JOIN users u ON t.user_id = u.user_id
                WHERE t.status = 'completed'
                AND u.created_at >= ? AND u.created_at <= ?
            """, (start_date.isoformat(), end_date.isoformat())).fetchone()[0]

            return paying_users / new_users if new_users > 0 else 0.0

        except Exception as e:
            logger.error(f"Failed to calculate conversion rate: {e}")
            return 0.0

    def _calculate_churn_rate(self, days: int) -> float:
        """Calculate churn rate."""
        try:
            current_period_start = datetime.utcnow() - timedelta(days=days)
            previous_period_start = current_period_start - timedelta(days=days)

            # Users active in previous period
            previous_active = self.database.execute("""
                SELECT COUNT(DISTINCT user_id) FROM conversations
                WHERE created_at >= ? AND created_at < ?
            """, (previous_period_start.isoformat(), current_period_start.isoformat())).fetchone()[0]

            # Users active in current period
            current_active = self.database.execute("""
                SELECT COUNT(DISTINCT user_id) FROM conversations
                WHERE created_at >= ?
            """, (current_period_start.isoformat(),)).fetchone()[0]

            if previous_active > 0:
                return max(0, (previous_active - current_active) / previous_active)
            return 0.0

        except Exception as e:
            logger.error(f"Failed to calculate churn rate: {e}")
            return 0.0

    def _calculate_revenue_growth_rate(self, days: int) -> float:
        """Calculate revenue growth rate."""
        try:
            current_period_end = datetime.utcnow()
            current_period_start = current_period_end - timedelta(days=days)
            previous_period_end = current_period_start
            previous_period_start = previous_period_end - timedelta(days=days)

            current_revenue = self._calculate_total_revenue(current_period_start, current_period_end)
            previous_revenue = self._calculate_total_revenue(previous_period_start, previous_period_end)

            if previous_revenue > 0:
                return (current_revenue - previous_revenue) / previous_revenue
            return 0.0

        except Exception as e:
            logger.error(f"Failed to calculate revenue growth rate: {e}")
            return 0.0

    def _calculate_payment_success_rate(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate payment success rate."""
        try:
            total_payments = self.database.execute("""
                SELECT COUNT(*) FROM transactions
                WHERE created_at >= ? AND created_at <= ?
            """, (start_date.isoformat(), end_date.isoformat())).fetchone()[0]

            successful_payments = self.database.execute("""
                SELECT COUNT(*) FROM transactions
                WHERE status = 'completed'
                AND created_at >= ? AND created_at <= ?
            """, (start_date.isoformat(), end_date.isoformat())).fetchone()[0]

            return successful_payments / total_payments if total_payments > 0 else 0.0

        except Exception as e:
            logger.error(f"Failed to calculate payment success rate: {e}")
            return 0.0

    def analyze_costs(self, days: int = 30) -> CostAnalysis:
        """
        Analyze operational costs.

        Args:
            days: Number of days to analyze

        Returns:
            Cost analysis
        """
        try:
            end_date = datetime.utcnow()
            start_date = end_date - timedelta(days=days)

            # AI API costs (estimated based on message count)
            message_count = self.database.execute("""
                SELECT COUNT(*) FROM messages m
                JOIN conversations c ON m.conversation_id = c.conversation_id
                WHERE c.created_at >= ? AND c.created_at <= ?
            """, (start_date.isoformat(), end_date.isoformat())).fetchone()[0]

            # Estimate tokens (rough approximation)
            estimated_tokens = message_count * 100  # 100 tokens per message average
            ai_costs = (estimated_tokens / 1000) * self.cost_assumptions["ai_cost_per_1k_tokens"]

            # Voice processing costs
            voice_messages = self.database.execute("""
                SELECT COUNT(*) FROM messages m
                JOIN conversations c ON m.conversation_id = c.conversation_id
                WHERE c.created_at >= ? AND c.created_at <= ?
                AND m.message_type = 'voice'
            """, (start_date.isoformat(), end_date.isoformat())).fetchone()[0]

            # Estimate voice duration (rough approximation)
            estimated_voice_minutes = voice_messages * 0.5  # 30 seconds average
            voice_costs = estimated_voice_minutes * self.cost_assumptions["voice_cost_per_minute"]

            # Infrastructure costs
            active_users = self.database.execute("""
                SELECT COUNT(DISTINCT user_id) FROM conversations
                WHERE created_at >= ? AND created_at <= ?
            """, (start_date.isoformat(), end_date.isoformat())).fetchone()[0]

            monthly_multiplier = days / 30.0
            infrastructure_costs = (active_users *
                                  self.cost_assumptions["infrastructure_cost_per_user_per_month"] *
                                  monthly_multiplier)

            total_costs = ai_costs + voice_costs + infrastructure_costs

            # Calculate per-unit costs
            cost_per_user = total_costs / active_users if active_users > 0 else 0.0

            conversation_count = self.database.execute("""
                SELECT COUNT(*) FROM conversations
                WHERE created_at >= ? AND created_at <= ?
            """, (start_date.isoformat(), end_date.isoformat())).fetchone()[0]

            cost_per_conversation = total_costs / conversation_count if conversation_count > 0 else 0.0

            # Calculate profit margin
            revenue = self._calculate_total_revenue(start_date, end_date)
            profit_margin = (revenue - total_costs) / revenue if revenue > 0 else -1.0

            return CostAnalysis(
                ai_api_costs=ai_costs,
                voice_processing_costs=voice_costs,
                infrastructure_costs=infrastructure_costs,
                total_costs=total_costs,
                cost_per_user=cost_per_user,
                cost_per_conversation=cost_per_conversation,
                profit_margin=profit_margin
            )

        except Exception as e:
            logger.error(f"Failed to analyze costs: {e}")
            return CostAnalysis(
                ai_api_costs=0.0, voice_processing_costs=0.0,
                infrastructure_costs=0.0, total_costs=0.0,
                cost_per_user=0.0, cost_per_conversation=0.0,
                profit_margin=0.0
            )

    def generate_business_insights(self, days: int = 30) -> List[BusinessInsight]:
        """
        Generate actionable business insights.

        Args:
            days: Number of days to analyze

        Returns:
            List of business insights
        """
        try:
            insights = []

            # Get metrics
            revenue_metrics = self.get_revenue_metrics(days)
            cost_analysis = self.analyze_costs(days)

            # Revenue insights
            if revenue_metrics.revenue_growth_rate > 0.2:
                insights.append(BusinessInsight(
                    insight_type="revenue_growth",
                    title="Strong Revenue Growth",
                    description=f"Revenue grew by {revenue_metrics.revenue_growth_rate:.1%} in the last {days} days",
                    impact="high",
                    recommendation="Consider scaling marketing efforts to capitalize on growth momentum",
                    value=revenue_metrics.revenue_growth_rate,
                    timestamp=datetime.utcnow()
                ))
            elif revenue_metrics.revenue_growth_rate < -0.1:
                insights.append(BusinessInsight(
                    insight_type="revenue_decline",
                    title="Revenue Decline",
                    description=f"Revenue declined by {abs(revenue_metrics.revenue_growth_rate):.1%} in the last {days} days",
                    impact="high",
                    recommendation="Investigate causes and implement retention strategies",
                    value=revenue_metrics.revenue_growth_rate,
                    timestamp=datetime.utcnow()
                ))

            # Conversion insights
            if revenue_metrics.conversion_rate < 0.05:
                insights.append(BusinessInsight(
                    insight_type="low_conversion",
                    title="Low Conversion Rate",
                    description=f"Only {revenue_metrics.conversion_rate:.1%} of users convert to paid",
                    impact="high",
                    recommendation="Improve onboarding flow and value proposition",
                    value=revenue_metrics.conversion_rate,
                    timestamp=datetime.utcnow()
                ))

            # Churn insights
            if revenue_metrics.churn_rate > 0.1:
                insights.append(BusinessInsight(
                    insight_type="high_churn",
                    title="High Churn Rate",
                    description=f"Churn rate is {revenue_metrics.churn_rate:.1%}, above healthy threshold",
                    impact="high",
                    recommendation="Implement user retention programs and improve user experience",
                    value=revenue_metrics.churn_rate,
                    timestamp=datetime.utcnow()
                ))

            # Cost insights
            if cost_analysis.profit_margin < 0:
                insights.append(BusinessInsight(
                    insight_type="negative_margin",
                    title="Negative Profit Margin",
                    description=f"Costs exceed revenue by {abs(cost_analysis.profit_margin):.1%}",
                    impact="high",
                    recommendation="Optimize costs or increase pricing to achieve profitability",
                    value=cost_analysis.profit_margin,
                    timestamp=datetime.utcnow()
                ))
            elif cost_analysis.profit_margin < 0.2:
                insights.append(BusinessInsight(
                    insight_type="low_margin",
                    title="Low Profit Margin",
                    description=f"Profit margin is only {cost_analysis.profit_margin:.1%}",
                    impact="medium",
                    recommendation="Look for cost optimization opportunities",
                    value=cost_analysis.profit_margin,
                    timestamp=datetime.utcnow()
                ))

            # Payment insights
            if revenue_metrics.payment_success_rate < 0.9:
                insights.append(BusinessInsight(
                    insight_type="payment_issues",
                    title="Payment Success Rate Issues",
                    description=f"Only {revenue_metrics.payment_success_rate:.1%} of payments succeed",
                    impact="medium",
                    recommendation="Investigate payment gateway issues and improve payment flow",
                    value=revenue_metrics.payment_success_rate,
                    timestamp=datetime.utcnow()
                ))

            return insights

        except Exception as e:
            logger.error(f"Failed to generate business insights: {e}")
            return []

    def get_revenue_forecast(self, months: int = 6) -> Dict[str, List[Tuple[str, float]]]:
        """
        Generate revenue forecast based on historical trends.

        Args:
            months: Number of months to forecast

        Returns:
            Revenue forecast data
        """
        try:
            # Get historical monthly revenue for the last 12 months
            historical_revenue = []
            end_date = datetime.utcnow()

            for i in range(12):
                month_end = end_date - timedelta(days=30*i)
                month_start = month_end - timedelta(days=30)

                revenue = self._calculate_total_revenue(month_start, month_end)
                historical_revenue.append(revenue)

            historical_revenue.reverse()  # Chronological order

            # Simple linear trend forecast
            if len(historical_revenue) >= 3:
                # Calculate trend
                x_values = list(range(len(historical_revenue)))
                y_values = historical_revenue

                # Simple linear regression
                n = len(x_values)
                sum_x = sum(x_values)
                sum_y = sum(y_values)
                sum_xy = sum(x * y for x, y in zip(x_values, y_values))
                sum_x2 = sum(x * x for x in x_values)

                slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x * sum_x)
                intercept = (sum_y - slope * sum_x) / n

                # Generate forecast
                forecast = []
                for i in range(months):
                    future_month = len(historical_revenue) + i
                    predicted_revenue = slope * future_month + intercept
                    predicted_revenue = max(0, predicted_revenue)  # No negative revenue

                    forecast_date = end_date + timedelta(days=30*i)
                    forecast.append((forecast_date.strftime("%Y-%m"), predicted_revenue))

                return {
                    "historical": [((end_date - timedelta(days=30*(11-i))).strftime("%Y-%m"), rev)
                                   for i, rev in enumerate(historical_revenue)],
                    "forecast": forecast
                }
            else:
                return {"historical": [], "forecast": []}

        except Exception as e:
            logger.error(f"Failed to generate revenue forecast: {e}")
            return {"historical": [], "forecast": []}
