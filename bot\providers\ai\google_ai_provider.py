"""
Google AI provider for VoicePal.

This module provides an implementation of the AI provider interface using
Google AI Studio's Gemini model with proper conversation history management.
This implementation supports both SDK and direct API approaches for flexibility.
"""

import logging
import asyncio
import requests
from datetime import datetime
from typing import Dict, List, Optional, Any

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

from bot.providers.ai_provider_interface import AIProviderInterface

# Import Google AI SDK
try:
    # Try both import styles for compatibility
    try:
        import google.generativeai as genai
        GOOGLE_AI_AVAILABLE = True
        logger.info("Imported Google AI SDK (google.generativeai)")
    except ImportError:
        import google_generativeai as genai
        GOOGLE_AI_AVAILABLE = True
        logger.info("Imported Google AI SDK (google_generativeai)")
except ImportError:
    GOOGLE_AI_AVAILABLE = False
    logger.warning("Google AI SDK not found, will use direct API calls")

# Constants
CONTENT_TYPE_JSON = "application/json"
DEFAULT_TIMEOUT = 30  # seconds
DEFAULT_ERROR_RESPONSE = "I'm sorry, I couldn't generate a response. Please try again."
DEFAULT_SUMMARY = "No summary available."
DEFAULT_REFLECTION_PROMPT = "Reflect on your recent experiences. What patterns do you notice? What insights have you gained?"
API_ERROR_LOG_MESSAGE = "API request failed: %s %s"

class GoogleAIProvider(AIProviderInterface):
    """Google AI provider implementation with SDK and direct API support."""

    def __init__(self, api_key: str, model_name: str = "gemini-1.5-flash"):
        """
        Initialize the Google AI provider.

        Args:
            api_key: Google AI API key
            model_name: Model name
        """
        self.api_key = api_key
        self.model_name = model_name
        self.personality = "friendly"
        self._supported_features = ["text_generation", "sentiment_analysis"]
        self.api_url = "https://generativelanguage.googleapis.com/v1beta/models"

        # Store chat history for each user
        self.chat_histories = {}

        # Maximum number of conversation turns to keep in history
        self.max_history_turns = 10

        # Initialize SDK if available
        self.use_sdk = False
        self.model = None

        if GOOGLE_AI_AVAILABLE:
            try:
                genai.configure(api_key=api_key)
                self.model = genai.GenerativeModel(model_name)
                self.use_sdk = True
                logger.info("Google AI SDK initialized with model %s", model_name)
            except Exception as e:
                logger.warning("Error initializing Google AI SDK: %s", e)
                self.use_sdk = False

        # Test API connection if not using SDK
        if not self.use_sdk:
            try:
                self._test_api_connection()
                logger.info("Google AI direct API initialized with model %s", model_name)
            except (ConnectionError, requests.RequestException) as e:
                logger.warning("Error initializing Google AI direct API: %s", e)

    def _test_api_connection(self):
        """Test the API connection to ensure the API key is valid."""
        url = f"{self.api_url}/{self.model_name}?key={self.api_key}"
        response = requests.get(url, timeout=DEFAULT_TIMEOUT)
        if response.status_code != 200:
            logger.error("Failed to connect to Google AI API: %s %s", response.status_code, response.text)
            raise ConnectionError(f"Failed to connect to Google AI API: {response.status_code}")
        return True

    def supports_feature(self, feature_name: str) -> bool:
        """
        Check if a feature is supported.

        Args:
            feature_name: Feature name

        Returns:
            bool: True if feature is supported, False otherwise
        """
        return feature_name in self._supported_features

    def set_personality(self, personality: str) -> None:
        """
        Set the personality for responses.

        Args:
            personality: Personality type
        """
        self.personality = personality
        logger.info("Set personality to %s", personality)

    async def generate_response(self, message: str, language: Optional[str] = None,
                              user_context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Generate a response to a message.

        Args:
            message: User message
            language: Language code
            user_context: User context

        Returns:
            Dict containing response text and metadata
        """
        try:
            # Get user ID from context
            user_id = user_context.get("user_data", {}).get("id") if user_context else None

            if not user_id:
                logger.warning("No user ID found in context, using stateless mode")
                return await self._generate_stateless_response(message, language, user_context)

            # Initialize chat history for this user if it doesn't exist
            if user_id not in self.chat_histories:
                self.chat_histories[user_id] = []

            # Prepare system prompt based on personality
            system_prompt = self._get_system_prompt(self.personality, language)

            # Add user context if available
            if user_context:
                # Add user summary
                user_summary = user_context.get("summary")
                if user_summary:
                    system_prompt += f"\nUser context: {user_summary}"

                # Add special instructions from conversation state
                if "context" in user_context and "special_instructions" in user_context["context"]:
                    special_instructions = user_context["context"]["special_instructions"]
                    system_prompt += f"\n\nIMPORTANT CONVERSATION INSTRUCTIONS: {special_instructions}"

                # Add conversation state information
                if "context" in user_context and "conversation_state" in user_context["context"]:
                    conv_state = user_context["context"]["conversation_state"]

                    # Add teaching mode instructions
                    if conv_state.get("teaching_mode"):
                        subject = conv_state.get("teaching_subject", "")
                        system_prompt += f"\n\nYou are currently in teaching mode for {subject}."

                        # Add confirmed intents to avoid redundant questions
                        confirmed_intents = conv_state.get("confirmed_intents", set())
                        if confirmed_intents:
                            system_prompt += "\nThe user has already confirmed interest in: " + ", ".join(confirmed_intents)
                            system_prompt += "\nDO NOT ask for confirmation on these topics again."

                # Add response guidance based on message analysis
                if "context" in user_context and "response_guidance" in user_context["context"]:
                    response_guidance = user_context["context"]["response_guidance"]
                    system_prompt += f"\n\nResponse guidance: {response_guidance}"

                # Add message complexity information
                if "context" in user_context and "message_analysis" in user_context["context"]:
                    message_analysis = user_context["context"]["message_analysis"]
                    complexity = message_analysis.get("complexity", "medium")
                    is_greeting = message_analysis.get("is_greeting", False)

                    if is_greeting:
                        system_prompt += "\nThis is a simple greeting. Keep your response very brief and friendly."
                    elif complexity == "simple":
                        system_prompt += "\nThis is a simple message. Keep your response concise and direct."
                    elif complexity == "complex":
                        system_prompt += "\nThis is a complex message. You can provide a more detailed response."

            # Build conversation history from both stored history and context
            conversation_history = user_context.get("conversations", []) if user_context else []

            # Sort by timestamp if available
            if conversation_history:
                try:
                    conversation_history = sorted(
                        conversation_history,
                        key=lambda x: x.get("timestamp", ""),
                        reverse=False  # Oldest first
                    )
                except Exception as e:
                    logger.warning(f"Error sorting conversation history: {e}")

            # If using SDK, use the SDK approach
            if self.use_sdk and self.model:
                response_text = await self._generate_with_sdk(
                    message=message,
                    system_prompt=system_prompt,
                    conversation_history=conversation_history,
                    user_id=user_id
                )
            else:
                # Use direct API approach
                response_text = await self._generate_with_direct_api(
                    message=message,
                    system_prompt=system_prompt,
                    conversation_history=conversation_history,
                    user_id=user_id
                )

            # Update chat history
            self.chat_histories[user_id].append({
                "user": message,
                "assistant": response_text,
                "timestamp": datetime.now().isoformat()
            })

            # Trim history if needed
            if len(self.chat_histories[user_id]) > self.max_history_turns:
                self.chat_histories[user_id] = self.chat_histories[user_id][-self.max_history_turns:]

            return {
                "text": response_text,
                "language": language or "en"
            }
        except Exception as e:
            logger.error("Error generating response: %s", e)
            import traceback
            logger.error(traceback.format_exc())
            return {
                "text": DEFAULT_ERROR_RESPONSE,
                "language": language or "en"
            }

    async def _generate_stateless_response(self, message: str, language: Optional[str] = None,
                                         user_context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Generate a response without using chat history (fallback method)."""
        try:
            # Prepare system prompt based on personality
            system_prompt = self._get_system_prompt(self.personality, language)

            # Add user context if available
            if user_context:
                user_summary = user_context.get("summary")
                if user_summary:
                    system_prompt += f"\nUser context: {user_summary}"

                # Add recent conversation history
                conversation_history = user_context.get("conversations", [])
                if conversation_history:
                    system_prompt += "\nRecent conversation:"
                    for conv in conversation_history[:5]:
                        system_prompt += f"\nUser: {conv.get('message', '')}\nYou: {conv.get('response', '')}"

            # Combine system prompt with user message
            full_prompt = f"{system_prompt}\n\nUser: {message}\nAssistant:"

            # Prepare the request payload
            payload = {
                "contents": [
                    {
                        "parts": [
                            {
                                "text": full_prompt
                            }
                        ]
                    }
                ],
                "generationConfig": {
                    "temperature": self._get_temperature_for_personality(self.personality),
                    "topP": 0.95,
                    "topK": 20,
                    "maxOutputTokens": 1024,
                }
            }

            # Make the API call asynchronously
            url = f"{self.api_url}/{self.model_name}:generateContent?key={self.api_key}"
            response_text = await self._async_post_request(url, payload)

            return {
                "text": response_text,
                "language": language or "en"
            }
        except Exception as e:
            logger.error("Error generating stateless response: %s", e)
            return {
                "text": DEFAULT_ERROR_RESPONSE,
                "language": language or "en"
            }

    async def _generate_with_sdk(self, message: str, system_prompt: str,
                             conversation_history: List[Dict[str, Any]], user_id: str) -> str:
        """Generate a response using the Google AI SDK."""
        try:
            # Create a chat session
            chat = self.model.start_chat(history=[])

            # Add system prompt
            await asyncio.to_thread(
                chat.send_message,
                system_prompt,
                stream=False
            )

            # Extract user name from context
            user_name = None

            # Look for user data in conversation history
            for conv in conversation_history:
                if isinstance(conv, dict) and "user_data" in conv:
                    user_data = conv["user_data"]
                    if user_data.get("preferred_name"):
                        user_name = user_data.get("preferred_name")
                    elif user_data.get("first_name"):
                        user_name = user_data.get("first_name")
                    break

            # If we found a user name, add it to the context
            if user_name:
                user_context_prompt = f"The user's name is {user_name}. Remember to use their name occasionally in your responses."
                await asyncio.to_thread(
                    chat.send_message,
                    user_context_prompt,
                    stream=False
                )

            # Add conversation history
            for conv in conversation_history:
                # Add user message
                if conv.get("message"):
                    await asyncio.to_thread(
                        chat.send_message,
                        conv.get("message", ""),
                        stream=False
                    )

                # Add assistant response
                if conv.get("response"):
                    # We need to add the assistant's response to the history
                    # This is a bit of a hack since the SDK doesn't support adding model messages directly
                    # So we'll use the internal _history attribute
                    chat._history.append({
                        "role": "model",
                        "parts": [{"text": conv.get("response", "")}]
                    })

            # Add in-memory history
            for turn in self.chat_histories.get(user_id, []):
                # Skip duplicates
                if any(conv.get("message") == turn["user"] for conv in conversation_history):
                    continue

                # Add user message
                await asyncio.to_thread(
                    chat.send_message,
                    turn["user"],
                    stream=False
                )

                # Add assistant response
                chat._history.append({
                    "role": "model",
                    "parts": [{"text": turn["assistant"]}]
                })

            # Send the current message and get response
            response = await asyncio.to_thread(
                chat.send_message,
                message,
                generation_config={
                    "temperature": self._get_temperature_for_personality(self.personality),
                    "top_p": 0.95,
                    "top_k": 20,
                    "max_output_tokens": 1024,
                },
                stream=False
            )

            return response.text
        except Exception as e:
            logger.error("Error generating response with SDK: %s", e)
            # Fall back to direct API
            return await self._generate_with_direct_api(
                message=message,
                system_prompt=system_prompt,
                conversation_history=conversation_history,
                user_id=user_id
            )

    async def _generate_with_direct_api(self, message: str, system_prompt: str,
                                  conversation_history: List[Dict[str, Any]], user_id: str) -> str:
        """Generate a response using direct API calls."""
        try:
            # Build history messages
            history_messages = []

            # Extract user name and preferences from context
            user_name = None

            # Look for user data in conversation history
            for conv in conversation_history:
                if isinstance(conv, dict) and "user_data" in conv:
                    user_data = conv["user_data"]
                    if user_data.get("preferred_name"):
                        user_name = user_data.get("preferred_name")
                    elif user_data.get("first_name"):
                        user_name = user_data.get("first_name")
                    break

            # If we found a user name, add it to the context
            if user_name:
                history_messages.append({
                    "role": "user",
                    "parts": [{"text": f"My name is {user_name}."}]
                })
                history_messages.append({
                    "role": "model",
                    "parts": [{"text": f"Nice to meet you, {user_name}! I'll remember your name."}]
                })

            # Add database history first (older messages)
            for conv in conversation_history:
                if conv.get("message"):
                    history_messages.append({
                        "role": "user",
                        "parts": [{"text": conv.get("message", "")}]
                    })
                if conv.get("response"):
                    history_messages.append({
                        "role": "model",
                        "parts": [{"text": conv.get("response", "")}]
                    })

            # Then add in-memory history (more recent messages)
            for turn in self.chat_histories.get(user_id, []):
                # Skip duplicates (messages that might be in both histories)
                if any(m.get("parts", [{}])[0].get("text") == turn["user"]
                       for m in history_messages if m.get("role") == "user"):
                    continue

                history_messages.append({
                    "role": "user",
                    "parts": [{"text": turn["user"]}]
                })
                history_messages.append({
                    "role": "model",
                    "parts": [{"text": turn["assistant"]}]
                })

            # Limit history to prevent token overflow
            if len(history_messages) > self.max_history_turns * 2:
                # Keep most recent messages
                history_messages = history_messages[-(self.max_history_turns * 2):]

            # Prepare the request payload with history
            payload = {
                "contents": [
                    {
                        "role": "system",
                        "parts": [{"text": system_prompt}]
                    }
                ] + history_messages + [
                    {
                        "role": "user",
                        "parts": [{"text": message}]
                    }
                ],
                "generationConfig": {
                    "temperature": self._get_temperature_for_personality(self.personality),
                    "topP": 0.95,
                    "topK": 20,
                    "maxOutputTokens": 1024,
                }
            }

            # Make the API call asynchronously
            url = f"{self.api_url}/{self.model_name}:generateContent?key={self.api_key}"
            return await self._async_post_request(url, payload)
        except Exception as e:
            logger.error("Error generating response with direct API: %s", e)
            return DEFAULT_ERROR_RESPONSE

    async def _async_post_request(self, url: str, payload: Dict) -> str:
        """Make an asynchronous POST request to the API."""
        try:
            # Use asyncio.to_thread to make the request asynchronously
            response = await asyncio.to_thread(
                requests.post,
                url,
                json=payload,
                headers={"Content-Type": CONTENT_TYPE_JSON},
                timeout=DEFAULT_TIMEOUT
            )

            if response.status_code != 200:
                logger.error(API_ERROR_LOG_MESSAGE, response.status_code, response.text)
                return DEFAULT_ERROR_RESPONSE

            # Parse the response
            response_json = response.json()

            # Extract the text from the response
            if "candidates" in response_json and len(response_json["candidates"]) > 0:
                candidate = response_json["candidates"][0]
                if "content" in candidate and "parts" in candidate["content"]:
                    parts = candidate["content"]["parts"]
                    if len(parts) > 0 and "text" in parts[0]:
                        return parts[0]["text"]

            return DEFAULT_ERROR_RESPONSE
        except Exception as e:
            logger.error("Error in async POST request: %s", e)
            return DEFAULT_ERROR_RESPONSE

    def analyze_sentiment(self, text: str) -> Dict[str, Any]:
        """
        Analyze sentiment of text.

        Args:
            text: Text to analyze

        Returns:
            Dict containing sentiment information
        """
        # Default response for errors
        default_response = {
            "sentiment": "neutral",
            "confidence": 0.5
        }

        try:
            # Use the model to analyze sentiment
            prompt = (
                "Analyze the sentiment of the following text and respond with only one word: "
                f"positive, negative, or neutral.\n\nText: {text}"
            )

            # Prepare the request payload
            payload = {
                "contents": [
                    {
                        "parts": [
                            {
                                "text": prompt
                            }
                        ]
                    }
                ],
                "generationConfig": {
                    "temperature": 0.1,
                    "maxOutputTokens": 10,
                }
            }

            # Make the API call
            url = f"{self.api_url}/{self.model_name}:generateContent?key={self.api_key}"
            response = requests.post(
                url,
                json=payload,
                headers={"Content-Type": CONTENT_TYPE_JSON},
                timeout=DEFAULT_TIMEOUT
            )

            if response.status_code != 200:
                logger.error(API_ERROR_LOG_MESSAGE, response.status_code, response.text)
                return default_response

            # Parse the response
            response_json = response.json()

            # Extract the text from the response
            sentiment = "neutral"
            if "candidates" in response_json and len(response_json["candidates"]) > 0:
                candidate = response_json["candidates"][0]
                if "content" in candidate and "parts" in candidate["content"]:
                    parts = candidate["content"]["parts"]
                    if len(parts) > 0 and "text" in parts[0]:
                        sentiment = parts[0]["text"].strip().lower()

            # Validate sentiment
            if sentiment not in ["positive", "negative", "neutral"]:
                sentiment = "neutral"

            # Determine confidence (mock for now)
            confidence = 0.8

            return {
                "sentiment": sentiment,
                "confidence": confidence
            }
        except Exception as e:
            logger.error("Error analyzing sentiment: %s", e)
            return default_response

    def summarize_conversation(self, conversation_history: List[Dict[str, Any]]) -> str:
        """
        Summarize conversation history.

        Args:
            conversation_history: List of conversation entries

        Returns:
            str: Conversation summary
        """
        try:
            if not conversation_history:
                return "This user has had several conversations about various topics."

            # Prepare conversation for summarization
            conversation_text = "Conversation history:\n"
            for entry in conversation_history:
                conversation_text += f"User: {entry['message']}\nBot: {entry['response']}\n"

            # Generate summary
            prompt = (
                "Summarize the following conversation in a concise paragraph. "
                "Focus on the user's interests, preferences, and recurring topics.\n\n"
                f"{conversation_text}"
            )

            # Prepare the request payload
            payload = {
                "contents": [
                    {
                        "parts": [
                            {
                                "text": prompt
                            }
                        ]
                    }
                ],
                "generationConfig": {
                    "temperature": 0.3,
                    "maxOutputTokens": 200,
                }
            }

            # Make the API call
            url = f"{self.api_url}/{self.model_name}:generateContent?key={self.api_key}"
            response = requests.post(
                url,
                json=payload,
                headers={"Content-Type": CONTENT_TYPE_JSON},
                timeout=DEFAULT_TIMEOUT
            )

            if response.status_code != 200:
                logger.error(API_ERROR_LOG_MESSAGE, response.status_code, response.text)
                return DEFAULT_SUMMARY

            # Parse the response
            response_json = response.json()

            # Extract the text from the response
            if "candidates" in response_json and len(response_json["candidates"]) > 0:
                candidate = response_json["candidates"][0]
                if "content" in candidate and "parts" in candidate["content"]:
                    parts = candidate["content"]["parts"]
                    if len(parts) > 0 and "text" in parts[0]:
                        return parts[0]["text"]

            return DEFAULT_SUMMARY
        except Exception as e:
            logger.error("Error summarizing conversation: %s", e)
            return DEFAULT_SUMMARY

    def analyze_mood_patterns(self, mood_history: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Analyze mood patterns.

        Args:
            mood_history: List of mood entries

        Returns:
            Dict containing mood pattern information
        """
        # Define default response
        default_response = {
            "dominant_mood": "neutral",
            "mood_stability": "stable",
            "mood_trend": "neutral"
        }

        try:
            if not mood_history:
                return {
                    "dominant_mood": "positive",
                    "mood_stability": "stable",
                    "mood_trend": "improving"
                }

            # Analyze mood patterns directly without using the API
            # This is a simple analysis that doesn't require AI
            return self._analyze_mood_patterns_locally(mood_history)
        except Exception as e:
            logger.error("Error analyzing mood patterns: %s", e)
            return default_response

    def _analyze_mood_patterns_locally(self, mood_history: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze mood patterns locally without using the API."""
        # Count sentiments
        sentiment_counts = {}
        for entry in mood_history:
            sentiment = entry.get("sentiment", "neutral")
            sentiment_counts[sentiment] = sentiment_counts.get(sentiment, 0) + 1

        # Get dominant mood
        if sentiment_counts:
            dominant_mood = max(sentiment_counts.items(), key=lambda x: x[1])[0]
        else:
            dominant_mood = "neutral"

        # Calculate mood stability
        unique_sentiments = len(sentiment_counts)
        if unique_sentiments == 1:
            mood_stability = "very stable"
        elif unique_sentiments == 2:
            mood_stability = "stable"
        elif unique_sentiments == 3:
            mood_stability = "somewhat unstable"
        else:
            mood_stability = "unstable"

        # Calculate mood trend
        mood_trend = self._calculate_mood_trend(mood_history)

        return {
            "dominant_mood": dominant_mood,
            "mood_stability": mood_stability,
            "mood_trend": mood_trend
        }

    def _calculate_mood_trend(self, mood_history: List[Dict[str, Any]]) -> str:
        """Calculate mood trend from mood history."""
        if len(mood_history) >= 3:
            recent_moods = []
            for entry in mood_history[-3:]:
                recent_moods.append(entry.get("sentiment", "neutral"))

            if all(s == "positive" for s in recent_moods):
                return "improving"
            elif all(s == "negative" for s in recent_moods):
                return "deteriorating"
            else:
                return "fluctuating"
        else:
            return "neutral"

    def generate_reflection_prompt(self, journal_entries: List[Dict[str, Any]]) -> str:
        """
        Generate a reflection prompt based on journal entries.

        Args:
            journal_entries: List of journal entries

        Returns:
            str: Reflection prompt
        """
        try:
            if not journal_entries:
                return DEFAULT_REFLECTION_PROMPT

            # Prepare journal entries for prompt
            entries_text = "Journal entries:\n"
            for i, entry in enumerate(journal_entries[:5]):
                date = entry.get("date", "Unknown date")
                content = entry.get("content", "")
                entries_text += f"Entry {i+1} ({date}): {content}\n"

            # Generate reflection prompt
            prompt = (
                "Based on these journal entries, create a thoughtful reflection prompt "
                "that encourages deeper thinking:\n\n"
                f"{entries_text}"
            )

            # Prepare the request payload
            payload = {
                "contents": [
                    {
                        "parts": [
                            {
                                "text": prompt
                            }
                        ]
                    }
                ],
                "generationConfig": {
                    "temperature": 0.7,
                    "maxOutputTokens": 150,
                }
            }

            # Make the API call
            url = f"{self.api_url}/{self.model_name}:generateContent?key={self.api_key}"
            response = requests.post(
                url,
                json=payload,
                headers={"Content-Type": CONTENT_TYPE_JSON},
                timeout=DEFAULT_TIMEOUT
            )

            if response.status_code != 200:
                logger.error(API_ERROR_LOG_MESSAGE, response.status_code, response.text)
                return DEFAULT_REFLECTION_PROMPT

            # Parse the response
            response_json = response.json()

            # Extract the text from the response
            if "candidates" in response_json and len(response_json["candidates"]) > 0:
                candidate = response_json["candidates"][0]
                if "content" in candidate and "parts" in candidate["content"]:
                    parts = candidate["content"]["parts"]
                    if len(parts) > 0 and "text" in parts[0]:
                        return parts[0]["text"]

            return DEFAULT_REFLECTION_PROMPT
        except Exception as e:
            logger.error("Error generating reflection prompt: %s", e)
            return DEFAULT_REFLECTION_PROMPT

    def _get_temperature_for_personality(self, personality: str) -> float:
        """
        Get temperature value based on personality.

        Args:
            personality: Personality type

        Returns:
            float: Temperature value
        """
        # Map personalities to temperature values
        temperature_map = {
            "friendly": 0.7,
            "witty": 0.9,
            "calm": 0.5,
            "motivational": 0.8,
            "thoughtful": 0.6
        }

        # Return the appropriate temperature or default to 0.7
        return temperature_map.get(personality, 0.7)

    def _get_system_prompt(self, personality: str, language: Optional[str] = None) -> str:
        """
        Get system prompt based on personality.

        Args:
            personality: Personality type
            language: Language code

        Returns:
            str: System prompt
        """
        # Base prompt broken into parts for readability
        base_prompt = (
            "You are VoicePal, an AI friend that people talk to via text or voice messages. "
            "You should act like a close friend having a natural conversation, not like a service or assistant. "
            "Use a warm, casual, and natural conversational style as if chatting with a good friend. "
            "Adapt your response length to the user's message - brief for greetings, more detailed for complex topics."
        )

        if language and language != "en":
            base_prompt += f" Respond in {language} language."

        # Add adaptive response guidance
        response_guidance = (
            " IMPORTANT CONVERSATION STYLE GUIDELINES:"
            " - Talk like a real friend, not a service bot. Avoid phrases like 'how can I assist you' or 'is there anything I can do for you'"
            " - Use casual language, contractions, and natural speech patterns"
            " - Show genuine interest in the user's life and feelings"
            " - Share opinions and preferences (you can make these up) to seem more human-like"
            " - Avoid asking too many questions - provide information directly when the user's intent is clear"
            " - When teaching something, provide multiple pieces of information at once rather than one at a time"
            " - ALWAYS remember and use the user's name if you know it from context"
            " - Reference specific details from previous conversations to show continuity and build rapport"
            " - Keep responses concise (1-3 sentences) as this is a voice chat application"
            " - Use emojis sparingly and only when they fit the emotional context"
            " - NEVER verbalize emojis like '(smile)' or '(laughing)' - just use natural expressions"
            " - If the user expresses negative emotions, match their tone with empathy"
            " - Vary your greeting styles to sound more natural"
            " - When the user says 'yes' or gives a simple confirmation, continue with more content rather than asking another question"
            " - Avoid asking for confirmation when the user's intent is already clear"
            " - When teaching, provide multiple examples at once rather than one at a time"
        )
        base_prompt += response_guidance

        # Personality-specific instructions
        personality_prompts = {
            "friendly": (
                " As a friendly personality:"
                " - Be warm, supportive, and genuinely caring"
                " - Use upbeat but natural language like 'Hey! So good to hear from you!'"
                " - Show authentic interest in the user's experiences"
                " - Share small personal anecdotes (you can make these up)"
                " - Use casual language like you would with a close friend"
                " - Remember personal details the user has shared before"
                " - Refer to the user by name when appropriate"
            ),
            "witty": (
                " As a witty personality:"
                " - Use clever humor, wordplay, and occasional light sarcasm"
                " - Reference pop culture in a natural way"
                " - Be playful but never mean-spirited"
                " - Keep responses entertaining and engaging"
                " - Use humor that matches the conversation's tone"
                " - Remember jokes or humorous exchanges you've had with the user"
                " - Refer to the user by name when appropriate"
            ),
            "calm": (
                " As a calm personality:"
                " - Use a soothing, measured conversational style"
                " - Speak thoughtfully and with a sense of peace"
                " - Use slightly longer sentences with a gentle rhythm"
                " - Offer reassurance and perspective when appropriate"
                " - Avoid overly excited language but still be warm"
                " - Remember calming topics the user has responded well to"
                " - Refer to the user by name when appropriate"
            ),
            "motivational": (
                " As a motivational personality:"
                " - Be energetic and encouraging like a supportive friend"
                " - Share enthusiasm for the user's goals and interests"
                " - Offer genuine encouragement rather than generic motivational quotes"
                " - Balance positivity with realism and empathy"
                " - Use language that inspires but still sounds natural"
                " - Remember the user's goals and aspirations they've shared"
                " - Refer to the user by name when appropriate"
            ),
            "thoughtful": (
                " As a thoughtful personality:"
                " - Be reflective and contemplative in your responses"
                " - Ask meaningful questions that show you're really thinking"
                " - Share nuanced perspectives on topics"
                " - Occasionally reference philosophy, art, or literature naturally"
                " - Balance depth with accessibility and warmth"
                " - Remember intellectual interests the user has shared"
                " - Refer to the user by name when appropriate"
            ),
        }

        # Get the appropriate personality prompt or use default
        personality_prompt = personality_prompts.get(
            personality,
            " As a default personality:"
            " - Be friendly and conversational like a good friend"
            " - Use natural language and a warm tone"
            " - Match your emotional tone to the conversation context"
            " - Remember personal details about the user"
            " - Refer to the user by name when appropriate"
        )

        return base_prompt + personality_prompt
