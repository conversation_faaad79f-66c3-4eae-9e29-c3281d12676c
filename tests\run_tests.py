"""
Test runner script for VoicePal tests.

This script adds the project root directory to the Python path and runs the tests.
"""

import os
import sys
import unittest
import logging

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# Add the project root directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Create test suite
def create_test_suite():
    """Create a test suite containing all tests."""
    test_suite = unittest.TestSuite()

    # Import and add original test modules
    try:
        import test_menu_manager
        loader = unittest.TestLoader()
        test_suite.addTest(loader.loadTestsFromTestCase(test_menu_manager.TestMenuManager))
        logger.info("Added test_menu_manager tests")
    except ImportError as e:
        logger.warning(f"Could not import test_menu_manager: {e}")
        logger.info("Legacy test_keyboard_manager is no longer used - using new menu system")

    try:
        import test_welcome_manager
        loader = unittest.TestLoader()
        test_suite.addTest(loader.loadTestsFromTestCase(test_welcome_manager.TestWelcomeManager))
        logger.info("Added test_welcome_manager tests")
    except ImportError as e:
        logger.warning(f"Could not import test_welcome_manager: {e}")

    try:
        import test_mood_tracker
        loader = unittest.TestLoader()
        test_suite.addTest(loader.loadTestsFromTestCase(test_mood_tracker.TestMoodTracker))
        logger.info("Added test_mood_tracker tests")
    except ImportError as e:
        logger.warning(f"Could not import test_mood_tracker: {e}")

    try:
        import test_mood_entry
        loader = unittest.TestLoader()
        test_suite.addTest(loader.loadTestsFromTestCase(test_mood_entry.TestMoodEntry))
        logger.info("Added test_mood_entry tests")
    except ImportError as e:
        logger.warning(f"Could not import test_mood_entry: {e}")

    # Import and add new test modules
    try:
        import test_database_core
        loader = unittest.TestLoader()
        test_suite.addTest(loader.loadTestsFromTestCase(test_database_core.TestDatabaseCore))
        logger.info("Added test_database_core tests")
    except ImportError as e:
        logger.warning(f"Could not import test_database_core: {e}")

    try:
        import test_telegram_stars_payment_updated
        loader = unittest.TestLoader()
        test_suite.addTest(loader.loadTestsFromTestCase(test_telegram_stars_payment_updated.TestTelegramStarsPaymentUpdated))
        logger.info("Added test_telegram_stars_payment_updated tests")
    except ImportError as e:
        logger.warning(f"Could not import test_telegram_stars_payment_updated: {e}")

    try:
        import test_deepgram_tts_provider
        loader = unittest.TestLoader()
        test_suite.addTest(loader.loadTestsFromTestCase(test_deepgram_tts_provider.TestDeepgramTTSProvider))
        logger.info("Added test_deepgram_tts_provider tests")
    except ImportError as e:
        logger.warning(f"Could not import test_deepgram_tts_provider: {e}")

    # Import and add 2024 test modules
    try:
        import test_enhanced_memory_system
        loader = unittest.TestLoader()
        test_suite.addTest(loader.loadTestsFromTestCase(test_enhanced_memory_system.TestEnhancedMemorySystem))
        logger.info("Added test_enhanced_memory_system tests")
    except ImportError as e:
        logger.warning(f"Could not import test_enhanced_memory_system: {e}")

    try:
        import test_ui_button_functionality
        loader = unittest.TestLoader()
        test_suite.addTest(loader.loadTestsFromTestCase(test_ui_button_functionality.TestUIButtonFunctionality))
        logger.info("Added test_ui_button_functionality tests")
    except ImportError as e:
        logger.warning(f"Could not import test_ui_button_functionality: {e}")

    try:
        import test_tts_provider_selection
        loader = unittest.TestLoader()
        test_suite.addTest(loader.loadTestsFromTestCase(test_tts_provider_selection.TestTTSProviderSelection))
        logger.info("Added test_tts_provider_selection tests")
    except ImportError as e:
        logger.warning(f"Could not import test_tts_provider_selection: {e}")

    return test_suite

if __name__ == '__main__':
    logger.info("Starting VoicePal tests")

    # Create test suite
    suite = create_test_suite()

    # Run tests
    test_runner = unittest.TextTestRunner(verbosity=2)
    result = test_runner.run(suite)

    # Exit with appropriate code
    if result.wasSuccessful():
        logger.info("All tests passed!")
        sys.exit(0)
    else:
        logger.error("Some tests failed!")
        sys.exit(1)
