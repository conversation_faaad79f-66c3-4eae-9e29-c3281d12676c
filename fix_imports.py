"""
Fix import paths and resolve issues in the VoicePal bot.

This script:
1. Updates import paths to match the new folder structure
2. Resolves duplicate implementations
3. Fixes any other issues found

Usage:
    python fix_imports.py
"""

import os
import re
import sys
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# Define import mappings (old import -> new import)
IMPORT_MAPPINGS = {
    # Provider imports
    "from bot.providers.ai.google_ai_provider import GoogleAIProvider": "from bot.providers.ai.google_ai_provider import GoogleAIProvider",
    "from bot.providers.tts.elevenlabs_provider import ElevenLabsProvider": "from bot.providers.tts.elevenlabs_provider import ElevenLabsProvider",
    "from bot.providers.voice.processor import VoiceProcessor": "from bot.providers.voice.processor import VoiceProcessor",

    # Database imports
    "from bot.database.extensions import extend_database_schema": "from bot.database.extensions import extend_database_schema",
    "from bot.database.extensions.payment import extend_database_for_payment": "from bot.database.extensions.payment import extend_database_for_payment",
    "from bot.database.extensions.sentiment import extend_database_for_sentiment": "from bot.database.extensions.sentiment import extend_database_for_sentiment",
    "from bot.database.extensions.memory import extend_database_for_memory": "from bot.database.extensions.memory import extend_database_for_memory",

    # Core imports
    "from bot.core.payment_system import PaymentSystem": "from bot.core.payment_system import PaymentSystem",
    "from bot.core.enhanced_dialog_engine import EnhancedDialogEngine as DialogEngine": "from bot.core.enhanced_dialog_engine import EnhancedDialogEngine as DialogEngine",
    "from bot.core.user_manager import UserManager": "from bot.core.user_manager import UserManager",

    # Feature imports
    "from bot.features.enhanced_memory_manager import EnhancedMemoryManager as MemoryManager": "from bot.features.enhanced_memory_manager import EnhancedMemoryManager as MemoryManager",
    "from bot.features.mood_tracker import MoodTracker": "from bot.features.mood_tracker import MoodTracker",
    "from bot.features.personalization_manager import PersonalizationManager": "from bot.features.personalization_manager import PersonalizationManager",
}

def fix_imports_in_file(file_path):
    """Fix imports in a single file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        original_content = content

        # Apply import mappings
        for old_import, new_import in IMPORT_MAPPINGS.items():
            content = content.replace(old_import, new_import)

        # Fix relative imports if needed
        if "from . import" in content or "from .." in content:
            # This is more complex and would need custom handling
            logger.warning(f"File {file_path} contains relative imports that may need manual review")

        # Only write back if changes were made
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            logger.info(f"Updated imports in {file_path}")
            return True
        return False
    except Exception as e:
        logger.error(f"Error fixing imports in {file_path}: {e}")
        return False

def fix_imports_in_directory(directory):
    """Fix imports in all Python files in a directory and its subdirectories."""
    fixed_files = 0
    for root, _, files in os.walk(directory):
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                if fix_imports_in_file(file_path):
                    fixed_files += 1
    return fixed_files

def check_for_duplicate_implementations():
    """Check for duplicate implementations and log warnings."""
    # Define patterns to check for duplicates
    duplicate_patterns = [
        ("Provider Factory", ["provider_factory.py"]),
        ("Database Extensions", ["database_extension.py", "database_extension_payment.py", "database_extension_sentiment.py", "database_extension_memory.py"]),
        ("Configuration", ["config.py", "config_manager.py"]),
        ("Voice Processing", ["voice_processing.py", "processor.py"]),
        ("Dialog Engine", ["dialog_engine.py", "enhanced_dialog_engine.py"]),
    ]

    for name, patterns in duplicate_patterns:
        found_files = []
        for pattern in patterns:
            for root, _, files in os.walk('.'):
                for file in files:
                    if file == pattern:
                        found_files.append(os.path.join(root, file))

        if len(found_files) > 1:
            logger.warning(f"Potential duplicate {name} implementations found:")
            for file in found_files:
                logger.warning(f"  - {file}")

def main():
    """Main function to fix imports and resolve issues."""
    logger.info("Starting import fixes for VoicePal bot...")

    # Check for duplicate implementations
    check_for_duplicate_implementations()

    # Fix imports in bot directory
    if os.path.exists('bot'):
        logger.info("Fixing imports in bot directory...")
        fixed_count = fix_imports_in_directory('bot')
        logger.info(f"Fixed imports in {fixed_count} files in bot directory")
    else:
        logger.error("Bot directory not found!")

    # Fix imports in scripts directory
    if os.path.exists('scripts'):
        logger.info("Fixing imports in scripts directory...")
        fixed_count = fix_imports_in_directory('scripts')
        logger.info(f"Fixed imports in {fixed_count} files in scripts directory")

    # Fix imports in root Python files
    logger.info("Fixing imports in root Python files...")
    fixed_count = 0
    for file in os.listdir('.'):
        if file.endswith('.py'):
            if fix_imports_in_file(file):
                fixed_count += 1
    logger.info(f"Fixed imports in {fixed_count} root Python files")

    logger.info("Import fixes completed!")
    logger.info("Please review the changes and test the bot.")
    logger.info("You may need to install the updated dependencies:")
    logger.info("  python -m pip install -r requirements.txt")

    return 0

if __name__ == "__main__":
    sys.exit(main())
