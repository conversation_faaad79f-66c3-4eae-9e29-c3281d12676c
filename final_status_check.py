#!/usr/bin/env python3
"""
Final Status Check - Quick Production Readiness Assessment

This script provides a quick assessment of your bot's production readiness.
"""

import os
import sys
import json
import asyncio
import httpx
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def quick_api_test():
    """Quick test of all APIs."""
    logger.info("🔍 Quick API Status Check...")
    
    # Load config
    try:
        with open('config.json', 'r') as f:
            config = json.load(f)
        
        # Set environment variables
        if 'telegram' in config:
            os.environ['BOT_TOKEN'] = config['telegram']['token']
        if 'providers' in config:
            providers = config['providers']
            if 'stt' in providers:
                os.environ['DEEPGRAM_API_KEY'] = providers['stt']['api_key']
            if 'ai' in providers:
                os.environ['GOOGLE_AI_API_KEY'] = providers['ai']['api_key']
    except:
        logger.error("❌ Could not load config.json")
        return False
    
    results = {}
    
    # Test Telegram Bot
    bot_token = os.getenv('BOT_TOKEN')
    if bot_token:
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(f"https://api.telegram.org/bot{bot_token}/getMe", timeout=10)
                if response.status_code == 200:
                    bot_info = response.json()['result']
                    results['telegram'] = f"✅ Bot: @{bot_info['username']}"
                else:
                    results['telegram'] = "❌ Invalid bot token"
        except:
            results['telegram'] = "❌ Telegram API error"
    else:
        results['telegram'] = "❌ No bot token"
    
    # Test Deepgram
    deepgram_key = os.getenv('DEEPGRAM_API_KEY')
    if deepgram_key:
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    "https://api.deepgram.com/v1/projects",
                    headers={"Authorization": f"Token {deepgram_key}"},
                    timeout=10
                )
                if response.status_code == 200:
                    results['deepgram'] = "✅ Voice processing ready"
                else:
                    results['deepgram'] = "❌ Invalid Deepgram key"
        except:
            results['deepgram'] = "❌ Deepgram API error"
    else:
        results['deepgram'] = "❌ No Deepgram key"
    
    # Test Google AI with correct model
    google_key = os.getenv('GOOGLE_AI_API_KEY')
    if google_key:
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key={google_key}",
                    json={
                        "contents": [{"parts": [{"text": "test"}]}],
                        "generationConfig": {"maxOutputTokens": 1}
                    },
                    timeout=15
                )
                if response.status_code == 200:
                    results['google_ai'] = "✅ AI responses ready"
                else:
                    results['google_ai'] = f"❌ Google AI error: {response.status_code}"
        except Exception as e:
            results['google_ai'] = f"❌ Google AI error: {e}"
    else:
        results['google_ai'] = "❌ No Google AI key"
    
    # Test ElevenLabs
    elevenlabs_key = os.getenv('ELEVENLABS_API_KEY')
    if elevenlabs_key:
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    "https://api.elevenlabs.io/v1/voices",
                    headers={"xi-api-key": elevenlabs_key},
                    timeout=10
                )
                if response.status_code == 200:
                    results['elevenlabs'] = "✅ Premium voice ready"
                else:
                    results['elevenlabs'] = "❌ Invalid ElevenLabs key"
        except:
            results['elevenlabs'] = "❌ ElevenLabs API error"
    else:
        results['elevenlabs'] = "❌ No ElevenLabs key"
    
    # Test webhook status
    if bot_token:
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(f"https://api.telegram.org/bot{bot_token}/getWebhookInfo", timeout=10)
                if response.status_code == 200:
                    webhook_info = response.json()['result']
                    webhook_url = webhook_info.get('url', '')
                    if webhook_url:
                        results['webhook'] = f"✅ Webhook: {webhook_url}"
                    else:
                        results['webhook'] = "⚠️ No webhook configured"
        except:
            results['webhook'] = "❌ Webhook check failed"
    
    return results

def analyze_readiness(results):
    """Analyze production readiness."""
    working = sum(1 for result in results.values() if result.startswith('✅'))
    total = len(results)
    
    critical_services = ['telegram', 'deepgram', 'google_ai']
    critical_working = sum(1 for service in critical_services if results.get(service, '').startswith('✅'))
    
    if critical_working == 3:
        status = "✅ PRODUCTION READY"
        percentage = 100
    elif critical_working == 2:
        status = "⚠️ MOSTLY READY"
        percentage = 85
    else:
        status = "❌ NOT READY"
        percentage = 50
    
    return status, percentage, working, total

def generate_deployment_instructions(status, results):
    """Generate deployment instructions."""
    instructions = []
    
    instructions.append("🚀 DEPLOYMENT STATUS")
    instructions.append("=" * 50)
    instructions.append(f"Status: {status}")
    instructions.append("")
    
    # Show service status
    for service, result in results.items():
        instructions.append(f"{result} {service.title()}")
    
    instructions.append("")
    
    if status.startswith("✅"):
        instructions.extend([
            "🎉 YOUR BOT IS READY FOR DEPLOYMENT!",
            "",
            "📋 NEXT STEPS:",
            "1. Push your code to GitHub",
            "2. Deploy to Render.com",
            "3. Set environment variables in Render:",
            "",
            "Environment Variables for Render:",
            f"BOT_TOKEN={os.getenv('BOT_TOKEN', 'your_bot_token')[:20]}...",
            f"DEEPGRAM_API_KEY={os.getenv('DEEPGRAM_API_KEY', 'your_deepgram_key')[:20]}...",
            f"GOOGLE_AI_API_KEY={os.getenv('GOOGLE_AI_API_KEY', 'your_google_key')[:20]}...",
            f"ELEVENLABS_API_KEY={os.getenv('ELEVENLABS_API_KEY', 'your_elevenlabs_key')[:20]}...",
            "ENVIRONMENT=production",
            "PORT=8443",
            "",
            "4. Test the deployment",
            "5. Start making money! 💰"
        ])
    else:
        instructions.extend([
            "🔧 ISSUES TO FIX:",
            ""
        ])
        
        for service, result in results.items():
            if not result.startswith('✅'):
                instructions.append(f"   • {service}: {result}")
        
        instructions.extend([
            "",
            "📋 REQUIRED ACTIONS:",
            "1. Fix the issues above",
            "2. Ensure all API keys are valid",
            "3. Run this script again to verify"
        ])
    
    return "\n".join(instructions)

async def main():
    """Main function."""
    print("🎯 MoneyMule Bot - Final Production Status Check")
    print("=" * 60)
    
    # Quick API test
    results = await quick_api_test()
    
    if not results:
        print("❌ Failed to run status check")
        return
    
    # Analyze readiness
    status, percentage, working, total = analyze_readiness(results)
    
    # Generate instructions
    instructions = generate_deployment_instructions(status, results)
    
    # Print results
    print(f"\n{instructions}")
    
    print(f"\n📊 SUMMARY:")
    print(f"Working Services: {working}/{total}")
    print(f"Readiness: {percentage}%")
    
    # Save report
    report = {
        'status': status,
        'percentage': percentage,
        'services': results,
        'instructions': instructions
    }
    
    with open('final_status_report.json', 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"\n📄 Report saved to: final_status_report.json")
    
    if status.startswith("✅"):
        print("\n🎉 CONGRATULATIONS! Your bot is ready for production!")
        print("💰 Deploy now and start making money!")
    else:
        print(f"\n🔧 Please fix the issues above and run again.")

if __name__ == "__main__":
    asyncio.run(main())
