"""
Security audit logging integration for VoicePal.

This module integrates the security audit logging system with the VoicePal bot.
"""

import logging
import asyncio
import functools
import inspect
from typing import Dict, Any, Optional, List, Tuple, Callable
from datetime import datetime

from telegram import Update
from telegram.ext import <PERSON><PERSON><PERSON><PERSON>s, CommandHandler, CallbackQueryHandler

from bot.core.security_audit import (
    SecurityAudit, 
    SEVERITY_INFO, SEVERITY_LOW, SEVERITY_MEDIUM, SEVERITY_HIGH, SEVE<PERSON><PERSON>_CRITICAL,
    CATEGORY_AUTH, CATEGORY_ACCESS, CATEGORY_PAYMENT, CATEGORY_ABUSE, 
    CATEGORY_RATE_LIMIT, CATEGORY_API, CATEG<PERSON>Y_SYSTEM, CATEGORY_DATA,
    CATEGORY_ADMIN, CATEGORY_USER
)

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

def integrate_security_audit(bot_instance):
    """
    Integrate security audit logging with the VoicePal bot.
    
    Args:
        bot_instance: VoicePalBot instance
    """
    # Initialize security audit logger
    bot_instance.security_audit = SecurityAudit(
        database=bot_instance.database,
        config_manager=bot_instance.config_manager
    )
    
    # Register security audit command handlers
    bot_instance.application.add_handler(
        CommandHandler("auditlogs", audit_logs_command, filters=bot_instance.admin_filter)
    )
    
    # Register callback query handlers for audit logs
    bot_instance.application.add_handler(
        CallbackQueryHandler(handle_audit_logs_callback, pattern=r"^audit_")
    )
    
    # Set up periodic audit log cleanup
    asyncio.create_task(periodic_audit_cleanup(bot_instance))
    
    # Patch key methods to add security audit logging
    patch_methods(bot_instance)
    
    logger.info("Security audit logging integrated with VoicePal bot")

def audit_decorator(event_type: str, category: str, severity: str = SEVERITY_INFO):
    """
    Decorator for adding security audit logging to methods.
    
    Args:
        event_type: Type of security event
        category: Category of security event
        severity: Severity level of event
        
    Returns:
        Decorator function
    """
    def decorator(func):
        @functools.wraps(func)
        async def wrapper(self, update: Update, context: ContextTypes.DEFAULT_TYPE, *args, **kwargs):
            # Get user ID if available
            user_id = None
            if update and update.effective_user:
                user_id = update.effective_user.id
            
            # Get IP address if available
            ip_address = None
            if hasattr(self, "get_user_ip") and callable(self.get_user_ip):
                ip_address = self.get_user_ip(update)
            
            # Get device ID if available
            device_id = None
            if hasattr(self, "get_user_device_id") and callable(self.get_user_device_id):
                device_id = self.get_user_device_id(update)
            
            # Get function name and module
            source = f"{func.__module__}.{func.__name__}"
            
            # Log audit event before function execution
            try:
                self.security_audit.log_event(
                    event_type=f"{event_type}_start",
                    category=category,
                    severity=severity,
                    user_id=user_id,
                    ip_address=ip_address,
                    device_id=device_id,
                    description=f"Started {event_type} operation",
                    source=source
                )
            except Exception as e:
                logger.error(f"Error logging audit event: {e}")
            
            # Execute function
            try:
                result = await func(self, update, context, *args, **kwargs)
                
                # Log successful audit event
                try:
                    self.security_audit.log_event(
                        event_type=f"{event_type}_success",
                        category=category,
                        severity=severity,
                        user_id=user_id,
                        ip_address=ip_address,
                        device_id=device_id,
                        description=f"Completed {event_type} operation",
                        source=source,
                        success=True
                    )
                except Exception as e:
                    logger.error(f"Error logging audit event: {e}")
                
                return result
            except Exception as e:
                # Log failed audit event
                try:
                    self.security_audit.log_event(
                        event_type=f"{event_type}_failure",
                        category=category,
                        severity=SEVERITY_MEDIUM,  # Increase severity for failures
                        user_id=user_id,
                        ip_address=ip_address,
                        device_id=device_id,
                        description=f"Failed {event_type} operation: {str(e)}",
                        details={"error": str(e), "error_type": type(e).__name__},
                        source=source,
                        success=False
                    )
                except Exception as audit_error:
                    logger.error(f"Error logging audit event: {audit_error}")
                
                # Re-raise the original exception
                raise
        
        return wrapper
    
    return decorator

def patch_methods(bot_instance):
    """
    Patch key methods to add security audit logging.
    
    Args:
        bot_instance: VoicePalBot instance
    """
    # Define methods to patch with their audit parameters
    methods_to_patch = {
        # User authentication and management
        "start_command": ("user_start", CATEGORY_USER),
        "help_command": ("user_help", CATEGORY_USER),
        "credits_command": ("user_credits", CATEGORY_USER),
        "buy_command": ("user_buy", CATEGORY_PAYMENT),
        "personality_command": ("user_personality", CATEGORY_USER),
        
        # Payment handling
        "handle_pre_checkout": ("payment_pre_checkout", CATEGORY_PAYMENT, SEVERITY_MEDIUM),
        "handle_successful_payment": ("payment_successful", CATEGORY_PAYMENT, SEVERITY_MEDIUM),
        
        # Message handling
        "handle_text": ("message_text", CATEGORY_USER),
        "handle_voice": ("message_voice", CATEGORY_USER),
        
        # Admin commands
        "stats_command": ("admin_stats", CATEGORY_ADMIN, SEVERITY_MEDIUM),
        "add_credits_command": ("admin_add_credits", CATEGORY_ADMIN, SEVERITY_HIGH),
        
        # Error handling
        "error_handler": ("error_handler", CATEGORY_SYSTEM, SEVERITY_MEDIUM)
    }
    
    # Patch each method
    for method_name, audit_params in methods_to_patch.items():
        if hasattr(bot_instance, method_name) and callable(getattr(bot_instance, method_name)):
            original_method = getattr(bot_instance, method_name)
            
            # Apply audit decorator with appropriate parameters
            if len(audit_params) == 2:
                event_type, category = audit_params
                decorated_method = audit_decorator(event_type, category)(original_method)
            else:
                event_type, category, severity = audit_params
                decorated_method = audit_decorator(event_type, category, severity)(original_method)
            
            # Replace original method with decorated version
            setattr(bot_instance, method_name, decorated_method)
            logger.debug(f"Patched {method_name} with security audit logging")

async def periodic_audit_cleanup(bot_instance):
    """
    Run periodic cleanup of old audit logs.
    
    Args:
        bot_instance: VoicePalBot instance
    """
    while True:
        try:
            # Wait for 24 hours between cleanups
            await asyncio.sleep(24 * 60 * 60)
            
            # Clean up old logs
            deleted_count = bot_instance.security_audit.cleanup_old_logs()
            
            # Log cleanup
            if deleted_count > 0:
                logger.info(f"Cleaned up {deleted_count} old security audit logs")
        except Exception as e:
            logger.error(f"Error in periodic audit cleanup: {e}")
            # Wait a bit before retrying
            await asyncio.sleep(60)

async def audit_logs_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """
    Handle the /auditlogs command (admin only).
    
    Args:
        update: Update object
        context: Context object
    """
    user_id = update.effective_user.id
    bot_instance = context.bot_data.get("bot_instance")
    
    if not bot_instance or not hasattr(bot_instance, "security_audit"):
        await update.message.reply_text("Security audit logging is not available.")
        return
    
    # Check if user is admin
    admin_ids = bot_instance.config_manager.get_admin_user_ids()
    if user_id not in admin_ids:
        await update.message.reply_text("This command is only available to administrators.")
        return
    
    # Show audit logs dashboard
    await show_audit_logs_dashboard(update, context)

async def show_audit_logs_dashboard(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """
    Show the audit logs dashboard.
    
    Args:
        update: Update object
        context: Context object
    """
    from telegram import InlineKeyboardButton, InlineKeyboardMarkup
    
    bot_instance = context.bot_data.get("bot_instance")
    
    if not bot_instance or not hasattr(bot_instance, "security_audit"):
        await update.message.reply_text("Security audit logging is not available.")
        return
    
    # Get recent audit logs
    recent_logs = bot_instance.security_audit.get_audit_logs(limit=10)
    
    # Get audit log counts by category
    cursor = bot_instance.database.conn.cursor()
    cursor.execute("""
        SELECT category, COUNT(*) as count
        FROM security_audit_logs
        GROUP BY category
        ORDER BY count DESC
    """)
    
    category_counts = cursor.fetchall()
    
    # Format dashboard message
    message = (
        "📋 *Security Audit Logs* 📋\n\n"
        "*Recent Activity:*\n"
    )
    
    if recent_logs:
        for log in recent_logs[:5]:  # Show only the 5 most recent logs
            severity_emoji = {
                SEVERITY_INFO: "ℹ️",
                SEVERITY_LOW: "🔵",
                SEVERITY_MEDIUM: "🟡",
                SEVERITY_HIGH: "🔴",
                SEVERITY_CRITICAL: "⚠️"
            }.get(log['severity'], "🔔")
            
            log_time = datetime.fromisoformat(log['timestamp']).strftime("%Y-%m-%d %H:%M")
            message += f"{severity_emoji} {log_time} - {log['event_type']}"
            
            if log['user_id']:
                message += f" (User: {log['user_id']})"
            
            message += "\n"
    else:
        message += "No recent audit logs.\n"
    
    message += "\n*Logs by Category:*\n"
    
    if category_counts:
        for category in category_counts:
            category_name = category['category']
            count = category['count']
            message += f"- {category_name}: {count} logs\n"
    else:
        message += "No audit logs found.\n"
    
    # Create dashboard keyboard
    keyboard = [
        [
            InlineKeyboardButton("View All Logs", callback_data="audit_all_logs"),
            InlineKeyboardButton("Filter by Category", callback_data="audit_filter_category")
        ],
        [
            InlineKeyboardButton("Filter by User", callback_data="audit_filter_user"),
            InlineKeyboardButton("Filter by Severity", callback_data="audit_filter_severity")
        ],
        [
            InlineKeyboardButton("Export Logs", callback_data="audit_export"),
            InlineKeyboardButton("Refresh", callback_data="audit_refresh")
        ]
    ]
    
    reply_markup = InlineKeyboardMarkup(keyboard)
    
    # Send or edit message
    if update.callback_query:
        await update.callback_query.edit_message_text(
            text=message,
            reply_markup=reply_markup,
            parse_mode="Markdown"
        )
    else:
        await update.message.reply_text(
            text=message,
            reply_markup=reply_markup,
            parse_mode="Markdown"
        )

async def handle_audit_logs_callback(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """
    Handle audit logs dashboard callback queries.
    
    Args:
        update: Update object
        context: Context object
    """
    from telegram import InlineKeyboardButton, InlineKeyboardMarkup
    
    query = update.callback_query
    await query.answer()
    
    callback_data = query.data
    bot_instance = context.bot_data.get("bot_instance")
    
    if not bot_instance or not hasattr(bot_instance, "security_audit"):
        await query.edit_message_text("Security audit logging is not available.")
        return
    
    if callback_data == "audit_refresh":
        await show_audit_logs_dashboard(update, context)
    elif callback_data == "audit_all_logs":
        # Show all logs with pagination
        context.user_data["audit_page"] = 0
        await show_all_audit_logs(update, context)
    elif callback_data == "audit_filter_category":
        await show_category_filter(update, context)
    elif callback_data == "audit_filter_user":
        await show_user_filter(update, context)
    elif callback_data == "audit_filter_severity":
        await show_severity_filter(update, context)
    elif callback_data == "audit_export":
        await show_export_options(update, context)
    elif callback_data.startswith("audit_page_"):
        # Handle pagination
        page = int(callback_data.replace("audit_page_", ""))
        context.user_data["audit_page"] = page
        await show_all_audit_logs(update, context)
    elif callback_data.startswith("audit_category_"):
        # Handle category filter
        category = callback_data.replace("audit_category_", "")
        context.user_data["audit_filter_category"] = category
        context.user_data["audit_page"] = 0
        await show_filtered_audit_logs(update, context)
    elif callback_data.startswith("audit_severity_"):
        # Handle severity filter
        severity = callback_data.replace("audit_severity_", "")
        context.user_data["audit_filter_severity"] = severity
        context.user_data["audit_page"] = 0
        await show_filtered_audit_logs(update, context)
    elif callback_data == "audit_back":
        # Go back to dashboard
        await show_audit_logs_dashboard(update, context)
    else:
        await query.edit_message_text(
            "Unknown audit logs option.",
            reply_markup=InlineKeyboardMarkup([[
                InlineKeyboardButton("Back to Dashboard", callback_data="audit_back")
            ]])
        )

# Additional dashboard view functions will be implemented in the next task
