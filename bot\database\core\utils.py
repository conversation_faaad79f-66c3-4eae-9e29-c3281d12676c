"""
Database utility functions for VoicePal.

This module provides utility functions for database operations.
"""

import logging
import sqlite3
from typing import Dict, List, Any, Optional, Tuple, Union

# Set up logging
logger = logging.getLogger(__name__)

def dict_to_sql_params(data: Dict[str, Any]) -> Tuple[str, Tuple[Any, ...]]:
    """Convert a dictionary to SQL parameters for INSERT or UPDATE.
    
    Args:
        data: Dictionary of column names and values
        
    Returns:
        Tuple of (column_names, placeholders, values) for INSERT
        or (set_clause, values) for UPDATE
    """
    columns = []
    placeholders = []
    values = []
    
    for column, value in data.items():
        columns.append(column)
        placeholders.append('?')
        values.append(value)
    
    return (
        ', '.join(columns),
        ', '.join(placeholders),
        tuple(values)
    )

def build_insert_query(table: str, data: Dict[str, Any]) -> Tuple[str, Tuple[Any, ...]]:
    """Build an INSERT query from a dictionary.
    
    Args:
        table: Table name
        data: Dictionary of column names and values
        
    Returns:
        Tuple of (query, values)
    """
    columns, placeholders, values = dict_to_sql_params(data)
    
    query = f"INSERT INTO {table} ({columns}) VALUES ({placeholders})"
    
    return query, values

def build_update_query(table: str, data: Dict[str, Any], where: Dict[str, Any]) -> Tuple[str, Tuple[Any, ...]]:
    """Build an UPDATE query from dictionaries.
    
    Args:
        table: Table name
        data: Dictionary of column names and values to update
        where: Dictionary of column names and values for WHERE clause
        
    Returns:
        Tuple of (query, values)
    """
    set_clauses = []
    values = []
    
    for column, value in data.items():
        set_clauses.append(f"{column} = ?")
        values.append(value)
    
    where_clauses = []
    
    for column, value in where.items():
        where_clauses.append(f"{column} = ?")
        values.append(value)
    
    query = f"UPDATE {table} SET {', '.join(set_clauses)} WHERE {' AND '.join(where_clauses)}"
    
    return query, tuple(values)

def build_select_query(table: str, columns: Optional[List[str]] = None, 
                      where: Optional[Dict[str, Any]] = None,
                      order_by: Optional[str] = None,
                      limit: Optional[int] = None,
                      offset: Optional[int] = None) -> Tuple[str, Tuple[Any, ...]]:
    """Build a SELECT query.
    
    Args:
        table: Table name
        columns: List of columns to select (None for *)
        where: Dictionary of column names and values for WHERE clause
        order_by: ORDER BY clause
        limit: LIMIT clause
        offset: OFFSET clause
        
    Returns:
        Tuple of (query, values)
    """
    cols = ', '.join(columns) if columns else '*'
    query = f"SELECT {cols} FROM {table}"
    values = []
    
    if where:
        where_clauses = []
        
        for column, value in where.items():
            where_clauses.append(f"{column} = ?")
            values.append(value)
        
        query += f" WHERE {' AND '.join(where_clauses)}"
    
    if order_by:
        query += f" ORDER BY {order_by}"
    
    if limit is not None:
        query += f" LIMIT {limit}"
    
    if offset is not None:
        query += f" OFFSET {offset}"
    
    return query, tuple(values)

def row_to_dict(row: sqlite3.Row) -> Dict[str, Any]:
    """Convert a sqlite3.Row to a dictionary.
    
    Args:
        row: SQLite row
        
    Returns:
        Dictionary representation of the row
    """
    return dict(row)

def rows_to_list(rows: List[sqlite3.Row]) -> List[Dict[str, Any]]:
    """Convert a list of sqlite3.Row to a list of dictionaries.
    
    Args:
        rows: List of SQLite rows
        
    Returns:
        List of dictionaries
    """
    return [row_to_dict(row) for row in rows]
