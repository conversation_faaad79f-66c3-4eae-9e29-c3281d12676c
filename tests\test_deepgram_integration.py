"""
Enhanced test script for Deepgram integration.

This script tests the Deepgram integration in the VoiceProcessor class with various
models and parameters to optimize speech-to-text accuracy.

Usage:
    python test_deepgram_integration.py
"""

import os
import asyncio
import unittest
import tempfile
import wave
import struct
import numpy as np
import logging
import json
from pathlib import Path
from gtts import gTTS
from difflib import SequenceMatcher

# Import the VoiceProcessor class
from bot.providers.voice.processor import VoiceProcessor

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# Test phrases with known text
TEST_PHRASES = [
    "Hello, this is a test of the speech recognition system.",
    "VoicePal is a Telegram bot that provides companionship through conversation.",
    "The weather today is sunny with a chance of rain later.",
    "I'm feeling a bit tired today, but otherwise I'm doing well.",
    "Can you tell me about your day? I'd love to hear how you're doing."
]

# Deepgram models to test
DEEPGRAM_MODELS = [
    "nova-3",
    "nova-2",
    "enhanced"
]

# Deepgram parameters to test
DEEPGRAM_PARAMS = [
    {"smart_format": True},
    {"smart_format": True, "diarize": True},
    {"smart_format": True, "detect_topics": True},
    {"smart_format": True, "punctuate": True},
    {"smart_format": True, "diarize": True, "detect_topics": True, "punctuate": True}
]

class TestDeepgramIntegration(unittest.TestCase):
    """Enhanced test cases for Deepgram integration."""

    def setUp(self):
        """Set up test environment."""
        # Use the provided Deepgram API key
        self.api_key = "****************************************"

        # Create a voice processor instance with the API key
        self.voice_processor = VoiceProcessor(deepgram_api_key=self.api_key)

        # Create test audio files
        self.test_audio_files = []
        for phrase in TEST_PHRASES:
            audio_path = self._create_speech_audio_file(phrase)
            self.test_audio_files.append((audio_path, phrase))

        # Create a simple sine wave audio file
        self.sine_wave_audio_path = self._create_sine_wave_audio_file()

    def tearDown(self):
        """Clean up after tests."""
        # Clean up test audio files
        for audio_path, _ in self.test_audio_files:
            if os.path.exists(audio_path):
                os.remove(audio_path)

        # Clean up sine wave audio file
        if hasattr(self, 'sine_wave_audio_path') and os.path.exists(self.sine_wave_audio_path):
            os.remove(self.sine_wave_audio_path)

    def _create_speech_audio_file(self, text):
        """Create a test audio file with speech using gTTS."""
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".mp3")
        temp_file_path = temp_file.name
        temp_file.close()

        # Generate speech
        tts = gTTS(text=text, lang='en', slow=False)
        tts.save(temp_file_path)

        return temp_file_path

    def _create_sine_wave_audio_file(self):
        """Create a simple test audio file with a sine wave."""
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".wav")
        temp_file_path = temp_file.name
        temp_file.close()

        # Audio parameters
        duration = 1  # seconds
        sample_rate = 16000  # Hz
        frequency = 440  # Hz (A4 note)

        # Generate sine wave
        t = np.linspace(0, duration, int(sample_rate * duration), endpoint=False)
        audio_data = (32767 * 0.5 * np.sin(2 * np.pi * frequency * t)).astype(np.int16)

        # Write to WAV file
        with wave.open(temp_file_path, 'wb') as wav_file:
            wav_file.setnchannels(1)  # Mono
            wav_file.setsampwidth(2)  # 16-bit
            wav_file.setframerate(sample_rate)
            wav_file.writeframes(audio_data.tobytes())

        return temp_file_path

    def _calculate_similarity(self, text1, text2):
        """Calculate similarity between two strings."""
        return SequenceMatcher(None, text1.lower(), text2.lower()).ratio()

    def test_deepgram_initialization(self):
        """Test Deepgram client initialization."""
        # Check if Deepgram client was initialized
        self.assertIsNotNone(self.voice_processor.deepgram)

    def test_transcribe_sine_wave(self):
        """Test transcribing a sine wave (should return empty or minimal text)."""
        # Run the async test
        result = asyncio.run(self._async_test_transcribe_sine_wave())

        # Sine wave should return empty or minimal text
        self.assertTrue(result is None or result.strip() == "" or len(result) < 10)

    async def _async_test_transcribe_sine_wave(self):
        """Async helper for testing sine wave transcription."""
        # Transcribe audio
        transcript = await self.voice_processor.transcribe_audio(self.sine_wave_audio_path)
        return transcript

    def test_transcribe_speech(self):
        """Test transcribing speech with default parameters."""
        # Run the async test
        results = asyncio.run(self._async_test_transcribe_speech())

        # Check results
        for original, transcript, similarity in results:
            logger.info("Original: '%s'", original)
            logger.info("Transcript: '%s'", transcript)
            logger.info("Similarity: %.2f", similarity)

            # Check if transcription was successful
            self.assertIsNotNone(transcript)

            # Check if similarity is above threshold
            self.assertGreater(similarity, 0.5)  # 50% similarity threshold

    async def _async_test_transcribe_speech(self):
        """Async helper for testing speech transcription."""
        results = []

        for audio_path, original_text in self.test_audio_files:
            # Transcribe audio
            transcript = await self.voice_processor.transcribe_audio(audio_path)

            # Calculate similarity
            similarity = self._calculate_similarity(original_text, transcript) if transcript else 0

            results.append((original_text, transcript, similarity))

        return results

    def test_compare_models(self):
        """Test and compare different Deepgram models."""
        # Run the async test
        results = asyncio.run(self._async_test_compare_models())

        # Log and check results
        logger.info("\n=== MODEL COMPARISON RESULTS ===")

        for model, model_results in results.items():
            avg_similarity = sum(s for _, _, s in model_results) / len(model_results)
            logger.info("\nModel: %s", model)
            logger.info("Average similarity: %.2f", avg_similarity)

            for original, transcript, similarity in model_results:
                logger.info("  Original: '%s'", original)
                logger.info("  Transcript: '%s'", transcript)
                logger.info("  Similarity: %.2f", similarity)

            # Check if average similarity is above threshold
            self.assertGreater(avg_similarity, 0.5)  # 50% similarity threshold

    async def _async_test_compare_models(self):
        """Async helper for comparing different models."""
        results = {model: [] for model in DEEPGRAM_MODELS}

        # Test first audio file with different models
        audio_path, original_text = self.test_audio_files[0]

        for model in DEEPGRAM_MODELS:
            # Transcribe audio with specific model
            transcript = await self.voice_processor.transcribe_audio(
                audio_path, model=model
            )

            # Calculate similarity
            similarity = self._calculate_similarity(original_text, transcript) if transcript else 0

            results[model].append((original_text, transcript, similarity))

        return results

    def test_compare_parameters(self):
        """Test and compare different Deepgram parameters."""
        # Run the async test
        results = asyncio.run(self._async_test_compare_parameters())

        # Log and check results
        logger.info("\n=== PARAMETER COMPARISON RESULTS ===")

        for i, (params, param_results) in enumerate(results.items()):
            avg_similarity = sum(s for _, _, s in param_results) / len(param_results)
            logger.info("\nParameter set %d: %s", i+1, params)
            logger.info("Average similarity: %.2f", avg_similarity)

            for original, transcript, similarity in param_results:
                logger.info("  Original: '%s'", original)
                logger.info("  Transcript: '%s'", transcript)
                logger.info("  Similarity: %.2f", similarity)

            # Check if average similarity is above threshold
            self.assertGreater(avg_similarity, 0.5)  # 50% similarity threshold

    async def _async_test_compare_parameters(self):
        """Async helper for comparing different parameters."""
        results = {json.dumps(params): [] for params in DEEPGRAM_PARAMS}

        # Test first audio file with different parameters
        audio_path, original_text = self.test_audio_files[0]

        for params in DEEPGRAM_PARAMS:
            # Transcribe audio with specific parameters
            transcript = await self.voice_processor.transcribe_audio_with_params(
                audio_path, **params
            )

            # Calculate similarity
            similarity = self._calculate_similarity(original_text, transcript) if transcript else 0

            results[json.dumps(params)].append((original_text, transcript, similarity))

        return results

if __name__ == "__main__":
    unittest.main()
