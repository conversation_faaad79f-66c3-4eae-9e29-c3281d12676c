# VoicePal Bot - Production Environment Configuration
# =================================================
# Copy this file to .env and fill in your actual values

# Telegram Bot Configuration
TELEGRAM_TOKEN=your_telegram_bot_token_here
TELEGRAM_ADMIN_ID=616696346

# API Keys for Voice and AI Services
DEEPGRAM_API_KEY=your_deepgram_api_key_here
GOOGLE_AI_API_KEY=your_google_ai_api_key_here
ELEVENLABS_API_KEY=your_elevenlabs_api_key_here
GROQ_API_KEY=your_groq_api_key_here

# Webhook Configuration (for production deployment)
WEBHOOK_URL=https://your-app-name.onrender.com/webhook
PORT=8443

# Platform-specific Environment Variables
# Render.com
RENDER=true
RENDER_SERVICE_NAME=your-service-name

# <PERSON>ku (if using <PERSON>ku instead)
# HEROKU=true
# HEROKU_APP_NAME=your-app-name

# Database Configuration
DATABASE_URL=sqlite:///voicepal.db

# Feature Flags
ENABLE_MEMORY=true
ENABLE_MOOD_TRACKING=true
ENABLE_PERSONALIZATION=true
ENABLE_SECURITY_MONITORING=true
ENABLE_SENTIMENT_ANALYSIS=true

# Payment Configuration
PAYMENT_PROVIDER=telegram_stars
PAYMENT_PROVIDER_TOKEN=your_payment_provider_token_here

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=voicepal.log

# Performance Configuration
MAX_WORKERS=4
CONVERSATION_MEMORY_LIMIT=20
CACHE_TTL_MINUTES=5

# Security Configuration
RATE_LIMIT_ENABLED=true
MAX_REQUESTS_PER_MINUTE=30
SECURITY_AUDIT_ENABLED=true

# Voice Configuration
DEFAULT_TTS_PROVIDER=deepgram
DEFAULT_VOICE_ID=aura-thalia-en
DEFAULT_LANGUAGE=en

# AI Configuration
DEFAULT_AI_MODEL=gemini-2.0-flash
AI_TEMPERATURE=0.7
MAX_TOKENS=1000
