"""
Secure Provider Factory for VoicePal.

This module provides a factory for creating secure providers with API keys.
"""

import logging
from typing import Dict, Any, Optional, Type

from bot.core.key_manager import KeyManager
from bot.providers.ai.ai_provider import AIProvider
from bot.providers.ai.google_ai_provider import GoogleAIProvider
from bot.providers.ai.groq_ai_provider import GroqAIProvider
from bot.providers.stt_provider_interface import STTProviderInterface
from bot.providers.stt.deepgram_provider import DeepgramProvider
from bot.providers.tts_provider_interface import TTSProviderInterface
from bot.providers.tts.elevenlabs_provider import ElevenLabsProvider
from bot.providers.tts.google_provider import GoogleTTSProvider
from bot.providers.tts.deepgram_tts_provider import DeepgramTTSProvider

# Set up logging
logger = logging.getLogger(__name__)

class SecureProviderFactory:
    """Factory for creating secure providers with API keys."""

    def __init__(self, key_manager: KeyManager):
        """
        Initialize the secure provider factory.

        Args:
            key_manager: Key manager for retrieving API keys
        """
        self.key_manager = key_manager
        self.ai_providers = {
            "google_ai": GoogleAIProvider,
            "groq": GroqAIProvider
        }
        self.stt_providers = {
            "deepgram": DeepgramProvider
        }
        self.tts_providers = {
            "elevenlabs": ElevenLabsProvider,
            "google_tts": GoogleTTSProvider,
            "deepgram": DeepgramTTSProvider
        }

    def create_ai_provider(self, provider_type: str, **kwargs) -> AIProvider:
        """
        Create an AI provider with secure API key.

        Args:
            provider_type: Type of AI provider
            **kwargs: Additional provider options

        Returns:
            AIProvider instance

        Raises:
            ValueError: If provider type is not supported
        """
        if provider_type not in self.ai_providers:
            logger.error(f"Unsupported AI provider type: {provider_type}")
            logger.info(f"Falling back to default AI provider: google_ai")
            provider_type = "google_ai"

        provider_class = self.ai_providers[provider_type]

        # Get API key from key manager
        api_key = self.key_manager.get_key(provider_type)

        # If key not found in key manager, try to get from kwargs
        if not api_key and "api_key" in kwargs:
            api_key = kwargs["api_key"]
            # Store in key manager for future use
            if api_key:
                self.key_manager.store_key(provider_type, api_key)

        # Create provider with API key
        if api_key:
            kwargs["api_key"] = api_key

        logger.info(f"Creating AI provider: {provider_type}")
        return provider_class(**kwargs)

    def create_stt_provider(self, provider_type: str, **kwargs) -> STTProviderInterface:
        """
        Create an STT provider with secure API key.

        Args:
            provider_type: Type of STT provider
            **kwargs: Additional provider options

        Returns:
            STTProviderInterface instance

        Raises:
            ValueError: If provider type is not supported
        """
        if provider_type not in self.stt_providers:
            logger.error(f"Unsupported STT provider type: {provider_type}")
            logger.info(f"Falling back to default STT provider: deepgram")
            provider_type = "deepgram"

        provider_class = self.stt_providers[provider_type]

        # Get API key from key manager
        api_key = self.key_manager.get_key(provider_type)

        # If key not found in key manager, try to get from kwargs
        if not api_key and "api_key" in kwargs:
            api_key = kwargs["api_key"]
            # Store in key manager for future use
            if api_key:
                self.key_manager.store_key(provider_type, api_key)

        # Create provider with API key
        if api_key:
            kwargs["api_key"] = api_key

        logger.info(f"Creating STT provider: {provider_type}")
        return provider_class(**kwargs)

    def create_tts_provider(self, provider_type: str, **kwargs) -> TTSProviderInterface:
        """
        Create a TTS provider with secure API key.

        Args:
            provider_type: Type of TTS provider
            **kwargs: Additional provider options

        Returns:
            TTSProviderInterface instance

        Raises:
            ValueError: If provider type is not supported
        """
        # Always prioritize Deepgram for TTS
        if provider_type != "deepgram":
            logger.info(f"Prioritizing Deepgram TTS over {provider_type}")
            provider_type = "deepgram"

        if provider_type not in self.tts_providers:
            logger.error(f"Unsupported TTS provider type: {provider_type}")
            logger.info(f"Falling back to default TTS provider: deepgram")
            provider_type = "deepgram"

        provider_class = self.tts_providers[provider_type]

        # Get API key from key manager
        api_key = self.key_manager.get_key(provider_type)

        # If key not found in key manager, try to get from kwargs
        if not api_key and "api_key" in kwargs:
            api_key = kwargs["api_key"]
            # Store in key manager for future use
            if api_key:
                self.key_manager.store_key(provider_type, api_key)

        # Create provider with API key
        if api_key:
            kwargs["api_key"] = api_key

        logger.info(f"Creating TTS provider: {provider_type}")
        return provider_class(**kwargs)
