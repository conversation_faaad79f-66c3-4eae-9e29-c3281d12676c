"""
Sc<PERSON>t to optimize database operations.

This script performs various optimizations on the database to improve performance
and fix potential issues.
"""

import os
import sys
import logging
import sqlite3
from datetime import datetime

# Add parent directory to path to import bot modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import bot modules
from bot.database.core import Database

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

def backup_database(db_path):
    """
    Create a backup of the database.
    
    Args:
        db_path: Path to the database file
        
    Returns:
        str: Path to the backup file
    """
    backup_path = f"{db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    try:
        # Copy the database file
        with open(db_path, 'rb') as src, open(backup_path, 'wb') as dst:
            dst.write(src.read())
        logger.info(f"Database backup created at {backup_path}")
        return backup_path
    except Exception as e:
        logger.error(f"Error creating database backup: {e}")
        raise

def optimize_database(db_path):
    """
    Optimize database operations.
    
    Args:
        db_path: Path to the database file
    """
    try:
        # Create a backup first
        backup_path = backup_database(db_path)
        
        # Connect to the database
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # Enable foreign keys
        cursor.execute("PRAGMA foreign_keys = ON")
        
        # Check foreign key constraints
        logger.info("Checking foreign key constraints...")
        cursor.execute("PRAGMA foreign_key_check")
        fk_violations = cursor.fetchall()
        
        if fk_violations:
            logger.warning(f"Found {len(fk_violations)} foreign key constraint violations")
            for violation in fk_violations:
                logger.warning(f"Violation: {dict(violation)}")
            
            # Fix foreign key violations
            logger.info("Fixing foreign key violations...")
            for violation in fk_violations:
                table = violation['table']
                rowid = violation['rowid']
                
                # Get the row data
                cursor.execute(f"SELECT * FROM {table} WHERE rowid = ?", (rowid,))
                row = cursor.fetchone()
                
                if row:
                    logger.info(f"Deleting row with invalid foreign key: {dict(row)}")
                    cursor.execute(f"DELETE FROM {table} WHERE rowid = ?", (rowid,))
            
            conn.commit()
            logger.info("Foreign key violations fixed")
        else:
            logger.info("No foreign key constraint violations found")
        
        # Analyze the database
        logger.info("Analyzing database...")
        cursor.execute("ANALYZE")
        
        # Optimize the database
        logger.info("Optimizing database...")
        cursor.execute("VACUUM")
        
        # Rebuild indexes
        logger.info("Rebuilding indexes...")
        cursor.execute("REINDEX")
        
        # Check database integrity
        logger.info("Checking database integrity...")
        cursor.execute("PRAGMA integrity_check")
        integrity_check = cursor.fetchone()[0]
        
        if integrity_check == "ok":
            logger.info("Database integrity check passed")
        else:
            logger.warning(f"Database integrity check failed: {integrity_check}")
        
        # Close connection
        conn.close()
        
        logger.info("Database optimization completed successfully")
    except Exception as e:
        logger.error(f"Error optimizing database: {e}")
        import traceback
        logger.error(traceback.format_exc())
        
        # Try to restore from backup if something went wrong
        if 'backup_path' in locals():
            try:
                logger.info(f"Attempting to restore from backup {backup_path}")
                with open(backup_path, 'rb') as src, open(db_path, 'wb') as dst:
                    dst.write(src.read())
                logger.info("Database restored from backup")
            except Exception as restore_error:
                logger.error(f"Error restoring database from backup: {restore_error}")
        
        raise

def check_database_performance(db_path):
    """
    Check database performance.
    
    Args:
        db_path: Path to the database file
        
    Returns:
        dict: Performance metrics
    """
    try:
        # Connect to the database
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # Get database size
        cursor.execute("PRAGMA page_count")
        page_count = cursor.fetchone()[0]
        
        cursor.execute("PRAGMA page_size")
        page_size = cursor.fetchone()[0]
        
        db_size = page_count * page_size / (1024 * 1024)  # Size in MB
        
        # Get table counts
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall() if not row[0].startswith('sqlite_')]
        
        table_counts = {}
        for table in tables:
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            table_counts[table] = count
        
        # Close connection
        conn.close()
        
        # Create performance metrics
        metrics = {
            "database_size_mb": db_size,
            "table_counts": table_counts
        }
        
        logger.info(f"Database size: {db_size:.2f} MB")
        for table, count in table_counts.items():
            logger.info(f"Table {table}: {count} rows")
        
        return metrics
    except Exception as e:
        logger.error(f"Error checking database performance: {e}")
        return {}

if __name__ == "__main__":
    # Get database path from command line or use default
    if len(sys.argv) > 1:
        db_path = sys.argv[1]
    else:
        db_path = "voicepal.db"
    
    logger.info(f"Checking database performance for {db_path}")
    check_database_performance(db_path)
    
    logger.info(f"Optimizing database at {db_path}")
    optimize_database(db_path)
