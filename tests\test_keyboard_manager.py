"""
Test script for KeyboardManager.

This script tests the KeyboardManager class functionality.
"""

import unittest
from unittest.mock import Magic<PERSON>ock, patch

from telegram import InlineKeyboardButton, InlineKeyboardMarkup, ReplyKeyboardMarkup

from bot.features.keyboard_manager import KeyboardManager


class TestKeyboardManager(unittest.TestCase):
    """Test cases for the KeyboardManager class."""

    def setUp(self):
        """Set up test environment."""
        # Create a mock database
        self.mock_db = MagicMock()

        # Create a mock config
        self.mock_config = {
            "credit_packages": [
                {"credits": 100, "price": 4.99, "label": "100 Credits"},
                {"credits": 300, "price": 9.99, "label": "300 Credits"}
            ]
        }

        # Create a KeyboardManager instance
        self.keyboard_manager = KeyboardManager(
            database=self.mock_db,
            config=self.mock_config
        )

    def test_get_main_menu_keyboard(self):
        """Test get_main_menu_keyboard method."""
        # Get main menu keyboard
        keyboard = self.keyboard_manager.get_main_menu_keyboard()

        # Check that it's an InlineKeyboardMarkup
        self.assertIsInstance(keyboard, InlineKeyboardMarkup)

        # Check that it has the expected buttons
        self.assertEqual(len(keyboard.inline_keyboard), 3)

        # Check first row buttons
        self.assertEqual(len(keyboard.inline_keyboard[0]), 2)
        self.assertEqual(keyboard.inline_keyboard[0][0].text, "💬 Start Conversation")
        self.assertEqual(keyboard.inline_keyboard[0][0].callback_data, "start_conversation")
        self.assertEqual(keyboard.inline_keyboard[0][1].text, "💰 Check Credits")
        self.assertEqual(keyboard.inline_keyboard[0][1].callback_data, "show_credits")

        # Check second row buttons
        self.assertEqual(len(keyboard.inline_keyboard[1]), 2)
        self.assertEqual(keyboard.inline_keyboard[1][0].text, "👤 Change Personality")
        self.assertEqual(keyboard.inline_keyboard[1][0].callback_data, "show_personalities")
        self.assertEqual(keyboard.inline_keyboard[1][1].text, "📊 Mood Diary")
        self.assertEqual(keyboard.inline_keyboard[1][1].callback_data, "show_mood_diary")

        # Check third row buttons
        self.assertEqual(len(keyboard.inline_keyboard[2]), 1)
        self.assertEqual(keyboard.inline_keyboard[2][0].text, "❓ Help")
        self.assertEqual(keyboard.inline_keyboard[2][0].callback_data, "show_help")

    def test_get_personality_keyboard(self):
        """Test get_personality_keyboard method."""
        # Get personality keyboard
        keyboard = self.keyboard_manager.get_personality_keyboard()

        # Check that it's an InlineKeyboardMarkup
        self.assertIsInstance(keyboard, InlineKeyboardMarkup)

        # Check that it has the expected number of buttons (5 personalities + back button)
        self.assertEqual(len(keyboard.inline_keyboard), 6)

        # Check that the last button is the back button
        self.assertEqual(keyboard.inline_keyboard[-1][0].text, "🔙 Back to Menu")
        self.assertEqual(keyboard.inline_keyboard[-1][0].callback_data, "back_to_main")

        # Check that all buttons have the expected format
        for i in range(5):
            self.assertEqual(len(keyboard.inline_keyboard[i]), 1)
            self.assertTrue(keyboard.inline_keyboard[i][0].callback_data.startswith("set_personality_"))

    def test_get_mood_diary_keyboard(self):
        """Test get_mood_diary_keyboard method."""
        # Get mood diary keyboard
        keyboard = self.keyboard_manager.get_mood_diary_keyboard()

        # Check that it's an InlineKeyboardMarkup
        self.assertIsInstance(keyboard, InlineKeyboardMarkup)

        # Check that it has the expected buttons
        self.assertEqual(len(keyboard.inline_keyboard), 3)

        # Check first row buttons
        self.assertEqual(len(keyboard.inline_keyboard[0]), 2)
        self.assertEqual(keyboard.inline_keyboard[0][0].text, "📅 Today's Mood")
        self.assertEqual(keyboard.inline_keyboard[0][0].callback_data, "mood_today")
        self.assertEqual(keyboard.inline_keyboard[0][1].text, "📊 Weekly Summary")
        self.assertEqual(keyboard.inline_keyboard[0][1].callback_data, "mood_weekly")

        # Check second row buttons
        self.assertEqual(len(keyboard.inline_keyboard[1]), 2)
        self.assertEqual(keyboard.inline_keyboard[1][0].text, "📈 Monthly Summary")
        self.assertEqual(keyboard.inline_keyboard[1][0].callback_data, "mood_monthly")
        self.assertEqual(keyboard.inline_keyboard[1][1].text, "🔍 Mood Insights")
        self.assertEqual(keyboard.inline_keyboard[1][1].callback_data, "mood_insights")

        # Check back button
        self.assertEqual(len(keyboard.inline_keyboard[2]), 1)
        self.assertEqual(keyboard.inline_keyboard[2][0].text, "🔙 Back to Menu")
        self.assertEqual(keyboard.inline_keyboard[2][0].callback_data, "back_to_main")

    def test_get_credits_keyboard(self):
        """Test get_credits_keyboard method."""
        # Get credits keyboard
        keyboard = self.keyboard_manager.get_credits_keyboard()

        # Check that it's an InlineKeyboardMarkup
        self.assertIsInstance(keyboard, InlineKeyboardMarkup)

        # Check that it has the expected buttons (credit packages + back button)
        self.assertEqual(len(keyboard.inline_keyboard), 4)

        # Check credit package buttons for the first two packages
        for i in range(2):
            self.assertEqual(len(keyboard.inline_keyboard[i]), 1)
            package = self.mock_config["credit_packages"][i]
            expected_text = f"{package['credits']} Credits - ${package['price']:.2f}"
            expected_callback = f"buy_credits_{package['credits']}_{int(package['price'] * 100)}"
            self.assertEqual(keyboard.inline_keyboard[i][0].text, expected_text)
            self.assertEqual(keyboard.inline_keyboard[i][0].callback_data, expected_callback)

        # Check that there's a third credit package
        self.assertEqual(len(keyboard.inline_keyboard[2]), 1)
        # Just check that it's a string and has a callback data that starts with buy_credits_
        self.assertIsInstance(keyboard.inline_keyboard[2][0].text, str)
        self.assertTrue(keyboard.inline_keyboard[2][0].callback_data.startswith("buy_credits_"))

        # Check back button
        self.assertEqual(len(keyboard.inline_keyboard[3]), 1)
        self.assertEqual(keyboard.inline_keyboard[3][0].text, "🔙 Back to Menu")
        self.assertEqual(keyboard.inline_keyboard[3][0].callback_data, "back_to_main")

    def test_get_help_keyboard(self):
        """Test get_help_keyboard method."""
        # Get help keyboard
        keyboard = self.keyboard_manager.get_help_keyboard()

        # Check that it's an InlineKeyboardMarkup
        self.assertIsInstance(keyboard, InlineKeyboardMarkup)

        # Check that it has the expected buttons
        self.assertEqual(len(keyboard.inline_keyboard), 3)

        # Check first row buttons
        self.assertEqual(len(keyboard.inline_keyboard[0]), 2)
        self.assertEqual(keyboard.inline_keyboard[0][0].text, "📚 Commands")
        self.assertEqual(keyboard.inline_keyboard[0][0].callback_data, "help_commands")

        # Check last row buttons
        self.assertEqual(len(keyboard.inline_keyboard[2]), 2)
        self.assertEqual(keyboard.inline_keyboard[2][1].text, "🔙 Back to Menu")
        self.assertEqual(keyboard.inline_keyboard[2][1].callback_data, "back_to_main")

    def test_get_contextual_reply_keyboard(self):
        """Test get_contextual_reply_keyboard method."""
        # Test greeting context
        keyboard = self.keyboard_manager.get_contextual_reply_keyboard("greeting")
        self.assertIsInstance(keyboard, InlineKeyboardMarkup)
        self.assertEqual(len(keyboard.inline_keyboard), 2)

        # Test mood context
        keyboard = self.keyboard_manager.get_contextual_reply_keyboard("mood")
        self.assertIsInstance(keyboard, InlineKeyboardMarkup)
        self.assertEqual(len(keyboard.inline_keyboard), 2)

        # Test question context
        keyboard = self.keyboard_manager.get_contextual_reply_keyboard("question")
        self.assertIsInstance(keyboard, InlineKeyboardMarkup)
        self.assertEqual(len(keyboard.inline_keyboard), 2)

        # Test default context
        keyboard = self.keyboard_manager.get_contextual_reply_keyboard("unknown")
        self.assertIsInstance(keyboard, InlineKeyboardMarkup)
        self.assertEqual(len(keyboard.inline_keyboard), 2)

    def test_get_reply_keyboard(self):
        """Test get_reply_keyboard method."""
        # Test main menu keyboard
        keyboard = self.keyboard_manager.get_reply_keyboard("main_menu")
        self.assertIsInstance(keyboard, ReplyKeyboardMarkup)
        self.assertEqual(len(keyboard.keyboard), 3)

        # Test yes/no keyboard
        keyboard = self.keyboard_manager.get_reply_keyboard("yes_no")
        self.assertIsInstance(keyboard, ReplyKeyboardMarkup)
        self.assertEqual(len(keyboard.keyboard), 2)

        # Test mood selection keyboard
        keyboard = self.keyboard_manager.get_reply_keyboard("mood_selection")
        self.assertIsInstance(keyboard, ReplyKeyboardMarkup)
        self.assertEqual(len(keyboard.keyboard), 3)

        # Test default keyboard
        keyboard = self.keyboard_manager.get_reply_keyboard("unknown")
        self.assertIsInstance(keyboard, ReplyKeyboardMarkup)
        self.assertEqual(len(keyboard.keyboard), 2)

    def test_process_callback_query(self):
        """Test process_callback_query method."""
        # Test back_to_main callback
        response_text, reply_markup = self.keyboard_manager.process_callback_query("back_to_main", 123)
        self.assertEqual(response_text, "Main Menu")
        self.assertIsInstance(reply_markup, InlineKeyboardMarkup)

        # Test show_personalities callback
        response_text, reply_markup = self.keyboard_manager.process_callback_query("show_personalities", 123)
        self.assertEqual(response_text, "Select a personality for me to adopt:")
        self.assertIsInstance(reply_markup, InlineKeyboardMarkup)

        # Test set_personality callback
        self.mock_db.update_user_personality.return_value = True
        response_text, reply_markup = self.keyboard_manager.process_callback_query("set_personality_friendly", 123)
        self.mock_db.update_user_personality.assert_called_once_with(123, "friendly")
        self.assertTrue("Personality set to" in response_text)
        self.assertIsInstance(reply_markup, InlineKeyboardMarkup)

        # Test invalid personality
        response_text, reply_markup = self.keyboard_manager.process_callback_query("set_personality_invalid", 123)
        self.assertEqual(response_text, "Invalid personality selection.")
        self.assertIsInstance(reply_markup, InlineKeyboardMarkup)

        # Test mood callback
        response_text, reply_markup = self.keyboard_manager.process_callback_query("mood_today", 123)
        self.assertEqual(response_text, "Showing mood data for today.")
        self.assertIsNone(reply_markup)

        # Test unknown callback
        response_text, reply_markup = self.keyboard_manager.process_callback_query("unknown", 123)
        self.assertEqual(response_text, "I'm not sure how to handle that request.")
        self.assertIsInstance(reply_markup, InlineKeyboardMarkup)


if __name__ == "__main__":
    unittest.main()
