"""
Personalization manager for VoicePal.

This module manages user personalization.
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class PersonalizationManager:
    """Manages user personalization."""

    def __init__(self, database, config: Dict[str, Any] = None, user_manager = None, config_manager = None):
        """
        Initialize the personalization manager.

        Args:
            database: Database instance
            config: Configuration dictionary
            user_manager: Optional UserManager instance for preference management
            config_manager: Optional ConfigManager instance for configuration
        """
        self.database = database
        self.config = config or {}
        self.user_manager = user_manager
        self.config_manager = config_manager

    def get_user_preferences(self, user_id: int) -> Dict[str, Any]:
        """
        Get user preferences.

        Args:
            user_id: User ID

        Returns:
            Dict containing user preferences
        """
        # Delegate to user_manager if available
        if hasattr(self, 'user_manager') and self.user_manager:
            return self.user_manager.get_user_preferences(user_id)

        # Fallback to direct database access if user_manager not available
        try:
            # Get user preferences from database
            preferences = self.database.get_user_preferences(user_id)

            # If no preferences found, return default preferences
            if not preferences:
                # Get default preferences from config if available
                default_preferences = {}
                if hasattr(self, 'config_manager') and self.config_manager:
                    personalization_config = self.config_manager.get_feature_config("personalization") or {}
                    default_preferences = personalization_config.get("default_preferences", {})

                # Merge with hardcoded defaults for essential preferences
                default_preferences = {
                    "personality": self.database.get_user_personality(user_id) or "friendly",
                    "language": "en",
                    "voice": "default",
                    "response_length": "medium",
                    "formality": "casual",
                    **default_preferences
                }

                return default_preferences

            return preferences
        except Exception as e:
            logger.error(f"Error getting user preferences for user {user_id}: {e}")
            return {
                "personality": "friendly",
                "language": "en",
                "voice": "default",
                "response_length": "medium",
                "formality": "casual"
            }

    def update_user_preference(self, user_id: int, preference_key: str, preference_value: Any) -> bool:
        """
        Update a user preference.

        Args:
            user_id: User ID
            preference_key: Preference key
            preference_value: Preference value

        Returns:
            bool: True if successful, False otherwise
        """
        # Delegate to user_manager if available
        if hasattr(self, 'user_manager') and self.user_manager:
            return self.user_manager.update_user_preference(user_id, preference_key, preference_value)

        # Fallback to direct database access if user_manager not available
        try:
            # Update user preference in database
            self.database.update_user_preference(user_id, preference_key, preference_value)
            logger.info(f"Updated preference '{preference_key}' for user {user_id}")
            return True
        except Exception as e:
            logger.error(f"Error updating preference '{preference_key}' for user {user_id}: {e}")
            return False

    def get_voice_preferences(self, user_id: int) -> Dict[str, Any]:
        """
        Get voice preferences.

        Args:
            user_id: User ID

        Returns:
            Dict containing voice preferences
        """
        try:
            # Get user preferences
            preferences = self.get_user_preferences(user_id)

            # Extract voice-related preferences
            voice_preferences = {
                "voice": preferences.get("voice", "default"),
                "speed": preferences.get("voice_speed", 1.0),
                "pitch": preferences.get("voice_pitch", 0.0),
                "personality": preferences.get("personality", "friendly")
            }

            return voice_preferences
        except Exception as e:
            logger.error(f"Error getting voice preferences for user {user_id}: {e}")
            return {
                "voice": "default",
                "speed": 1.0,
                "pitch": 0.0,
                "personality": "friendly"
            }

    def get_language_preference(self, user_id: int) -> str:
        """
        Get language preference.

        Args:
            user_id: User ID

        Returns:
            str: Language code
        """
        try:
            # Get user preferences
            preferences = self.get_user_preferences(user_id)

            # Get language preference
            language = preferences.get("language", "en")

            return language
        except Exception as e:
            logger.error(f"Error getting language preference for user {user_id}: {e}")
            return "en"

    def update_language_preference(self, user_id: int, language: str) -> bool:
        """
        Update language preference.

        Args:
            user_id: User ID
            language: Language code

        Returns:
            bool: True if successful, False otherwise
        """
        return self.update_user_preference(user_id, "language", language)

    def get_personality_preference(self, user_id: int) -> str:
        """
        Get personality preference.

        Args:
            user_id: User ID

        Returns:
            str: Personality type
        """
        try:
            # Get user preferences
            preferences = self.get_user_preferences(user_id)

            # Get personality preference
            personality = preferences.get("personality", "friendly")

            return personality
        except Exception as e:
            logger.error(f"Error getting personality preference for user {user_id}: {e}")
            return "friendly"

    def update_personality_preference(self, user_id: int, personality: str) -> bool:
        """
        Update personality preference.

        Args:
            user_id: User ID
            personality: Personality type

        Returns:
            bool: True if successful, False otherwise
        """
        return self.update_user_preference(user_id, "personality", personality)
