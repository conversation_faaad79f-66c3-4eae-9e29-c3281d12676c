"""
Base repository class for VoicePal database repositories.

This module provides the base repository class for all database repositories.
"""

import logging
import sqlite3
from typing import Dict, List, Any, Optional, Type, TypeVar, Generic, Tuple, Union, Set
from contextlib import contextmanager

from bot.database.core.connection import DatabaseConnection
from bot.database.core.exceptions import (
    DatabaseError,
    DatabaseQueryError,
    DatabaseNotFoundError,
    DatabaseDuplicateError,
    DatabaseIntegrityError
)
from bot.database.core.utils import (
    build_insert_query,
    build_update_query,
    build_select_query,
    row_to_dict,
    rows_to_list
)
from bot.database.models.model import Model

# Set up logging
logger = logging.getLogger(__name__)

# Type variable for Model subclasses
T = TypeVar('T', bound=Model)

class Repository(Generic[T]):
    """Base class for all database repositories."""
    
    # Model class (to be overridden by subclasses)
    _model_class: Type[T] = Model
    
    def __init__(self, connection: DatabaseConnection):
        """Initialize repository with database connection.
        
        Args:
            connection: Database connection
        """
        self.connection = connection
    
    @contextmanager
    def transaction(self):
        """Context manager for database transactions.
        
        Yields:
            None
            
        Raises:
            DatabaseError: If transaction fails
        """
        try:
            self.connection.begin_transaction()
            yield
            self.connection.commit()
        except Exception as e:
            self.connection.rollback()
            logger.error(f"Transaction failed: {e}")
            raise DatabaseError(f"Transaction failed: {e}") from e
    
    def create(self, model: T) -> T:
        """Create a new record.
        
        Args:
            model: Model instance
            
        Returns:
            Created model instance
            
        Raises:
            DatabaseError: If creation fails
            DatabaseIntegrityError: If model validation fails
        """
        try:
            # Validate model
            errors = model.validate()
            if errors:
                error_message = "; ".join(errors)
                logger.error(f"Model validation failed: {error_message}")
                raise DatabaseIntegrityError(f"Model validation failed: {error_message}")
            
            # Convert model to dictionary
            data = model.to_dict()
            
            # Build and execute query
            query, params = build_insert_query(
                self._model_class.get_table_name(),
                data
            )
            
            with self.transaction():
                self.connection.execute(query, params)
            
            logger.debug(f"Created {self._model_class.__name__}: {model}")
            return model
        except sqlite3.IntegrityError as e:
            if "UNIQUE constraint failed" in str(e):
                logger.error(f"Duplicate record: {e}")
                raise DatabaseDuplicateError(f"Duplicate record: {e}") from e
            else:
                logger.error(f"Failed to create record: {e}")
                raise DatabaseIntegrityError(f"Failed to create record: {e}") from e
        except DatabaseIntegrityError:
            # Re-raise without wrapping
            raise
        except Exception as e:
            logger.error(f"Failed to create record: {e}")
            raise DatabaseError(f"Failed to create record: {e}") from e
    
    def update(self, model: T) -> T:
        """Update an existing record.
        
        Args:
            model: Model instance
            
        Returns:
            Updated model instance
            
        Raises:
            DatabaseError: If update fails
            DatabaseNotFoundError: If record not found
            DatabaseIntegrityError: If model validation fails
        """
        try:
            # Validate model
            errors = model.validate()
            if errors:
                error_message = "; ".join(errors)
                logger.error(f"Model validation failed: {error_message}")
                raise DatabaseIntegrityError(f"Model validation failed: {error_message}")
            
            # Convert model to dictionary
            data = model.to_dict()
            
            # Remove primary key from data
            primary_key = self._model_class.get_primary_key()
            pk_value = data.pop(primary_key)
            
            # Build and execute query
            query, params = build_update_query(
                self._model_class.get_table_name(),
                data,
                {primary_key: pk_value}
            )
            
            with self.transaction():
                cursor = self.connection.execute(query, params)
                
                if cursor.rowcount == 0:
                    raise DatabaseNotFoundError(f"Record not found: {primary_key}={pk_value}")
            
            logger.debug(f"Updated {self._model_class.__name__}: {model}")
            return model
        except (DatabaseNotFoundError, DatabaseIntegrityError):
            # Re-raise without wrapping
            raise
        except Exception as e:
            logger.error(f"Failed to update record: {e}")
            raise DatabaseError(f"Failed to update record: {e}") from e
    
    def delete(self, model_or_id: Union[T, str, int]) -> bool:
        """Delete a record.
        
        Args:
            model_or_id: Model instance or primary key value
            
        Returns:
            True if record was deleted, False otherwise
            
        Raises:
            DatabaseError: If deletion fails
        """
        try:
            # Get primary key value
            primary_key = self._model_class.get_primary_key()
            
            if isinstance(model_or_id, Model):
                pk_value = getattr(model_or_id, primary_key)
            else:
                pk_value = model_or_id
            
            # Build and execute query
            query = f"DELETE FROM {self._model_class.get_table_name()} WHERE {primary_key} = ?"
            
            with self.transaction():
                cursor = self.connection.execute(query, (pk_value,))
                deleted = cursor.rowcount > 0
            
            if deleted:
                logger.debug(f"Deleted {self._model_class.__name__}: {primary_key}={pk_value}")
            else:
                logger.debug(f"Record not found for deletion: {primary_key}={pk_value}")
            
            return deleted
        except Exception as e:
            logger.error(f"Failed to delete record: {e}")
            raise DatabaseError(f"Failed to delete record: {e}") from e
    
    def find_by_id(self, id_value: Union[str, int]) -> Optional[T]:
        """Find a record by primary key.
        
        Args:
            id_value: Primary key value
            
        Returns:
            Model instance or None if not found
            
        Raises:
            DatabaseError: If query fails
        """
        try:
            # Get primary key
            primary_key = self._model_class.get_primary_key()
            
            # Build and execute query
            query, params = build_select_query(
                self._model_class.get_table_name(),
                where={primary_key: id_value}
            )
            
            cursor = self.connection.execute(query, params)
            row = cursor.fetchone()
            
            if not row:
                return None
            
            # Convert row to model instance
            data = row_to_dict(row)
            model = self._model_class.from_dict(data)
            
            return model
        except Exception as e:
            logger.error(f"Failed to find record by ID: {e}")
            raise DatabaseError(f"Failed to find record by ID: {e}") from e
    
    def find_all(self, where: Optional[Dict[str, Any]] = None,
                order_by: Optional[str] = None,
                limit: Optional[int] = None,
                offset: Optional[int] = None) -> List[T]:
        """Find all records matching criteria.
        
        Args:
            where: Where conditions
            order_by: Order by clause
            limit: Maximum number of records
            offset: Offset for pagination
            
        Returns:
            List of model instances
            
        Raises:
            DatabaseError: If query fails
        """
        try:
            # Build and execute query
            query, params = build_select_query(
                self._model_class.get_table_name(),
                where=where,
                order_by=order_by,
                limit=limit,
                offset=offset
            )
            
            cursor = self.connection.execute(query, params)
            rows = cursor.fetchall()
            
            # Convert rows to model instances
            models = []
            for row in rows:
                data = row_to_dict(row)
                model = self._model_class.from_dict(data)
                models.append(model)
            
            return models
        except Exception as e:
            logger.error(f"Failed to find records: {e}")
            raise DatabaseError(f"Failed to find records: {e}") from e
    
    def find_one(self, where: Dict[str, Any]) -> Optional[T]:
        """Find a single record matching criteria.
        
        Args:
            where: Where conditions
            
        Returns:
            Model instance or None if not found
            
        Raises:
            DatabaseError: If query fails
        """
        try:
            results = self.find_all(where=where, limit=1)
            return results[0] if results else None
        except Exception as e:
            logger.error(f"Failed to find record: {e}")
            raise DatabaseError(f"Failed to find record: {e}") from e
    
    def count(self, where: Optional[Dict[str, Any]] = None) -> int:
        """Count records matching criteria.
        
        Args:
            where: Where conditions
            
        Returns:
            Number of records
            
        Raises:
            DatabaseError: If query fails
        """
        try:
            # Build where clause
            where_clause = ""
            params = []
            
            if where:
                conditions = []
                for column, value in where.items():
                    conditions.append(f"{column} = ?")
                    params.append(value)
                
                where_clause = f"WHERE {' AND '.join(conditions)}"
            
            # Build and execute query
            query = f"SELECT COUNT(*) FROM {self._model_class.get_table_name()} {where_clause}"
            
            cursor = self.connection.execute(query, tuple(params))
            count = cursor.fetchone()[0]
            
            return count
        except Exception as e:
            logger.error(f"Failed to count records: {e}")
            raise DatabaseError(f"Failed to count records: {e}") from e
    
    def exists(self, where: Dict[str, Any]) -> bool:
        """Check if a record exists.
        
        Args:
            where: Where conditions
            
        Returns:
            True if record exists, False otherwise
            
        Raises:
            DatabaseError: If query fails
        """
        return self.count(where) > 0
    
    def save(self, model: T) -> T:
        """Save a model (create or update).
        
        Args:
            model: Model instance
            
        Returns:
            Saved model instance
            
        Raises:
            DatabaseError: If save fails
        """
        primary_key = self._model_class.get_primary_key()
        pk_value = getattr(model, primary_key)
        
        # Check if record exists
        existing = self.find_by_id(pk_value)
        
        if existing:
            return self.update(model)
        else:
            return self.create(model)
    
    def bulk_create(self, models: List[T]) -> List[T]:
        """Create multiple records.
        
        Args:
            models: List of model instances
            
        Returns:
            List of created model instances
            
        Raises:
            DatabaseError: If creation fails
        """
        if not models:
            return []
        
        try:
            with self.transaction():
                for model in models:
                    self.create(model)
            
            return models
        except Exception as e:
            logger.error(f"Failed to bulk create records: {e}")
            raise DatabaseError(f"Failed to bulk create records: {e}") from e
    
    def bulk_update(self, models: List[T]) -> List[T]:
        """Update multiple records.
        
        Args:
            models: List of model instances
            
        Returns:
            List of updated model instances
            
        Raises:
            DatabaseError: If update fails
        """
        if not models:
            return []
        
        try:
            with self.transaction():
                for model in models:
                    self.update(model)
            
            return models
        except Exception as e:
            logger.error(f"Failed to bulk update records: {e}")
            raise DatabaseError(f"Failed to bulk update records: {e}") from e
    
    def bulk_delete(self, models_or_ids: List[Union[T, str, int]]) -> int:
        """Delete multiple records.
        
        Args:
            models_or_ids: List of model instances or primary key values
            
        Returns:
            Number of deleted records
            
        Raises:
            DatabaseError: If deletion fails
        """
        if not models_or_ids:
            return 0
        
        try:
            deleted_count = 0
            
            with self.transaction():
                for model_or_id in models_or_ids:
                    if self.delete(model_or_id):
                        deleted_count += 1
            
            return deleted_count
        except Exception as e:
            logger.error(f"Failed to bulk delete records: {e}")
            raise DatabaseError(f"Failed to bulk delete records: {e}") from e
