"""
Google TTS provider for VoicePal.

This module provides a Google-based TTS provider using gTTS.
"""

import os
import tempfile
import logging
from typing import Optional, Dict

from gtts import gTTS
from gtts.lang import tts_langs
from gtts.tts import gTTSError

from bot.providers.tts_provider_interface import TTSProviderInterface

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class GoogleTTSProvider(TTSProviderInterface):
    """Google TTS provider using gTTS."""

    def __init__(self):
        """Initialize the Google TTS provider."""
        # Get available TTS languages
        try:
            self.available_languages = tts_langs()
            logger.info(f"Initialized Google TTS provider with {len(self.available_languages)} available languages")
        except Exception as e:
            logger.error(f"Error getting available TTS languages: {e}")
            self.available_languages = {'en': 'English'}

    def generate_speech(self, text: str,
                       language: Optional[str] = None,
                       voice: Optional[str] = None,
                       personality: Optional[str] = None,
                       **kwargs) -> Optional[str]:
        """
        Generate speech from text using Google TTS.

        Args:
            text: Text to convert to speech
            language: Language code (e.g., 'en', 'fr', etc.)
            voice: Not used in Google TTS
            personality: Not used in Google TTS
            **kwargs: Additional parameters

        Returns:
            Path to the generated audio file or None if generation failed
        """
        try:
            # Use default language if not specified
            lang = language or 'en'
            
            # Get slow parameter from kwargs
            slow = kwargs.get('slow', False)

            # Validate language
            if lang not in self.available_languages:
                logger.warning(f"Language '{lang}' not available for Google TTS. Using default: en")
                lang = 'en'

            # Create a temporary file
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".mp3")
            temp_file_path = temp_file.name
            temp_file.close()

            # Generate speech
            tts = gTTS(text=text, lang=lang, slow=slow)
            tts.save(temp_file_path)

            logger.info(f"Successfully generated voice response with Google TTS: {temp_file_path}")
            return temp_file_path

        except gTTSError as e:
            logger.error(f"gTTS error generating voice response: {e}")
            return None
        except Exception as e:
            logger.error(f"Error generating voice response: {e}")
            return None

    def supports_feature(self, feature_name: str) -> bool:
        """
        Check if provider supports a specific feature.
        
        Args:
            feature_name: Name of the feature to check
            
        Returns:
            bool: True if the feature is supported, False otherwise
        """
        return feature_name in ["text_to_speech", "multilingual"]

    def get_available_languages(self) -> Dict[str, str]:
        """
        Get available languages.

        Returns:
            Dictionary of language codes and names
        """
        return self.available_languages

    def get_available_voices(self, language: Optional[str] = None) -> Dict[str, str]:
        """
        Get available voices.

        Args:
            language: Language code to filter voices

        Returns:
            Dictionary of voice identifiers and names (empty for Google TTS)
        """
        # Google TTS doesn't support voice selection
        return {}
