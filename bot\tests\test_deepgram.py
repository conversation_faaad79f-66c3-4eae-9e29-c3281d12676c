"""
Test script to verify Deepgram functionality.

This script tests:
1. Deepgram SDK initialization
2. Deepgram transcription
3. Deepgram TTS
"""

import os
import sys
import asyncio
import logging
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# Import bot modules
from bot.providers.voice.processor import VoiceProcessor
from bot.providers.tts.deepgram_provider import DeepgramTTSProvider

async def test_deepgram_sdk_initialization():
    """Test Deepgram SDK initialization."""
    logger.info("Testing Deepgram SDK initialization...")
    
    # Get API key from environment or use a test key
    api_key = os.environ.get('DEEPGRAM_API_KEY', 'test_key')
    
    # Initialize TTS provider
    provider = DeepgramTTSProvider(api_key=api_key, voice_id='aura-thalia-en')
    
    # Check if provider was initialized
    if provider.deepgram_client:
        logger.info("Deepgram SDK initialized successfully")
    else:
        logger.warning("Deepgram SDK initialization failed or using mock")
    
    logger.info("Deepgram SDK initialization test completed")

async def test_deepgram_tts():
    """Test Deepgram TTS functionality."""
    logger.info("Testing Deepgram TTS...")
    
    # Get API key from environment or use a test key
    api_key = os.environ.get('DEEPGRAM_API_KEY', 'test_key')
    
    # Initialize TTS provider
    provider = DeepgramTTSProvider(api_key=api_key, voice_id='aura-thalia-en')
    
    # Test text-to-speech
    try:
        # Only run this test if we have a real API key
        if api_key != 'test_key':
            text = "Hello, this is a test of the Deepgram text-to-speech functionality."
            audio_file = await provider.generate_speech(text)
            
            if audio_file:
                logger.info(f"TTS successful, audio file created: {audio_file}")
            else:
                logger.warning("TTS failed, no audio file created")
        else:
            logger.info("Skipping TTS test with test key")
    except Exception as e:
        logger.error(f"Error testing Deepgram TTS: {e}")
    
    logger.info("Deepgram TTS test completed")

async def test_voice_processor():
    """Test voice processor with Deepgram."""
    logger.info("Testing voice processor with Deepgram...")
    
    # Get API key from environment or use a test key
    api_key = os.environ.get('DEEPGRAM_API_KEY', 'test_key')
    
    # Initialize voice processor
    processor = VoiceProcessor(
        stt_provider_type='deepgram',
        stt_provider_options={'api_key': api_key},
        tts_provider_type='deepgram',
        tts_provider_options={'api_key': api_key, 'voice_id': 'aura-thalia-en'}
    )
    
    # Check if processor was initialized
    if processor.stt_provider and processor.tts_provider:
        logger.info("Voice processor initialized successfully")
    else:
        logger.warning("Voice processor initialization incomplete")
    
    # Test text-to-speech via processor
    try:
        # Only run this test if we have a real API key
        if api_key != 'test_key':
            text = "This is a test of the voice processor with Deepgram."
            audio_file = await processor.text_to_speech(text)
            
            if audio_file:
                logger.info(f"Processor TTS successful, audio file created: {audio_file}")
            else:
                logger.warning("Processor TTS failed, no audio file created")
        else:
            logger.info("Skipping processor TTS test with test key")
    except Exception as e:
        logger.error(f"Error testing voice processor TTS: {e}")
    
    logger.info("Voice processor test completed")

async def test_voice_id_cleaning():
    """Test voice ID cleaning functionality."""
    logger.info("Testing voice ID cleaning...")
    
    # Get API key from environment or use a test key
    api_key = os.environ.get('DEEPGRAM_API_KEY', 'test_key')
    
    # Test with different voice IDs
    voice_ids = [
        'aura-2-thalia-en',
        'aura-bella-en',
        'aura-thalia-en'
    ]
    
    for voice_id in voice_ids:
        # Initialize TTS provider
        provider = DeepgramTTSProvider(api_key=api_key, voice_id=voice_id)
        
        logger.info(f"Initialized provider with voice ID: {voice_id}")
        logger.info(f"Provider is using voice ID: {provider.voice_id}")
    
    logger.info("Voice ID cleaning test completed")

async def run_tests():
    """Run all tests."""
    logger.info("Starting Deepgram tests...")
    
    await test_deepgram_sdk_initialization()
    await test_deepgram_tts()
    await test_voice_processor()
    await test_voice_id_cleaning()
    
    logger.info("All Deepgram tests completed")

if __name__ == '__main__':
    asyncio.run(run_tests())
