"""
Update imports script for VoicePal.

This script updates imports in files to use the best provider implementations.
"""

import os
import re
import logging
from typing import Dict, List, Set, Tuple

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# Define import mappings
IMPORT_MAPPINGS = {
    # Old import -> New import
    "bot.providers.ai.google_ai_provider": "bot.ai_conversation",
    "bot.providers.tts.elevenlabs_provider": "bot.elevenlabs_tts_provider",
    "bot.tts_providers": "bot.elevenlabs_tts_provider",
    "bot.tts_provider_base": "bot.elevenlabs_tts_provider",
    "bot.google_ai_conversation": "bot.ai_conversation",
    "bot.dia_tts_provider": "bot.elevenlabs_tts_provider",
    "bot.test_ai_conversation": "bot.ai_conversation"
}

def update_file_imports(file_path: str, import_mappings: Dict[str, str]) -> bool:
    """
    Update imports in a file.
    
    Args:
        file_path: Path to the file to update
        import_mappings: Dict mapping old imports to new imports
        
    Returns:
        bool: True if file was updated, False otherwise
    """
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()
        
        # Track if file was updated
        updated = False
        
        # Update imports
        for old_import, new_import in import_mappings.items():
            # Update "from X import Y" style imports
            pattern = f"from\\s+{re.escape(old_import)}\\s+import\\s+([\\w,\\s]+)"
            matches = re.findall(pattern, content)
            
            for match in matches:
                old_line = f"from {old_import} import {match}"
                new_line = f"from {new_import} import {match}"
                content = content.replace(old_line, new_line)
                logger.info(f"  Updated: {old_line} -> {new_line}")
                updated = True
            
            # Update "import X" style imports
            pattern = f"import\\s+{re.escape(old_import)}"
            if re.search(pattern, content):
                old_line = f"import {old_import}"
                new_line = f"import {new_import}"
                content = content.replace(old_line, new_line)
                logger.info(f"  Updated: {old_line} -> {new_line}")
                updated = True
            
            # Update "import X as Y" style imports
            pattern = f"import\\s+{re.escape(old_import)}\\s+as\\s+(\\w+)"
            matches = re.findall(pattern, content)
            
            for match in matches:
                old_line = f"import {old_import} as {match}"
                new_line = f"import {new_import} as {match}"
                content = content.replace(old_line, new_line)
                logger.info(f"  Updated: {old_line} -> {new_line}")
                updated = True
        
        # Write updated content back to file
        if updated:
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(content)
            
            return True
        
        return False
    except Exception as e:
        logger.error(f"Error updating imports in {file_path}: {e}")
        return False

def find_python_files() -> List[str]:
    """
    Find all Python files in the bot directory.
    
    Returns:
        List of file paths
    """
    python_files = []
    
    # Walk through the bot directory
    for root, dirs, files in os.walk("bot"):
        for file in files:
            if file.endswith(".py"):
                python_files.append(os.path.join(root, file))
    
    return python_files

def main():
    """Main function to update imports."""
    try:
        # Find Python files
        python_files = find_python_files()
        logger.info(f"Found {len(python_files)} Python files")
        
        # Update imports in each file
        updated_files = []
        for file_path in python_files:
            logger.info(f"Checking {file_path}")
            if update_file_imports(file_path, IMPORT_MAPPINGS):
                updated_files.append(file_path)
        
        logger.info(f"\nUpdated imports in {len(updated_files)} files:")
        for file_path in updated_files:
            logger.info(f"  {file_path}")
        
        logger.info("\nImport update completed successfully")
    except Exception as e:
        logger.error(f"Error during import update: {e}")

if __name__ == "__main__":
    main()
