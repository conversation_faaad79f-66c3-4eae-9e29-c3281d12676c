"""
Performance and load tests for VoicePal.
"""

import pytest
import time
import asyncio
import concurrent.futures
from unittest.mock import Mock, patch
import tempfile
import os

from bot.database.database_manager import DatabaseManager
from bot.performance.database_pool import DatabaseConnectionPool
from bot.performance.cache_manager import CacheManager
from bot.performance.async_processor import AsyncProcessor
from bot.analytics.metrics_collector import MetricsCollector


@pytest.fixture
def temp_db_path():
    """Create temporary database file."""
    fd, path = tempfile.mkstemp(suffix='.db')
    os.close(fd)
    yield path
    try:
        os.unlink(path)
    except OSError:
        pass


@pytest.fixture
def database_manager(temp_db_path):
    """Create database manager for testing."""
    db_manager = DatabaseManager(temp_db_path)
    db_manager.initialize_database()
    return db_manager


@pytest.fixture
def redis_client():
    """Create mock Redis client."""
    redis = Mock()
    redis.get = Mock(return_value=None)
    redis.set = Mock()
    redis.delete = Mock()
    redis.exists = Mock(return_value=False)
    redis.expire = Mock()
    return redis


class TestDatabasePerformance:
    """Test database performance under load."""
    
    def test_database_connection_pool_performance(self, temp_db_path):
        """Test database connection pool performance."""
        pool = DatabaseConnectionPool(
            database_url=f"sqlite:///{temp_db_path}",
            pool_size=10,
            max_overflow=20
        )
        
        start_time = time.time()
        
        # Simulate concurrent database operations
        def db_operation():
            with pool.get_connection() as conn:
                conn.execute("SELECT 1").fetchone()
                time.sleep(0.01)  # Simulate work
        
        # Run 100 concurrent operations
        with concurrent.futures.ThreadPoolExecutor(max_workers=20) as executor:
            futures = [executor.submit(db_operation) for _ in range(100)]
            concurrent.futures.wait(futures)
        
        end_time = time.time()
        duration = end_time - start_time
        
        # Should complete within reasonable time
        assert duration < 5.0, f"Database operations took too long: {duration}s"
        
        # Check pool statistics
        stats = pool.get_pool_stats()
        assert stats["total_connections"] <= 30  # pool_size + max_overflow
    
    def test_database_query_performance(self, database_manager):
        """Test database query performance."""
        # Insert test data
        for i in range(1000):
            database_manager.create_user(
                user_id=i,
                username=f"user_{i}",
                first_name=f"User {i}"
            )
        
        # Test query performance
        start_time = time.time()
        
        for _ in range(100):
            users = database_manager.get_all_users(limit=10)
            assert len(users) == 10
        
        end_time = time.time()
        duration = end_time - start_time
        
        # Should complete queries quickly
        assert duration < 1.0, f"Queries took too long: {duration}s"
    
    def test_concurrent_user_operations(self, database_manager):
        """Test concurrent user operations."""
        def create_and_update_user(user_id):
            # Create user
            database_manager.create_user(
                user_id=user_id,
                username=f"user_{user_id}",
                first_name=f"User {user_id}"
            )
            
            # Update credits
            database_manager.update_user_credits(user_id, 100)
            
            # Get user
            user = database_manager.get_user(user_id)
            assert user is not None
            assert user['credits'] == 100
        
        start_time = time.time()
        
        # Run concurrent operations
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(create_and_update_user, i) for i in range(100, 200)]
            concurrent.futures.wait(futures)
        
        end_time = time.time()
        duration = end_time - start_time
        
        # Should handle concurrent operations efficiently
        assert duration < 10.0, f"Concurrent operations took too long: {duration}s"


class TestCachePerformance:
    """Test cache performance under load."""
    
    def test_cache_manager_performance(self, redis_client):
        """Test cache manager performance."""
        cache = CacheManager(redis_client=redis_client, enable_local_cache=True)
        
        # Test cache operations performance
        start_time = time.time()
        
        # Perform many cache operations
        for i in range(1000):
            key = f"test_key_{i}"
            value = f"test_value_{i}"
            
            # Set value
            cache.set(key, value, ttl=3600)
            
            # Get value
            retrieved = cache.get(key)
            assert retrieved == value
        
        end_time = time.time()
        duration = end_time - start_time
        
        # Should complete cache operations quickly
        assert duration < 2.0, f"Cache operations took too long: {duration}s"
    
    def test_concurrent_cache_operations(self, redis_client):
        """Test concurrent cache operations."""
        cache = CacheManager(redis_client=redis_client, enable_local_cache=True)
        
        def cache_operations(thread_id):
            for i in range(100):
                key = f"thread_{thread_id}_key_{i}"
                value = f"thread_{thread_id}_value_{i}"
                
                cache.set(key, value, ttl=3600)
                retrieved = cache.get(key)
                assert retrieved == value
        
        start_time = time.time()
        
        # Run concurrent cache operations
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(cache_operations, i) for i in range(10)]
            concurrent.futures.wait(futures)
        
        end_time = time.time()
        duration = end_time - start_time
        
        # Should handle concurrent cache operations efficiently
        assert duration < 5.0, f"Concurrent cache operations took too long: {duration}s"
    
    def test_cache_memory_usage(self, redis_client):
        """Test cache memory usage."""
        cache = CacheManager(
            redis_client=redis_client,
            enable_local_cache=True,
            local_cache_size=1000
        )
        
        # Fill cache with data
        for i in range(2000):  # More than cache size
            key = f"memory_test_key_{i}"
            value = f"memory_test_value_{i}" * 100  # Larger values
            cache.set(key, value, ttl=3600)
        
        # Check that cache doesn't grow indefinitely
        cache_stats = cache.get_cache_stats()
        assert cache_stats["local_cache_size"] <= 1000, "Local cache exceeded size limit"


class TestAsyncProcessorPerformance:
    """Test async processor performance."""
    
    @pytest.mark.asyncio
    async def test_async_processor_throughput(self):
        """Test async processor throughput."""
        processor = AsyncProcessor(max_workers=10, queue_size=1000)
        processor.start()
        
        try:
            # Define a simple async task
            async def simple_task(data):
                await asyncio.sleep(0.01)  # Simulate work
                return data * 2
            
            start_time = time.time()
            
            # Submit many tasks
            tasks = []
            for i in range(500):
                task_id = await processor.submit_task(simple_task, i)
                tasks.append(task_id)
            
            # Wait for all tasks to complete
            results = []
            for task_id in tasks:
                result = await processor.get_result(task_id, timeout=10)
                results.append(result)
            
            end_time = time.time()
            duration = end_time - start_time
            
            # Check results
            assert len(results) == 500
            assert all(results[i] == i * 2 for i in range(500))
            
            # Should process tasks efficiently
            assert duration < 15.0, f"Async processing took too long: {duration}s"
            
            # Check processor stats
            stats = processor.get_processor_stats()
            assert stats["completed_tasks"] == 500
            
        finally:
            processor.stop()
    
    @pytest.mark.asyncio
    async def test_async_processor_error_handling(self):
        """Test async processor error handling performance."""
        processor = AsyncProcessor(max_workers=5, queue_size=100)
        processor.start()
        
        try:
            # Define a task that sometimes fails
            async def failing_task(data):
                if data % 10 == 0:
                    raise ValueError(f"Task {data} failed")
                await asyncio.sleep(0.01)
                return data * 2
            
            start_time = time.time()
            
            # Submit tasks (some will fail)
            tasks = []
            for i in range(100):
                task_id = await processor.submit_task(failing_task, i)
                tasks.append(task_id)
            
            # Collect results and errors
            successful_results = 0
            failed_results = 0
            
            for task_id in tasks:
                try:
                    result = await processor.get_result(task_id, timeout=5)
                    successful_results += 1
                except Exception:
                    failed_results += 1
            
            end_time = time.time()
            duration = end_time - start_time
            
            # Should handle errors efficiently
            assert duration < 10.0, f"Error handling took too long: {duration}s"
            assert successful_results == 90  # 90% should succeed
            assert failed_results == 10     # 10% should fail
            
        finally:
            processor.stop()


class TestMetricsCollectionPerformance:
    """Test metrics collection performance."""
    
    def test_metrics_collection_throughput(self, temp_db_path, redis_client):
        """Test metrics collection throughput."""
        database = Mock()
        database.execute = Mock()
        database.executemany = Mock()
        database.commit = Mock()
        
        collector = MetricsCollector(
            database=database,
            redis_client=redis_client,
            collection_interval=1
        )
        
        start_time = time.time()
        
        # Record many metrics
        for i in range(10000):
            collector.record_metric(f"test.metric.{i % 100}", float(i), unit="count")
        
        # Flush metrics
        collector.flush_metrics()
        
        end_time = time.time()
        duration = end_time - start_time
        
        # Should handle high-volume metrics efficiently
        assert duration < 5.0, f"Metrics collection took too long: {duration}s"
        
        # Check that metrics were batched efficiently
        assert database.executemany.called
    
    def test_concurrent_metrics_recording(self, temp_db_path, redis_client):
        """Test concurrent metrics recording."""
        database = Mock()
        database.execute = Mock()
        database.executemany = Mock()
        database.commit = Mock()
        
        collector = MetricsCollector(
            database=database,
            redis_client=redis_client
        )
        
        def record_metrics(thread_id):
            for i in range(1000):
                collector.record_metric(
                    f"thread.{thread_id}.metric.{i}",
                    float(i),
                    unit="count"
                )
        
        start_time = time.time()
        
        # Run concurrent metrics recording
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(record_metrics, i) for i in range(10)]
            concurrent.futures.wait(futures)
        
        # Flush all metrics
        collector.flush_metrics()
        
        end_time = time.time()
        duration = end_time - start_time
        
        # Should handle concurrent metrics recording efficiently
        assert duration < 10.0, f"Concurrent metrics recording took too long: {duration}s"


class TestMemoryUsage:
    """Test memory usage under load."""
    
    def test_memory_usage_under_load(self, database_manager, redis_client):
        """Test memory usage under sustained load."""
        import psutil
        import gc
        
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Simulate sustained load
        cache = CacheManager(redis_client=redis_client, enable_local_cache=True)
        
        for iteration in range(10):
            # Create many objects
            for i in range(1000):
                user_id = iteration * 1000 + i
                
                # Database operations
                database_manager.create_user(
                    user_id=user_id,
                    username=f"user_{user_id}",
                    first_name=f"User {user_id}"
                )
                
                # Cache operations
                cache.set(f"user_{user_id}", {"id": user_id, "name": f"User {user_id}"})
            
            # Force garbage collection
            gc.collect()
            
            # Check memory usage
            current_memory = process.memory_info().rss / 1024 / 1024  # MB
            memory_growth = current_memory - initial_memory
            
            # Memory growth should be reasonable
            assert memory_growth < 500, f"Excessive memory growth: {memory_growth}MB"
    
    def test_cache_memory_limits(self, redis_client):
        """Test cache memory limits."""
        cache = CacheManager(
            redis_client=redis_client,
            enable_local_cache=True,
            local_cache_size=100  # Small cache size
        )
        
        # Fill cache beyond limit
        for i in range(500):
            cache.set(f"key_{i}", f"value_{i}" * 1000)  # Large values
        
        # Check cache size
        stats = cache.get_cache_stats()
        assert stats["local_cache_size"] <= 100, "Cache exceeded size limit"
        
        # Verify LRU eviction works
        # Recent items should still be in cache
        assert cache.get("key_499") is not None
        assert cache.get("key_498") is not None
        
        # Old items should be evicted
        assert cache.get("key_0") is None
        assert cache.get("key_1") is None


class TestScalabilityLimits:
    """Test system scalability limits."""
    
    def test_concurrent_user_limit(self, database_manager):
        """Test system behavior with many concurrent users."""
        def simulate_user_session(user_id):
            # Create user
            database_manager.create_user(
                user_id=user_id,
                username=f"user_{user_id}",
                first_name=f"User {user_id}"
            )
            
            # Simulate conversation
            conversation_id = database_manager.create_conversation(user_id)
            
            # Add messages
            for i in range(10):
                database_manager.add_message(
                    conversation_id=conversation_id,
                    role="user" if i % 2 == 0 else "assistant",
                    content=f"Message {i}",
                    message_type="text"
                )
            
            # Update credits
            database_manager.update_user_credits(user_id, 50)
        
        start_time = time.time()
        
        # Simulate many concurrent users
        with concurrent.futures.ThreadPoolExecutor(max_workers=50) as executor:
            futures = [executor.submit(simulate_user_session, i) for i in range(1000, 1500)]
            concurrent.futures.wait(futures)
        
        end_time = time.time()
        duration = end_time - start_time
        
        # Should handle many concurrent users
        assert duration < 30.0, f"Concurrent user simulation took too long: {duration}s"
        
        # Verify all users were created
        total_users = len(database_manager.get_all_users())
        assert total_users >= 500, f"Not all users were created: {total_users}"
