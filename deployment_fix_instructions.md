# VoicePal Bot Deployment Fix Instructions

This document provides instructions for fixing the deployment issues with the VoicePal bot.

## Issue

The bot is failing to deploy on Render with the following error:

```
ModuleNotFoundError: No module named 'bot.features.feature_registry'
```

## Fix

1. Edit the file `bot/core/initialization_manager.py` and change the import:

```python
# Change this line:
from bot.features.feature_registry import FeatureRegistry

# To this:
from bot.core.feature_registry import FeatureRegistry
```

2. Also in the same file, change the import for DialogEngine:

```python
# Change this line:
from bot.core.dialog_engine import DialogEngine

# To this:
from bot.core.enhanced_dialog_engine import EnhancedDialogEngine as DialogEngine
```

3. Edit the file `bot/main.py` and update the dialog_engine initialization step:

```python
# Change this:
self.init_manager.register_initialization_step(
    name="dialog_engine",
    func=self._init_dialog_engine,
    dependencies=["ai_provider", "memory_manager", "user_manager"]
)

# To this:
self.init_manager.register_initialization_step(
    name="dialog_engine",
    func=self._init_dialog_engine,
    dependencies=["providers", "feature_managers"]
)
```

## Testing

After making these changes, the bot should be able to deploy successfully on Render.

## Additional Notes

There are still some non-critical issues with the telegram_stars_payment and enhanced_memory integration steps, but these are not required for the bot to function. These issues can be addressed in future updates.
