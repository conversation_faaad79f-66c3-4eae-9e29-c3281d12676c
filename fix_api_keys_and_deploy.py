#!/usr/bin/env python3
"""
Fix API Keys and Make Bot 100% Production Ready

This script:
1. Fixes the Google AI API key issue
2. Sets up optional Groq API key
3. Validates all integrations
4. Prepares for deployment
"""

import os
import sys
import json
import asyncio
import httpx
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def fix_google_ai_api():
    """Fix Google AI API key by testing different endpoints."""
    logger.info("🔧 Fixing Google AI API integration...")
    
    api_key = os.getenv('GOOGLE_AI_API_KEY')
    if not api_key:
        return False, "No Google AI API key found"
    
    # Test different Google AI endpoints and models
    endpoints_to_try = [
        {
            'url': f"https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key={api_key}",
            'model': 'gemini-1.5-flash'
        },
        {
            'url': f"https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro:generateContent?key={api_key}",
            'model': 'gemini-1.5-pro'
        },
        {
            'url': f"https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key={api_key}",
            'model': 'gemini-pro'
        }
    ]
    
    async with httpx.AsyncClient() as client:
        for endpoint in endpoints_to_try:
            try:
                logger.info(f"Testing {endpoint['model']}...")
                response = await client.post(
                    endpoint['url'],
                    json={
                        "contents": [{"parts": [{"text": "Hello, respond with just 'OK'"}]}],
                        "generationConfig": {"maxOutputTokens": 10}
                    },
                    timeout=15.0
                )
                
                if response.status_code == 200:
                    logger.info(f"✅ {endpoint['model']} working!")
                    
                    # Update config.json with working model
                    await update_config_with_working_model(endpoint['model'])
                    return True, f"Fixed: Using {endpoint['model']}"
                else:
                    logger.warning(f"❌ {endpoint['model']} failed: {response.status_code}")
                    
            except Exception as e:
                logger.warning(f"❌ {endpoint['model']} error: {e}")
    
    return False, "No working Google AI model found"

async def update_config_with_working_model(model_name):
    """Update config.json with working model."""
    try:
        with open('config.json', 'r') as f:
            config = json.load(f)
        
        if 'providers' in config and 'ai' in config['providers']:
            config['providers']['ai']['model_name'] = model_name
            
            with open('config.json', 'w') as f:
                json.dump(config, f, indent=4)
            
            logger.info(f"✅ Updated config.json with model: {model_name}")
    except Exception as e:
        logger.error(f"❌ Failed to update config: {e}")

async def setup_groq_api():
    """Set up Groq API key if not present."""
    logger.info("🚀 Setting up Groq API (optional fast AI)...")
    
    groq_key = os.getenv('GROQ_API_KEY')
    if groq_key:
        logger.info("✅ Groq API key already configured")
        return True, "Already configured"
    
    logger.info("⚠️ Groq API key not found")
    logger.info("📋 To get Groq API key (optional but recommended):")
    logger.info("   1. Go to https://console.groq.com/")
    logger.info("   2. Sign up for free account")
    logger.info("   3. Create API key")
    logger.info("   4. Set GROQ_API_KEY environment variable")
    logger.info("   5. Very fast AI responses, free tier available")
    
    return False, "Not configured (optional)"

async def test_all_integrations():
    """Test all API integrations."""
    logger.info("🧪 Testing all integrations...")
    
    results = {}
    
    # Test Telegram Bot
    bot_token = os.getenv('BOT_TOKEN')
    if bot_token:
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(f"https://api.telegram.org/bot{bot_token}/getMe")
                if response.status_code == 200:
                    bot_info = response.json()['result']
                    results['telegram'] = f"✅ Bot: @{bot_info['username']}"
                else:
                    results['telegram'] = "❌ Invalid bot token"
        except:
            results['telegram'] = "❌ Telegram API error"
    else:
        results['telegram'] = "❌ No bot token"
    
    # Test Deepgram
    deepgram_key = os.getenv('DEEPGRAM_API_KEY')
    if deepgram_key:
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    "https://api.deepgram.com/v1/projects",
                    headers={"Authorization": f"Token {deepgram_key}"}
                )
                if response.status_code == 200:
                    results['deepgram'] = "✅ Voice processing ready"
                else:
                    results['deepgram'] = "❌ Invalid Deepgram key"
        except:
            results['deepgram'] = "❌ Deepgram API error"
    else:
        results['deepgram'] = "❌ No Deepgram key"
    
    # Test Google AI (after fix)
    google_key = os.getenv('GOOGLE_AI_API_KEY')
    if google_key:
        # Try the fixed endpoint
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key={google_key}",
                    json={
                        "contents": [{"parts": [{"text": "test"}]}],
                        "generationConfig": {"maxOutputTokens": 1}
                    }
                )
                if response.status_code == 200:
                    results['google_ai'] = "✅ AI responses ready"
                else:
                    results['google_ai'] = "❌ Google AI still not working"
        except:
            results['google_ai'] = "❌ Google AI API error"
    else:
        results['google_ai'] = "❌ No Google AI key"
    
    # Test ElevenLabs
    elevenlabs_key = os.getenv('ELEVENLABS_API_KEY')
    if elevenlabs_key:
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    "https://api.elevenlabs.io/v1/voices",
                    headers={"xi-api-key": elevenlabs_key}
                )
                if response.status_code == 200:
                    results['elevenlabs'] = "✅ Premium voice ready"
                else:
                    results['elevenlabs'] = "❌ Invalid ElevenLabs key"
        except:
            results['elevenlabs'] = "❌ ElevenLabs API error"
    else:
        results['elevenlabs'] = "❌ No ElevenLabs key"
    
    return results

def generate_deployment_guide(integration_results):
    """Generate final deployment guide."""
    working_integrations = sum(1 for result in integration_results.values() if result.startswith('✅'))
    total_integrations = len(integration_results)
    
    guide = []
    guide.append("🚀 FINAL DEPLOYMENT GUIDE")
    guide.append("=" * 50)
    guide.append(f"Integration Status: {working_integrations}/{total_integrations} working")
    guide.append("")
    
    # Show integration status
    for service, status in integration_results.items():
        guide.append(f"{status} {service.title()}")
    
    guide.append("")
    
    if working_integrations >= 3:  # Telegram, Deepgram, and one AI provider
        guide.append("✅ READY FOR DEPLOYMENT!")
        guide.append("")
        guide.append("📋 DEPLOYMENT STEPS:")
        guide.append("1. Push code to GitHub repository")
        guide.append("2. Connect Render.com to your GitHub repo")
        guide.append("3. Set environment variables in Render dashboard:")
        guide.append("")
        
        # Environment variables for Render
        env_vars = {
            'BOT_TOKEN': os.getenv('BOT_TOKEN', ''),
            'DEEPGRAM_API_KEY': os.getenv('DEEPGRAM_API_KEY', ''),
            'GOOGLE_AI_API_KEY': os.getenv('GOOGLE_AI_API_KEY', ''),
            'ELEVENLABS_API_KEY': os.getenv('ELEVENLABS_API_KEY', ''),
            'GROQ_API_KEY': os.getenv('GROQ_API_KEY', 'optional'),
            'PAYMENT_PROVIDER_TOKEN': os.getenv('PAYMENT_PROVIDER_TOKEN', ''),
            'ENVIRONMENT': 'production',
            'PORT': '8443'
        }
        
        for var, value in env_vars.items():
            if value and value != 'optional':
                guide.append(f"   {var}={value[:20]}...")
            else:
                guide.append(f"   {var}=<your_value>")
        
        guide.append("")
        guide.append("4. Deploy and monitor logs")
        guide.append("5. Test webhook: https://your-app.onrender.com/webhook")
        guide.append("6. Test bot functionality in Telegram")
        guide.append("")
        guide.append("💰 MONETIZATION FEATURES:")
        guide.append("• ✅ Telegram Stars payments")
        guide.append("• ✅ Credit system")
        guide.append("• ✅ Voice processing")
        guide.append("• ✅ AI conversations")
        guide.append("• ✅ User management")
        guide.append("")
        guide.append("🎉 YOUR BOT IS READY TO MAKE MONEY!")
        
    else:
        guide.append("❌ NOT READY - Fix integrations above")
        guide.append("")
        guide.append("🔧 REQUIRED FIXES:")
        for service, status in integration_results.items():
            if not status.startswith('✅'):
                guide.append(f"   • {service}: {status}")
    
    return "\n".join(guide)

async def main():
    """Main function."""
    print("🔧 Fixing API Keys and Making Bot 100% Production Ready")
    print("=" * 60)
    
    # Load environment from config
    try:
        with open('config.json', 'r') as f:
            config = json.load(f)
        
        # Set environment variables
        if 'telegram' in config:
            os.environ['BOT_TOKEN'] = config['telegram']['token']
            os.environ['PAYMENT_PROVIDER_TOKEN'] = config['telegram']['payment_provider_token']
        
        if 'providers' in config:
            providers = config['providers']
            if 'stt' in providers:
                os.environ['DEEPGRAM_API_KEY'] = providers['stt']['api_key']
            if 'ai' in providers:
                os.environ['GOOGLE_AI_API_KEY'] = providers['ai']['api_key']
    except:
        logger.error("❌ Could not load config.json")
        return
    
    # Step 1: Fix Google AI API
    google_fixed, google_message = await fix_google_ai_api()
    logger.info(f"Google AI: {google_message}")
    
    # Step 2: Setup Groq (optional)
    groq_setup, groq_message = await setup_groq_api()
    logger.info(f"Groq: {groq_message}")
    
    # Step 3: Test all integrations
    integration_results = await test_all_integrations()
    
    # Step 4: Generate deployment guide
    deployment_guide = generate_deployment_guide(integration_results)
    
    print("\n" + deployment_guide)
    
    # Save final report
    final_report = {
        'google_ai_fixed': google_fixed,
        'groq_setup': groq_setup,
        'integration_results': integration_results,
        'deployment_guide': deployment_guide
    }
    
    with open('final_production_report.json', 'w') as f:
        json.dump(final_report, f, indent=2)
    
    print(f"\n📄 Final report saved to: final_production_report.json")

if __name__ == "__main__":
    asyncio.run(main())
