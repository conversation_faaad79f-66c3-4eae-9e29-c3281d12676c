@echo off
echo Pushing changes to the test branch on GitHub...

REM Switch to the deployment-test branch
git checkout deployment-test

REM Add all changes
git add .

REM Commit the changes
git commit -m "Fix Deepgram transcription, menu navigation, audio processing, and response quality issues"

REM Push to the deployment-test branch
git push origin deployment-test

echo Changes pushed to the deployment-test branch on GitHub.
