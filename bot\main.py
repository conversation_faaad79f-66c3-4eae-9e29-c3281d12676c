"""
Main module for VoicePal Telegram bot with modular architecture.

This module integrates all the components and handles Telegram bot interactions.
It sets up the bot, registers handlers, and starts the bot.
"""

import os
import json
import logging
import asyncio
import tempfile
from typing import Dict, List, Optional, Any, Tuple, Union

from telegram import (
    Update,
    InlineKeyboardButton,
    InlineKeyboardMarkup,
    BotCommand,
    BotCommandScopeChat
)
from telegram.constants import ChatAction
from telegram.ext import (
    <PERSON>,
    CommandHandler,
    MessageHandler,
    CallbackQueryHandler,
    PreCheckoutQueryHandler,
    filters,
    ContextTypes
)
from telegram.error import TelegramError, RetryAfter

from bot.config_manager import ConfigManager
from bot.database import Database
from bot.database.extensions import extend_database
from bot.providers.provider_factory import ProviderFactory
from bot.providers.secure_provider_factory import SecureProviderFactory
from bot.core.key_manager import KeyManager
from bot.core.user_manager import UserManager
from bot.core.enhanced_dialog_engine import EnhancedDialogEngine as DialogEngine
from bot.core.feature_registry import FeatureRegistry
from bot.features.hierarchical_memory_manager import HierarchicalMemoryManager as MemoryManager
from bot.features.mood_tracker import MoodTracker
from bot.features.personalization_manager import PersonalizationManager
# Legacy keyboard manager removed - using new menu system
from bot.features.welcome_manager import WelcomeManager
from bot.features.mood_entry import MoodEntry
from bot.features.button_state_manager import ButtonStateManager
from bot.core.navigation_router import NavigationRouter
from bot.core.menu_manager import MenuManager
from bot.providers.voice.processor import VoiceProcessor
from bot.handlers.voice_settings_handlers import tts_provider_command, deepgram_voices_command, elevenlabs_voices_command, handle_voice_settings_callback
from bot.handlers.key_management_handlers import key_command, handle_key_management_callback
from bot.handlers.free_credits_handlers import free_credits_command, verify_command, handle_free_credits_callback
from bot.payment.telegram_stars_payment import TelegramStarsPayment
from bot.payment.payment_integration import integrate_telegram_stars_payment
from bot.features.sentiment_analyzer import SentimentAnalyzer
from bot.features.sentiment_integration import integrate_sentiment_analyzer
from bot.features.memory_integration import integrate_enhanced_memory
from bot.features.security_integration import integrate_security_monitoring
from bot.features.security_audit_integration import integrate_security_audit
from bot.features.payment_security_integration import integrate_payment_security
from bot.core.rate_limiter import rate_limit
from bot.core.security import is_admin_user

# Import centralized logging configuration
from bot.core.logging_config import configure_logging, get_logger

# Set up logging
configure_logging()
logger = get_logger(__name__)

class VoicePalBot:
    """Main VoicePal bot class that integrates all components."""

    def __init__(self):
        """Initialize the bot and its components."""
        # Create initialization manager
        from bot.core.initialization_manager import InitializationManager
        self.init_manager = InitializationManager()

        # Register core components with initialization manager
        self._register_initialization_steps()

        # Initialize components synchronously for backward compatibility
        # In the future, this should be done asynchronously
        asyncio.run(self._initialize_components())

        logger.info("VoicePal bot initialized with modular architecture")

    async def _initialize_components(self) -> bool:
        """
        Initialize all components asynchronously.

        Returns:
            bool: True if initialization was successful, False otherwise
        """
        # Initialize all components
        success = await self.init_manager.initialize()

        if not success:
            logger.error("Bot initialization failed")
            return False

        # Get initialized components
        self.config_manager = self.init_manager.get_component("config_manager")
        self.database = self.init_manager.get_component("database")
        self.user_manager = self.init_manager.get_component("user_manager")
        self.key_manager = self.init_manager.get_component("key_manager")
        self.secure_provider_factory = self.init_manager.get_component("secure_provider_factory")
        self.stt_provider = self.init_manager.get_component("stt_provider")
        self.ai_provider = self.init_manager.get_component("ai_provider")
        self.feature_registry = self.init_manager.get_component("feature_registry")
        self.memory_manager = self.init_manager.get_component("memory_manager")
        self.enhanced_memory_manager = self.init_manager.get_component("enhanced_memory_manager")
        self.personalization_manager = self.init_manager.get_component("personalization_manager")
        self.button_state_manager = self.init_manager.get_component("button_state_manager")
        self.voice_processor = self.init_manager.get_component("voice_processor")
        self.payment_system = self.init_manager.get_component("payment_system")
        self.dialog_engine = self.init_manager.get_component("dialog_engine")
        self.application = self.init_manager.get_component("application")
        self.menu_manager = self.init_manager.get_component("menu_manager")
        self.navigation_router = self.init_manager.get_component("navigation_router")

        # Store bot instance, database, and voice processor in application.bot_data
        self.application.bot_data["bot_instance"] = self
        self.application.bot_data["db_manager"] = self.database
        self.application.bot_data["voice_processor"] = self.voice_processor

        return True

    def _register_initialization_steps(self) -> None:
        """Register initialization steps with the initialization manager."""
        # Register core initialization steps
        self.init_manager.register_initialization_step(
            name="config_manager",
            func=self._init_config_manager,
            dependencies=[]
        )

        self.init_manager.register_initialization_step(
            name="database",
            func=self._init_database,
            dependencies=["config_manager"]
        )

        self.init_manager.register_initialization_step(
            name="user_manager",
            func=self._init_user_manager,
            dependencies=["database"]
        )

        self.init_manager.register_initialization_step(
            name="key_manager",
            func=self._init_key_manager,
            dependencies=["database"]
        )

        self.init_manager.register_initialization_step(
            name="secure_provider_factory",
            func=self._init_secure_provider_factory,
            dependencies=["key_manager"]
        )

        self.init_manager.register_initialization_step(
            name="providers",
            func=self._init_providers_async,
            dependencies=["secure_provider_factory", "config_manager"]
        )

        self.init_manager.register_initialization_step(
            name="feature_registry",
            func=self._init_feature_registry,
            dependencies=[]
        )

        self.init_manager.register_initialization_step(
            name="feature_managers",
            func=self._init_feature_managers_async,
            dependencies=["database", "config_manager", "user_manager"]
        )

        self.init_manager.register_initialization_step(
            name="voice_processor",
            func=self._init_voice_processor_async,
            dependencies=["key_manager", "config_manager"]
        )

        self.init_manager.register_initialization_step(
            name="payment_system",
            func=self._init_payment_system,
            dependencies=["database", "config_manager"]
        )

        self.init_manager.register_initialization_step(
            name="dialog_engine",
            func=self._init_dialog_engine,
            dependencies=["providers", "feature_managers"]
        )

        self.init_manager.register_initialization_step(
            name="application",
            func=self._init_application,
            dependencies=["config_manager"]
        )

        self.init_manager.register_initialization_step(
            name="menu_manager",
            func=self._init_menu_manager,
            dependencies=[]
        )

        self.init_manager.register_initialization_step(
            name="navigation_router",
            func=self._init_navigation_router,
            dependencies=["menu_manager"]
        )

        self.init_manager.register_initialization_step(
            name="welcome_manager",
            func=self._init_welcome_manager,
            dependencies=["database", "config_manager"]
        )

        # Register integration steps
        self.init_manager.register_integration_step(
            name="register_handlers",
            func=self._register_handlers_async,
            dependencies=["application"]
        )

        self.init_manager.register_integration_step(
            name="register_features",
            func=self._register_features_async,
            dependencies=["application", "feature_registry"]
        )

        # Register optional integration steps
        self.init_manager.register_integration_step(
            name="telegram_stars_payment",
            func=self._integrate_telegram_stars_payment,
            dependencies=["application", "payment_system"],
            required=False
        )

        self.init_manager.register_integration_step(
            name="sentiment_analyzer",
            func=self._integrate_sentiment_analyzer,
            dependencies=["application", "config_manager"],
            required=False
        )

        self.init_manager.register_integration_step(
            name="enhanced_memory",
            func=self._integrate_enhanced_memory,
            dependencies=["application", "config_manager", "memory_manager"],
            required=False
        )

        self.init_manager.register_integration_step(
            name="hierarchical_memory",
            func=self._integrate_hierarchical_memory,
            dependencies=["application", "config_manager", "memory_manager"],
            required=False
        )

    async def _init_config_manager(self) -> tuple[bool, Optional[str]]:
        """
        Initialize configuration manager.

        Returns:
            Tuple of (success, error_message)
        """
        try:
            # Initialize configuration
            config_manager = ConfigManager()

            # Register with initialization manager
            self.init_manager.register_component("config_manager", config_manager)

            return True, None
        except Exception as e:
            logger.error(f"Error initializing configuration manager: {e}")
            return False, str(e)

    async def _init_database(self) -> tuple[bool, Optional[str]]:
        """
        Initialize database.

        Returns:
            Tuple of (success, error_message)
        """
        try:
            # Get config manager
            config_manager = self.init_manager.get_component("config_manager")

            # Initialize database
            db_config = config_manager.get_database_config()
            db_file = db_config.get("file", "voicepal.db")

            # Initialize database with unified schema
            database = Database(db_file)

            # Extend database with all extensions
            extend_database(database)

            # Register with initialization manager
            self.init_manager.register_component("database", database)

            return True, None
        except Exception as e:
            logger.error(f"Error initializing database: {e}")
            return False, str(e)

    async def _init_user_manager(self) -> tuple[bool, Optional[str]]:
        """
        Initialize user manager.

        Returns:
            Tuple of (success, error_message)
        """
        try:
            # Get database
            database = self.init_manager.get_component("database")

            # Initialize user manager
            user_manager = UserManager(database)

            # Register with initialization manager
            self.init_manager.register_component("user_manager", user_manager)

            return True, None
        except Exception as e:
            logger.error(f"Error initializing user manager: {e}")
            return False, str(e)

    async def _init_key_manager(self) -> tuple[bool, Optional[str]]:
        """
        Initialize key manager.

        Returns:
            Tuple of (success, error_message)
        """
        try:
            # Get database
            database = self.init_manager.get_component("database")

            # Initialize key manager
            key_manager = KeyManager(database.conn)

            # Register with initialization manager
            self.init_manager.register_component("key_manager", key_manager)

            return True, None
        except Exception as e:
            logger.error(f"Error initializing key manager: {e}")
            return False, str(e)

    async def _init_secure_provider_factory(self) -> tuple[bool, Optional[str]]:
        """
        Initialize secure provider factory.

        Returns:
            Tuple of (success, error_message)
        """
        try:
            # Get key manager
            key_manager = self.init_manager.get_component("key_manager")

            # Initialize secure provider factory
            secure_provider_factory = SecureProviderFactory(key_manager)

            # Register with initialization manager
            self.init_manager.register_component("secure_provider_factory", secure_provider_factory)

            return True, None
        except Exception as e:
            logger.error(f"Error initializing secure provider factory: {e}")
            return False, str(e)

    async def _init_providers_async(self) -> tuple[bool, Optional[str]]:
        """
        Initialize providers asynchronously.

        Returns:
            Tuple of (success, error_message)
        """
        try:
            # Get secure provider factory and config manager
            secure_provider_factory = self.init_manager.get_component("secure_provider_factory")
            config_manager = self.init_manager.get_component("config_manager")

            # STT provider
            stt_config = config_manager.get_provider_config("stt")
            stt_type = stt_config.get("type", "deepgram")
            stt_provider = secure_provider_factory.create_stt_provider(
                provider_type=stt_type,
                **stt_config
            )

            # AI provider
            ai_config = config_manager.get_provider_config("ai")
            ai_type = ai_config.get("type", "google_ai")
            ai_provider = secure_provider_factory.create_ai_provider(
                provider_type=ai_type,
                **ai_config
            )

            # Register with initialization manager
            self.init_manager.register_component("stt_provider", stt_provider)
            self.init_manager.register_component("ai_provider", ai_provider)

            return True, None
        except Exception as e:
            logger.error(f"Error initializing providers: {e}")
            return False, str(e)

    def _init_providers(self) -> None:
        """Initialize providers based on configuration (legacy method)."""
        # This method is kept for backward compatibility
        # It should not be used in new code
        logger.warning("Using legacy _init_providers method. This should be replaced with _init_providers_async.")

        # Initialize key manager
        self.key_manager = KeyManager(self.database.conn)

        # Initialize secure provider factory
        self.secure_provider_factory = SecureProviderFactory(self.key_manager)

        # STT provider
        stt_config = self.config_manager.get_provider_config("stt")
        stt_type = stt_config.get("type", "deepgram")
        self.stt_provider = self.secure_provider_factory.create_stt_provider(
            provider_type=stt_type,
            **stt_config
        )

        # AI provider
        ai_config = self.config_manager.get_provider_config("ai")
        ai_type = ai_config.get("type", "google_ai")
        self.ai_provider = self.secure_provider_factory.create_ai_provider(
            provider_type=ai_type,
            **ai_config
        )

    async def _init_feature_registry(self) -> tuple[bool, Optional[str]]:
        """
        Initialize feature registry.

        Returns:
            Tuple of (success, error_message)
        """
        try:
            # Initialize feature registry
            feature_registry = FeatureRegistry()

            # Register with initialization manager
            self.init_manager.register_component("feature_registry", feature_registry)

            return True, None
        except Exception as e:
            logger.error(f"Error initializing feature registry: {e}")
            return False, str(e)

    async def _init_feature_managers_async(self) -> tuple[bool, Optional[str]]:
        """
        Initialize feature managers asynchronously.

        Returns:
            Tuple of (success, error_message)
        """
        try:
            # Get dependencies
            database = self.init_manager.get_component("database")
            config_manager = self.init_manager.get_component("config_manager")
            user_manager = self.init_manager.get_component("user_manager")
            ai_provider = self.init_manager.get_component("ai_provider")
            stt_provider = self.init_manager.get_component("stt_provider")

            # Memory manager - will be replaced by enhanced memory manager in integrate_enhanced_memory
            # Initialize a basic memory manager as a fallback
            memory_manager = None
            enhanced_memory_manager = None

            if config_manager.is_feature_enabled("memory"):
                memory_config = config_manager.get_feature_config("memory")
                memory_manager = MemoryManager(
                    database=database,
                    ai_provider=ai_provider,
                    config=memory_config
                )

            # Mood tracker
            mood_tracker = None
            if config_manager.is_feature_enabled("mood_tracking"):
                mood_config = config_manager.get_feature_config("mood_tracking")
                mood_tracker = MoodTracker(
                    database=database,
                    stt_provider=stt_provider,
                    ai_provider=ai_provider,
                    config=mood_config
                )

            # Personalization manager
            personalization_manager = None
            if config_manager.is_feature_enabled("personalization"):
                personalization_config = config_manager.get_feature_config("personalization")
                personalization_manager = PersonalizationManager(
                    database=database,
                    config=personalization_config,
                    user_manager=user_manager,
                    config_manager=config_manager
                )

            # Button state manager (always enabled)
            button_state_manager = ButtonStateManager(
                database=database
            )

            # Register with initialization manager
            self.init_manager.register_component("memory_manager", memory_manager)
            self.init_manager.register_component("enhanced_memory_manager", enhanced_memory_manager)
            self.init_manager.register_component("mood_tracker", mood_tracker)
            self.init_manager.register_component("personalization_manager", personalization_manager)
            self.init_manager.register_component("button_state_manager", button_state_manager)

            return True, None
        except Exception as e:
            logger.error(f"Error initializing feature managers: {e}")
            return False, str(e)

    def _init_feature_managers(self) -> None:
        """Initialize feature managers (legacy method)."""
        # This method is kept for backward compatibility
        # It should not be used in new code
        logger.warning("Using legacy _init_feature_managers method. This should be replaced with _init_feature_managers_async.")

        # Memory manager - will be replaced by enhanced memory manager in integrate_enhanced_memory
        # Initialize a basic memory manager as a fallback
        if self.config_manager.is_feature_enabled("memory"):
            memory_config = self.config_manager.get_feature_config("memory")
            self.memory_manager = MemoryManager(
                database=self.database,
                ai_provider=self.ai_provider,
                config=memory_config
            )
            # Initialize as None, will be set in integrate_enhanced_memory
            self.enhanced_memory_manager = None
        else:
            self.memory_manager = None
            self.enhanced_memory_manager = None

        # Mood tracker
        if self.config_manager.is_feature_enabled("mood_tracking"):
            mood_config = self.config_manager.get_feature_config("mood_tracking")
            self.mood_tracker = MoodTracker(
                database=self.database,
                stt_provider=self.stt_provider,
                ai_provider=self.ai_provider,
                config=mood_config
            )
        else:
            self.mood_tracker = None

        # Personalization manager
        if self.config_manager.is_feature_enabled("personalization"):
            personalization_config = self.config_manager.get_feature_config("personalization")
            self.personalization_manager = PersonalizationManager(
                database=self.database,
                config=personalization_config,
                user_manager=self.user_manager,  # Pass user_manager for preference management
                config_manager=self.config_manager  # Pass config_manager for configuration
            )
        else:
            self.personalization_manager = None

        # Button state manager (always enabled)
        self.button_state_manager = ButtonStateManager(
            database=self.database
        )

        # Legacy keyboard manager removed - using new menu system
        credit_system_config = self.config_manager.get_credit_system_config()

        # Initialize new menu manager with custom UI config
        ui_config = self.config_manager.get_feature_config("ui") or {}

        # Override persistent menu setting to fix the menu appearing with every message
        ui_config["show_persistent_menu"] = False
        ui_config["menu_frequency"] = 5  # Show menu every 5 messages

        self.menu_manager = MenuManager(
            database=self.database,
            config=ui_config
        )

        # Initialize navigation router
        self.navigation_router = NavigationRouter(self)

        # Register module handlers with navigation router
        self._register_navigation_handlers()

        # Welcome manager (always enabled)
        self.welcome_manager = WelcomeManager(
            database=self.database,
            mood_tracker=self.mood_tracker,
            config=credit_system_config
        )

        # Mood entry handler (enabled if mood tracking is enabled)
        if self.config_manager.is_feature_enabled("mood_tracking"):
            self.mood_entry = MoodEntry(
                database=self.database,
                mood_tracker=self.mood_tracker,
                config=self.config_manager.get_feature_config("mood_tracking")
            )
        else:
            self.mood_entry = None

    def _register_navigation_handlers(self) -> None:
        """Register navigation handlers with the navigation router."""
        try:
            # Register payment handlers
            payment_handlers = {
                "buy_credits": self._handle_buy_credits_navigation,
                "show_buy_credits": self._handle_buy_credits_navigation,
            }

            payment_patterns = [
                ("buy_credits_", self._handle_buy_credits_pattern),
                ("buy_credits:", self._handle_buy_credits_pattern),
            ]

            self.navigation_router.register_module_handlers("payment", payment_handlers)
            self.navigation_router.register_module_pattern_handlers("payment", payment_patterns)

            # Register voice settings handlers
            voice_handlers = {
                "show_tts_providers": self._handle_voice_settings_navigation,
                "show_voice_selection": self._handle_voice_settings_navigation,
                "show_speech_speed": self._handle_voice_settings_navigation,
                "show_voice_tone": self._handle_voice_settings_navigation,
            }

            voice_patterns = [
                ("set_voice_", self._handle_voice_settings_pattern),
                ("tts_provider_", self._handle_voice_settings_pattern),
            ]

            self.navigation_router.register_module_handlers("voice_settings", voice_handlers)
            self.navigation_router.register_module_pattern_handlers("voice_settings", voice_patterns)

            # Register personality handlers
            personality_patterns = [
                ("set_personality_", self._handle_personality_pattern),
            ]

            self.navigation_router.register_module_pattern_handlers("personality", personality_patterns)

            logger.info("Navigation handlers registered successfully")

        except Exception as e:
            logger.error(f"Error registering navigation handlers: {e}")
            import traceback
            logger.error(traceback.format_exc())

    async def _handle_buy_credits_navigation(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> Tuple[str, InlineKeyboardMarkup]:
        """Handle buy credits navigation."""
        return "💳 Buy Credits:", self.menu_manager.get_credits_keyboard()

    async def _handle_buy_credits_pattern(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> Optional[Tuple[str, InlineKeyboardMarkup]]:
        """Handle buy credits pattern callbacks."""
        # Delegate to legacy handler for now
        return None

    async def _handle_voice_settings_navigation(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> Tuple[str, InlineKeyboardMarkup]:
        """Handle voice settings navigation."""
        return "🎤 Voice Settings:", self.menu_manager.get_voice_settings_keyboard()

    async def _handle_voice_settings_pattern(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> Optional[Tuple[str, InlineKeyboardMarkup]]:
        """Handle voice settings pattern callbacks."""
        # Delegate to legacy handler for now
        return None

    async def _handle_personality_pattern(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> Optional[Tuple[str, InlineKeyboardMarkup]]:
        """Handle personality pattern callbacks."""
        # Delegate to legacy handler for now
        return None

    async def _init_voice_processor_async(self) -> tuple[bool, Optional[str]]:
        """
        Initialize voice processor asynchronously.

        Returns:
            Tuple of (success, error_message)
        """
        try:
            # Get dependencies
            key_manager = self.init_manager.get_component("key_manager")
            config_manager = self.init_manager.get_component("config_manager")

            # Get STT and TTS provider configurations
            stt_config = config_manager.get_provider_config("stt")
            tts_config = config_manager.get_provider_config("tts")

            # Get personalization configuration
            personalization_config = config_manager.get_feature_config("personalization")

            # Get API keys securely
            stt_type = stt_config.get("type", "deepgram")
            tts_type = tts_config.get("type", "elevenlabs")

            # Get API keys from key manager
            deepgram_api_key = key_manager.get_key(stt_type)
            if not deepgram_api_key:
                # Fallback to config or environment
                deepgram_api_key = stt_config.get("api_key", "")
                if deepgram_api_key:
                    # Store in key manager for future use
                    key_manager.store_key(stt_type, deepgram_api_key)

            # Update TTS options with secure API key
            tts_api_key = key_manager.get_key(tts_type)
            if tts_api_key:
                tts_config["api_key"] = tts_api_key

            # Initialize voice processor
            voice_processor = VoiceProcessor(
                deepgram_api_key=deepgram_api_key,
                default_language=personalization_config.get("default_language", "en"),
                tts_provider=tts_type,
                tts_provider_options=tts_config,
                personality=personalization_config.get("default_personality", "friendly")
            )

            # Register with initialization manager
            self.init_manager.register_component("voice_processor", voice_processor)

            return True, None
        except Exception as e:
            logger.error(f"Error initializing voice processor: {e}")
            return False, str(e)

    async def _init_payment_system(self) -> tuple[bool, Optional[str]]:
        """
        Initialize payment system asynchronously.

        Returns:
            Tuple of (success, error_message)
        """
        try:
            # Get dependencies
            database = self.init_manager.get_component("database")
            config_manager = self.init_manager.get_component("config_manager")

            # Initialize payment system
            payment_config = config_manager.get_telegram_config()

            # Get provider token from config
            provider_token = payment_config.get("payment_provider_token", "")

            # Initialize Telegram Stars payment (only supported payment system)
            payment_system = TelegramStarsPayment(
                database=database,
                config_manager=config_manager,
                provider_token=provider_token,
                digital_goods_mode=True
            )

            # Register with initialization manager
            self.init_manager.register_component("payment_system", payment_system)

            return True, None
        except Exception as e:
            logger.error(f"Error initializing payment system: {e}")
            return False, str(e)

    async def _init_dialog_engine(self) -> tuple[bool, Optional[str]]:
        """
        Initialize dialog engine asynchronously.

        Returns:
            Tuple of (success, error_message)
        """
        try:
            # Get dependencies
            ai_provider = self.init_manager.get_component("ai_provider")
            memory_manager = self.init_manager.get_component("memory_manager")
            user_manager = self.init_manager.get_component("user_manager")

            # Initialize dialog engine
            dialog_engine = DialogEngine(
                ai_provider=ai_provider,
                memory_manager=memory_manager,
                user_manager=user_manager
            )

            # Register with initialization manager
            self.init_manager.register_component("dialog_engine", dialog_engine)

            return True, None
        except Exception as e:
            logger.error(f"Error initializing dialog engine: {e}")
            return False, str(e)

    async def _init_application(self) -> tuple[bool, Optional[str]]:
        """
        Initialize Telegram application asynchronously.

        Returns:
            Tuple of (success, error_message)
        """
        try:
            # Get dependencies
            config_manager = self.init_manager.get_component("config_manager")

            # Initialize bot application
            telegram_config = config_manager.get_telegram_config()
            application = Application.builder().token(telegram_config.get("token", "")).build()

            # Register with initialization manager
            self.init_manager.register_component("application", application)

            return True, None
        except Exception as e:
            logger.error(f"Error initializing application: {e}")
            return False, str(e)

    async def _init_menu_manager(self) -> tuple[bool, Optional[str]]:
        """
        Initialize menu manager asynchronously.

        Returns:
            Tuple of (success, error_message)
        """
        try:
            # Get dependencies
            database = self.init_manager.get_component("database")
            config_manager = self.init_manager.get_component("config_manager")

            # Initialize menu manager
            ui_config = config_manager.get_feature_config("ui") or {}

            # Override persistent menu setting to fix the menu appearing with every message
            ui_config["show_persistent_menu"] = False
            ui_config["menu_frequency"] = 5  # Show menu every 5 messages

            menu_manager = MenuManager(
                database=database,
                config=ui_config
            )

            # Register with initialization manager
            self.init_manager.register_component("menu_manager", menu_manager)

            return True, None
        except Exception as e:
            logger.error(f"Error initializing menu manager: {e}")
            return False, str(e)

    async def _init_navigation_router(self) -> tuple[bool, Optional[str]]:
        """
        Initialize navigation router asynchronously.

        Returns:
            Tuple of (success, error_message)
        """
        try:
            # Get dependencies
            menu_manager = self.init_manager.get_component("menu_manager")

            # Initialize navigation router
            navigation_router = NavigationRouter(self)

            # Register with initialization manager
            self.init_manager.register_component("navigation_router", navigation_router)

            return True, None
        except Exception as e:
            logger.error(f"Error initializing navigation router: {e}")
            return False, str(e)

    async def _init_welcome_manager(self) -> tuple[bool, Optional[str]]:
        """
        Initialize welcome manager asynchronously.

        Returns:
            Tuple of (success, error_message)
        """
        try:
            # Get dependencies
            database = self.init_manager.get_component("database")
            config_manager = self.init_manager.get_component("config_manager")

            # Get mood tracker if available
            mood_tracker = self.init_manager.get_component("mood_tracker")

            # Get credit system config
            credit_system_config = config_manager.get_credit_system_config()

            # Initialize welcome manager
            from bot.features.welcome_manager import WelcomeManager
            welcome_manager = WelcomeManager(
                database=database,
                mood_tracker=mood_tracker,
                config=credit_system_config
            )

            # Register with initialization manager
            self.init_manager.register_component("welcome_manager", welcome_manager)

            # Also set it as an instance attribute for backward compatibility
            self.welcome_manager = welcome_manager

            return True, None
        except Exception as e:
            logger.error(f"Error initializing welcome manager: {e}")
            return False, str(e)

    def _init_voice_processor(self) -> None:
        """Initialize voice processor (legacy method)."""
        # This method is kept for backward compatibility
        # It should not be used in new code
        logger.warning("Using legacy _init_voice_processor method. This should be replaced with _init_voice_processor_async.")

        # Get STT and TTS provider configurations
        stt_config = self.config_manager.get_provider_config("stt")
        tts_config = self.config_manager.get_provider_config("tts")

        # Get personalization configuration
        personalization_config = self.config_manager.get_feature_config("personalization")

        # Get API keys securely
        stt_type = stt_config.get("type", "deepgram")
        tts_type = tts_config.get("type", "elevenlabs")

        # Get API keys from key manager
        deepgram_api_key = self.key_manager.get_key(stt_type)
        if not deepgram_api_key:
            # Fallback to config or environment
            deepgram_api_key = stt_config.get("api_key", "")
            if deepgram_api_key:
                # Store in key manager for future use
                self.key_manager.store_key(stt_type, deepgram_api_key)

        # Update TTS options with secure API key
        tts_api_key = self.key_manager.get_key(tts_type)
        if tts_api_key:
            tts_config["api_key"] = tts_api_key

        # Initialize voice processor
        self.voice_processor = VoiceProcessor(
            deepgram_api_key=deepgram_api_key,
            default_language=personalization_config.get("default_language", "en"),
            tts_provider=tts_type,
            tts_provider_options=tts_config,
            personality=personalization_config.get("default_personality", "friendly")
        )

    def _register_features(self) -> None:
        """Register features in the feature registry."""
        # Register memory features
        if self.memory_manager:
            self.feature_registry.register_feature(
                "personalized_response",
                self._handle_personalized_response,
                ["ai"]
            )
            self.feature_registry.enable_feature("personalized_response")

        # Register mood tracking features
        if self.mood_tracker:
            self.feature_registry.register_feature(
                "mood_analysis",
                self._handle_mood_analysis,
                ["stt", "ai"]
            )
            self.feature_registry.enable_feature("mood_analysis")

        # Register personalization features
        if self.personalization_manager:
            self.feature_registry.register_feature(
                "voice_customization",
                self._handle_voice_customization,
                ["tts"]
            )
            self.feature_registry.enable_feature("voice_customization")

    async def _register_handlers_async(self) -> tuple[bool, Optional[str]]:
        """
        Register message and command handlers asynchronously.

        Returns:
            Tuple of (success, error_message)
        """
        try:
            # Get dependencies
            application = self.init_manager.get_component("application")

            # Command handlers
            application.add_handler(CommandHandler("start", self.start_command))
            application.add_handler(CommandHandler("help", self.help_command))
            application.add_handler(CommandHandler("credits", self.credits_command))
            application.add_handler(CommandHandler("freecredits", free_credits_command))
            application.add_handler(CommandHandler("verify", verify_command))
            application.add_handler(CommandHandler("personality", self.personality_command))
            application.add_handler(CommandHandler("terms", self.terms_command))
            application.add_handler(CommandHandler("privacy", self.privacy_command))
            application.add_handler(CommandHandler("support", self.support_command))
            application.add_handler(CommandHandler("feedback", self.feedback_command))

            # Voice settings command handlers
            application.add_handler(CommandHandler("tts_provider", tts_provider_command))
            application.add_handler(CommandHandler("deepgram_voices", deepgram_voices_command))
            application.add_handler(CommandHandler("elevenlabs_voices", elevenlabs_voices_command))

            # Feature-specific command handlers
            config_manager = self.init_manager.get_component("config_manager")
            if config_manager.is_feature_enabled("mood_tracking"):
                application.add_handler(CommandHandler("mood", self.mood_command))
                application.add_handler(CommandHandler("moodentry", self.mood_entry_command))

            # Admin command handlers
            application.add_handler(CommandHandler("stats", self.stats_command))
            application.add_handler(CommandHandler("addcredits", self.add_credits_command))
            application.add_handler(CommandHandler("key", key_command))

            # Add security command if security monitoring is enabled
            if config_manager.is_feature_enabled("security_monitoring"):
                from bot.features.security_integration import security_command
                from bot.features.security_audit_integration import audit_logs_command
                from bot.features.payment_security_integration import payment_security_command
                application.add_handler(CommandHandler("security", security_command))
                application.add_handler(CommandHandler("auditlogs", audit_logs_command))
                application.add_handler(CommandHandler("paymentsecurity", payment_security_command))

            # Message handlers
            application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, self.handle_text))
            application.add_handler(MessageHandler(filters.VOICE, self.handle_voice))

            # Callback query handler
            application.add_handler(CallbackQueryHandler(self.handle_callback_query))

            # Error handler
            application.add_error_handler(self.error_handler)

            return True, None
        except Exception as e:
            logger.error(f"Error registering handlers: {e}")
            return False, str(e)

    async def _register_features_async(self) -> tuple[bool, Optional[str]]:
        """
        Register features in the feature registry asynchronously.

        Returns:
            Tuple of (success, error_message)
        """
        try:
            # Get dependencies
            feature_registry = self.init_manager.get_component("feature_registry")
            memory_manager = self.init_manager.get_component("memory_manager")
            mood_tracker = self.init_manager.get_component("mood_tracker")
            personalization_manager = self.init_manager.get_component("personalization_manager")

            # Register memory features
            if memory_manager:
                feature_registry.register_feature(
                    "personalized_response",
                    self._handle_personalized_response,
                    ["ai"]
                )
                feature_registry.enable_feature("personalized_response")

            # Register mood tracking features
            if mood_tracker:
                feature_registry.register_feature(
                    "mood_analysis",
                    self._handle_mood_analysis,
                    ["stt", "ai"]
                )
                feature_registry.enable_feature("mood_analysis")

            # Register personalization features
            if personalization_manager:
                feature_registry.register_feature(
                    "voice_customization",
                    self._handle_voice_customization,
                    ["tts"]
                )
                feature_registry.enable_feature("voice_customization")

            return True, None
        except Exception as e:
            logger.error(f"Error registering features: {e}")
            return False, str(e)

    async def _integrate_telegram_stars_payment(self) -> tuple[bool, Optional[str]]:
        """
        Integrate Telegram Stars payment asynchronously.

        Returns:
            Tuple of (success, error_message)
        """
        try:
            # Get dependencies
            application = self.init_manager.get_component("application")
            payment_system = self.init_manager.get_component("payment_system")

            # Initialize Telegram Stars payment integration
            from bot.payment.telegram_stars_integration import integrate_telegram_stars_payment
            integrate_telegram_stars_payment(self)

            return True, None
        except Exception as e:
            logger.error(f"Error integrating Telegram Stars payment: {e}")
            return False, str(e)

    async def _integrate_sentiment_analyzer(self) -> tuple[bool, Optional[str]]:
        """
        Integrate sentiment analyzer asynchronously.

        Returns:
            Tuple of (success, error_message)
        """
        try:
            # Get dependencies
            config_manager = self.init_manager.get_component("config_manager")

            # Initialize sentiment analyzer if enabled
            if config_manager.is_feature_enabled("sentiment_analysis"):
                # Integrate sentiment analyzer
                from bot.features.sentiment_integration import integrate_sentiment_analyzer
                integrate_sentiment_analyzer(self)

            return True, None
        except Exception as e:
            logger.error(f"Error integrating sentiment analyzer: {e}")
            return False, str(e)

    async def _integrate_enhanced_memory(self) -> tuple[bool, Optional[str]]:
        """
        Integrate enhanced memory system asynchronously.

        Returns:
            Tuple of (success, error_message)
        """
        try:
            # Get dependencies
            config_manager = self.init_manager.get_component("config_manager")
            memory_manager = self.init_manager.get_component("memory_manager")

            # Initialize enhanced memory system if memory is enabled
            if config_manager.is_feature_enabled("memory") and memory_manager:
                # Integrate enhanced memory system
                from bot.features.memory_integration import integrate_enhanced_memory
                enhanced_memory_manager = integrate_enhanced_memory(self)

                # Register with initialization manager
                self.init_manager.register_component("enhanced_memory_manager", enhanced_memory_manager)

            return True, None
        except Exception as e:
            logger.error(f"Error integrating enhanced memory: {e}")
            return False, str(e)

    async def _integrate_hierarchical_memory(self) -> tuple[bool, Optional[str]]:
        """
        Integrate hierarchical memory system with Redis and Qdrant.

        Returns:
            Tuple of (success, error_message)
        """
        try:
            # Get dependencies
            config_manager = self.init_manager.get_component("config_manager")
            memory_manager = self.init_manager.get_component("memory_manager")

            # Initialize hierarchical memory system if memory is enabled
            if config_manager.is_feature_enabled("memory") and memory_manager:
                # Check if hierarchical memory is specifically enabled
                memory_config = config_manager.get_feature_config("memory") or {}
                use_hierarchical = memory_config.get("use_hierarchical", True)

                if use_hierarchical:
                    # Integrate hierarchical memory system
                    from bot.features.memory_integration import integrate_hierarchical_memory
                    success = await integrate_hierarchical_memory(self)

                    if success:
                        # Update feature registry
                        if hasattr(self, 'feature_registry'):
                            self.feature_registry.register_feature(
                                "hierarchical_memory",
                                True,
                                "Hierarchical memory system with Redis and vector search"
                            )

                        logger.info("Hierarchical memory system integrated successfully")
                    else:
                        logger.warning("Failed to integrate hierarchical memory system")

            return True, None
        except Exception as e:
            logger.error(f"Error integrating hierarchical memory: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False, str(e)

    def _register_handlers(self) -> None:
        """Register message and command handlers (legacy method)."""
        # This method is kept for backward compatibility
        # It should not be used in new code
        logger.warning("Using legacy _register_handlers method. This should be replaced with _register_handlers_async.")

        # Command handlers
        self.application.add_handler(CommandHandler("start", self.start_command))
        self.application.add_handler(CommandHandler("help", self.help_command))
        self.application.add_handler(CommandHandler("credits", self.credits_command))
        self.application.add_handler(CommandHandler("freecredits", free_credits_command))
        self.application.add_handler(CommandHandler("verify", verify_command))
        self.application.add_handler(CommandHandler("personality", self.personality_command))
        self.application.add_handler(CommandHandler("terms", self.terms_command))
        self.application.add_handler(CommandHandler("privacy", self.privacy_command))
        self.application.add_handler(CommandHandler("support", self.support_command))
        self.application.add_handler(CommandHandler("feedback", self.feedback_command))

        # Initialize Telegram Stars payment integration
        # This will register all payment-related handlers
        integrate_telegram_stars_payment(self)

        # Initialize sentiment analyzer if enabled
        if self.config_manager.is_feature_enabled("sentiment_analysis"):
            # Integrate sentiment analyzer
            integrate_sentiment_analyzer(self)

        # Initialize enhanced memory system if memory is enabled
        if self.config_manager.is_feature_enabled("memory"):
            # Integrate enhanced memory system
            integrate_enhanced_memory(self)

        # Initialize security monitoring system
        if self.config_manager.is_feature_enabled("security_monitoring"):
            # Integrate security monitoring system
            integrate_security_monitoring(self)

            # Integrate security audit logging
            integrate_security_audit(self)

            # Integrate payment security monitoring
            integrate_payment_security(self)

        # Voice settings command handlers
        self.application.add_handler(CommandHandler("tts_provider", tts_provider_command))
        self.application.add_handler(CommandHandler("deepgram_voices", deepgram_voices_command))
        self.application.add_handler(CommandHandler("elevenlabs_voices", elevenlabs_voices_command))

        # Feature-specific command handlers
        if self.config_manager.is_feature_enabled("mood_tracking"):
            self.application.add_handler(CommandHandler("mood", self.mood_command))
            self.application.add_handler(CommandHandler("moodentry", self.mood_entry_command))

        # Admin command handlers
        self.application.add_handler(CommandHandler("stats", self.stats_command))
        self.application.add_handler(CommandHandler("addcredits", self.add_credits_command))
        self.application.add_handler(CommandHandler("key", key_command))

        # Add security command if security monitoring is enabled
        if self.config_manager.is_feature_enabled("security_monitoring"):
            from bot.features.security_integration import security_command
            from bot.features.security_audit_integration import audit_logs_command
            from bot.features.payment_security_integration import payment_security_command
            self.application.add_handler(CommandHandler("security", security_command))
            self.application.add_handler(CommandHandler("auditlogs", audit_logs_command))
            self.application.add_handler(CommandHandler("paymentsecurity", payment_security_command))

        # Message handlers
        self.application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, self.handle_text))
        self.application.add_handler(MessageHandler(filters.VOICE, self.handle_voice))

        # Callback query handler
        self.application.add_handler(CallbackQueryHandler(self.handle_callback_query))

        # Error handler
        self.application.add_error_handler(self.error_handler)

    # Command handlers and other methods are defined directly in the class
    # These were previously imported from main_rest*.py files

    # Feature handlers
    async def _handle_personalized_response(self, user_id: int, message: str, **kwargs) -> str:
        """
        Handle personalized response feature.

        Args:
            user_id: User ID
            message: User message
            **kwargs: Additional parameters

        Returns:
            str: Personalized response
        """
        # Get user context from memory manager
        user_context = self.memory_manager.get_conversation_context(user_id)
        logger.info(f"Getting conversation context for user {user_id}")

        # Log user context for debugging
        logger.info(f"User context for {user_id}: {user_context.keys() if user_context else 'None'}")

        # Add sentiment data to user context if available
        sentiment = kwargs.get("sentiment")
        if sentiment:
            if not user_context:
                user_context = {}
            user_context["sentiment"] = sentiment

        # Add response adjustment to user context if available
        response_adjustment = kwargs.get("response_adjustment")
        if response_adjustment:
            if not user_context:
                user_context = {}
            user_context["response_adjustment"] = response_adjustment

        # Process message using dialog engine
        response = await self.dialog_engine.process_message(
            user_id=user_id,
            message=message,
            language=kwargs.get("language"),
            is_voice=kwargs.get("is_voice", False)
        )

        # Format response with sentiment if available
        response_text = response.get("text", "")
        if hasattr(self, "sentiment_analyzer") and sentiment:
            from bot.features.sentiment_integration import format_response_with_sentiment
            response_text = format_response_with_sentiment(
                response_text,
                {"sentiment": sentiment, "response_adjustment": response_adjustment},
                self.database
            )

        return response_text

    async def _handle_mood_analysis(self, user_id: int, audio_file_path: str = None, text: str = None) -> Dict[str, Any]:
        """
        Handle mood analysis feature.

        Args:
            user_id: User ID
            audio_file_path: Path to audio file
            text: Text to analyze

        Returns:
            Dict containing mood information
        """
        # First try to use the dialog engine if it has analyze_mood method
        if hasattr(self.dialog_engine, "analyze_mood"):
            logger.info(f"Using dialog engine for mood analysis for user {user_id}")
            return await self.dialog_engine.analyze_mood(
                user_id=user_id,
                text=text,
                audio_file_path=audio_file_path
            )
        # Otherwise use the mood tracker if available
        elif hasattr(self, "mood_tracker") and self.mood_tracker:
            logger.info(f"Using mood tracker for mood analysis for user {user_id}")
            if audio_file_path:
                return await self.mood_tracker.analyze_voice_mood(user_id, audio_file_path)
            elif text:
                return self.mood_tracker.analyze_text_mood(user_id, text)
        # Fallback to default sentiment
        logger.warning(f"No mood analysis capability available for user {user_id}")
        return {"sentiment": "neutral", "confidence": 0.0, "response_adjustment": "Be neutral and factual."}

    async def _handle_voice_customization(self, user_id: int, text: str, **kwargs) -> str:
        """
        Handle voice customization feature.

        Args:
            user_id: User ID
            text: Text to convert to speech
            **kwargs: Additional parameters

        Returns:
            str: Path to generated audio file
        """
        try:
            # Get voice preferences
            voice_preferences = {}

            # Try to get from personalization manager if available
            if hasattr(self, 'personalization_manager') and self.personalization_manager:
                voice_preferences = self.personalization_manager.get_voice_preferences(user_id)
                logger.info(f"Got voice preferences from personalization manager for user {user_id}: {voice_preferences}")
            else:
                # Fall back to direct database queries
                logger.info(f"No personalization manager available, using direct database queries for user {user_id}")

                # Get user personality
                personality = self.database.get_user_personality(user_id) or "friendly"
                voice_preferences["personality"] = personality

                # Get TTS provider preference
                tts_provider = self.database.get_user_preference(user_id, "tts_provider")
                if tts_provider:
                    voice_preferences["tts_provider"] = tts_provider

                # Get voice ID preference
                voice_id = self.database.get_user_preference(user_id, "voice_id")
                if voice_id:
                    voice_preferences["voice_id"] = voice_id

            # Log voice preferences for debugging
            logger.info(f"Voice preferences for user {user_id}: {voice_preferences}")

            # Get TTS provider from preferences or use default
            tts_provider = voice_preferences.get("tts_provider")
            if not tts_provider:
                # Default to the configured TTS provider
                tts_config = self.config_manager.get_provider_config("tts")
                tts_provider = tts_config.get("type", "elevenlabs")

                # Save the default preference
                self.database.update_user_preference(user_id, "tts_provider", tts_provider)
                voice_preferences["tts_provider"] = tts_provider

            # Log the TTS provider
            logger.info(f"Using TTS provider: {tts_provider} for user {user_id}")

            # Check if we need to switch TTS provider
            if tts_provider != self.voice_processor.tts_provider_type:
                logger.info(f"Switching TTS provider from {self.voice_processor.tts_provider_type} to {tts_provider} for user {user_id}")

                # Get provider options
                provider_options = {}

                # Get API key securely from key manager
                if tts_provider == "deepgram" or tts_provider == "elevenlabs":
                    # Get API key from key manager
                    api_key = self.key_manager.get_key(tts_provider, user_id)

                    if api_key:
                        provider_options["api_key"] = api_key
                    else:
                        # Try config first
                        if tts_provider == "deepgram":
                            stt_config = self.config_manager.get_provider_config("stt")
                            api_key = stt_config.get("api_key")
                        else:  # elevenlabs
                            tts_config = self.config_manager.get_provider_config("tts")
                            api_key = tts_config.get("api_key")

                        # If not in config, try environment variable
                        if not api_key:
                            env_var_name = f"{tts_provider.upper()}_API_KEY"
                            api_key = os.getenv(env_var_name)

                        if api_key:
                            provider_options["api_key"] = api_key
                            # Store in key manager for future use
                            self.key_manager.store_key(tts_provider, api_key)

                    # Get voice ID from preferences or use default
                    voice_id = voice_preferences.get("voice_id")
                    if not voice_id:
                        # Default voice ID based on provider
                        if tts_provider == "elevenlabs":
                            voice_id = "21m00Tcm4TlvDq8ikWAM"  # Default ElevenLabs voice (Rachel)
                        elif tts_provider == "deepgram":
                            voice_id = "aura-thalia-en"  # Default Deepgram voice (fixed)

                        # Save the default preference
                        self.database.update_user_preference(user_id, "voice_id", voice_id)
                        voice_preferences["voice_id"] = voice_id

                    # Set voice ID in provider options
                    provider_options["voice_id"] = voice_id

                    # Set model ID based on provider
                    if tts_provider == "deepgram":
                        # Note: model_id is not used directly by Deepgram TTS, voice_id is used as the model parameter
                        provider_options["model_id"] = None  # Not needed for Deepgram
                    elif tts_provider == "elevenlabs":
                        provider_options["model_id"] = "eleven_multilingual_v2"

                # Try to switch provider
                try:
                    success = self.voice_processor.set_tts_provider(tts_provider, **provider_options)
                    if not success:
                        logger.warning(f"Failed to switch TTS provider to {tts_provider}, using current provider")
                except Exception as provider_error:
                    logger.error(f"Error switching TTS provider: {provider_error}")
                    import traceback
                    logger.error(traceback.format_exc())

            # Get voice ID from preferences
            voice_id = voice_preferences.get("voice_id")

            # Get personality from preferences
            personality = voice_preferences.get("personality", "friendly")

            # Check if streaming is enabled in kwargs
            use_streaming = kwargs.get("use_streaming", False)

            if use_streaming and self.voice_processor.tts_provider_type == "deepgram":
                # Use streaming TTS for Deepgram
                logger.info(f"Using streaming TTS for user {user_id}")

                try:
                    # We'll still generate a file for Telegram to send
                    voice_response_path = self.voice_processor.generate_voice_response(
                        text=text,
                        personality=personality,
                        language=kwargs.get("language", "en"),
                        voice=voice_id
                    )

                    # Also stream the audio for immediate playback (this happens in parallel)
                    if hasattr(self.voice_processor, 'stream_voice_response'):
                        asyncio.create_task(self.voice_processor.stream_voice_response(
                            text=text,
                            personality=personality,
                            language=kwargs.get("language", "en"),
                            voice=voice_id
                        ))

                    if voice_response_path:
                        logger.info(f"Successfully generated voice response for user {user_id} with streaming")
                    else:
                        logger.error(f"Failed to generate voice response for user {user_id} with streaming")

                    return voice_response_path
                except Exception as streaming_error:
                    logger.error(f"Error with streaming TTS: {streaming_error}")
                    logger.error(traceback.format_exc())

                    # Fall back to regular TTS
                    logger.info(f"Falling back to regular TTS due to streaming error")

            # Use regular TTS
            logger.info(f"Using regular TTS for user {user_id}")

            voice_response_path = self.voice_processor.generate_voice_response(
                text=text,
                personality=personality,
                language=kwargs.get("language", "en"),
                voice=voice_id
            )

            if voice_response_path:
                logger.info(f"Successfully generated voice response for user {user_id}")
            else:
                logger.error(f"Failed to generate voice response for user {user_id}")

            return voice_response_path

        except Exception as e:
            logger.error(f"Error in _handle_voice_customization: {e}")
            import traceback
            logger.error(traceback.format_exc())

            # Fall back to default voice processor
            try:
                logger.info("Attempting fallback to default voice processor")
                return self.voice_processor.generate_voice_response(
                    text=text,
                    language=kwargs.get("language", "en")
                )
            except Exception as e2:
                logger.error(f"Error in fallback voice generation: {e2}")
                logger.error(traceback.format_exc())

                # Try fallback to Google TTS as last resort
                try:
                    logger.info("Attempting fallback to Google TTS as last resort")
                    from gtts import gTTS
                    import tempfile

                    # Create a temporary file
                    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".mp3")
                    temp_file_path = temp_file.name
                    temp_file.close()

                    # Generate speech with Google TTS
                    lang = kwargs.get("language", "en")
                    if len(lang) > 2:
                        lang = lang[:2]  # Use first 2 chars of lang code for Google TTS

                    tts = gTTS(text=text, lang=lang, slow=False)
                    tts.save(temp_file_path)

                    logger.info(f"Successfully generated voice response with Google TTS fallback: {temp_file_path}")
                    return temp_file_path
                except Exception as fallback_error:
                    logger.error(f"Error with Google TTS fallback: {fallback_error}")
                    logger.error(traceback.format_exc())
                    return None

    # Command handlers
    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle the /start command."""
        from bot.core.security import generate_device_id, extract_ip_info

        user = update.effective_user
        user_id = user.id

        # Generate device ID and IP info
        device_id = generate_device_id(user, update)
        ip_info = extract_ip_info(update)

        # Add user to database with device tracking
        is_new_user = self.database.add_user(
            user_id=user_id,
            username=user.username,
            first_name=user.first_name,
            last_name=user.last_name,
            ip_address=ip_info,
            device_id=device_id
        )

        # Update user's last active time with IP tracking
        self.database.update_user_activity(user_id, ip_info)

        # Give free credits to new users
        credit_config = self.config_manager.get_credit_system_config()
        logger.info(f"User {user_id}: is_new_user={is_new_user}, credit_system_enabled={credit_config.get('enabled', True)}")

        if is_new_user and credit_config.get("enabled", True):
            free_credits = credit_config.get("free_trial_credits", 100)
            logger.info(f"Adding {free_credits} free trial credits to new user {user_id}")
            new_balance = self.database.add_credits(user_id, free_credits, source="free_trial")
            logger.info(f"User {user_id} new credit balance: {new_balance}")
        else:
            current_credits = self.database.get_user_credits(user_id)
            logger.info(f"Existing user {user_id} has {current_credits} credits")

            # For testing: if user has 0 credits and is admin, give them some credits
            admin_ids = self.config_manager.get_admin_user_ids()
            if current_credits == 0 and user_id in admin_ids:
                logger.info(f"Admin user {user_id} has 0 credits, adding 100 for testing")
                new_balance = self.database.add_credits(user_id, 100, source="admin_testing")
                logger.info(f"Admin user {user_id} new credit balance: {new_balance}")

        # Get user's personality from database or use default
        personalization_config = self.config_manager.get_feature_config("personalization")
        user_personality = self.database.get_user_personality(user_id) or personalization_config.get("default_personality", "friendly")

        # Set personality in AI provider and voice processor
        self.ai_provider.set_personality(user_personality)
        self.voice_processor.set_personality(user_personality)

        # Get greeting from AI
        greeting = await self.dialog_engine.process_message(user_id, "Hello")

        # Generate personalized welcome message using WelcomeManager
        welcome_text = self.welcome_manager.generate_welcome_message(user_id, is_new_user)

        # Get AI greeting text
        greeting_text = greeting.get("text", "Hello! How can I help you today?")

        # Combine welcome and greeting messages
        combined_message = f"{welcome_text}\n\n{greeting_text}"

        # Get main menu keyboard from new menu manager
        reply_markup = self.menu_manager.get_main_menu_keyboard()

        # Send combined welcome message with main menu
        await update.message.reply_text(combined_message, reply_markup=reply_markup, parse_mode="Markdown")

    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle the /help command."""
        credit_config = self.config_manager.get_credit_system_config()
        text_cost = credit_config.get("text_credit_cost", 1)
        voice_cost = credit_config.get("voice_credit_cost", 3)

        help_text = (
            "🤖 *VoicePal Help*\n\n"
            "I'm your AI friend that you can talk to via text or voice messages.\n\n"
            "*Commands:*\n"
            "/start - Start the bot\n"
            "/help - Show this help message\n"
            "/credits - Check your credit balance\n"
            "/buy - Purchase more credits\n"
            "/freecredits - Get free trial credits\n"
            "/verify - Verify your account for free credits\n"
            "/personality - Change my personality\n"
            "/terms - Show terms and conditions\n"
            "/privacy - View privacy policy\n"
            "/feedback - Send feedback\n"
            "/support - Get support\n\n"
            f"*Credits:*\n"
            f"Text messages cost {text_cost} credit\n"
            f"Voice messages cost {voice_cost} credits\n\n"
            "*Features:*\n"
        )

        # Add feature-specific help
        if self.config_manager.is_feature_enabled("memory"):
            help_text += "- I remember our conversations and adapt to your preferences\n"

        if self.config_manager.is_feature_enabled("mood_tracking"):
            help_text += "- I can track your mood over time with /mood command\n"

        if self.config_manager.is_feature_enabled("personalization"):
            help_text += "- You can customize my voice and personality\n"

        await update.message.reply_text(help_text, parse_mode="Markdown")

    async def credits_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle the /credits command."""
        user_id = update.effective_user.id
        user_credits = self.database.get_user_credits(user_id)

        credit_config = self.config_manager.get_credit_system_config()
        text_cost = credit_config.get("text_credit_cost", 1)
        voice_cost = credit_config.get("voice_credit_cost", 3)

        # Create message
        message = (
            f"💰 *Your Credits Balance* 💰\n\n"
            f"You have *{user_credits} credits* remaining ✨\n\n"
            f"📝 Text messages cost {text_cost} credit\n"
            f"🎤 Voice messages cost {voice_cost} credits\n\n"
            f"💳 Need more credits? Use /buy to purchase! 🛒"
        )

        # Get credits keyboard from new menu manager
        reply_markup = self.menu_manager.get_credits_keyboard()

        await update.message.reply_text(message, reply_markup=reply_markup, parse_mode="Markdown")

    # The buy_command is now handled directly by the payment_integration module

    async def personality_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle the /personality command."""
        # Create message
        message = (
            "👤 *Choose My Personality*\n\n"
            "Select a personality for me to adopt:"
        )

        # Get personality keyboard from MenuManager
        reply_markup = self.menu_manager.get_personality_keyboard()

        await update.message.reply_text(message, reply_markup=reply_markup, parse_mode="Markdown")

    async def terms_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle the /terms command."""
        terms_text = (
            "📜 *Terms and Conditions*\n\n"
            "By using VoicePal, you agree to the following terms:\n\n"
            "1. VoicePal is provided 'as is' without warranties.\n"
            "2. Credits purchased are non-refundable.\n"
            "3. We reserve the right to modify or discontinue the service.\n"
            "4. You are responsible for your interactions with the bot.\n"
            "5. We may update these terms at any time.\n\n"
            "For the full terms, visit our website."
        )

        await update.message.reply_text(terms_text, parse_mode="Markdown")

    async def privacy_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle the /privacy command."""
        privacy_text = (
            "🔒 *Privacy Policy*\n\n"
            "We take your privacy seriously:\n\n"
            "1. We store your conversations to provide the service.\n"
            "2. We use your data to improve the bot's responses.\n"
            "3. We do not sell your personal data to third parties.\n"
            "4. You can request deletion of your data at any time.\n"
            "5. We use secure methods to process payments.\n\n"
            "For the full privacy policy, visit our website."
        )

        await update.message.reply_text(privacy_text, parse_mode="Markdown")

    async def support_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle the /support command."""
        support_text = (
            "🆘 *Support*\n\n"
            "Need help with VoicePal? Here's how to get support:\n\n"
            "1. Check the /help command for basic information.\n"
            "2. Send feedback with /feedback [your message].\n"
            "3. Contact our support <NAME_EMAIL>\n\n"
            "We'll get back to you as soon as possible!"
        )

        await update.message.reply_text(support_text, parse_mode="Markdown")

    async def feedback_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle the /feedback command."""
        user_id = update.effective_user.id

        # Check if there are arguments (feedback text)
        if not context.args:
            await update.message.reply_text(
                "Please provide your feedback after the command.\n"
                "Example: /feedback I really enjoy talking with VoicePal!"
            )
            return

        # Get feedback text
        feedback_text = " ".join(context.args)

        # Store feedback in database (we would need to add a feedback table)
        # For now, just log it
        logger.info(f"Feedback from user {user_id}: {feedback_text}")

        await update.message.reply_text(
            "🙏 Thank you for your feedback! We appreciate your input and will use it to improve VoicePal."
        )

    async def mood_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle the /mood command."""
        if not self.config_manager.is_feature_enabled("mood_tracking"):
            await update.message.reply_text("Mood tracking is not enabled.")
            return

        user_id = update.effective_user.id

        # Show mood diary options using new menu manager
        message = "Mood Diary - Track and analyze your mood over time:"
        reply_markup = self.menu_manager.get_mood_diary_keyboard()

        await update.message.reply_text(message, reply_markup=reply_markup)

    async def mood_entry_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle the /moodentry command."""
        if not self.config_manager.is_feature_enabled("mood_tracking"):
            await update.message.reply_text("Mood tracking is not enabled.")
            return

        if not self.mood_entry:
            await update.message.reply_text("Mood entry is not available.")
            return

        # Start mood entry process
        await self.mood_entry.start_mood_entry(update, context)

    @rate_limit(rate=0.1, max_tokens=3, action="stats_command", exempt_func=is_admin_user)
    async def stats_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle the /stats command (admin only)."""
        user_id = update.effective_user.id
        admin_ids = self.config_manager.get_admin_user_ids()

        # Check if user is admin
        if user_id not in admin_ids:
            await update.message.reply_text("You don't have permission to use this command.")
            return

        # Get stats
        # pylint: disable=no-member
        stats = self.database.get_stats()
        # pylint: enable=no-member

        # Create message
        message = (
            "📊 *System Statistics*\n\n"
            f"Total users: {stats.get('total_users', 0)}\n"
            f"Active users (7d): {stats.get('active_users', 0)}\n"
            f"Total conversations: {stats.get('total_conversations', 0)}\n"
            f"Voice conversations: {stats.get('voice_conversations', 0)}\n"
            f"Total credits used: {stats.get('total_credits_used', 0)}\n"
            f"Total revenue: ${stats.get('total_revenue', 0):.2f}\n"
        )

        await update.message.reply_text(message, parse_mode="Markdown")

    # is_admin_user function moved to bot.core.security

    @rate_limit(rate=0.2, max_tokens=3, action="add_credits_command", exempt_func=is_admin_user)
    async def add_credits_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle the /addcredits command (admin only)."""
        from bot.core.validators import CommandValidator

        user_id = update.effective_user.id
        admin_ids = self.config_manager.get_admin_user_ids()

        # Check if user is admin
        if user_id not in admin_ids:
            await update.message.reply_text("You don't have permission to use this command.")
            return

        # Validate command arguments
        is_valid, error_message = CommandValidator.validate_addcredits_command(context.args)
        if not is_valid:
            await update.message.reply_text(error_message)
            return

        # Parse arguments (we know they're valid at this point)
        target_user_id = int(context.args[0])
        credit_amount = int(context.args[1])

        # Check if user exists
        user = self.database.get_user(target_user_id)
        if not user:
            await update.message.reply_text(f"User {target_user_id} not found.")
            return

        # Add credits
        new_balance = self.database.add_credits(target_user_id, credit_amount, source="admin")

        await update.message.reply_text(
            f"Added {credit_amount} credits to user {target_user_id}.\n"
            f"New balance: {new_balance} credits."
        )

    # Message handlers
    @rate_limit(rate=0.5, max_tokens=10, action="handle_text")
    async def handle_text(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle text messages."""
        from bot.core.security import generate_device_id, extract_ip_info
        from bot.core.validators import MessageValidator

        user = update.effective_user
        user_id = user.id
        message_text = update.message.text

        # Validate and sanitize input
        is_valid, error_message = MessageValidator.validate_text_message(message_text)
        if not is_valid:
            await update.message.reply_text(error_message)
            logger.warning(f"Invalid message from user {user_id}: {error_message}")
            return

        # Generate device ID and IP info
        device_id = generate_device_id(user, update)
        ip_info = extract_ip_info(update)

        # Update user with device tracking
        self.database.add_user(
            user_id=user_id,
            username=user.username,
            first_name=user.first_name,
            last_name=user.last_name,
            ip_address=ip_info,
            device_id=device_id
        )

        # Update user's last active time with IP tracking
        self.database.update_user_activity(user_id, ip_info)

        # Check if this is the user's first message in this session
        is_first_message = False
        if not hasattr(context, "user_data") or not context.user_data.get("has_sent_message", False):
            is_first_message = True
            if not hasattr(context, "user_data"):
                context.user_data = {}
            context.user_data["has_sent_message"] = True

        # Check if user has enough credits
        credit_config = self.config_manager.get_credit_system_config()
        text_cost = credit_config.get("text_credit_cost", 1)

        if self.database.get_user_credits(user_id) < text_cost:
            # Show menu buttons with the message using new menu manager
            reply_markup = self.menu_manager.get_main_menu_keyboard()
            await update.message.reply_text(
                "⚠️ You don't have enough credits. Use /buy to purchase more credits! 💰",
                reply_markup=reply_markup
            )
            return

        # Deduct credits
        self.database.use_credits(user_id, text_cost)

        # Log remaining credits
        remaining_credits = self.database.get_user_credits(user_id)
        logger.info(f"User {user_id} has {remaining_credits} credits remaining")

        # Send typing action
        await context.bot.send_chat_action(chat_id=update.effective_chat.id, action=ChatAction.TYPING)

        # Analyze sentiment if sentiment analyzer is available
        sentiment_data = None
        response_adjustment = None
        if hasattr(self, "sentiment_analyzer"):
            sentiment_data = await self.sentiment_analyzer.analyze_text_sentiment(
                message_text, user_id
            )
            response_adjustment = self.sentiment_analyzer.get_response_adjustment(sentiment_data)

            # Store sentiment data in context
            if not hasattr(context, "user_data"):
                context.user_data = {}

            context.user_data["sentiment"] = sentiment_data
            context.user_data["response_adjustment"] = response_adjustment

            # Log sentiment analysis
            logger.info(f"Sentiment analysis for user {user_id}: {sentiment_data.get('sentiment')} "
                       f"(confidence: {sentiment_data.get('confidence')})")

        # Process message
        response_text = ""
        try:
            if self.feature_registry.is_feature_enabled("personalized_response"):
                # Use personalized response feature
                response_text = await self._handle_personalized_response(
                    user_id=user_id,
                    message=message_text,
                    is_voice=False,
                    sentiment=sentiment_data,
                    response_adjustment=response_adjustment
                )

                # Analyze mood if mood tracking is enabled and sentiment analyzer is not available
                if self.feature_registry.is_feature_enabled("mood_analysis") and not hasattr(self, "sentiment_analyzer"):
                    await self._handle_mood_analysis(user_id=user_id, text=message_text)
            else:
                # Use simple response
                response = await self.dialog_engine.process_message(
                    user_id=user_id,
                    message=message_text,
                    is_voice=False
                )
                response_text = response.get("text", "I'm sorry, I couldn't process your message.")

            # Store conversation in database
            # This is important for memory and conversation history
            try:
                # Only store if we have a valid response
                if response_text:
                    conversation_id = self.database.add_conversation(
                        user_id=user_id,
                        message=message_text,
                        response=response_text,
                        is_voice=False,
                        credits_used=text_cost
                    )
                    logger.info(f"Stored conversation {conversation_id} for user {user_id}")

                    # Clear conversation cache if using enhanced memory manager
                    if hasattr(self, "enhanced_memory_manager"):
                        self.enhanced_memory_manager.clear_conversation_cache(user_id)
                        logger.info(f"Cleared conversation cache for user {user_id}")
            except Exception as e:
                logger.error(f"Error storing conversation for user {user_id}: {e}")
                import traceback
                logger.error(traceback.format_exc())

        except Exception as e:
            logger.error(f"Error processing message for user {user_id}: {e}")
            import traceback
            logger.error(traceback.format_exc())
            response_text = "I'm sorry, I couldn't process your message. Please try again."

        # Send response with appropriate buttons using new menu manager
        if not hasattr(context, "user_data"):
            context.user_data = {}

        if "message_count" not in context.user_data:
            context.user_data["message_count"] = 1
        else:
            context.user_data["message_count"] += 1

        # Use new menu manager to determine if menu should be shown
        show_menu = self.menu_manager.should_show_menu(
            message_count=context.user_data["message_count"],
            is_first_message=is_first_message
        )

        if show_menu:
            if is_first_message:
                # Show full menu for first message
                reply_markup = self.menu_manager.get_main_menu_keyboard()
            else:
                # Show persistent menu button for subsequent messages
                reply_markup = self.menu_manager.get_persistent_menu_button()

            await update.message.reply_text(response_text, reply_markup=reply_markup)
        else:
            # No menu button
            await update.message.reply_text(response_text)

    @rate_limit(rate=0.2, max_tokens=5, action="handle_voice")
    async def handle_voice(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle voice messages."""
        from bot.core.security import generate_device_id, extract_ip_info
        from bot.core.validators import MessageValidator

        user = update.effective_user
        user_id = user.id
        voice = update.message.voice

        # Validate voice message
        is_valid, error_message = MessageValidator.validate_voice_message(
            duration=voice.duration,
            file_size=voice.file_size
        )
        if not is_valid:
            await update.message.reply_text(error_message)
            logger.warning(f"Invalid voice message from user {user_id}: {error_message}")
            return

        # Generate device ID and IP info
        device_id = generate_device_id(user, update)
        ip_info = extract_ip_info(update)

        # Update user with device tracking
        self.database.add_user(
            user_id=user_id,
            username=user.username,
            first_name=user.first_name,
            last_name=user.last_name,
            ip_address=ip_info,
            device_id=device_id
        )

        # Update user's last active time with IP tracking
        self.database.update_user_activity(user_id, ip_info)

        # Check if this is the user's first message in this session
        is_first_message = False
        if not hasattr(context, "user_data") or not context.user_data.get("has_sent_message", False):
            is_first_message = True
            if not hasattr(context, "user_data"):
                context.user_data = {}
            context.user_data["has_sent_message"] = True

        # Check if user has enough credits
        credit_config = self.config_manager.get_credit_system_config()
        voice_cost = credit_config.get("voice_credit_cost", 3)

        if self.database.get_user_credits(user_id) < voice_cost:
            # Show menu buttons with the message
            reply_markup = self.keyboard_manager.get_main_menu_keyboard()
            await update.message.reply_text(
                "⚠️ You don't have enough credits. Use /buy to purchase more credits! 💰",
                reply_markup=reply_markup
            )
            return

        # Deduct credits
        self.database.use_credits(user_id, voice_cost)

        # Log remaining credits
        remaining_credits = self.database.get_user_credits(user_id)
        logger.info(f"User {user_id} has {remaining_credits} credits remaining")

        # Send typing action
        await context.bot.send_chat_action(chat_id=update.effective_chat.id, action=ChatAction.TYPING)

        # Download voice message
        voice_file = await context.bot.get_file(voice.file_id)

        with tempfile.NamedTemporaryFile(suffix=".ogg", delete=False) as temp_file:
            voice_path = temp_file.name
            await voice_file.download_to_drive(custom_path=voice_path)

        try:
            # Transcribe voice message
            transcription_result = await self.voice_processor.transcribe_audio(voice_path)
            transcript = transcription_result.get("transcript", "")
            detected_language = transcription_result.get("language", "en")

            # If transcription failed
            if not transcript:
                await update.message.reply_text(
                    "I couldn't understand your voice message. Please try again or send a text message."
                )
                return

            # Store transcript but don't display it to maintain conversation flow

            # Analyze sentiment if sentiment analyzer is available
            sentiment_data = None
            response_adjustment = None
            if hasattr(self, "sentiment_analyzer"):
                sentiment_data = await self.sentiment_analyzer.analyze_voice_sentiment(
                    voice_path, user_id
                )
                response_adjustment = self.sentiment_analyzer.get_response_adjustment(sentiment_data)

                # Store sentiment data in context
                if not hasattr(context, "user_data"):
                    context.user_data = {}

                context.user_data["sentiment"] = sentiment_data
                context.user_data["response_adjustment"] = response_adjustment

                # Log sentiment analysis
                logger.info(f"Sentiment analysis for user {user_id}: {sentiment_data.get('sentiment')} "
                           f"(confidence: {sentiment_data.get('confidence')})")

            # Process message
            response_text = ""
            try:
                if self.feature_registry.is_feature_enabled("personalized_response"):
                    # Use personalized response feature
                    response_text = await self._handle_personalized_response(
                        user_id=user_id,
                        message=transcript,
                        language=detected_language,
                        is_voice=True,
                        sentiment=sentiment_data,
                        response_adjustment=response_adjustment
                    )

                    # Analyze mood if mood tracking is enabled and sentiment analyzer is not available
                    if self.feature_registry.is_feature_enabled("mood_analysis") and not hasattr(self, "sentiment_analyzer"):
                        await self._handle_mood_analysis(user_id=user_id, audio_file_path=voice_path)
                else:
                    # Use simple response
                    response = await self.dialog_engine.process_message(
                        user_id=user_id,
                        message=transcript,
                        language=detected_language,
                        is_voice=True
                    )
                    response_text = response.get("text", "I'm sorry, I couldn't process your message.")

                # Store conversation in database
                # This is important for memory and conversation history
                try:
                    # Only store if we have a valid response
                    if response_text:
                        conversation_id = self.database.add_conversation(
                            user_id=user_id,
                            message=transcript,
                            response=response_text,
                            is_voice=True,
                            credits_used=voice_cost
                        )
                        logger.info(f"Stored voice conversation {conversation_id} for user {user_id}")

                        # Clear conversation cache if using enhanced memory manager
                        if hasattr(self, "enhanced_memory_manager"):
                            self.enhanced_memory_manager.clear_conversation_cache(user_id)
                            logger.info(f"Cleared conversation cache for user {user_id}")
                except Exception as e:
                    logger.error(f"Error storing voice conversation for user {user_id}: {e}")
                    import traceback
                    logger.error(traceback.format_exc())

            except Exception as e:
                logger.error(f"Error processing voice message for user {user_id}: {e}")
                import traceback
                logger.error(traceback.format_exc())
                response_text = "I'm sorry, I couldn't process your voice message. Please try again."

            # Generate voice response
            try:
                if self.feature_registry.is_feature_enabled("voice_customization"):
                    # Use voice customization feature with streaming for Deepgram
                    logger.info(f"Using voice customization for user {user_id}")
                    voice_response_path = await self._handle_voice_customization(
                        user_id=user_id,
                        text=response_text,
                        language=detected_language,
                        use_streaming=True  # Enable streaming for faster response
                    )
                else:
                    # Use simple voice response
                    logger.info(f"Using simple voice response for user {user_id}")
                    voice_response_path = self.voice_processor.generate_voice_response(
                        text=response_text,
                        language=detected_language
                    )

                logger.info(f"Voice response path: {voice_response_path}")
            except Exception as e:
                logger.error(f"Error generating voice response: {e}")
                import traceback
                logger.error(traceback.format_exc())
                voice_response_path = None

            # Send voice response with menu buttons if this is the first message
            if voice_response_path and os.path.exists(voice_response_path):
                try:
                    logger.info(f"Sending voice response from {voice_response_path}")

                    # Check file size
                    file_size = os.path.getsize(voice_response_path)
                    logger.info(f"Voice file size: {file_size} bytes")

                    if file_size == 0:
                        logger.error("Voice file is empty, falling back to text response")
                        voice_response_path = None
                    else:
                        # Open the file and send it
                        with open(voice_response_path, "rb") as voice_file:
                            if is_first_message:
                                # First send voice response
                                await context.bot.send_voice(
                                    chat_id=update.effective_chat.id,
                                    voice=voice_file
                                )
                                # Then send menu buttons in a separate message
                                reply_markup = self.keyboard_manager.get_main_menu_keyboard()
                                await update.message.reply_text(
                                    "Here are some options to help you navigate:",
                                    reply_markup=reply_markup
                                )
                            else:
                                # Regular voice response without persistent menu button for cleaner interface
                                await context.bot.send_voice(
                                    chat_id=update.effective_chat.id,
                                    voice=voice_file
                                )

                                # Only show menu button occasionally
                                show_menu = False
                                if not hasattr(context, "user_data"):
                                    context.user_data = {}

                                if "message_count" not in context.user_data:
                                    context.user_data["message_count"] = 1
                                else:
                                    context.user_data["message_count"] += 1

                                # Show menu button every 5th message
                                if context.user_data["message_count"] % 5 == 0:
                                    show_menu = True

                                if show_menu:
                                    # Add persistent menu button in a separate message for better navigation
                                    reply_markup = self.keyboard_manager.get_persistent_menu_button()
                                    await update.message.reply_text(
                                        "Need anything else?",
                                        reply_markup=reply_markup
                                    )
                except Exception as e:
                    logger.error(f"Error sending voice response: {e}")
                    import traceback
                    logger.error(traceback.format_exc())
                    voice_response_path = None

            # If voice response failed, fall back to text response
            if not voice_response_path or not os.path.exists(voice_response_path):
                logger.info(f"Falling back to text response for user {user_id}")
                if is_first_message:
                    # Show menu buttons with the first response
                    reply_markup = self.keyboard_manager.get_main_menu_keyboard()
                    await update.message.reply_text(response_text, reply_markup=reply_markup)
                else:
                    # Regular response without persistent menu button for cleaner interface
                    # Only show menu button occasionally
                    show_menu = False
                    if not hasattr(context, "user_data"):
                        context.user_data = {}

                    if "message_count" not in context.user_data:
                        context.user_data["message_count"] = 1
                    else:
                        context.user_data["message_count"] += 1

                    # Only show menu when explicitly requested, not automatically
                    show_menu = False

                    if show_menu:
                        reply_markup = self.keyboard_manager.get_persistent_menu_button()
                        await update.message.reply_text(response_text, reply_markup=reply_markup)
                    else:
                        # No menu button for most messages
                        await update.message.reply_text(response_text)
        finally:
            # Clean up temporary files
            try:
                os.unlink(voice_path)
            except Exception as e:
                logger.error(f"Error deleting temporary file {voice_path}: {e}")

    @rate_limit(rate=1.0, max_tokens=20, action="handle_callback_query")
    async def handle_callback_query(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """
        Handle callback queries using the navigation router.

        This is the centralized entry point for all callback query handling.
        All callbacks are routed through the NavigationRouter for consistent handling.
        """
        from bot.core.security import extract_ip_info

        query = update.callback_query
        user = query.from_user
        user_id = user.id
        callback_data = query.data

        # Acknowledge the callback query immediately to prevent timeout
        try:
            await query.answer()
        except Exception as e:
            logger.warning(f"Failed to acknowledge callback query: {e}")
            # Continue processing even if acknowledgment fails

        # Generate IP info for tracking
        ip_info = extract_ip_info(update)

        # Update user's last active time with IP tracking
        self.database.update_user_activity(user_id, ip_info)

        # Log the callback data for debugging
        logger.info(f"Received callback query: {callback_data} from user {user_id}")

        # Make sure bot instance is set in context for handlers that need it
        context.application.bot_data["bot_instance"] = self
        context.bot_data["bot_instance"] = self

        # Route the callback through the navigation router
        try:
            handled = await self.navigation_router.route_callback(update, context)

            if not handled:
                logger.warning(f"No handler found for callback: {callback_data} from user {user_id}")

                # Show error message and main menu if no handler was found
                try:
                    # Check if we have a button state manager for recovery suggestions
                    if hasattr(self, 'button_state_manager') and self.button_state_manager:
                        # Register error for recovery
                        self.button_state_manager.register_error(user_id, callback_data, Exception("Unhandled callback"))

                        recovery_suggestion = self.button_state_manager.get_error_recovery_suggestion(user_id, callback_data)
                        if recovery_suggestion:
                            logger.info(f"Using recovery suggestion: {recovery_suggestion} for user {user_id}")

                            # Process the recovery suggestion
                            if recovery_suggestion == "back_to_main":
                                await query.edit_message_text(
                                    "Sorry, I couldn't process that request. Here's the main menu:",
                                    reply_markup=self.menu_manager.get_main_menu_keyboard()
                                )
                                return
                            elif recovery_suggestion == "show_help":
                                await query.edit_message_text(
                                    "Sorry, I couldn't process that request. Here's some help:",
                                    reply_markup=self.menu_manager.get_help_keyboard()
                                )
                                return

                    # If no recovery suggestion, show the main menu
                    await query.edit_message_text(
                        "I'm not sure how to handle that request. Here's the main menu:",
                        reply_markup=self.menu_manager.get_main_menu_keyboard()
                    )
                except Exception as recovery_error:
                    logger.error(f"Error showing recovery menu: {recovery_error}")

                    # Last resort: send a simple message with the main menu
                    try:
                        await query.message.reply_text(
                            "Something went wrong. Here's the main menu:",
                            reply_markup=self.menu_manager.get_main_menu_keyboard()
                        )
                    except Exception:
                        logger.error("Failed to show any fallback menu")

        except Exception as e:
            logger.error(f"Error in navigation router for callback {callback_data}: {e}")
            import traceback
            logger.error(traceback.format_exc())

            # Show error message and main menu
            try:
                await query.edit_message_text(
                    "Sorry, there was an error processing your request. Please try again.",
                    reply_markup=self.menu_manager.get_main_menu_keyboard()
                )
            except Exception as edit_error:
                logger.error(f"Error sending error message: {edit_error}")

                # Try sending a new message if editing fails
                try:
                    await query.message.reply_text(
                        "Sorry, there was an error processing your request. Please try again.",
                        reply_markup=self.menu_manager.get_main_menu_keyboard()
                    )
                except Exception:
                    logger.error("Failed to send any error message")

    async def _handle_callback_legacy(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle callback queries using legacy handlers for compatibility."""
        query = update.callback_query
        user_id = query.from_user.id
        callback_data = query.data

        # Handle payment callbacks first (they need special handling)
        if callback_data.startswith("buy_credits_"):
            # Forward to payment system
            try:
                package_id = callback_data.replace("buy_credits_", "")
                logger.info(f"Starting payment for package {package_id} for user {user_id}")

                # Get credit package details
                package = self.payment_system.get_credit_package(package_id)
                if package:
                    await self.payment_system.start_payment(update, context, package_id)
                    logger.info(f"Payment process started for user {user_id}")
                    return
                else:
                    await query.message.reply_text("Invalid credit package. Please try again.")
                    return
            except Exception as e:
                logger.error(f"Error starting payment: {e}")
                await query.message.reply_text("Sorry, there was an error processing your payment request. Please try again later.")
                return

        # Handle voice settings callbacks
        try:
            # Make sure bot instance is set in context
            context.application.bot_data["bot_instance"] = self
            context.bot_data["bot_instance"] = self

            # Call voice settings callback handler
            voice_settings_response = await handle_voice_settings_callback(update, context)
            if voice_settings_response:
                response_text, reply_markup = voice_settings_response
                if response_text:
                    await query.edit_message_text(response_text, reply_markup=reply_markup)
                    logger.info(f"Voice settings updated for user {user_id}")
                    return
        except Exception as e:
            logger.error(f"Error handling voice settings callback: {e}")
            import traceback
            logger.error(traceback.format_exc())

        # Handle key management callbacks
        try:
            # Call key management callback handler
            key_management_response = await handle_key_management_callback(update, context)
            if key_management_response:
                response_text, reply_markup = key_management_response
                if response_text:
                    await query.edit_message_text(response_text, reply_markup=reply_markup, parse_mode="Markdown")
                    logger.info(f"Key management action performed for user {user_id}")
                    return
        except Exception as e:
            logger.error(f"Error handling key management callback: {e}")
            import traceback
            logger.error(traceback.format_exc())

        # Handle free credits callbacks
        try:
            # Call free credits callback handler
            free_credits_response = await handle_free_credits_callback(update, context)
            if free_credits_response:
                response_text, reply_markup = free_credits_response
                if response_text:
                    await query.edit_message_text(response_text, reply_markup=reply_markup, parse_mode="Markdown")
                    logger.info(f"Free credits action performed for user {user_id}")
                    return
        except Exception as e:
            logger.error(f"Error handling free credits callback: {e}")
            import traceback
            logger.error(traceback.format_exc())

        # Handle personality selection
        if callback_data.startswith("set_personality_"):
            try:
                personality_id = callback_data.replace("set_personality_", "")

                # Log the personality selection
                logger.info(f"Setting personality to {personality_id} for user {user_id}")

                # Update personality in database - try both methods for compatibility
                try:
                    # First try update_user_personality
                    if hasattr(self.database, 'update_user_personality'):
                        self.database.update_user_personality(user_id, personality_id)
                    # Then try set_user_personality as fallback
                    elif hasattr(self.database, 'set_user_personality'):
                        self.database.set_user_personality(user_id, personality_id)
                    else:
                        # Last resort: update user preference
                        self.database.update_user_preference(user_id, "personality", personality_id)

                    logger.info(f"Successfully updated personality in database for user {user_id}")
                except Exception as db_error:
                    logger.error(f"Error updating personality in database: {db_error}")
                    import traceback
                    logger.error(traceback.format_exc())

                # Update personality in AI provider
                try:
                    if self.ai_provider and hasattr(self.ai_provider, 'set_personality'):
                        self.ai_provider.set_personality(personality_id)
                        logger.info(f"Successfully updated personality in AI provider for user {user_id}")
                    else:
                        logger.warning(f"AI provider does not support set_personality method")
                except Exception as ai_error:
                    logger.error(f"Error updating personality in AI provider: {ai_error}")
                    import traceback
                    logger.error(traceback.format_exc())

                # Update personality in voice processor
                try:
                    if self.voice_processor and hasattr(self.voice_processor, 'set_personality'):
                        self.voice_processor.set_personality(personality_id)
                        logger.info(f"Successfully updated personality in voice processor for user {user_id}")
                    else:
                        logger.warning(f"Voice processor does not support set_personality method")
                except Exception as voice_error:
                    logger.error(f"Error updating personality in voice processor: {voice_error}")
                    import traceback
                    logger.error(traceback.format_exc())

                # Get personality details from keyboard manager
                personality = self.keyboard_manager.personalities.get(personality_id, {"name": personality_id, "emoji": "😊"})

                # Create response
                response_text = f"Personality set to {personality['emoji']} {personality['name']}. I'll adapt my responses accordingly."

                # Save button state
                if hasattr(self, 'button_state_manager') and self.button_state_manager:
                    self.button_state_manager.save_button_state(
                        user_id=user_id,
                        menu_id="personality",
                        state={"personality": personality_id}
                    )

                # Get main menu keyboard from new menu manager
                reply_markup = self.menu_manager.get_main_menu_keyboard()

                # Send response
                await query.edit_message_text(response_text, reply_markup=reply_markup, parse_mode="Markdown")
                logger.info(f"Personality set to {personality_id} for user {user_id}")
                return
            except Exception as e:
                logger.error(f"Error setting personality: {e}")

        # Use the NavigationRouter to handle menu navigation
        try:
            # Route the callback through the navigation router
            handled = await self.navigation_router.route_callback(update, context)
            if handled:
                logger.info(f"Processed callback query {callback_data} for user {user_id} via navigation router")
                return
        except Exception as e:
            logger.error(f"Error processing callback query with navigation router: {e}")

        # Handle mood diary options
        if callback_data.startswith("mood_"):
            try:
                # Handle mood diary options
                period = callback_data.replace("mood_", "")

                if not self.mood_tracker:
                    await query.edit_message_text("Mood tracking is not enabled.")
                    return

                if period == "today":
                    # Get today's mood summary
                    mood_summary = self.mood_tracker.get_daily_mood_summary(user_id)
                    message = self.mood_tracker.format_mood_summary_text(mood_summary, "daily")
                elif period == "weekly":
                    # Get weekly mood summary
                    mood_summary = self.mood_tracker.get_weekly_mood_summary(user_id)
                    message = self.mood_tracker.format_mood_summary_text(mood_summary, "weekly")
                elif period == "monthly":
                    # Get monthly mood summary
                    mood_summary = self.mood_tracker.get_monthly_mood_summary(user_id)
                    message = self.mood_tracker.format_mood_summary_text(mood_summary, "monthly")
                elif period == "insights":
                    # Get mood insights
                    mood_insights = self.mood_tracker.generate_mood_insights(user_id)
                    message = self.mood_tracker.format_mood_summary_text(mood_insights, "insights")
                else:
                    message = "Invalid mood period selected."

                # Add back button
                keyboard = [[InlineKeyboardButton("🔙 Back to Mood Diary", callback_data="show_mood_diary")]]
                reply_markup = InlineKeyboardMarkup(keyboard)

                # Send mood summary
                await query.edit_message_text(message, reply_markup=reply_markup, parse_mode="Markdown")
                logger.info(f"Displayed mood {period} for user {user_id}")
                return
            except Exception as e:
                logger.error(f"Error handling mood callback: {e}")

        elif callback_data.startswith("mood_entry_"):
            try:
                # Always acknowledge the callback query first to prevent timeout
                await query.answer()

                # Handle mood entry callback
                if self.mood_entry:
                    result = await self.mood_entry.handle_mood_entry_callback(update, context)
                    logger.info(f"Handled mood entry callback for user {user_id}")

                    # If the handler returned a result, use it to update the message
                    if result and isinstance(result, tuple) and len(result) == 2:
                        message, reply_markup = result
                        if message:
                            try:
                                await query.edit_message_text(message, reply_markup=reply_markup, parse_mode="Markdown")
                            except Exception as edit_error:
                                if "Message is not modified" in str(edit_error):
                                    logger.info("Message content unchanged")
                                else:
                                    logger.error(f"Error editing message: {edit_error}")
                else:
                    await query.answer("Mood tracking is not enabled")
                    await query.edit_message_text("Mood tracking is not enabled.")
                return
            except Exception as e:
                logger.error(f"Error handling mood entry callback: {e}")
                import traceback
                logger.error(traceback.format_exc())
                # Try to acknowledge the query even if there was an error
                try:
                    await query.answer("An error occurred")
                except:
                    pass

        elif callback_data.startswith("mood_followup_"):
            try:
                # Always acknowledge the callback query first to prevent timeout
                await query.answer()

                # Handle mood followup callback
                if self.mood_entry:
                    result = await self.mood_entry.handle_mood_followup_callback(update, context)
                    logger.info(f"Handled mood followup callback for user {user_id}")

                    # If the handler returned a result, use it to update the message
                    if result and isinstance(result, tuple) and len(result) == 2:
                        message, reply_markup = result
                        if message:
                            try:
                                await query.edit_message_text(message, reply_markup=reply_markup, parse_mode="Markdown")
                            except Exception as edit_error:
                                if "Message is not modified" in str(edit_error):
                                    logger.info("Message content unchanged")
                                else:
                                    logger.error(f"Error editing message: {edit_error}")
                else:
                    await query.answer("Mood tracking is not enabled")
                    await query.edit_message_text("Mood tracking is not enabled.")
                return
            except Exception as e:
                logger.error(f"Error handling mood followup callback: {e}")
                import traceback
                logger.error(traceback.format_exc())
                # Try to acknowledge the query even if there was an error
                try:
                    await query.answer("An error occurred")
                except:
                    pass

        # If we get here, the callback wasn't handled
        logger.warning(f"Unhandled callback query: {callback_data} from user {user_id}")

        # Try to recover using button state manager
        if hasattr(self, 'button_state_manager') and self.button_state_manager:
            # Register error for recovery
            self.button_state_manager.register_error(user_id, callback_data, Exception("Unhandled callback"))

            # Try to get recovery suggestion
            recovery_suggestion = self.button_state_manager.get_recovery_suggestion(user_id, callback_data)
            if recovery_suggestion:
                logger.info(f"Using recovery suggestion: {recovery_suggestion} for user {user_id}")

                # Process the recovery suggestion
                response, reply_markup = self.keyboard_manager.process_callback_query(recovery_suggestion, user_id)
                if response and reply_markup:
                    try:
                        await query.edit_message_text(
                            f"Sorry, I couldn't process that request. {response}",
                            reply_markup=reply_markup,
                            parse_mode="Markdown"
                        )
                        return
                    except Exception as e:
                        logger.error(f"Error showing recovery menu: {e}")
                        import traceback
                        logger.error(traceback.format_exc())

            # Try to get previous menu from history
            previous_menu = self.keyboard_manager.get_previous_menu(user_id)
            if previous_menu:
                logger.info(f"Using previous menu: {previous_menu} for user {user_id}")

                # Process the previous menu
                response, reply_markup = self.keyboard_manager.process_callback_query(previous_menu, user_id)
                if response and reply_markup:
                    try:
                        await query.edit_message_text(
                            f"Sorry, I couldn't process that request. Going back to the previous menu.",
                            reply_markup=reply_markup,
                            parse_mode="Markdown"
                        )
                        return
                    except Exception as e:
                        logger.error(f"Error showing previous menu: {e}")
                        import traceback
                        logger.error(traceback.format_exc())

        # Show main menu as fallback
        try:
            # Try to edit the message first
            try:
                await query.edit_message_text(
                    "I'm not sure how to handle that request. Here's the main menu:",
                    reply_markup=self.menu_manager.get_main_menu_keyboard()
                )
            except Exception as edit_error:
                # If editing fails (e.g., message is too old), send a new message
                logger.error(f"Error editing message: {edit_error}")
                await query.message.reply_text(
                    "I'm not sure how to handle that request. Here's the main menu:",
                    reply_markup=self.menu_manager.get_main_menu_keyboard()
                )
        except Exception as e:
            logger.error(f"Error showing fallback menu: {e}")
            import traceback
            logger.error(traceback.format_exc())

            # Last resort: send a simple message with the main menu
            try:
                await query.message.reply_text(
                    "Something went wrong. Here's the main menu:",
                    reply_markup=self.menu_manager.get_main_menu_keyboard()
                )
            except Exception:
                logger.error("Failed to show any fallback menu")

    # Payment handlers are now registered directly by the payment_integration module

    async def error_handler(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle errors."""
        logger.error(f"Update {update} caused error {context.error}")

        # Send error message to user if possible
        if update and update.effective_chat:
            await context.bot.send_message(
                chat_id=update.effective_chat.id,
                text="Sorry, an error occurred while processing your request. Please try again later."
            )

    async def set_commands(self) -> None:
        """Set bot commands that will be shown in the menu."""
        from telegram import MenuButtonCommands

        # Only include the most essential commands in the menu
        # The rest are accessible through the button-based menu
        commands = [
            BotCommand("start", "🚀 Start the bot and show menu"),
            BotCommand("help", "❓ Show help information"),
            BotCommand("feedback", "📝 Send feedback"),
            BotCommand("support", "🆘 Get support")
        ]

        # Add admin commands for admin users
        admin_commands = [
            BotCommand("stats", "📊 Show system statistics (admin only)"),
            BotCommand("addcredits", "💰 Add credits to a user (admin only)"),
            BotCommand("key", "🔑 Manage API keys (admin only)")
        ]

        # Add security command if security monitoring is enabled
        if self.config_manager.is_feature_enabled("security_monitoring", True):
            admin_commands.append(
                BotCommand("security", "🛡️ Security monitoring dashboard (admin only)")
            )
            admin_commands.append(
                BotCommand("auditlogs", "📋 Security audit logs (admin only)")
            )
            admin_commands.append(
                BotCommand("paymentsecurity", "💳 Payment security monitoring (admin only)")
            )

        # Set regular commands for all users
        await self.application.bot.set_my_commands(commands)

        # Enable the menu button (4 squares icon)
        try:
            await self.application.bot.set_chat_menu_button(
                menu_button=MenuButtonCommands()
            )
            logger.info("Enabled menu button with commands")
        except Exception as e:
            logger.error(f"Error enabling menu button: {e}")
            import traceback
            logger.error(traceback.format_exc())

        # Set admin commands for admin users
        admin_ids = self.config_manager.get_admin_user_ids()
        for admin_id in admin_ids:
            try:
                await self.application.bot.set_my_commands(
                    commands + admin_commands,
                    scope=BotCommandScopeChat(chat_id=admin_id)
                )
                logger.info(f"Set admin commands for admin user {admin_id}")
            except Exception as e:
                logger.error(f"Failed to set admin commands for admin user {admin_id}: {e}")

    async def _send_welcome_to_active_users(self) -> None:
        """Send welcome message to active users when the bot starts."""
        try:
            # Get active users (users who have interacted with the bot in the last 7 days)
            active_users = self.database.get_active_users(days=7)

            if not active_users:
                logger.info("No active users found to send welcome message")
                return

            logger.info(f"Sending welcome message to {len(active_users)} active users")

            # Create welcome message
            welcome_text = (
                "🎉 *VoicePal is now online!*\n\n"
                "I'm here to chat with you via text or voice messages. "
                "Use the buttons below to get started or type /help for more information."
            )

            # Get main menu keyboard from new menu manager
            reply_markup = self.menu_manager.get_main_menu_keyboard()

            # Send message to each active user
            for user in active_users:
                try:
                    user_id = user.get('user_id')
                    if user_id:
                        await self.application.bot.send_message(
                            chat_id=user_id,
                            text=welcome_text,
                            reply_markup=reply_markup,
                            parse_mode="Markdown"
                        )
                        logger.info(f"Sent welcome message to user {user_id}")
                except Exception as e:
                    logger.error(f"Error sending welcome message to user {user_id}: {e}")

        except Exception as e:
            logger.error(f"Error sending welcome messages to active users: {e}")

    async def run(self, webhook_url: Optional[str] = None) -> None:
        """
        Run the bot.

        Args:
            webhook_url: Optional webhook URL for production deployment
        """
        # Set bot commands
        await self.set_commands()

        # Start the bot
        await self.application.initialize()

        # Log initialization status
        init_status = self.init_manager.get_initialization_status()
        logger.info(f"Initialization status: {len(init_status['initialization_steps'])} steps, {len(init_status['integration_steps'])} integrations")

        # Log any failed steps
        failed_steps = [step for step in init_status['initialization_steps'] if step['executed'] and not step['success']]
        if failed_steps:
            logger.warning(f"Failed initialization steps: {[step['name'] for step in failed_steps]}")

        failed_integrations = [step for step in init_status['integration_steps'] if step['executed'] and not step['success']]
        if failed_integrations:
            logger.warning(f"Failed integration steps: {[step['name'] for step in failed_integrations]}")

        # Configure webhook if URL is provided
        if webhook_url:
            # Extract the path from the webhook URL
            from urllib.parse import urlparse
            parsed_url = urlparse(webhook_url)
            webhook_path = parsed_url.path or "/webhook"

            # Get the port from environment or use default
            port = int(os.environ.get("PORT", 8443))

            # Set up the webhook server
            # For Render, we need to use a simpler approach
            from telegram.ext import Application

            # Set the webhook with retry logic for rate limiting
            max_retries = 3
            retry_count = 0
            success = False

            while retry_count < max_retries and not success:
                try:
                    # Set the webhook directly
                    await self.application.bot.set_webhook(url=webhook_url)
                    success = True
                    logger.info("Webhook set successfully")
                except RetryAfter as e:
                    retry_count += 1
                    retry_seconds = e.retry_after
                    logger.warning(f"Rate limit hit when setting webhook. Retrying in {retry_seconds} seconds (attempt {retry_count}/{max_retries})")
                    await asyncio.sleep(retry_seconds + 1)  # Add 1 second buffer
                except Exception as e:
                    retry_count += 1
                    logger.error(f"Error setting webhook (attempt {retry_count}/{max_retries}): {e}")
                    if retry_count < max_retries:
                        await asyncio.sleep(5)
                    else:
                        logger.error("Failed to set webhook after multiple attempts")
                        raise

            # Start the web server
            from telegram.ext import Updater

            # Log the port we're using
            logger.info(f"Starting webhook server on port {port}")

            # Instead of using run_webhook, we'll set up a custom webhook server
            # using aiohttp which works better with our existing event loop
            from aiohttp import web

            # Create webhook handler
            async def webhook_handler(request):
                try:
                    logger.info(f"Received webhook request from {request.remote}")

                    # Get the request data
                    update_data = await request.json()

                    # Create an Update object
                    from telegram import Update
                    update = Update.de_json(update_data, self.application.bot)

                    # Process the update
                    await self.application.process_update(update)

                    return web.Response(text="OK")
                except Exception as e:
                    logger.error(f"Error processing webhook update: {e}")
                    import traceback
                    logger.error(traceback.format_exc())
                    return web.Response(text="Error", status=500)

            # Create health check handler
            async def health_check_handler(request):
                logger.info(f"Health check request from {request.remote}")
                return web.Response(text="VoicePal Bot is running!")

            # Create the web app
            app = web.Application()
            app.router.add_post(webhook_path, webhook_handler)
            app.router.add_get("/", health_check_handler)

            # Set up the web server
            runner = web.AppRunner(app)
            await runner.setup()
            site = web.TCPSite(runner, "0.0.0.0", port)

            # Start the web server
            await site.start()
            logger.info(f"Webhook server started on port {port}")

            # Send welcome message to active users
            await self._send_welcome_to_active_users()
            logger.info(f"Bot initialized in webhook mode with URL: {webhook_url} on port {port}")

            # Return the runner so it can be cleaned up later if needed
            return runner
        else:
            # Development mode with polling
            logger.info("Starting bot in polling mode")
            await self.application.start()
            await self.application.updater.start_polling()

            # For development mode, we'll return here and let the main function
            # handle keeping the application running
            return


# Main function to run the bot
async def main() -> None:
    """Main function to run the bot."""
    bot = VoicePalBot()

    # Check if running in production (Render) or development mode
    webhook_url = os.environ.get("WEBHOOK_URL")
    is_render = os.environ.get("RENDER", "").lower() == "true"

    try:
        if webhook_url:
            logger.info(f"Running in production mode with webhook: {webhook_url}")
            try:
                # Run the bot with webhook
                runner = await bot.run(webhook_url=webhook_url)

                # Keep the application running
                while True:
                    await asyncio.sleep(3600)  # Sleep for an hour

            except RetryAfter as e:
                # If we hit a rate limit, wait and retry once
                retry_seconds = e.retry_after
                logger.warning(f"Rate limit hit when starting bot. Retrying in {retry_seconds} seconds")
                await asyncio.sleep(retry_seconds + 1)  # Add 1 second buffer

                # Run the bot with webhook after waiting
                runner = await bot.run(webhook_url=webhook_url)

                # Keep the application running
                while True:
                    await asyncio.sleep(3600)  # Sleep for an hour

            except KeyboardInterrupt:
                logger.info("Bot stopped by user")
                # Clean up if needed
                if runner:
                    await runner.cleanup()

        elif is_render:
            # If running on Render but no webhook URL is set, create a dummy webhook URL
            # This is just to make the bot listen on a port to satisfy Render's requirements
            render_service_name = os.environ.get("RENDER_SERVICE_NAME", "voicepal-bot")
            port = int(os.environ.get("PORT", 8443))
            dummy_webhook_url = f"https://{render_service_name}.onrender.com/webhook"
            logger.info(f"Running on Render with webhook: {dummy_webhook_url} on port {port}")

            try:
                # Run the bot with webhook
                runner = await bot.run(webhook_url=dummy_webhook_url)

                # Keep the application running
                while True:
                    await asyncio.sleep(3600)  # Sleep for an hour

            except RetryAfter as e:
                # If we hit a rate limit, wait and retry once
                retry_seconds = e.retry_after
                logger.warning(f"Rate limit hit when starting bot. Retrying in {retry_seconds} seconds")
                await asyncio.sleep(retry_seconds + 1)  # Add 1 second buffer

                # Run the bot with webhook after waiting
                runner = await bot.run(webhook_url=dummy_webhook_url)

                # Keep the application running
                while True:
                    await asyncio.sleep(3600)  # Sleep for an hour

            except KeyboardInterrupt:
                logger.info("Bot stopped by user")
                # Clean up if needed
                if runner:
                    await runner.cleanup()

        else:
            logger.info("Running in development mode with polling")
            await bot.run()

            # In development mode, keep the application running
            await bot.application.updater.idle()

    except KeyboardInterrupt:
        logger.info("Bot stopped by user")
    except Exception as e:
        logger.error(f"Error running bot: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
