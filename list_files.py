"""
<PERSON><PERSON><PERSON> to list all Python files in the project.
"""

import os
import sys
from pathlib import Path
from collections import defaultdict

# Define directories to ignore
IGNORE_DIRS = [
    '.git',
    'venv',
    '__pycache__',
    '.idea',
    '.vscode',
    'node_modules',
]

def list_python_files(base_dir):
    """List all Python files in the project."""
    python_files = []
    
    # Walk through the directory
    for root, dirs, files in os.walk(base_dir):
        # <PERSON><PERSON> ignored directories
        dirs[:] = [d for d in dirs if d not in IGNORE_DIRS]
        
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                rel_path = os.path.relpath(file_path, base_dir)
                python_files.append(rel_path)
    
    return python_files

def find_similar_filenames(files):
    """Find files with similar names."""
    similar_files = defaultdict(list)
    
    for file in files:
        base_name = os.path.basename(file)
        name_without_ext = os.path.splitext(base_name)[0]
        
        # Check for common patterns indicating duplicates
        for pattern in ['_old', '_new', '_backup', '_copy', '.bak', '.old', '.new']:
            if pattern in name_without_ext:
                clean_name = name_without_ext.replace(pattern, '')
                similar_files[clean_name].append(file)
    
    return similar_files

def main():
    """Main function."""
    base_dir = os.path.dirname(os.path.abspath(__file__))
    print(f"Scanning directory: {base_dir}")
    
    # List all Python files
    python_files = list_python_files(base_dir)
    
    # Group files by directory
    files_by_dir = defaultdict(list)
    for file in python_files:
        dir_name = os.path.dirname(file)
        files_by_dir[dir_name].append(os.path.basename(file))
    
    # Find similar filenames
    similar_files = find_similar_filenames(python_files)
    
    # Print results
    print(f"\nFound {len(python_files)} Python files in {len(files_by_dir)} directories.")
    
    print("\n=== FILES BY DIRECTORY ===")
    for dir_name, files in sorted(files_by_dir.items()):
        print(f"\n{dir_name or '.'} ({len(files)} files):")
        for file in sorted(files):
            print(f"  - {file}")
    
    print("\n=== POTENTIAL DUPLICATE FILES ===")
    for base_name, files in similar_files.items():
        if len(files) > 1:
            print(f"\nPotential duplicates for {base_name}:")
            for file in files:
                print(f"  - {file}")
    
    # Look for .bak files
    bak_files = [f for f in python_files if f.endswith('.bak') or '.bak.' in f]
    if bak_files:
        print("\n=== BACKUP FILES ===")
        for file in bak_files:
            print(f"  - {file}")
    
    # Look for test_*.py files outside of tests directory
    test_files = [f for f in python_files if os.path.basename(f).startswith('test_') and not f.startswith('tests/')]
    if test_files:
        print("\n=== TEST FILES OUTSIDE TESTS DIRECTORY ===")
        for file in test_files:
            print(f"  - {file}")

if __name__ == "__main__":
    main()
