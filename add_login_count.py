#!/usr/bin/env python3
"""
Add login_count column to the users table.
"""

import sqlite3
import sys

def add_login_count_column(db_path: str) -> None:
    """
    Add login_count column to the users table.
    
    Args:
        db_path: Path to the database file
    """
    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # Add login_count column
        print(f"Adding login_count column to users table in {db_path}")
        cursor.execute("ALTER TABLE users ADD COLUMN login_count INTEGER DEFAULT 1")
        conn.commit()
        print("Successfully added login_count column")
        
        # Verify column was added
        cursor.execute("PRAGMA table_info(users)")
        columns = cursor.fetchall()
        column_names = [column['name'] for column in columns]
        
        if 'login_count' in column_names:
            print("Verified login_count column exists")
        else:
            print("Failed to add login_count column")
        
    except Exception as e:
        print(f"Error adding login_count column: {e}")
    finally:
        if conn:
            conn.close()

def main():
    """Main entry point."""
    # Get database path from command line or use default
    db_path = sys.argv[1] if len(sys.argv) > 1 else "voicepal.db"
    
    # Add login_count column
    add_login_count_column(db_path)

if __name__ == "__main__":
    main()
