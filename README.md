# VoicePal - Enterprise AI Voice Companion Bot

VoicePal is a production-ready, enterprise-grade Telegram bot that provides AI-powered voice conversations with comprehensive analytics, security, and scalability features.

## 🚀 Latest Updates (December 2024)

### Major Architecture Overhaul
- **Enterprise-Grade Architecture**: Complete refactoring with modular, scalable design
- **REST API Layer**: FastAPI-based API with comprehensive endpoints and WebSocket support
- **Advanced Security**: End-to-end encryption, audit logging, and GDPR compliance
- **Performance Optimization**: Database pooling, Redis caching, and async processing
- **Analytics & BI**: Comprehensive analytics with conversation insights and business intelligence
- **A/B Testing**: Feature experimentation and gradual rollouts
- **Notification System**: Multi-channel notifications with scheduling
- **Feature Flags**: Dynamic feature management and user targeting
- **Scalability**: Load balancing, service registry, and horizontal scaling support

### Production-Ready Features
- **Database Connection Pooling**: Efficient database management with SQLite optimization
- **Redis Integration**: Multi-level caching and real-time features
- **Comprehensive Testing**: Unit, integration, and performance tests
- **Security Hardening**: Rate limiting, input validation, and threat detection
- **Monitoring & Alerting**: Real-time performance monitoring with alerts
- **Documentation**: Complete API documentation and deployment guides

## 🚀 Features

### Core Functionality
- 🎤 **Advanced Voice Processing**: Deepgram STT with fallback providers
- 🤖 **Multi-Provider AI**: Google Gemini, Groq, and extensible AI providers
- 🔊 **High-Quality TTS**: Deepgram, ElevenLabs with voice customization
- 💬 **Smart Conversations**: Context-aware responses with memory management
- 🎯 **Mood & Sentiment**: Real-time emotion tracking and adaptive responses

### Enterprise Features
- 🏢 **REST API**: FastAPI-based API with comprehensive endpoints
- 🔒 **Advanced Security**: End-to-end encryption, audit logging, rate limiting
- 📊 **Analytics & BI**: Conversation analytics, user metrics, business intelligence
- ⚡ **Performance**: Database pooling, Redis caching, async processing
- 🧪 **A/B Testing**: Feature experimentation and gradual rollouts
- 📱 **Notifications**: Multi-channel notification system
- 🔧 **Feature Flags**: Dynamic feature management and targeting
- 📈 **Scalability**: Load balancing, service registry, horizontal scaling

### Monetization
- 💳 **Flexible Payments**: Telegram Stars, Stripe, cryptocurrency support
- 💰 **Credit System**: Usage-based billing with multiple packages
- 📊 **Revenue Analytics**: Comprehensive financial reporting and forecasting
- 🎁 **Promotions**: Discount codes, referral programs, loyalty rewards

## 🏗️ Architecture

VoicePal follows a modern, microservices-inspired architecture:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Telegram Bot  │    │    REST API     │    │   Admin Panel   │
│    (Frontend)   │    │   (FastAPI)     │    │    (Future)     │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────┴─────────────┐
                    │      Core Engine          │
                    │  ┌─────────────────────┐  │
                    │  │   Feature System    │  │
                    │  │ ┌─────┬─────┬─────┐ │  │
                    │  │ │ AI  │Voice│ Pay │ │  │
                    │  │ └─────┴─────┴─────┘ │  │
                    │  └─────────────────────┘  │
                    └─────────────┬─────────────┘
                                  │
          ┌───────────────────────┼───────────────────────┐
          │                       │                       │
    ┌─────┴─────┐         ┌───────┴───────┐       ┌───────┴───────┐
    │ Database  │         │ Performance   │       │   Advanced    │
    │  Layer    │         │ Optimization  │       │   Features    │
    │           │         │               │       │               │
    │ • SQLite  │         │ • Redis Cache │       │ • A/B Testing │
    │ • Pool    │         │ • Async Proc  │       │ • Notifications│
    │ • Encrypt │         │ • Monitoring  │       │ • Scalability │
    └───────────┘         └───────────────┘       └───────────────┘
```

### Module Structure

```
bot/
├── core/                 # Core bot functionality
├── database/            # Database management and models
├── providers/           # External service providers (AI, Voice, Payment)
├── features/            # Modular feature system
├── api/                 # REST API endpoints
├── security/            # Security and encryption
├── performance/         # Performance optimization
├── analytics/           # Analytics and monitoring
├── advanced/            # Advanced features (A/B testing, etc.)
└── utils/               # Utility functions
```

## 🚀 Quick Start

### Prerequisites
- Python 3.10+
- Redis (optional, for caching and scalability)
- PostgreSQL (optional, for production)

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/voicepal.git
   cd voicepal
   ```

2. **Create virtual environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Configure environment**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

5. **Initialize database**
   ```bash
   python -c "from bot.database.database_manager import DatabaseManager; DatabaseManager('voicepal.db').initialize_database()"
   ```

6. **Run the bot**
   ```bash
   python main.py
   ```

### Docker Deployment

```bash
# Build image
docker build -t voicepal .

# Run with docker-compose
docker-compose up -d
```

## ⚙️ Configuration

### Environment Variables

```env
# === Core Configuration ===
TELEGRAM_BOT_TOKEN=your_bot_token
TELEGRAM_WEBHOOK_URL=https://yourdomain.com/webhook
ADMIN_USER_IDS=123456789,987654321

# === AI Providers ===
GOOGLE_AI_API_KEY=your_google_ai_key
GROQ_API_KEY=your_groq_key
HUGGINGFACE_API_KEY=your_hf_key

# === Voice Services ===
DEEPGRAM_API_KEY=your_deepgram_key
ELEVENLABS_API_KEY=your_elevenlabs_key

# === Payment Systems ===
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...
TELEGRAM_PAYMENT_TOKEN=your_payment_token

# === Database ===
DATABASE_URL=sqlite:///voicepal.db
# DATABASE_URL=postgresql://user:pass@localhost/voicepal

# === Redis (Optional) ===
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=your_redis_password

# === Security ===
ENCRYPTION_KEY=your_32_byte_encryption_key
JWT_SECRET=your_jwt_secret

# === Performance ===
MAX_WORKERS=4
CACHE_TTL=3600
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=3600

# === Features ===
ENABLE_ANALYTICS=true
ENABLE_AB_TESTING=true
ENABLE_NOTIFICATIONS=true
ENABLE_FEATURE_FLAGS=true
```

## 📚 API Documentation

### REST API Endpoints

The bot includes a comprehensive REST API built with FastAPI:

#### Health & Status
- `GET /health` - Health check
- `GET /status` - Detailed system status
- `GET /metrics` - Prometheus metrics

#### User Management
- `GET /api/v1/users` - List users
- `GET /api/v1/users/{user_id}` - Get user details
- `PUT /api/v1/users/{user_id}` - Update user
- `DELETE /api/v1/users/{user_id}` - Delete user

#### Analytics
- `GET /api/v1/analytics/dashboard` - Analytics dashboard
- `GET /api/v1/analytics/users` - User analytics
- `GET /api/v1/analytics/conversations` - Conversation analytics
- `GET /api/v1/analytics/revenue` - Revenue analytics

#### A/B Testing
- `POST /api/v1/ab-tests` - Create A/B test
- `GET /api/v1/ab-tests` - List A/B tests
- `GET /api/v1/ab-tests/{test_id}/results` - Get test results

#### Feature Flags
- `POST /api/v1/feature-flags` - Create feature flag
- `GET /api/v1/feature-flags` - List feature flags
- `PUT /api/v1/feature-flags/{flag_id}` - Update feature flag

## 🧪 Testing

### Running Tests

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=bot --cov-report=html

# Run specific test categories
pytest tests/unit/
pytest tests/integration/
pytest tests/performance/
```

## 🚀 Deployment

### Production Deployment

1. **Environment Setup**
   ```bash
   export ENVIRONMENT=production
   export DEBUG=false
   ```

2. **Database Migration**
   ```bash
   python scripts/migrate_database.py
   ```

3. **Process Management**
   ```bash
   sudo systemctl enable voicepal
   sudo systemctl start voicepal
   ```

### Scaling

#### Horizontal Scaling

```yaml
# docker-compose.scale.yml
version: '3.8'
services:
  voicepal:
    image: voicepal:latest
    deploy:
      replicas: 3
    environment:
      - REDIS_URL=redis://redis:6379
      - DATABASE_URL=******************************/voicepal
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass: `pytest`
6. Commit your changes: `git commit -m 'Add amazing feature'`
7. Push to the branch: `git push origin feature/amazing-feature`
8. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Telegram Bot API](https://core.telegram.org/bots/api)
- [FastAPI](https://fastapi.tiangolo.com/)
- [Deepgram](https://deepgram.com/)
- [Google AI](https://ai.google.dev/)
- [ElevenLabs](https://elevenlabs.io/)
- [Stripe](https://stripe.com/)

---

**VoicePal** - Bringing AI conversations to life! 🎤✨
