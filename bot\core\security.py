"""
Security utilities for VoicePal.

This module provides security-related utilities for the VoicePal bot,
including device identification, input validation, and API key protection.
"""

import hashlib
import logging
import re
from typing import Optional, Dict, Any, Union
from datetime import datetime
from telegram import Update, User

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

def is_admin_user(*args, **kwargs):
    """
    Check if the user is an admin.

    This function is used as a callback for rate limiting exemptions.
    It checks if the user making the request is in the admin list.

    Args:
        *args: Variable length argument list (expects Update as the second argument)
        **kwargs: Arbitrary keyword arguments

    Returns:
        bool: True if the user is an admin, False otherwise
    """
    from bot.config import ConfigManager

    # Check if we have an Update object in the arguments
    if len(args) >= 2 and hasattr(args[1], 'effective_user'):
        update = args[1]
        user_id = update.effective_user.id

        # Get admin IDs from config
        config_manager = ConfigManager()
        admin_ids = config_manager.get_admin_user_ids()

        return user_id in admin_ids

    return False

def generate_device_id(user: User, update: Update) -> str:
    """
    Generate a device identifier based on available user information.

    This function creates a unique identifier for a user's device based on
    various attributes available in the Telegram Update object. Since Telegram
    doesn't provide direct access to device information or IP addresses, we use
    a combination of user attributes to create a pseudo-device identifier.

    Args:
        user: The Telegram user
        update: The Telegram update object

    Returns:
        str: A hashed device identifier
    """
    # Collect all available user information
    user_data = {
        'user_id': str(user.id),
        'username': user.username or '',
        'first_name': user.first_name or '',
        'last_name': user.last_name or '',
        'language_code': user.language_code or '',
        'is_bot': str(user.is_bot),
        'is_premium': str(getattr(user, 'is_premium', False)),
        'added_to_attachment_menu': str(getattr(user, 'added_to_attachment_menu', False)),
    }

    # Add chat information if available
    if update.effective_chat:
        user_data.update({
            'chat_id': str(update.effective_chat.id),
            'chat_type': update.effective_chat.type,
        })

    # Create a string from all collected data
    data_string = '|'.join([f"{k}:{v}" for k, v in sorted(user_data.items())])

    # Hash the string to create a device ID
    device_id = hashlib.sha256(data_string.encode()).hexdigest()

    return device_id

def extract_ip_info(update: Update) -> Optional[str]:
    """
    Extract IP-related information from the update.

    Since Telegram doesn't provide direct access to IP addresses,
    this function creates a proxy identifier based on available information.

    Args:
        update: The Telegram update object

    Returns:
        Optional[str]: A proxy for IP information or None if not available
    """
    # Create a timestamp-based identifier
    # This isn't a real IP but serves as a placeholder for the database schema
    timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
    user_id = update.effective_user.id if update.effective_user else 'unknown'

    return f"proxy-{user_id}-{timestamp}"

class InputValidator:
    """
    Input validation framework for VoicePal.

    This class provides methods to validate and sanitize different types of user input.
    It includes validation for text messages, commands, numbers, and other input types.
    """

    # Maximum allowed lengths for different input types
    MAX_LENGTHS = {
        'text': 4096,  # Telegram message max length
        'command': 64,
        'username': 32,
        'phone': 20,
        'email': 254,
        'url': 2048,
        'callback_data': 64,  # Telegram callback data max length
    }

    # Regular expression patterns for different input types
    PATTERNS = {
        'command': r'^/[a-zA-Z0-9_]+(\s.*)?$',
        'number': r'^\d+$',
        'integer': r'^-?\d+$',
        'float': r'^-?\d+(\.\d+)?$',
        'email': r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
        'url': r'^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$',
        'phone': r'^\+?[0-9\s\-\(\)]{7,20}$',
        'username': r'^[a-zA-Z0-9_]{3,32}$',
        'callback_data': r'^[a-zA-Z0-9_\-:]+$',
    }

    @classmethod
    def validate(cls, text: str, input_type: str = 'text', **kwargs) -> bool:
        """
        Validate user input based on input type.

        Args:
            text: The text to validate
            input_type: The type of input ('text', 'command', 'number', etc.)
            **kwargs: Additional validation parameters

        Returns:
            bool: True if input is valid, False otherwise
        """
        if text is None:
            return False

        # Convert to string if not already
        if not isinstance(text, str):
            text = str(text)

        # Check if empty and if empty is allowed
        if not text and not kwargs.get('allow_empty', False):
            return False

        # Check length constraints
        max_length = kwargs.get('max_length', cls.MAX_LENGTHS.get(input_type, 4096))
        min_length = kwargs.get('min_length', 1)

        if not (min_length <= len(text) <= max_length):
            return False

        # Type-specific validation
        if input_type == 'text':
            # Basic text validation - check for reasonable length and no control characters
            # Allow newlines and tabs
            return not any(ord(c) < 32 and c not in '\n\t\r' for c in text)

        elif input_type in cls.PATTERNS:
            # Use regex pattern validation
            return bool(re.match(cls.PATTERNS[input_type], text))

        elif input_type == 'choice':
            # Validate against a list of choices
            choices = kwargs.get('choices', [])
            return text in choices

        elif input_type == 'json':
            # Validate JSON
            try:
                import json
                json.loads(text)
                return True
            except (json.JSONDecodeError, TypeError):
                return False

        # Default case - basic validation
        return True

    @classmethod
    def sanitize(cls, text: str, input_type: str = 'text', **kwargs) -> str:
        """
        Sanitize user input to prevent injection attacks.

        Args:
            text: The text to sanitize
            input_type: The type of input ('text', 'command', 'number', etc.)
            **kwargs: Additional sanitization parameters

        Returns:
            str: Sanitized text
        """
        if text is None:
            return ""

        # Convert to string if not already
        if not isinstance(text, str):
            text = str(text)

        if not text:
            return ""

        # Type-specific sanitization
        if input_type == 'text':
            # Remove control characters but allow newlines and tabs
            text = ''.join(c for c in text if ord(c) >= 32 or c in '\n\t\r')

        elif input_type == 'command':
            # Keep only valid command characters
            text = ''.join(c for c in text if c.isalnum() or c in '/_- ')

        elif input_type in ('number', 'integer'):
            # Keep only digits and minus sign
            text = ''.join(c for c in text if c.isdigit() or c == '-')

        elif input_type == 'float':
            # Keep only digits, minus sign, and decimal point
            text = ''.join(c for c in text if c.isdigit() or c in '.-')

        elif input_type == 'username':
            # Keep only valid username characters
            text = ''.join(c for c in text if c.isalnum() or c == '_')

        # Limit length
        max_length = kwargs.get('max_length', cls.MAX_LENGTHS.get(input_type, 4096))
        if len(text) > max_length:
            text = text[:max_length-3] + '...'

        return text

    @classmethod
    def validate_and_sanitize(cls, text: str, input_type: str = 'text', **kwargs) -> tuple:
        """
        Validate and sanitize input in one step.

        Args:
            text: The text to validate and sanitize
            input_type: The type of input
            **kwargs: Additional parameters

        Returns:
            tuple: (is_valid, sanitized_text)
        """
        sanitized = cls.sanitize(text, input_type, **kwargs)
        is_valid = cls.validate(sanitized, input_type, **kwargs)
        return (is_valid, sanitized)


# Legacy functions for backward compatibility
def validate_input(text: str, input_type: str = 'text', **kwargs) -> bool:
    """
    Validate user input based on input type.

    Args:
        text: The text to validate
        input_type: The type of input ('text', 'command', 'number', etc.)
        **kwargs: Additional validation parameters

    Returns:
        bool: True if input is valid, False otherwise
    """
    return InputValidator.validate(text, input_type, **kwargs)

def sanitize_input(text: str, input_type: str = 'text', **kwargs) -> str:
    """
    Sanitize user input to prevent injection attacks.

    Args:
        text: The text to sanitize
        input_type: The type of input ('text', 'command', 'number', etc.)
        **kwargs: Additional sanitization parameters

    Returns:
        str: Sanitized text
    """
    return InputValidator.sanitize(text, input_type, **kwargs)

def is_suspicious_activity(user_id: int, action_type: str, database) -> bool:
    """
    Check if the current activity seems suspicious.

    Args:
        user_id: The user ID
        action_type: The type of action being performed
        database: Database instance for checking history

    Returns:
        bool: True if activity seems suspicious, False otherwise
    """
    # For free credit requests, check if user has multiple accounts
    if action_type == 'free_credits':
        # Get all users with the same device ID
        user = database.get_user(user_id)
        if not user or not user.get('device_id'):
            return False

        device_id = user.get('device_id')

        # Check if there are other users with the same device ID
        # This would require a new database method
        # For now, we'll return False
        return False

    # Default case
    return False
