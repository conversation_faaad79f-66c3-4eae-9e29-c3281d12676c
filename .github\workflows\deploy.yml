# VoicePal Bot - CI/CD Pipeline
# =============================
# Automated testing and deployment workflow

name: 🚀 Deploy VoicePal Bot

on:
  push:
    branches: [ main, production ]
  pull_request:
    branches: [ main ]

env:
  PYTHON_VERSION: '3.11'

jobs:
  # Code Quality and Testing
  test:
    name: 🧪 Test & Quality Check
    runs-on: ubuntu-latest
    
    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4
    
    - name: 🐍 Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        cache: 'pip'
    
    - name: 📦 Install dependencies
      run: |
        python -m pip install --upgrade pip setuptools wheel
        pip install -r requirements.txt
        pip install pytest pytest-asyncio pytest-cov flake8 black isort
    
    - name: 🎨 Code formatting check
      run: |
        black --check --diff .
        isort --check-only --diff .
    
    - name: 🔍 Lint code
      run: |
        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
        flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics
    
    - name: 🏥 Health check
      run: |
        python run.py --health
      env:
        TELEGRAM_TOKEN: ${{ secrets.TELEGRAM_TOKEN }}
        DEEPGRAM_API_KEY: ${{ secrets.DEEPGRAM_API_KEY }}
        GOOGLE_AI_API_KEY: ${{ secrets.GOOGLE_AI_API_KEY }}
    
    - name: 🧪 Run tests
      run: |
        pytest tests/ -v --cov=bot --cov-report=xml --cov-report=term-missing
      env:
        TELEGRAM_TOKEN: ${{ secrets.TELEGRAM_TOKEN }}
        DEEPGRAM_API_KEY: ${{ secrets.DEEPGRAM_API_KEY }}
        GOOGLE_AI_API_KEY: ${{ secrets.GOOGLE_AI_API_KEY }}
    
    - name: 📊 Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella

  # Security Scanning
  security:
    name: 🛡️ Security Scan
    runs-on: ubuntu-latest
    
    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4
    
    - name: 🔒 Run security scan
      uses: pypa/gh-action-pip-audit@v1.0.8
      with:
        inputs: requirements.txt
    
    - name: 🔍 Scan for secrets
      uses: trufflesecurity/trufflehog@main
      with:
        path: ./
        base: main
        head: HEAD

  # Docker Build and Test
  docker:
    name: 🐳 Docker Build
    runs-on: ubuntu-latest
    needs: [test, security]
    
    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4
    
    - name: 🐳 Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: 🏗️ Build Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        push: false
        tags: voicepal-bot:latest
        cache-from: type=gha
        cache-to: type=gha,mode=max
    
    - name: 🧪 Test Docker image
      run: |
        docker run --rm voicepal-bot:latest python run.py --health

  # Deploy to Production
  deploy:
    name: 🚀 Deploy to Production
    runs-on: ubuntu-latest
    needs: [test, security, docker]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    
    environment:
      name: production
      url: https://voicepal-bot.onrender.com
    
    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4
    
    - name: 🚀 Deploy to Render
      uses: johnbeynon/render-deploy-action@v0.0.8
      with:
        service-id: ${{ secrets.RENDER_SERVICE_ID }}
        api-key: ${{ secrets.RENDER_API_KEY }}
    
    - name: ⏳ Wait for deployment
      run: |
        echo "Waiting for deployment to complete..."
        sleep 60
    
    - name: 🏥 Post-deployment health check
      run: |
        curl -f https://voicepal-bot.onrender.com/health || exit 1
        echo "✅ Deployment successful!"
    
    - name: 📢 Notify deployment success
      if: success()
      run: |
        echo "🎉 VoicePal Bot deployed successfully to production!"
    
    - name: 🚨 Notify deployment failure
      if: failure()
      run: |
        echo "❌ VoicePal Bot deployment failed!"
