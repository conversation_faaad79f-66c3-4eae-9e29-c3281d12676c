#!/usr/bin/env python3
"""
API Keys Checker and Configuration Validator

This script checks all API keys, validates their functionality,
and provides specific instructions for missing keys.
"""

import os
import asyncio
import httpx
import logging
from typing import Dict, List, Tuple
import json

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class APIKeysChecker:
    """Comprehensive API keys checker and validator."""
    
    def __init__(self):
        self.api_status = {}
        self.missing_keys = []
        self.invalid_keys = []
        self.valid_keys = []
        
    async def check_all_apis(self) -> Dict[str, str]:
        """Check all required API keys."""
        logger.info("🔍 Checking all API keys...")
        
        # Define all required APIs with their validation methods
        apis_to_check = {
            'BOT_TOKEN': self._check_telegram_bot,
            'DEEPGRAM_API_KEY': self._check_deepgram,
            'GOOGLE_AI_API_KEY': self._check_google_ai,
            'ELEVENLABS_API_KEY': self._check_elevenlabs,
            'GROQ_API_KEY': self._check_groq,
            'STRIPE_SECRET_KEY': self._check_stripe,
            'PAYMENT_PROVIDER_TOKEN': self._check_payment_provider
        }
        
        for api_name, check_function in apis_to_check.items():
            api_key = os.getenv(api_name)
            
            if not api_key or api_key.startswith('your_'):
                self.missing_keys.append(api_name)
                self.api_status[api_name] = 'MISSING'
                logger.warning(f"❌ {api_name}: MISSING")
            else:
                try:
                    is_valid = await check_function(api_key)
                    if is_valid:
                        self.valid_keys.append(api_name)
                        self.api_status[api_name] = 'VALID'
                        logger.info(f"✅ {api_name}: VALID")
                    else:
                        self.invalid_keys.append(api_name)
                        self.api_status[api_name] = 'INVALID'
                        logger.error(f"❌ {api_name}: INVALID")
                except Exception as e:
                    self.invalid_keys.append(api_name)
                    self.api_status[api_name] = f'ERROR: {str(e)}'
                    logger.error(f"❌ {api_name}: ERROR - {e}")
        
        return self.api_status
    
    async def _check_telegram_bot(self, token: str) -> bool:
        """Check Telegram Bot API."""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"https://api.telegram.org/bot{token}/getMe",
                    timeout=10.0
                )
                if response.status_code == 200:
                    data = response.json()
                    if data.get('ok'):
                        bot_info = data['result']
                        logger.info(f"  Bot: @{bot_info.get('username', 'unknown')}")
                        return True
                return False
        except Exception as e:
            logger.error(f"  Telegram API error: {e}")
            return False
    
    async def _check_deepgram(self, api_key: str) -> bool:
        """Check Deepgram API."""
        try:
            async with httpx.AsyncClient() as client:
                # Check projects endpoint
                response = await client.get(
                    "https://api.deepgram.com/v1/projects",
                    headers={"Authorization": f"Token {api_key}"},
                    timeout=10.0
                )
                if response.status_code == 200:
                    logger.info("  Deepgram: Projects accessible")
                    return True
                return False
        except Exception as e:
            logger.error(f"  Deepgram API error: {e}")
            return False
    
    async def _check_google_ai(self, api_key: str) -> bool:
        """Check Google AI API."""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key={api_key}",
                    json={
                        "contents": [{"parts": [{"text": "test"}]}],
                        "generationConfig": {"maxOutputTokens": 1}
                    },
                    timeout=15.0
                )
                if response.status_code == 200:
                    logger.info("  Google AI: API responding")
                    return True
                return False
        except Exception as e:
            logger.error(f"  Google AI API error: {e}")
            return False
    
    async def _check_elevenlabs(self, api_key: str) -> bool:
        """Check ElevenLabs API."""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    "https://api.elevenlabs.io/v1/voices",
                    headers={"xi-api-key": api_key},
                    timeout=10.0
                )
                if response.status_code == 200:
                    data = response.json()
                    voices = data.get('voices', [])
                    logger.info(f"  ElevenLabs: {len(voices)} voices available")
                    return True
                return False
        except Exception as e:
            logger.error(f"  ElevenLabs API error: {e}")
            return False
    
    async def _check_groq(self, api_key: str) -> bool:
        """Check Groq API."""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    "https://api.groq.com/openai/v1/chat/completions",
                    headers={"Authorization": f"Bearer {api_key}"},
                    json={
                        "messages": [{"role": "user", "content": "test"}],
                        "model": "mixtral-8x7b-32768",
                        "max_tokens": 1
                    },
                    timeout=15.0
                )
                if response.status_code == 200:
                    logger.info("  Groq: API responding")
                    return True
                return False
        except Exception as e:
            logger.error(f"  Groq API error: {e}")
            return False
    
    async def _check_stripe(self, api_key: str) -> bool:
        """Check Stripe API."""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    "https://api.stripe.com/v1/payment_methods",
                    headers={"Authorization": f"Bearer {api_key}"},
                    params={"type": "card", "limit": 1},
                    timeout=10.0
                )
                if response.status_code == 200:
                    logger.info("  Stripe: API responding")
                    return True
                return False
        except Exception as e:
            logger.error(f"  Stripe API error: {e}")
            return False
    
    async def _check_payment_provider(self, token: str) -> bool:
        """Check payment provider token."""
        # For Telegram Stars, this is usually the same as bot token
        # or a specific payment provider token
        if token.startswith('123456789:TEST:'):
            logger.info("  Payment Provider: Test token detected")
            return True
        elif ':' in token:
            logger.info("  Payment Provider: Token format valid")
            return True
        return False
    
    def generate_setup_instructions(self) -> str:
        """Generate setup instructions for missing API keys."""
        instructions = []
        
        instructions.append("🔧 API KEYS SETUP INSTRUCTIONS")
        instructions.append("=" * 50)
        
        if 'BOT_TOKEN' in self.missing_keys:
            instructions.extend([
                "",
                "📱 TELEGRAM BOT TOKEN (CRITICAL):",
                "1. Go to https://t.me/BotFather",
                "2. Send /newbot and follow instructions",
                "3. Copy the token and set: BOT_TOKEN=your_token_here",
                "4. Enable payments: /mybots → Select bot → Payments → Connect Stripe/Stars"
            ])
        
        if 'DEEPGRAM_API_KEY' in self.missing_keys:
            instructions.extend([
                "",
                "🎤 DEEPGRAM API KEY (CRITICAL - Voice Processing):",
                "1. Go to https://console.deepgram.com/",
                "2. Sign up for free account (includes $200 credit)",
                "3. Go to API Keys section",
                "4. Create new API key",
                "5. Set: DEEPGRAM_API_KEY=your_key_here"
            ])
        
        if 'GOOGLE_AI_API_KEY' in self.missing_keys:
            instructions.extend([
                "",
                "🤖 GOOGLE AI API KEY (CRITICAL - AI Responses):",
                "1. Go to https://makersuite.google.com/app/apikey",
                "2. Create new API key",
                "3. Set: GOOGLE_AI_API_KEY=your_key_here",
                "Note: Free tier includes generous limits"
            ])
        
        if 'ELEVENLABS_API_KEY' in self.missing_keys:
            instructions.extend([
                "",
                "🗣️ ELEVENLABS API KEY (Optional - Premium Voice):",
                "1. Go to https://elevenlabs.io/",
                "2. Sign up for account",
                "3. Go to Profile → API Keys",
                "4. Set: ELEVENLABS_API_KEY=your_key_here",
                "Note: Free tier available, premium voices"
            ])
        
        if 'GROQ_API_KEY' in self.missing_keys:
            instructions.extend([
                "",
                "⚡ GROQ API KEY (Optional - Fast AI):",
                "1. Go to https://console.groq.com/",
                "2. Sign up for account",
                "3. Create API key",
                "4. Set: GROQ_API_KEY=your_key_here",
                "Note: Very fast inference, free tier available"
            ])
        
        if 'STRIPE_SECRET_KEY' in self.missing_keys:
            instructions.extend([
                "",
                "💳 STRIPE API KEY (Optional - Credit Card Payments):",
                "1. Go to https://dashboard.stripe.com/",
                "2. Get API keys from Developers section",
                "3. Set: STRIPE_SECRET_KEY=sk_test_... (for testing)",
                "Note: Only needed if using Stripe instead of Telegram Stars"
            ])
        
        instructions.extend([
            "",
            "🚀 DEPLOYMENT SETUP:",
            "1. Copy .env.template to .env",
            "2. Fill in all your API keys",
            "3. For Render deployment, set environment variables in dashboard",
            "4. Test locally first: python run_bot.py",
            "5. Run tests: python production_100_percent_test.py"
        ])
        
        return "\n".join(instructions)
    
    def print_summary(self):
        """Print summary of API key status."""
        print("\n" + "="*60)
        print("API KEYS STATUS SUMMARY")
        print("="*60)
        
        print(f"✅ Valid Keys: {len(self.valid_keys)}")
        for key in self.valid_keys:
            print(f"   • {key}")
        
        if self.invalid_keys:
            print(f"\n❌ Invalid Keys: {len(self.invalid_keys)}")
            for key in self.invalid_keys:
                print(f"   • {key}")
        
        if self.missing_keys:
            print(f"\n⚠️ Missing Keys: {len(self.missing_keys)}")
            for key in self.missing_keys:
                print(f"   • {key}")
        
        # Calculate readiness percentage
        total_critical = 3  # BOT_TOKEN, DEEPGRAM_API_KEY, GOOGLE_AI_API_KEY
        critical_valid = sum(1 for key in ['BOT_TOKEN', 'DEEPGRAM_API_KEY', 'GOOGLE_AI_API_KEY'] 
                           if key in self.valid_keys)
        
        readiness = (critical_valid / total_critical) * 100
        
        print(f"\n📊 READINESS: {readiness:.0f}%")
        
        if readiness == 100:
            print("🎉 ALL CRITICAL APIs READY! Bot can be deployed.")
        elif readiness >= 67:
            print("⚠️ MOSTLY READY - Missing some critical APIs")
        else:
            print("❌ NOT READY - Missing critical APIs")
        
        return readiness

async def main():
    """Main function."""
    checker = APIKeysChecker()
    
    print("🔍 Checking API Keys and Services...")
    await checker.check_all_apis()
    
    readiness = checker.print_summary()
    
    if checker.missing_keys or checker.invalid_keys:
        print("\n" + checker.generate_setup_instructions())
    
    # Save detailed report
    report = {
        'api_status': checker.api_status,
        'valid_keys': checker.valid_keys,
        'invalid_keys': checker.invalid_keys,
        'missing_keys': checker.missing_keys,
        'readiness_percentage': readiness,
        'timestamp': str(asyncio.get_event_loop().time())
    }
    
    with open('api_keys_report.json', 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"\n📄 Detailed report saved to: api_keys_report.json")
    
    if readiness < 100:
        print("\n🔧 Next steps:")
        print("1. Set up missing API keys using instructions above")
        print("2. Run this script again to verify")
        print("3. Run production tests: python production_100_percent_test.py")

if __name__ == "__main__":
    asyncio.run(main())
