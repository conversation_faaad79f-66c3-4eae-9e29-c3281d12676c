"""
End-to-end tests for the bot with the hierarchical memory system.

These tests verify the complete user experience with the bot and memory system.
"""

import pytest
import os
import tempfile
import asyncio
import json
from datetime import datetime, timedelta
from unittest.mock import MagicMock, patch, AsyncMock

from telegram import Update, User, Message, Chat
from telegram.ext import ContextTypes

from bot.main import VoicePalBot
from bot.config.config_manager import ConfigManager
from bot.features.hierarchical_memory_manager import HierarchicalMemoryManager


@pytest.fixture
def temp_db_file():
    """Create a temporary database file."""
    fd, path = tempfile.mkstemp(suffix='.db')
    os.close(fd)
    yield path
    os.unlink(path)


@pytest.fixture
def temp_config_dir():
    """Create a temporary config directory."""
    temp_dir = tempfile.mkdtemp()
    
    # Create memory_config.json
    memory_config = {
        "memory": {
            "short_term_limit": 10,
            "medium_term_limit": 50,
            "long_term_threshold": 0.7,
            "cache_ttl_minutes": 30,
            "name_cache_ttl_minutes": 60,
            "enable_rate_limiting": True,
            "conversation_memory": 20,
            "summary_update_frequency": 24,
            "token_limit": 3000,
            "importance_threshold": 0.5,
            "use_hierarchical": True,
            "redis": {
                "url": None,  # Use in-memory for testing
                "cache_ttl_minutes": 30,
                "rate_limit_max_requests": 10,
                "rate_limit_window_seconds": 10
            },
            "qdrant": {
                "collection_name": "test_memories",
                "vector_size": 384,
                "distance": "cosine",
                "auto_init": True,
                "use_in_memory": True
            },
            "embedding": {
                "model_name": "all-MiniLM-L6-v2",
                "vector_size": 384,
                "batch_size": 32
            }
        }
    }
    
    # Write memory config
    with open(os.path.join(temp_dir, "memory_config.json"), "w") as f:
        json.dump(memory_config, f)
    
    # Create main_config.json
    main_config = {
        "bot": {
            "name": "VoicePalTestBot",
            "description": "Test bot for VoicePal",
            "version": "1.0.0-test"
        },
        "features": {
            "memory": {
                "enabled": True,
                "config_file": "memory_config.json"
            }
        },
        "database": {
            "file": "test_db.db"
        }
    }
    
    # Write main config
    with open(os.path.join(temp_dir, "main_config.json"), "w") as f:
        json.dump(main_config, f)
    
    yield temp_dir
    
    # Clean up
    import shutil
    shutil.rmtree(temp_dir)


@pytest.fixture
def mock_telegram_update():
    """Create a mock Telegram update."""
    user = MagicMock(spec=User)
    user.id = 123
    user.username = "test_user"
    user.first_name = "Test"
    user.last_name = "User"
    
    chat = MagicMock(spec=Chat)
    chat.id = 123
    
    message = MagicMock(spec=Message)
    message.message_id = 1
    message.chat = chat
    message.text = "Hello, how are you?"
    message.reply_text = AsyncMock()
    
    update = MagicMock(spec=Update)
    update.update_id = 1
    update.message = message
    update.effective_user = user
    update.effective_chat = chat
    
    return update


@pytest.fixture
def mock_telegram_context():
    """Create a mock Telegram context."""
    context = MagicMock(spec=ContextTypes.DEFAULT_TYPE)
    context.bot = MagicMock()
    context.bot.send_message = AsyncMock()
    return context


@pytest.fixture
def mock_ai_response():
    """Create a mock AI response."""
    return {
        "text": "I'm doing well, thank you for asking! How are you today?",
        "tokens_used": 10
    }


@pytest.fixture
def patched_bot(temp_db_file, temp_config_dir, mock_ai_response):
    """Create a patched VoicePalBot for testing."""
    # Set environment variables for testing
    os.environ["CONFIG_DIR"] = temp_config_dir
    os.environ["DB_FILE"] = temp_db_file
    
    # Create patches
    patches = []
    
    # Patch ConfigManager to use test config
    config_patch = patch('bot.config.config_manager.ConfigManager.load_config', 
                         return_value={"features": {"memory": {"enabled": True}}})
    patches.append(config_patch)
    
    # Patch AIProvider
    ai_patch = patch('bot.providers.ai.ai_provider.AIProvider.process_message', 
                     new_callable=AsyncMock, return_value=mock_ai_response)
    patches.append(ai_patch)
    
    # Patch Redis
    redis_patch = patch('bot.providers.memory.redis_provider.Redis', MagicMock())
    patches.append(redis_patch)
    
    # Patch Ratelimit
    ratelimit_patch = patch('bot.providers.memory.redis_provider.Ratelimit', MagicMock())
    patches.append(ratelimit_patch)
    
    # Patch QdrantClient
    qdrant_patch = patch('bot.providers.memory.qdrant_provider.QdrantClient', MagicMock())
    patches.append(qdrant_patch)
    
    # Patch SentenceTransformer
    transformer_patch = patch('bot.utils.embedding_utils.SentenceTransformer', MagicMock())
    patches.append(transformer_patch)
    
    # Patch Application
    app_patch = patch('telegram.ext.Application.builder')
    patches.append(app_patch)
    
    # Start all patches
    for p in patches:
        p.start()
    
    # Create bot
    bot = VoicePalBot()
    
    yield bot
    
    # Stop all patches
    for p in patches:
        p.stop()
    
    # Clean up environment variables
    if "CONFIG_DIR" in os.environ:
        del os.environ["CONFIG_DIR"]
    if "DB_FILE" in os.environ:
        del os.environ["DB_FILE"]


class TestBotWithMemory:
    """End-to-end tests for the bot with the hierarchical memory system."""

    def test_bot_initialization(self, patched_bot):
        """Test that the bot initializes with the hierarchical memory system."""
        # Verify that the memory manager is initialized
        assert patched_bot.memory_manager is not None
        
        # Verify that the hierarchical memory manager is initialized
        assert hasattr(patched_bot, 'hierarchical_memory_manager')
        
        # Verify that the providers are initialized
        assert hasattr(patched_bot, 'redis_provider')
        assert hasattr(patched_bot, 'qdrant_provider')
        assert hasattr(patched_bot, 'embedding_provider')
        
        # Verify that the feature registry has the hierarchical memory feature
        assert patched_bot.feature_registry.is_feature_enabled("hierarchical_memory")

    @pytest.mark.asyncio
    async def test_single_message_flow(self, patched_bot, mock_telegram_update, mock_telegram_context):
        """Test the flow of a single message through the bot."""
        # Process a message
        await patched_bot.handle_text(mock_telegram_update, mock_telegram_context)
        
        # Verify that the message was processed
        mock_telegram_update.message.reply_text.assert_called_once()
        
        # Verify that the conversation was stored
        user_id = mock_telegram_update.effective_user.id
        context = patched_bot.memory_manager.get_conversation_context(user_id)
        
        # Verify that the context contains the conversation
        assert "short_term_memory" in context
        assert len(context["short_term_memory"]) > 0
        
        # Verify that the conversation has the correct content
        found = False
        for conv in context["short_term_memory"]:
            if conv["message"] == "Hello, how are you?":
                assert conv["response"] == "I'm doing well, thank you for asking! How are you today?"
                found = True
                break
        
        assert found, "Conversation not found in context"

    @pytest.mark.asyncio
    async def test_multi_turn_conversation(self, patched_bot, mock_telegram_update, mock_telegram_context):
        """Test a multi-turn conversation with context preservation."""
        # First message
        mock_telegram_update.message.text = "Hello, my name is TestUser"
        await patched_bot.handle_text(mock_telegram_update, mock_telegram_context)
        
        # Second message (short follow-up)
        mock_telegram_update.message.text = "What's my name?"
        await patched_bot.handle_text(mock_telegram_update, mock_telegram_context)
        
        # Verify that the context was preserved for the short message
        user_id = mock_telegram_update.effective_user.id
        context = patched_bot.memory_manager.get_conversation_context(user_id)
        
        # Verify that both conversations are in the context
        assert "short_term_memory" in context
        assert len(context["short_term_memory"]) >= 2
        
        # Verify that the name was stored in an important memory
        # This is a bit tricky to test directly, but we can check if the first message
        # has a high importance score by checking if it's in medium-term memory
        if "medium_term_memory" in context and len(context["medium_term_memory"]) > 0:
            found = False
            for conv in context["medium_term_memory"]:
                if "my name is TestUser" in conv["message"]:
                    found = True
                    break
            
            assert found, "Name information not found in medium-term memory"

    @pytest.mark.asyncio
    async def test_context_preservation_for_short_message(self, patched_bot):
        """Test that context is preserved for short messages."""
        # Test with a short message
        assert patched_bot.memory_manager.should_preserve_context_for_short_message("Hi") is True
        
        # Test with a question
        assert patched_bot.memory_manager.should_preserve_context_for_short_message("What is your name?") is True
        
        # Test with a follow-up indicator
        assert patched_bot.memory_manager.should_preserve_context_for_short_message("Can you explain that?") is True
        
        # Test with a long message without follow-up indicators
        assert patched_bot.memory_manager.should_preserve_context_for_short_message(
            "This is a very long message that doesn't contain any follow-up indicators. " * 5
        ) is False

    @pytest.mark.asyncio
    async def test_importance_scoring(self, patched_bot):
        """Test that importance scoring works correctly."""
        # Test with name information (important)
        score1 = patched_bot.memory_manager._calculate_importance_score(
            "My name is TestUser",
            "Nice to meet you, TestUser!"
        )
        
        # Test with preference information (important)
        score2 = patched_bot.memory_manager._calculate_importance_score(
            "I prefer classical music",
            "I'll remember that you like classical music."
        )
        
        # Test with casual conversation (less important)
        score3 = patched_bot.memory_manager._calculate_importance_score(
            "How's the weather?",
            "I don't have access to weather information."
        )
        
        # Verify that important information has higher scores
        assert score1 > 0.5
        assert score2 > 0.5
        assert score3 < score1
