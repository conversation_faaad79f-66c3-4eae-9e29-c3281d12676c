"""
<PERSON><PERSON><PERSON> to fix provider token issues in the payment system.

This script checks and updates the provider token configuration for the payment system.
"""

import os
import sys
import logging
import json
from datetime import datetime

# Add parent directory to path to import bot modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import bot modules
from bot.config_manager import ConfigManager

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

def backup_config(config_path):
    """
    Create a backup of the config file.
    
    Args:
        config_path: Path to the config file
        
    Returns:
        str: Path to the backup file
    """
    backup_path = f"{config_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    try:
        # Copy the config file
        with open(config_path, 'r') as src, open(backup_path, 'w') as dst:
            dst.write(src.read())
        logger.info(f"Config backup created at {backup_path}")
        return backup_path
    except Exception as e:
        logger.error(f"Error creating config backup: {e}")
        raise

def fix_provider_token(config_path=None):
    """
    Fix provider token issues in the payment system.
    
    Args:
        config_path: Path to the config file (optional)
    """
    try:
        # Initialize config manager
        config_manager = ConfigManager(config_path)
        
        # Get config file path
        if config_path is None:
            config_path = config_manager.config_file
        
        # Create a backup first
        backup_path = backup_config(config_path)
        
        # Load the config file
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        # Check if telegram section exists
        if 'telegram' not in config:
            config['telegram'] = {}
        
        # Check if payment_provider_token exists
        if 'payment_provider_token' not in config['telegram']:
            logger.info("Adding empty payment_provider_token to config")
            config['telegram']['payment_provider_token'] = ""
        
        # Check if use_telegram_stars exists
        if 'use_telegram_stars' not in config['telegram']:
            logger.info("Setting use_telegram_stars to true in config")
            config['telegram']['use_telegram_stars'] = True
        
        # Save the updated config
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=4)
        
        logger.info("Provider token configuration fixed successfully")
        
        # Print instructions
        print("\n" + "="*80)
        print("PROVIDER TOKEN CONFIGURATION FIXED")
        print("="*80)
        print("\nTo complete the setup, you need to:")
        print("1. Get a payment provider token from @BotFather if you want to use real payments")
        print("2. Add the token to your config.json file in the telegram section:")
        print('   "payment_provider_token": "YOUR_TOKEN_HERE"')
        print("\nIf you want to use Telegram Stars for digital goods, you can leave the token empty.")
        print("="*80 + "\n")
        
    except Exception as e:
        logger.error(f"Error fixing provider token: {e}")
        import traceback
        logger.error(traceback.format_exc())
        
        # Try to restore from backup if something went wrong
        if 'backup_path' in locals():
            try:
                logger.info(f"Attempting to restore from backup {backup_path}")
                with open(backup_path, 'r') as src, open(config_path, 'w') as dst:
                    dst.write(src.read())
                logger.info("Config restored from backup")
            except Exception as restore_error:
                logger.error(f"Error restoring config from backup: {restore_error}")
        
        raise

def check_provider_token():
    """
    Check if the provider token is configured correctly.
    
    Returns:
        bool: True if configured correctly, False otherwise
    """
    try:
        # Initialize config manager
        config_manager = ConfigManager()
        
        # Get telegram config
        telegram_config = config_manager.get_telegram_config()
        
        # Check if payment_provider_token exists
        if 'payment_provider_token' not in telegram_config:
            logger.warning("payment_provider_token not found in config")
            return False
        
        # Check if use_telegram_stars exists
        if 'use_telegram_stars' not in telegram_config:
            logger.warning("use_telegram_stars not found in config")
            return False
        
        # If using Telegram Stars for digital goods, token can be empty
        if telegram_config.get('use_telegram_stars', True) and not telegram_config.get('payment_provider_token'):
            logger.info("Using Telegram Stars for digital goods (no provider token needed)")
            return True
        
        # If using regular payment system, token must be provided
        if not telegram_config.get('use_telegram_stars', True) and not telegram_config.get('payment_provider_token'):
            logger.warning("Provider token required for regular payment system")
            return False
        
        logger.info("Provider token configuration is correct")
        return True
    except Exception as e:
        logger.error(f"Error checking provider token: {e}")
        return False

if __name__ == "__main__":
    # Get config path from command line or use default
    if len(sys.argv) > 1:
        config_path = sys.argv[1]
    else:
        config_path = None
    
    # Check if provider token is configured correctly
    if not check_provider_token():
        logger.info("Fixing provider token configuration")
        fix_provider_token(config_path)
    else:
        logger.info("Provider token configuration is already correct")
