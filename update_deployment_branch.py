"""
<PERSON><PERSON><PERSON> to update the deployment-test branch with the fixed files.
"""

import os
import sys
import logging
import re

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.DEBUG
)
logger = logging.getLogger(__name__)

def fix_initialization_manager():
    """Fix the initialization_manager.py file."""
    logger.info("Fixing initialization_manager.py...")
    
    file_path = "bot/core/initialization_manager.py"
    
    try:
        # Read the file
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()
        
        # Fix the import
        content = content.replace(
            "from bot.features.feature_registry import FeatureRegistry",
            "from bot.core.feature_registry import FeatureRegistry"
        )
        
        # Fix the DialogEngine import
        content = content.replace(
            "from bot.core.dialog_engine import DialogEngine",
            "from bot.core.enhanced_dialog_engine import EnhancedDialogEngine as DialogEngine"
        )
        
        # Write the file
        with open(file_path, "w", encoding="utf-8") as f:
            f.write(content)
        
        logger.info("Fixed initialization_manager.py")
        return True
    except Exception as e:
        logger.error(f"Error fixing initialization_manager.py: {e}")
        return False

def add_welcome_manager_initialization():
    """Add welcome_manager initialization to main.py."""
    logger.info("Adding welcome_manager initialization to main.py...")
    
    file_path = "bot/main.py"
    
    try:
        # Read the file
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()
        
        # Add welcome_manager initialization step registration
        content = re.sub(
            r"self\.init_manager\.register_initialization_step\(\s*name=\"navigation_router\",\s*func=self\._init_navigation_router,\s*dependencies=\[\"menu_manager\"\]\s*\)",
            "self.init_manager.register_initialization_step(\n            name=\"navigation_router\",\n            func=self._init_navigation_router,\n            dependencies=[\"menu_manager\"]\n        )\n        \n        self.init_manager.register_initialization_step(\n            name=\"welcome_manager\",\n            func=self._init_welcome_manager,\n            dependencies=[\"database\", \"config_manager\"]\n        )",
            content
        )
        
        # Add welcome_manager initialization method
        welcome_manager_method = """
    async def _init_welcome_manager(self) -> tuple[bool, Optional[str]]:
        """
        Initialize welcome manager asynchronously.

        Returns:
            Tuple of (success, error_message)
        """
        try:
            # Get dependencies
            database = self.init_manager.get_component("database")
            config_manager = self.init_manager.get_component("config_manager")
            
            # Get mood tracker if available
            mood_tracker = self.init_manager.get_component("mood_tracker")
            
            # Get credit system config
            credit_system_config = config_manager.get_credit_system_config()
            
            # Initialize welcome manager
            from bot.features.welcome_manager import WelcomeManager
            welcome_manager = WelcomeManager(
                database=database,
                mood_tracker=mood_tracker,
                config=credit_system_config
            )
            
            # Register with initialization manager
            self.init_manager.register_component("welcome_manager", welcome_manager)
            
            # Also set it as an instance attribute for backward compatibility
            self.welcome_manager = welcome_manager
            
            return True, None
        except Exception as e:
            logger.error(f"Error initializing welcome manager: {e}")
            return False, str(e)"""
        
        # Add the method after _init_navigation_router
        content = re.sub(
            r"(async def _init_navigation_router.*?return False, str\(e\))",
            r"\1\n\n" + welcome_manager_method,
            content,
            flags=re.DOTALL
        )
        
        # Write the file
        with open(file_path, "w", encoding="utf-8") as f:
            f.write(content)
        
        logger.info("Added welcome_manager initialization to main.py")
        return True
    except Exception as e:
        logger.error(f"Error adding welcome_manager initialization to main.py: {e}")
        return False

def update_dialog_engine_dependencies():
    """Update dialog_engine dependencies in main.py."""
    logger.info("Updating dialog_engine dependencies in main.py...")
    
    file_path = "bot/main.py"
    
    try:
        # Read the file
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()
        
        # Update dialog_engine dependencies
        content = re.sub(
            r"dependencies=\[\"ai_provider\", \"memory_manager\", \"user_manager\"\]",
            "dependencies=[\"providers\", \"feature_managers\"]",
            content
        )
        
        # Write the file
        with open(file_path, "w", encoding="utf-8") as f:
            f.write(content)
        
        logger.info("Updated dialog_engine dependencies in main.py")
        return True
    except Exception as e:
        logger.error(f"Error updating dialog_engine dependencies in main.py: {e}")
        return False

def main():
    """Main function."""
    logger.info("Starting deployment branch update...")
    
    # Fix initialization_manager.py
    if not fix_initialization_manager():
        logger.error("Failed to fix initialization_manager.py")
        return 1
    
    # Add welcome_manager initialization to main.py
    if not add_welcome_manager_initialization():
        logger.error("Failed to add welcome_manager initialization to main.py")
        return 1
    
    # Update dialog_engine dependencies in main.py
    if not update_dialog_engine_dependencies():
        logger.error("Failed to update dialog_engine dependencies in main.py")
        return 1
    
    logger.info("Deployment branch update completed successfully")
    return 0

if __name__ == "__main__":
    sys.exit(main())
