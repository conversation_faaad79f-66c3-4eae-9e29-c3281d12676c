"""
Advanced features manager for VoicePal.

This module coordinates all advanced features and provides a unified interface.
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta

from bot.advanced.ab_testing import ABTest<PERSON>anager, ABTestType, ABTestVariant
from bot.advanced.notification_system import NotificationManager, NotificationType, NotificationChannel
from bot.advanced.scalability import ScalabilityManager
from bot.advanced.feature_flags import FeatureFlagManager, FeatureFlagType, RolloutStrategy, UserContext

logger = logging.getLogger(__name__)

class AdvancedFeaturesManager:
    """Centralized manager for advanced features."""

    def __init__(
        self,
        database,
        cache_manager=None,
        redis_client=None,
        telegram_bot=None,
        email_service=None,
        enable_ab_testing: bool = True,
        enable_notifications: bool = True,
        enable_scalability: bool = True,
        enable_feature_flags: bool = True
    ):
        """
        Initialize advanced features manager.

        Args:
            database: Database instance
            cache_manager: Cache manager for performance
            redis_client: Redis client for distributed features
            telegram_bot: Telegram bot instance
            email_service: Email service instance
            enable_ab_testing: Whether to enable A/B testing
            enable_notifications: Whether to enable notifications
            enable_scalability: Whether to enable scalability features
            enable_feature_flags: Whether to enable feature flags
        """
        self.database = database
        self.cache_manager = cache_manager
        self.redis_client = redis_client
        self.telegram_bot = telegram_bot
        self.email_service = email_service

        # Initialize components
        self.ab_test_manager: Optional[ABTestManager] = None
        self.notification_manager: Optional[NotificationManager] = None
        self.scalability_manager: Optional[ScalabilityManager] = None
        self.feature_flag_manager: Optional[FeatureFlagManager] = None

        if enable_ab_testing:
            self.ab_test_manager = ABTestManager(database, cache_manager)

        if enable_notifications:
            self.notification_manager = NotificationManager(
                database, telegram_bot, email_service, redis_client
            )

        if enable_scalability:
            self.scalability_manager = ScalabilityManager(redis_client)

        if enable_feature_flags:
            self.feature_flag_manager = FeatureFlagManager(database, cache_manager, redis_client)

        # Configuration
        self.config = {
            "ab_testing_enabled": enable_ab_testing,
            "notifications_enabled": enable_notifications,
            "scalability_enabled": enable_scalability,
            "feature_flags_enabled": enable_feature_flags
        }

        logger.info("Advanced features manager initialized")

    def start_services(self):
        """Start advanced feature services."""
        try:
            if self.notification_manager:
                self.notification_manager.start_scheduler()

            if self.scalability_manager:
                self.scalability_manager.start_services()

            logger.info("Advanced feature services started")

        except Exception as e:
            logger.error(f"Failed to start advanced feature services: {e}")

    def stop_services(self):
        """Stop advanced feature services."""
        try:
            if self.notification_manager:
                self.notification_manager.stop_scheduler()

            if self.scalability_manager:
                self.scalability_manager.stop_services()

            logger.info("Advanced feature services stopped")

        except Exception as e:
            logger.error(f"Failed to stop advanced feature services: {e}")

    # A/B Testing Methods

    def create_ab_test(
        self,
        name: str,
        description: str,
        test_type: ABTestType,
        variants: List[ABTestVariant],
        start_date: datetime,
        end_date: Optional[datetime] = None,
        target_audience: Optional[Dict[str, Any]] = None
    ) -> str:
        """Create A/B test."""
        if not self.ab_test_manager:
            raise RuntimeError("A/B testing not enabled")

        return self.ab_test_manager.create_test(
            name=name,
            description=description,
            test_type=test_type,
            variants=variants,
            start_date=start_date,
            end_date=end_date,
            target_audience=target_audience
        )

    def assign_user_to_test(self, test_id: str, user_id: int) -> Optional[str]:
        """Assign user to A/B test."""
        if not self.ab_test_manager:
            return None

        return self.ab_test_manager.assign_user_to_test(test_id, user_id)

    def track_ab_conversion(self, test_id: str, user_id: int, event_type: str, event_data: Optional[Dict[str, Any]] = None):
        """Track A/B test conversion."""
        if self.ab_test_manager:
            self.ab_test_manager.track_conversion(test_id, user_id, event_type, event_data)

    def get_ab_test_results(self, test_id: str) -> List[Dict[str, Any]]:
        """Get A/B test results."""
        if not self.ab_test_manager:
            return []

        results = self.ab_test_manager.get_test_results(test_id)
        return [result.__dict__ for result in results]

    # Notification Methods

    def send_notification(
        self,
        user_id: int,
        channel: NotificationChannel,
        notification_type: NotificationType,
        content: str,
        subject: Optional[str] = None,
        scheduled_at: Optional[datetime] = None
    ) -> bool:
        """Send notification."""
        if not self.notification_manager:
            return False

        if scheduled_at:
            # Create template and schedule
            template_id = self.notification_manager.create_template(
                name=f"temp_{int(datetime.utcnow().timestamp())}",
                notification_type=notification_type,
                channel=channel,
                content=content,
                subject=subject
            )

            self.notification_manager.schedule_notification(
                user_id=user_id,
                template_id=template_id,
                channel=channel,
                scheduled_at=scheduled_at
            )
            return True
        else:
            # Send immediately
            return self.notification_manager.send_immediate_notification(
                user_id=user_id,
                channel=channel,
                notification_type=notification_type,
                content=content,
                subject=subject
            )

    def create_notification_template(
        self,
        name: str,
        notification_type: NotificationType,
        channel: NotificationChannel,
        content: str,
        subject: Optional[str] = None,
        variables: Optional[List[str]] = None
    ) -> str:
        """Create notification template."""
        if not self.notification_manager:
            raise RuntimeError("Notifications not enabled")

        return self.notification_manager.create_template(
            name=name,
            notification_type=notification_type,
            channel=channel,
            content=content,
            subject=subject,
            variables=variables
        )

    def schedule_notification(
        self,
        user_id: int,
        template_id: str,
        channel: NotificationChannel,
        scheduled_at: datetime,
        variables: Optional[Dict[str, Any]] = None
    ) -> str:
        """Schedule notification."""
        if not self.notification_manager:
            raise RuntimeError("Notifications not enabled")

        return self.notification_manager.schedule_notification(
            user_id=user_id,
            template_id=template_id,
            channel=channel,
            scheduled_at=scheduled_at,
            variables=variables
        )

    # Feature Flag Methods

    def create_feature_flag(
        self,
        name: str,
        description: str,
        flag_type: FeatureFlagType,
        default_value: Any,
        rollout_strategy: RolloutStrategy = RolloutStrategy.ALL_USERS,
        rollout_config: Optional[Dict[str, Any]] = None
    ) -> str:
        """Create feature flag."""
        if not self.feature_flag_manager:
            raise RuntimeError("Feature flags not enabled")

        return self.feature_flag_manager.create_flag(
            name=name,
            description=description,
            flag_type=flag_type,
            default_value=default_value,
            rollout_strategy=rollout_strategy,
            rollout_config=rollout_config
        )

    def is_feature_enabled(self, flag_name: str, user_id: Optional[int] = None, user_attributes: Optional[Dict[str, Any]] = None) -> bool:
        """Check if feature is enabled for user."""
        if not self.feature_flag_manager:
            return False

        user_context = None
        if user_id:
            user_context = UserContext(
                user_id=user_id,
                attributes=user_attributes or {},
                groups=[]
            )

        return self.feature_flag_manager.is_feature_enabled(flag_name, user_context)

    def get_feature_value(self, flag_name: str, user_id: Optional[int] = None, user_attributes: Optional[Dict[str, Any]] = None, default: Any = None) -> Any:
        """Get feature flag value."""
        if not self.feature_flag_manager:
            return default

        user_context = None
        if user_id:
            user_context = UserContext(
                user_id=user_id,
                attributes=user_attributes or {},
                groups=[]
            )

        return self.feature_flag_manager.get_feature_value(flag_name, user_context, default)

    # Scalability Methods

    def register_service_instance(
        self,
        service_name: str,
        host: str,
        port: int,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """Register service instance."""
        if not self.scalability_manager or not self.scalability_manager.service_registry:
            raise RuntimeError("Scalability features not enabled")

        return self.scalability_manager.service_registry.register_service(
            service_name=service_name,
            host=host,
            port=port,
            metadata=metadata
        )

    def get_service_instance(self, service_name: str, request_id: Optional[str] = None):
        """Get service instance for load balancing."""
        if not self.scalability_manager or not self.scalability_manager.load_balancer:
            return None

        return self.scalability_manager.load_balancer.get_instance(service_name, request_id)

    # Integrated Methods

    def setup_user_onboarding_experiment(self, user_id: int) -> Dict[str, Any]:
        """Set up comprehensive user onboarding with A/B testing and notifications."""
        try:
            result = {
                "ab_test_variant": None,
                "notifications_scheduled": [],
                "feature_flags": {}
            }

            # A/B test for onboarding flow
            if self.ab_test_manager:
                variant = self.assign_user_to_test("onboarding_flow_test", user_id)
                result["ab_test_variant"] = variant

            # Schedule welcome notifications
            if self.notification_manager:
                # Immediate welcome
                welcome_sent = self.send_notification(
                    user_id=user_id,
                    channel=NotificationChannel.TELEGRAM,
                    notification_type=NotificationType.WELCOME,
                    content="Welcome to VoicePal! 🎉 Let's get started with your first conversation."
                )

                if welcome_sent:
                    result["notifications_scheduled"].append("welcome_immediate")

                # Follow-up after 1 hour
                followup_time = datetime.utcnow() + timedelta(hours=1)
                followup_sent = self.send_notification(
                    user_id=user_id,
                    channel=NotificationChannel.TELEGRAM,
                    notification_type=NotificationType.ENGAGEMENT,
                    content="How was your first conversation? Try asking me about the weather or your favorite topics! 🌟",
                    scheduled_at=followup_time
                )

                if followup_sent:
                    result["notifications_scheduled"].append("followup_1h")

            # Check feature flags for new user experience
            if self.feature_flag_manager:
                result["feature_flags"] = {
                    "enhanced_onboarding": self.is_feature_enabled("enhanced_onboarding", user_id),
                    "voice_tutorial": self.is_feature_enabled("voice_tutorial", user_id),
                    "premium_trial": self.is_feature_enabled("premium_trial", user_id)
                }

            return result

        except Exception as e:
            logger.error(f"Failed to setup user onboarding: {e}")
            return {"error": str(e)}

    def handle_user_retention(self, user_id: int, days_inactive: int) -> Dict[str, Any]:
        """Handle user retention with targeted notifications and experiments."""
        try:
            result = {
                "retention_strategy": None,
                "notifications_sent": [],
                "ab_test_assigned": None
            }

            # Determine retention strategy based on inactivity
            if days_inactive <= 3:
                strategy = "gentle_reminder"
                message = "Miss chatting? I'm here whenever you want to talk! 💬"
            elif days_inactive <= 7:
                strategy = "feature_highlight"
                message = "Did you know I can help with voice messages too? Try sending me a voice note! 🎤"
            elif days_inactive <= 14:
                strategy = "incentive_offer"
                message = "We miss you! Come back and get 50 bonus credits to continue our conversations! 🎁"
            else:
                strategy = "win_back"
                message = "It's been a while! What would you like to talk about today? I've learned some new things! ✨"

            result["retention_strategy"] = strategy

            # Send retention notification
            if self.notification_manager:
                sent = self.send_notification(
                    user_id=user_id,
                    channel=NotificationChannel.TELEGRAM,
                    notification_type=NotificationType.RETENTION,
                    content=message
                )

                if sent:
                    result["notifications_sent"].append(strategy)

            # Assign to retention A/B test
            if self.ab_test_manager:
                variant = self.assign_user_to_test("retention_strategy_test", user_id)
                result["ab_test_assigned"] = variant

            return result

        except Exception as e:
            logger.error(f"Failed to handle user retention: {e}")
            return {"error": str(e)}

    def get_advanced_features_status(self) -> Dict[str, Any]:
        """Get status of all advanced features."""
        status = {
            "timestamp": datetime.utcnow().isoformat(),
            "config": self.config,
            "components": {}
        }

        # A/B Testing status
        if self.ab_test_manager:
            active_tests = self.ab_test_manager.list_active_tests()
            status["components"]["ab_testing"] = {
                "enabled": True,
                "active_tests": len(active_tests),
                "test_names": [test.name for test in active_tests]
            }
        else:
            status["components"]["ab_testing"] = {"enabled": False}

        # Notifications status
        if self.notification_manager:
            status["components"]["notifications"] = {
                "enabled": True,
                "scheduler_running": self.notification_manager.is_running
            }
        else:
            status["components"]["notifications"] = {"enabled": False}

        # Scalability status
        if self.scalability_manager:
            status["components"]["scalability"] = self.scalability_manager.get_scalability_status()
        else:
            status["components"]["scalability"] = {"enabled": False}

        # Feature flags status
        if self.feature_flag_manager:
            flags = self.feature_flag_manager.list_flags(enabled_only=True)
            status["components"]["feature_flags"] = {
                "enabled": True,
                "active_flags": len(flags),
                "flag_names": [flag.name for flag in flags]
            }
        else:
            status["components"]["feature_flags"] = {"enabled": False}

        return status

    def cleanup_old_data(self, days: int = 90):
        """Clean up old data from advanced features."""
        try:
            # Clean up old A/B test data
            if self.ab_test_manager:
                # This would need to be implemented in ABTestManager
                pass

            # Clean up old notification data
            if self.notification_manager:
                # This would need to be implemented in NotificationManager
                pass

            logger.info(f"Cleaned up advanced features data older than {days} days")

        except Exception as e:
            logger.error(f"Failed to cleanup advanced features data: {e}")
