"""
Welcome manager for VoicePal.

This module manages personalized welcome messages based on user data, time of day, and mood history.
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import random
import pytz

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class WelcomeManager:
    """Manages personalized welcome messages for users."""

    def __init__(self, database, mood_tracker=None, config: Dict[str, Any] = None):
        """
        Initialize the welcome manager.

        Args:
            database: Database instance
            mood_tracker: MoodTracker instance (optional)
            config: Configuration dictionary
        """
        self.database = database
        self.mood_tracker = mood_tracker
        self.config = config or {}

        # Default greetings by time of day
        self.time_greetings = {
            "morning": [
                "🌅 Good morning",
                "🌞 Rise and shine",
                "🌄 Morning",
                "👋 Hello and good morning",
                "🌅 Top of the morning to you"
            ],
            "afternoon": [
                "☀️ Good afternoon",
                "👋 Hello",
                "👋 Hi there",
                "🤗 Greetings",
                "🌈 Hope you're having a good day"
            ],
            "evening": [
                "🌆 Good evening",
                "🌙 Evening",
                "👋 Hello",
                "👋 Hi there",
                "✨ Hope you had a good day"
            ],
            "night": [
                "🌙 Good night",
                "🦉 Hello night owl",
                "💫 Still up?",
                "👋 Hi there",
                "🔥 Burning the midnight oil?"
            ]
        }

        # Greetings by visit count
        self.visit_greetings = {
            "first_time": [
                "🎉 Welcome to VoicePal! I'm excited to meet you.",
                "👋 Hello and welcome! I'm your new AI friend.",
                "🤗 Nice to meet you! I'm VoicePal, your AI companion."
            ],
            "returning": [
                "🌟 Welcome back!",
                "😊 Good to see you again!",
                "👋 Hello again!",
                "🤗 Nice to have you back!"
            ],
            "frequent": [
                "✨ Always a pleasure to see you!",
                "🌟 Hello, my frequent friend!",
                "🎉 Back again! I'm always here for you.",
                "👋 Hello! You're becoming a regular!"
            ]
        }

        # Greetings by mood
        self.mood_greetings = {
            "positive": [
                "😊 You seem to be in a good mood lately!",
                "🌈 I'm glad to see you're doing well!",
                "✨ Your positive energy is contagious!"
            ],
            "negative": [
                "🌻 I hope I can brighten your day a bit.",
                "🤗 I'm here if you need someone to talk to.",
                "💭 Sometimes just talking helps. How are you feeling?"
            ],
            "neutral": [
                "💫 How are you feeling today?",
                "💭 What's on your mind?",
                "👂 I'm here to chat whenever you're ready."
            ]
        }

        # Absence greetings (when user has been away for a while)
        self.absence_greetings = {
            "short": [  # 1-3 days
                "👋 It's been a little while!",
                "🌟 Nice to see you again after a short break.",
                "🎉 Welcome back after your brief absence."
            ],
            "medium": [  # 4-14 days
                "⏳ It's been a while since we last talked!",
                "💫 I've missed our conversations!",
                "🎊 Great to have you back after some time away."
            ],
            "long": [  # 15+ days
                "🎉 Wow, it's been quite some time!",
                "✨ I've really missed our chats!",
                "🌈 Welcome back! It's been a long time."
            ]
        }

    def get_time_greeting(self, user_timezone: Optional[str] = None) -> str:
        """
        Get appropriate greeting based on time of day.

        Args:
            user_timezone: User's timezone (optional)

        Returns:
            str: Time-appropriate greeting
        """
        try:
            # Get current time in user's timezone if provided
            if user_timezone:
                try:
                    tz = pytz.timezone(user_timezone)
                    current_time = datetime.now(tz)
                except Exception:
                    # Invalid timezone, use UTC
                    current_time = datetime.now(pytz.UTC)
            else:
                # Default to UTC
                current_time = datetime.now(pytz.UTC)

            # Get hour (0-23)
            hour = current_time.hour

            # Determine time of day
            if 5 <= hour < 12:
                time_of_day = "morning"
            elif 12 <= hour < 17:
                time_of_day = "afternoon"
            elif 17 <= hour < 22:
                time_of_day = "evening"
            else:
                time_of_day = "night"

            # Get random greeting for this time of day
            greetings = self.time_greetings.get(time_of_day, self.time_greetings["afternoon"])
            return random.choice(greetings)
        except Exception as e:
            logger.error(f"Error getting time greeting: {e}")
            return "Hello"

    def get_visit_greeting(self, user_id: int) -> str:
        """
        Get greeting based on visit count.

        Args:
            user_id: User ID

        Returns:
            str: Visit-appropriate greeting
        """
        try:
            # Get user data
            user_data = self.database.get_user(user_id)

            if not user_data:
                return random.choice(self.visit_greetings["first_time"])

            # Get visit count
            visit_count = user_data.get("visit_count", 0)

            # Increment visit count
            if hasattr(self.database, "increment_visit_count"):
                self.database.increment_visit_count(user_id)

            # Determine visit type
            if visit_count == 0:
                visit_type = "first_time"
            elif visit_count < 5:
                visit_type = "returning"
            else:
                visit_type = "frequent"

            # Get random greeting for this visit type
            greetings = self.visit_greetings.get(visit_type, self.visit_greetings["returning"])
            return random.choice(greetings)
        except Exception as e:
            logger.error(f"Error getting visit greeting for user {user_id}: {e}")
            return "Welcome to VoicePal!"

    def get_absence_greeting(self, user_id: int) -> Optional[str]:
        """
        Get greeting based on absence duration.

        Args:
            user_id: User ID

        Returns:
            Optional[str]: Absence greeting or None if not applicable
        """
        try:
            # Get user data
            user_data = self.database.get_user(user_id)

            if not user_data or not user_data.get("last_active"):
                return None

            # Get last active time
            last_active_str = user_data.get("last_active")

            # Parse last active time
            if isinstance(last_active_str, str):
                try:
                    last_active = datetime.strptime(last_active_str, "%Y-%m-%d %H:%M:%S")
                except ValueError:
                    # Try ISO format
                    last_active = datetime.fromisoformat(last_active_str.replace("Z", "+00:00"))
            else:
                last_active = last_active_str

            # Calculate days since last active
            days_since = (datetime.now() - last_active).days

            # Determine absence type
            if days_since <= 0:
                return None  # Active today
            elif 1 <= days_since <= 3:
                absence_type = "short"
            elif 4 <= days_since <= 14:
                absence_type = "medium"
            else:
                absence_type = "long"

            # Get random greeting for this absence type
            greetings = self.absence_greetings.get(absence_type, self.absence_greetings["short"])
            return random.choice(greetings)
        except Exception as e:
            logger.error(f"Error getting absence greeting for user {user_id}: {e}")
            return None

    def get_mood_greeting(self, user_id: int) -> Optional[str]:
        """
        Get greeting based on user's recent mood.

        Args:
            user_id: User ID

        Returns:
            Optional[str]: Mood-based greeting or None if not applicable
        """
        try:
            # Check if mood tracker is available
            if not self.mood_tracker:
                return None

            # Get mood summary
            mood_summary = self.mood_tracker.get_mood_summary(user_id)

            if not mood_summary:
                return None

            # Get dominant mood
            dominant_mood = mood_summary.get("dominant_mood", "neutral")

            # Map mood to greeting type
            if dominant_mood in ["positive", "happy", "excited"]:
                mood_type = "positive"
            elif dominant_mood in ["negative", "sad", "angry", "anxious"]:
                mood_type = "negative"
            else:
                mood_type = "neutral"

            # Get random greeting for this mood type
            greetings = self.mood_greetings.get(mood_type, self.mood_greetings["neutral"])
            return random.choice(greetings)
        except Exception as e:
            logger.error(f"Error getting mood greeting for user {user_id}: {e}")
            return None

    def get_personalized_name(self, user_id: int) -> str:
        """
        Get personalized name for user.

        Args:
            user_id: User ID

        Returns:
            str: User's name or username
        """
        try:
            # Get user data
            user_data = self.database.get_user(user_id)

            if not user_data:
                return ""

            # Check for preferred name
            if "preferred_name" in user_data and user_data["preferred_name"]:
                return user_data["preferred_name"]

            # Use first name if available
            if "first_name" in user_data and user_data["first_name"]:
                return user_data["first_name"]

            # Use username if available
            if "username" in user_data and user_data["username"]:
                return user_data["username"]

            return ""
        except Exception as e:
            logger.error(f"Error getting personalized name for user {user_id}: {e}")
            return ""

    def generate_welcome_message(self, user_id: int, is_new_user: bool = False) -> str:
        """
        Generate personalized welcome message.

        Args:
            user_id: User ID
            is_new_user: Whether this is a new user

        Returns:
            str: Personalized welcome message
        """
        try:
            # Get user data
            user_data = self.database.get_user(user_id)

            if not user_data:
                # Default welcome for unknown user
                return "🎉 Welcome to VoicePal! I'm your AI friend that you can talk to via text or voice messages."

            # Get user's name
            user_name = self.get_personalized_name(user_id)
            name_suffix = f", {user_name}" if user_name else ""

            # Get time-based greeting
            timezone = user_data.get("timezone")
            time_greeting = self.get_time_greeting(timezone)

            # Start building the message - only include name for new users or after long absence
            absence_greeting = self.get_absence_greeting(user_id)
            if is_new_user or (absence_greeting and "long" in absence_greeting.lower()):
                message = f"{time_greeting}{name_suffix}! "
            else:
                message = f"{time_greeting}! "

            # Add appropriate greeting based on user status
            if is_new_user:
                # New user greeting
                message += random.choice(self.visit_greetings["first_time"])
            else:
                # Get absence greeting if applicable
                absence_greeting = self.get_absence_greeting(user_id)
                if absence_greeting:
                    message += f"{absence_greeting} "
                else:
                    # Get visit-based greeting
                    visit_greeting = self.get_visit_greeting(user_id)
                    message += f"{visit_greeting} "

                # Add mood-based greeting if available
                mood_greeting = self.get_mood_greeting(user_id)
                if mood_greeting:
                    message += f"{mood_greeting} "

            # Add standard information
            credit_config = self.config.get("credit_system", {})
            text_cost = credit_config.get("text_credit_cost", 1)
            voice_cost = credit_config.get("voice_credit_cost", 3)

            message += (
                f"\n\n💬 You can talk to me via text or voice messages. "
                f"📝 Text messages cost {text_cost} credit.\n"
                f"🎤 Voice messages cost {voice_cost} credits.\n\n"
                f"💰 You have {self.database.get_user_credits(user_id)} credits."
            )

            # Add free credits info for new users
            if is_new_user:
                free_credits = credit_config.get("free_trial_credits", 100)
                message += (
                    f"\n\n🎁 As a new user, you've received {free_credits} free credits "
                    f"to get started! These are one-time free credits for your account."
                )

            return message
        except Exception as e:
            logger.error(f"Error generating welcome message for user {user_id}: {e}")
            return (
                "🎉 Welcome to VoicePal! I'm your AI friend that you can talk to "
                "via text or voice messages."
            )

# End of welcome_manager.py