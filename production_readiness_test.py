#!/usr/bin/env python3
"""
Production Readiness Test Suite for MoneyMule Telegram Bot

This comprehensive test suite validates all aspects of the bot for production deployment,
including security, performance, monetization, user experience, and reliability.
"""

import os
import sys
import asyncio
import logging
import time
import json
import subprocess
import tempfile
import sqlite3
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import importlib.util

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('production_readiness.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ProductionReadinessTestSuite:
    """Comprehensive production readiness test suite."""

    def __init__(self):
        self.results = {
            'security': {},
            'performance': {},
            'monetization': {},
            'user_experience': {},
            'reliability': {},
            'deployment': {},
            'monitoring': {},
            'compliance': {}
        }
        self.start_time = time.time()
        self.critical_failures = []
        self.warnings = []

    def log_result(self, category: str, test_name: str, passed: bool, details: str = "", critical: bool = False):
        """Log test result."""
        self.results[category][test_name] = {
            'passed': passed,
            'details': details,
            'timestamp': datetime.now().isoformat(),
            'critical': critical
        }

        status = "✅ PASS" if passed else "❌ FAIL"
        if critical and not passed:
            self.critical_failures.append(f"{category}.{test_name}: {details}")
            status += " (CRITICAL)"
        elif not passed:
            self.warnings.append(f"{category}.{test_name}: {details}")

        logger.info(f"{status} - {category}.{test_name}: {details}")

    async def test_security(self):
        """Test security aspects."""
        logger.info("🔒 Testing Security...")

        # Test 1: Environment variables security
        env_vars = ['BOT_TOKEN', 'DEEPGRAM_API_KEY', 'GOOGLE_AI_API_KEY', 'ELEVENLABS_API_KEY']
        missing_env = [var for var in env_vars if not os.getenv(var)]
        self.log_result(
            'security', 'environment_variables',
            len(missing_env) == 0,
            f"Missing env vars: {missing_env}" if missing_env else "All required env vars present",
            critical=True
        )

        # Test 2: Check for hardcoded secrets
        hardcoded_secrets = await self._check_hardcoded_secrets()
        self.log_result(
            'security', 'hardcoded_secrets',
            len(hardcoded_secrets) == 0,
            f"Found hardcoded secrets: {hardcoded_secrets}" if hardcoded_secrets else "No hardcoded secrets found"
        )

        # Test 3: Database security
        db_security = await self._test_database_security()
        self.log_result(
            'security', 'database_security',
            db_security['secure'],
            db_security['details']
        )

        # Test 4: API endpoint security
        api_security = await self._test_api_security()
        self.log_result(
            'security', 'api_security',
            api_security['secure'],
            api_security['details']
        )

    async def test_performance(self):
        """Test performance aspects."""
        logger.info("⚡ Testing Performance...")

        # Test 1: Bot startup time
        startup_time = await self._measure_startup_time()
        self.log_result(
            'performance', 'startup_time',
            startup_time < 10.0,
            f"Startup time: {startup_time:.2f}s (target: <10s)"
        )

        # Test 2: Memory usage
        memory_usage = await self._check_memory_usage()
        self.log_result(
            'performance', 'memory_usage',
            memory_usage['acceptable'],
            memory_usage['details']
        )

        # Test 3: Database query performance
        db_performance = await self._test_database_performance()
        self.log_result(
            'performance', 'database_queries',
            db_performance['acceptable'],
            db_performance['details']
        )

        # Test 4: Voice processing performance
        voice_performance = await self._test_voice_processing_performance()
        self.log_result(
            'performance', 'voice_processing',
            voice_performance['acceptable'],
            voice_performance['details']
        )

    async def test_monetization(self):
        """Test monetization features."""
        logger.info("💰 Testing Monetization...")

        # Test 1: Payment system integration
        payment_integration = await self._test_payment_integration()
        self.log_result(
            'monetization', 'payment_integration',
            payment_integration['working'],
            payment_integration['details'],
            critical=True
        )

        # Test 2: Credit system
        credit_system = await self._test_credit_system()
        self.log_result(
            'monetization', 'credit_system',
            credit_system['working'],
            credit_system['details'],
            critical=True
        )

        # Test 3: Subscription handling
        subscription_handling = await self._test_subscription_handling()
        self.log_result(
            'monetization', 'subscription_handling',
            subscription_handling['working'],
            subscription_handling['details']
        )

    async def test_user_experience(self):
        """Test user experience aspects."""
        logger.info("👤 Testing User Experience...")

        # Test 1: Bot responsiveness
        responsiveness = await self._test_bot_responsiveness()
        self.log_result(
            'user_experience', 'responsiveness',
            responsiveness['acceptable'],
            responsiveness['details']
        )

        # Test 2: Menu navigation
        menu_navigation = await self._test_menu_navigation()
        self.log_result(
            'user_experience', 'menu_navigation',
            menu_navigation['working'],
            menu_navigation['details']
        )

        # Test 3: Voice quality
        voice_quality = await self._test_voice_quality()
        self.log_result(
            'user_experience', 'voice_quality',
            voice_quality['acceptable'],
            voice_quality['details']
        )

        # Test 4: Memory system
        memory_system = await self._test_memory_system()
        self.log_result(
            'user_experience', 'memory_system',
            memory_system['working'],
            memory_system['details']
        )

    async def test_reliability(self):
        """Test reliability aspects."""
        logger.info("🛡️ Testing Reliability...")

        # Test 1: Error handling
        error_handling = await self._test_error_handling()
        self.log_result(
            'reliability', 'error_handling',
            error_handling['robust'],
            error_handling['details']
        )

        # Test 2: Rate limiting
        rate_limiting = await self._test_rate_limiting()
        self.log_result(
            'reliability', 'rate_limiting',
            rate_limiting['working'],
            rate_limiting['details']
        )

        # Test 3: Graceful degradation
        graceful_degradation = await self._test_graceful_degradation()
        self.log_result(
            'reliability', 'graceful_degradation',
            graceful_degradation['working'],
            graceful_degradation['details']
        )

    async def test_deployment(self):
        """Test deployment readiness."""
        logger.info("🚀 Testing Deployment...")

        # Test 1: Dependencies
        dependencies = await self._check_dependencies()
        self.log_result(
            'deployment', 'dependencies',
            dependencies['satisfied'],
            dependencies['details'],
            critical=True
        )

        # Test 2: Configuration files
        config_files = await self._check_config_files()
        self.log_result(
            'deployment', 'configuration',
            config_files['complete'],
            config_files['details']
        )

        # Test 3: Health checks
        health_checks = await self._test_health_checks()
        self.log_result(
            'deployment', 'health_checks',
            health_checks['working'],
            health_checks['details']
        )

    async def test_monitoring(self):
        """Test monitoring capabilities."""
        logger.info("📊 Testing Monitoring...")

        # Test 1: Logging system
        logging_system = await self._test_logging_system()
        self.log_result(
            'monitoring', 'logging',
            logging_system['working'],
            logging_system['details']
        )

        # Test 2: Metrics collection
        metrics = await self._test_metrics_collection()
        self.log_result(
            'monitoring', 'metrics',
            metrics['working'],
            metrics['details']
        )

    async def test_compliance(self):
        """Test compliance aspects."""
        logger.info("📋 Testing Compliance...")

        # Test 1: Data privacy
        data_privacy = await self._test_data_privacy()
        self.log_result(
            'compliance', 'data_privacy',
            data_privacy['compliant'],
            data_privacy['details']
        )

        # Test 2: Terms of service
        terms_service = await self._check_terms_service()
        self.log_result(
            'compliance', 'terms_service',
            terms_service['present'],
            terms_service['details']
        )

    # Helper methods for specific tests
    async def _check_hardcoded_secrets(self) -> List[str]:
        """Check for hardcoded secrets in code."""
        secrets = []
        patterns = ['api_key', 'token', 'secret', 'password']

        for root, dirs, files in os.walk('.'):
            # Skip test directories and virtual environments
            if any(skip in root for skip in ['.git', '__pycache__', 'venv', '.env']):
                continue

            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read().lower()
                            for pattern in patterns:
                                if f'{pattern} = "' in content or f'{pattern}="' in content:
                                    secrets.append(f"{file_path}: potential {pattern}")
                    except Exception:
                        continue

        return secrets

    async def _test_database_security(self) -> Dict[str, Any]:
        """Test database security."""
        try:
            # Check if database file exists and has proper permissions
            db_path = "bot_database.db"
            if os.path.exists(db_path):
                stat = os.stat(db_path)
                # Check file permissions (should not be world-readable)
                secure = (stat.st_mode & 0o077) == 0
                return {
                    'secure': secure,
                    'details': f"Database permissions: {oct(stat.st_mode)[-3:]}"
                }
            else:
                return {'secure': True, 'details': "Database file not found (will be created)"}
        except Exception as e:
            return {'secure': False, 'details': f"Database security check failed: {e}"}

    async def _test_api_security(self) -> Dict[str, Any]:
        """Test API security."""
        try:
            # Check if API endpoints have proper authentication
            api_file = "bot/api/main.py"
            if os.path.exists(api_file):
                with open(api_file, 'r') as f:
                    content = f.read()
                    has_auth = 'authentication' in content.lower() or 'auth' in content.lower()
                    return {
                        'secure': has_auth,
                        'details': "API authentication found" if has_auth else "No API authentication detected"
                    }
            else:
                return {'secure': True, 'details': "No API endpoints found"}
        except Exception as e:
            return {'secure': False, 'details': f"API security check failed: {e}"}

    async def _measure_startup_time(self) -> float:
        """Measure bot startup time."""
        start_time = time.time()
        try:
            # Import main bot module to measure import time
            spec = importlib.util.spec_from_file_location("bot.main", "bot/main.py")
            if spec and spec.loader:
                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)
            return time.time() - start_time
        except Exception as e:
            logger.warning(f"Could not measure startup time: {e}")
            return 0.0

    async def _check_memory_usage(self) -> Dict[str, Any]:
        """Check memory usage."""
        try:
            import psutil
            process = psutil.Process()
            memory_mb = process.memory_info().rss / 1024 / 1024
            acceptable = memory_mb < 500  # Less than 500MB
            return {
                'acceptable': acceptable,
                'details': f"Memory usage: {memory_mb:.1f}MB (target: <500MB)"
            }
        except ImportError:
            return {
                'acceptable': True,
                'details': "psutil not available, cannot check memory usage"
            }
        except Exception as e:
            return {
                'acceptable': False,
                'details': f"Memory check failed: {e}"
            }

    async def _test_database_performance(self) -> Dict[str, Any]:
        """Test database query performance."""
        try:
            db_path = "bot_database.db"
            if not os.path.exists(db_path):
                return {'acceptable': True, 'details': "Database not found, will be created on first use"}

            start_time = time.time()
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # Test basic query performance
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()

            query_time = time.time() - start_time
            conn.close()

            acceptable = query_time < 1.0  # Less than 1 second
            return {
                'acceptable': acceptable,
                'details': f"Database query time: {query_time:.3f}s, Tables: {len(tables)}"
            }
        except Exception as e:
            return {'acceptable': False, 'details': f"Database performance test failed: {e}"}

    async def _test_voice_processing_performance(self) -> Dict[str, Any]:
        """Test voice processing performance."""
        try:
            # Check if voice processing modules are importable
            voice_modules = [
                'bot.providers.voice.processor',
                'bot.providers.tts.deepgram_provider',
                'bot.providers.stt.deepgram_provider'
            ]

            import_time = time.time()
            for module in voice_modules:
                try:
                    __import__(module)
                except ImportError as e:
                    return {'acceptable': False, 'details': f"Voice module import failed: {module} - {e}"}

            total_import_time = time.time() - import_time
            acceptable = total_import_time < 5.0  # Less than 5 seconds

            return {
                'acceptable': acceptable,
                'details': f"Voice modules import time: {total_import_time:.3f}s"
            }
        except Exception as e:
            return {'acceptable': False, 'details': f"Voice performance test failed: {e}"}

    async def _test_payment_integration(self) -> Dict[str, Any]:
        """Test payment system integration."""
        try:
            # Check if payment modules are importable
            from bot.payment.telegram_stars_payment import TelegramStarsPayment
            from bot.payment.payment_integration import integrate_telegram_stars_payment

            # Check if required environment variables are set
            required_vars = ['BOT_TOKEN']  # Telegram Stars uses bot token
            missing_vars = [var for var in required_vars if not os.getenv(var)]

            if missing_vars:
                return {
                    'working': False,
                    'details': f"Missing payment env vars: {missing_vars}"
                }

            return {
                'working': True,
                'details': "Payment modules importable, required env vars present"
            }
        except ImportError as e:
            return {'working': False, 'details': f"Payment module import failed: {e}"}
        except Exception as e:
            return {'working': False, 'details': f"Payment integration test failed: {e}"}

    async def _test_credit_system(self) -> Dict[str, Any]:
        """Test credit system functionality."""
        try:
            # Check if user management and credit system are working
            from bot.core.user_manager import UserManager
            from bot.database.models.user import User

            # Test basic credit operations (without actual database operations)
            return {
                'working': True,
                'details': "Credit system modules importable"
            }
        except ImportError as e:
            return {'working': False, 'details': f"Credit system import failed: {e}"}
        except Exception as e:
            return {'working': False, 'details': f"Credit system test failed: {e}"}

    async def _test_subscription_handling(self) -> Dict[str, Any]:
        """Test subscription handling."""
        try:
            from bot.database.models.payment import Subscription, PaymentPackage
            return {
                'working': True,
                'details': "Subscription models importable"
            }
        except ImportError as e:
            return {'working': False, 'details': f"Subscription module import failed: {e}"}
        except Exception as e:
            return {'working': False, 'details': f"Subscription test failed: {e}"}

    async def _test_bot_responsiveness(self) -> Dict[str, Any]:
        """Test bot responsiveness."""
        try:
            # Test if main bot components can be imported quickly
            start_time = time.time()
            from bot.main import VoicePalBot
            import_time = time.time() - start_time

            acceptable = import_time < 3.0  # Less than 3 seconds
            return {
                'acceptable': acceptable,
                'details': f"Bot import time: {import_time:.3f}s (target: <3s)"
            }
        except ImportError as e:
            return {'acceptable': False, 'details': f"Bot import failed: {e}"}
        except Exception as e:
            return {'acceptable': False, 'details': f"Responsiveness test failed: {e}"}

    async def _test_menu_navigation(self) -> Dict[str, Any]:
        """Test menu navigation system."""
        try:
            from bot.core.menu_manager import MenuManager
            from bot.core.navigation_router import NavigationRouter
            return {
                'working': True,
                'details': "Menu navigation modules importable"
            }
        except ImportError as e:
            return {'working': False, 'details': f"Menu navigation import failed: {e}"}
        except Exception as e:
            return {'working': False, 'details': f"Menu navigation test failed: {e}"}

    async def _test_voice_quality(self) -> Dict[str, Any]:
        """Test voice quality components."""
        try:
            from bot.providers.tts.deepgram_provider import DeepgramTTSProvider
            from bot.providers.tts.elevenlabs_provider import ElevenLabsTTSProvider

            # Check if API keys are available
            deepgram_key = os.getenv('DEEPGRAM_API_KEY')
            elevenlabs_key = os.getenv('ELEVENLABS_API_KEY')

            providers_available = []
            if deepgram_key:
                providers_available.append('Deepgram')
            if elevenlabs_key:
                providers_available.append('ElevenLabs')

            acceptable = len(providers_available) > 0
            return {
                'acceptable': acceptable,
                'details': f"Available TTS providers: {', '.join(providers_available) if providers_available else 'None'}"
            }
        except ImportError as e:
            return {'acceptable': False, 'details': f"Voice quality module import failed: {e}"}
        except Exception as e:
            return {'acceptable': False, 'details': f"Voice quality test failed: {e}"}

    async def _test_memory_system(self) -> Dict[str, Any]:
        """Test memory system functionality."""
        try:
            from bot.features.hierarchical_memory_manager import HierarchicalMemoryManager
            from bot.database.models.memory import Memory
            return {
                'working': True,
                'details': "Memory system modules importable"
            }
        except ImportError as e:
            return {'working': False, 'details': f"Memory system import failed: {e}"}
        except Exception as e:
            return {'working': False, 'details': f"Memory system test failed: {e}"}

    async def _test_error_handling(self) -> Dict[str, Any]:
        """Test error handling robustness."""
        try:
            # Check if error handling modules exist
            error_modules = [
                'bot.core.logging_config',
                'bot.payment.payment_error_handler'
            ]

            for module in error_modules:
                try:
                    __import__(module)
                except ImportError:
                    return {'robust': False, 'details': f"Error handling module missing: {module}"}

            return {
                'robust': True,
                'details': "Error handling modules present"
            }
        except Exception as e:
            return {'robust': False, 'details': f"Error handling test failed: {e}"}

    async def _test_rate_limiting(self) -> Dict[str, Any]:
        """Test rate limiting functionality."""
        try:
            from bot.core.rate_limiter import rate_limit
            return {
                'working': True,
                'details': "Rate limiting module importable"
            }
        except ImportError as e:
            return {'working': False, 'details': f"Rate limiting import failed: {e}"}
        except Exception as e:
            return {'working': False, 'details': f"Rate limiting test failed: {e}"}

    async def _test_graceful_degradation(self) -> Dict[str, Any]:
        """Test graceful degradation capabilities."""
        try:
            # Check if fallback mechanisms exist
            fallback_modules = [
                'bot.providers.provider_factory',
                'bot.providers.secure_provider_factory'
            ]

            for module in fallback_modules:
                try:
                    __import__(module)
                except ImportError:
                    return {'working': False, 'details': f"Fallback module missing: {module}"}

            return {
                'working': True,
                'details': "Graceful degradation modules present"
            }
        except Exception as e:
            return {'working': False, 'details': f"Graceful degradation test failed: {e}"}

    async def _check_dependencies(self) -> Dict[str, Any]:
        """Check if all dependencies are satisfied."""
        try:
            # Check requirements.txt
            if os.path.exists('requirements.txt'):
                with open('requirements.txt', 'r') as f:
                    requirements = f.read().strip().split('\n')

                missing_deps = []
                for req in requirements:
                    if req.strip() and not req.startswith('#'):
                        package_name = req.split('==')[0].split('>=')[0].split('<=')[0].strip()
                        try:
                            __import__(package_name.replace('-', '_'))
                        except ImportError:
                            missing_deps.append(package_name)

                satisfied = len(missing_deps) == 0
                return {
                    'satisfied': satisfied,
                    'details': f"Missing dependencies: {missing_deps}" if missing_deps else "All dependencies satisfied"
                }
            else:
                return {'satisfied': False, 'details': "requirements.txt not found"}
        except Exception as e:
            return {'satisfied': False, 'details': f"Dependency check failed: {e}"}

    async def _check_config_files(self) -> Dict[str, Any]:
        """Check configuration files."""
        required_files = [
            'requirements.txt',
            'Procfile',
            '.env.example'
        ]

        missing_files = [f for f in required_files if not os.path.exists(f)]

        return {
            'complete': len(missing_files) == 0,
            'details': f"Missing config files: {missing_files}" if missing_files else "All config files present"
        }

    async def _test_health_checks(self) -> Dict[str, Any]:
        """Test health check endpoints."""
        try:
            # Check if health check API exists
            if os.path.exists('bot/api/routes/health.py'):
                return {
                    'working': True,
                    'details': "Health check endpoint found"
                }
            else:
                return {
                    'working': False,
                    'details': "No health check endpoint found"
                }
        except Exception as e:
            return {'working': False, 'details': f"Health check test failed: {e}"}

    async def _test_logging_system(self) -> Dict[str, Any]:
        """Test logging system."""
        try:
            from bot.core.logging_config import configure_logging, get_logger
            return {
                'working': True,
                'details': "Logging system modules importable"
            }
        except ImportError as e:
            return {'working': False, 'details': f"Logging system import failed: {e}"}
        except Exception as e:
            return {'working': False, 'details': f"Logging system test failed: {e}"}

    async def _test_metrics_collection(self) -> Dict[str, Any]:
        """Test metrics collection."""
        try:
            # Check if metrics/analytics modules exist
            if os.path.exists('bot/api/routes/analytics.py'):
                return {
                    'working': True,
                    'details': "Analytics/metrics endpoint found"
                }
            else:
                return {
                    'working': False,
                    'details': "No metrics collection found"
                }
        except Exception as e:
            return {'working': False, 'details': f"Metrics test failed: {e}"}

    async def _test_data_privacy(self) -> Dict[str, Any]:
        """Test data privacy compliance."""
        try:
            # Check if privacy-related handlers exist
            privacy_files = [
                'bot/handlers/privacy_handlers.py',
                'privacy_policy.md',
                'terms_of_service.md'
            ]

            existing_files = [f for f in privacy_files if os.path.exists(f)]

            return {
                'compliant': len(existing_files) > 0,
                'details': f"Privacy files found: {existing_files}" if existing_files else "No privacy documentation found"
            }
        except Exception as e:
            return {'compliant': False, 'details': f"Privacy test failed: {e}"}

    async def _check_terms_service(self) -> Dict[str, Any]:
        """Check terms of service."""
        terms_files = ['terms_of_service.md', 'TERMS.md', 'terms.txt']
        existing_terms = [f for f in terms_files if os.path.exists(f)]

        return {
            'present': len(existing_terms) > 0,
            'details': f"Terms files found: {existing_terms}" if existing_terms else "No terms of service found"
        }

    async def run_all_tests(self) -> Dict[str, Any]:
        """Run all production readiness tests."""
        logger.info("🚀 Starting Production Readiness Test Suite")

        test_categories = [
            self.test_security,
            self.test_performance,
            self.test_monetization,
            self.test_user_experience,
            self.test_reliability,
            self.test_deployment,
            self.test_monitoring,
            self.test_compliance
        ]

        for test_category in test_categories:
            try:
                await test_category()
            except Exception as e:
                category_name = test_category.__name__.replace('test_', '')
                logger.error(f"Error in {category_name} tests: {e}")
                self.critical_failures.append(f"{category_name}: Test execution failed - {e}")

        return self._generate_report()

    def _generate_report(self) -> Dict[str, Any]:
        """Generate comprehensive test report."""
        total_time = time.time() - self.start_time

        # Calculate statistics
        total_tests = sum(len(category) for category in self.results.values())
        passed_tests = sum(
            sum(1 for test in category.values() if test['passed'])
            for category in self.results.values()
        )
        critical_failures_count = len(self.critical_failures)
        warnings_count = len(self.warnings)

        # Determine overall status
        if critical_failures_count > 0:
            overall_status = "❌ NOT READY FOR PRODUCTION"
            recommendation = "Critical issues must be resolved before deployment"
        elif warnings_count > 5:
            overall_status = "⚠️ NEEDS ATTENTION"
            recommendation = "Address warnings before production deployment"
        else:
            overall_status = "✅ PRODUCTION READY"
            recommendation = "Bot is ready for production deployment"

        report = {
            'overall_status': overall_status,
            'recommendation': recommendation,
            'statistics': {
                'total_tests': total_tests,
                'passed_tests': passed_tests,
                'failed_tests': total_tests - passed_tests,
                'critical_failures': critical_failures_count,
                'warnings': warnings_count,
                'success_rate': f"{(passed_tests/total_tests*100):.1f}%" if total_tests > 0 else "0%",
                'execution_time': f"{total_time:.2f}s"
            },
            'critical_failures': self.critical_failures,
            'warnings': self.warnings,
            'detailed_results': self.results,
            'timestamp': datetime.now().isoformat()
        }

        return report

async def main():
    """Main function to run production readiness tests."""
    suite = ProductionReadinessTestSuite()
    report = await suite.run_all_tests()

    # Print summary
    print("\n" + "="*80)
    print("PRODUCTION READINESS TEST REPORT")
    print("="*80)
    print(f"Overall Status: {report['overall_status']}")
    print(f"Recommendation: {report['recommendation']}")
    print(f"Success Rate: {report['statistics']['success_rate']}")
    print(f"Execution Time: {report['statistics']['execution_time']}")

    if report['critical_failures']:
        print(f"\n❌ CRITICAL FAILURES ({len(report['critical_failures'])}):")
        for failure in report['critical_failures']:
            print(f"  • {failure}")

    if report['warnings']:
        print(f"\n⚠️ WARNINGS ({len(report['warnings'])}):")
        for warning in report['warnings'][:10]:  # Show first 10 warnings
            print(f"  • {warning}")
        if len(report['warnings']) > 10:
            print(f"  ... and {len(report['warnings']) - 10} more warnings")

    # Save detailed report
    with open('production_readiness_report.json', 'w') as f:
        json.dump(report, f, indent=2)

    print(f"\n📄 Detailed report saved to: production_readiness_report.json")
    print(f"📄 Execution log saved to: production_readiness.log")

    # Exit with appropriate code
    if report['critical_failures']:
        sys.exit(1)
    else:
        sys.exit(0)

if __name__ == "__main__":
    asyncio.run(main())
