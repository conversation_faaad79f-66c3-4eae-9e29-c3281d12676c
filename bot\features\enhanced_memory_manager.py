"""
Enhanced memory manager for VoicePal.

This module provides an improved implementation of the memory manager
with better conversation context handling and summarization.
"""

import logging
import re
import json
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta

from bot.providers.ai_provider_interface import AIProviderInterface

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class EnhancedMemoryManager:
    """Enhanced memory manager with improved conversation context handling."""

    def __init__(self, database, ai_provider: AIProviderInterface, config: Dict[str, Any] = None, user_manager = None):
        """
        Initialize the enhanced memory manager.

        Args:
            database: Database instance
            ai_provider: AI provider instance
            config: Configuration dictionary
            user_manager: Optional UserManager instance for user profile access
        """
        self.database = database
        self.ai_provider = ai_provider
        self.config = config or {}
        self.user_manager = user_manager

        # Default configuration
        self.conversation_memory_limit = self.config.get("conversation_memory", 20)  # Increased from 10 to 20
        self.summary_update_frequency = self.config.get("summary_update_frequency", 24)  # hours
        self.token_limit = self.config.get("token_limit", 3000)  # Increased from 2000 to 3000
        self.importance_threshold = self.config.get("importance_threshold", 0.5)  # Threshold for important messages

        # Cache configuration
        self.cache_ttl_minutes = self.config.get("cache_ttl_minutes", 30)  # Increased from 1 to 30 minutes
        self.name_cache_ttl_minutes = self.config.get("name_cache_ttl_minutes", 60)  # 1 hour for name cache

        # Initialize caches
        self.conversation_cache = {}
        self.name_cache = {}
        self.name_cache_timestamps = {}
        self.summary_cache = {}
        self.summary_cache_timestamps = {}

        # Initialize context preservation patterns
        self._initialize_context_preservation_patterns()

    def get_conversation_context(self, user_id: int) -> Dict[str, Any]:
        """
        Get enhanced context for a conversation with optimized caching.

        Args:
            user_id: User ID

        Returns:
            Dict containing user data, conversations, preferences, summary, and more
        """
        # Check if we have a cached context that's still valid
        if user_id in self.conversation_cache:
            cache_entry = self.conversation_cache[user_id]
            cache_time = cache_entry.get("timestamp", datetime.min)
            cache_age = (datetime.now() - cache_time).total_seconds() / 60

            # Use cache if it's still valid
            if cache_age <= self.cache_ttl_minutes:
                logger.debug("Using cached conversation context for user %d (age: %.1f minutes)",
                           user_id, cache_age)
                return cache_entry.get("context", {})

        try:
            # Get basic user data with caching
            user_data = self._get_cached_user_data(user_id)

            # Get conversation history with optimized retrieval
            conversations, total_conversations = self._get_optimized_conversation_history(user_id)
            logger.debug(f"Retrieved {len(conversations)} conversations out of {total_conversations} total")

            # Get user preferences with caching
            preferences = self._get_cached_user_preferences(user_id)

            # Get or generate conversation summary with caching
            summary = self.get_or_generate_summary(user_id, conversations)

            # Get user interests and topics with caching
            interests = self._get_cached_user_interests(user_id)

            # Get mood history with caching
            mood_history = self._get_cached_mood_history(user_id)

            # Calculate conversation metrics
            metrics = self._calculate_conversation_metrics(conversations)

            # Format conversations for AI context
            formatted_conversations = self._format_conversations_for_context(conversations)

            # Build the context
            context = {
                "user_data": user_data,
                "conversations": formatted_conversations,
                "total_conversations": total_conversations,
                "preferences": preferences,
                "summary": summary,
                "interests": interests,
                "mood_history": mood_history,
                "metrics": metrics
            }

            # Cache the context
            self.conversation_cache[user_id] = {
                "context": context,
                "timestamp": datetime.now()
            }

            return context
        except Exception as e:
            logger.error("Error getting conversation context for user %d: %s", user_id, e)

            # Only log full traceback for unexpected errors
            if "database" not in str(e).lower() and "connection" not in str(e).lower():
                import traceback
                logger.error(traceback.format_exc())

            # Try to return a partial context if possible
            try:
                # Get basic user data as a minimum
                user_data = self.database.get_user(user_id) or {}

                # Try to get cached conversations if available
                cached_context = self.conversation_cache.get(user_id, {}).get("context", {})
                conversations = cached_context.get("conversations", [])

                # Return a minimal context
                return {
                    "user_data": user_data,
                    "conversations": conversations,
                    "total_conversations": len(conversations),
                    "preferences": {},
                    "summary": None,
                    "interests": [],
                    "mood_history": [],
                    "metrics": {}
                }
            except Exception:
                # If all else fails, return an empty context
                return {
                    "user_data": {},
                    "conversations": [],
                    "total_conversations": 0,
                    "preferences": {},
                    "summary": None,
                    "interests": [],
                    "mood_history": [],
                    "metrics": {}
                }

    def _get_cached_user_data(self, user_id: int) -> Dict[str, Any]:
        """Get user data with caching."""
        try:
            # Use user_manager if available
            if hasattr(self, 'user_manager') and self.user_manager:
                user_data = self.user_manager.get_user(user_id)
                if user_data:
                    return user_data

            # Fallback to direct database access
            return self.database.get_user(user_id) or {}
        except Exception as e:
            logger.error("Error getting user data for user %d: %s", user_id, e)
            # Try to get from cache if available
            cached_context = self.conversation_cache.get(user_id, {}).get("context", {})
            return cached_context.get("user_data", {})

    def _get_cached_user_preferences(self, user_id: int) -> Dict[str, Any]:
        """Get user preferences with caching."""
        try:
            # Use user_manager if available
            if hasattr(self, 'user_manager') and self.user_manager:
                preferences = self.user_manager.get_user_preferences(user_id)
                if preferences:
                    return preferences

            # Fallback to direct database access
            return self.database.get_user_preferences(user_id) or {}
        except Exception as e:
            logger.error(f"Error getting user preferences for user {user_id}: {e}")
            # Try to get from cache if available
            cached_context = self.conversation_cache.get(user_id, {}).get("context", {})
            return cached_context.get("preferences", {})

    def _get_cached_user_interests(self, user_id: int) -> List[str]:
        """Get user interests with caching."""
        try:
            return self.get_user_interests(user_id)
        except Exception as e:
            logger.error(f"Error getting user interests for user {user_id}: {e}")
            # Try to get from cache if available
            cached_context = self.conversation_cache.get(user_id, {}).get("context", {})
            return cached_context.get("interests", [])

    def _get_cached_mood_history(self, user_id: int) -> List[Dict[str, Any]]:
        """Get mood history with caching."""
        try:
            return self.get_mood_history(user_id)
        except Exception as e:
            logger.error(f"Error getting mood history for user {user_id}: {e}")
            # Try to get from cache if available
            cached_context = self.conversation_cache.get(user_id, {}).get("context", {})
            return cached_context.get("mood_history", [])

    def _get_optimized_conversation_history(self, user_id: int) -> Tuple[List[Dict[str, Any]], int]:
        """
        Get optimized conversation history using a sliding window approach with improved prioritization.

        Args:
            user_id: User ID

        Returns:
            Tuple containing list of conversations and total count
        """
        try:
            # Check if we have cached conversations for this user
            if user_id in self.conversation_cache:
                cached_data = self.conversation_cache[user_id]
                # Check if cache is still valid (increased from 1 minute to configurable TTL)
                if datetime.now() - cached_data["timestamp"] < timedelta(minutes=self.cache_ttl_minutes):
                    logger.debug(f"Using cached conversation history for user {user_id}")
                    return cached_data["conversations"], cached_data["total"]

            # Get total conversation count
            total = self.database.get_conversation_count(user_id)
            logger.debug(f"Total conversations for user {user_id}: {total}")

            # Get recent conversations (increased limit for better context)
            recent_limit = min(self.conversation_memory_limit, 15)  # At least 15 recent messages if possible
            recent = self.database.get_conversations(
                user_id,
                limit=recent_limit
            )
            logger.debug(f"Retrieved {len(recent)} recent conversations for user {user_id}")

            # If we have more than the limit, try to get some older important conversations
            if total > recent_limit:
                # Calculate how many older conversations we can include
                older_limit = self.conversation_memory_limit - len(recent)

                if older_limit > 0:
                    # Get some older conversations that might be important
                    older = self._get_important_older_conversations(user_id, limit=older_limit)
                    logger.debug(f"Retrieved {len(older)} older important conversations for user {user_id}")

                    # Combine recent and older conversations, ensuring no duplicates
                    combined = recent.copy()
                    recent_ids = {conv["id"] for conv in recent if "id" in conv}
                    for conv in older:
                        if "id" in conv and conv["id"] not in recent_ids:
                            combined.append(conv)
                else:
                    combined = recent

                # Apply topic-based prioritization
                prioritized = self._prioritize_conversations(combined)

                # Limit to conversation_memory_limit
                conversations = prioritized[:self.conversation_memory_limit]
            else:
                conversations = recent

            # Ensure all conversations have required fields
            validated_conversations = []
            for conv in conversations:
                if isinstance(conv, dict):
                    # Ensure required fields exist
                    if "message" not in conv:
                        conv["message"] = ""
                    if "response" not in conv:
                        conv["response"] = ""
                    if "is_voice" not in conv:
                        conv["is_voice"] = False
                    if "created_at" not in conv:
                        conv["created_at"] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

                    validated_conversations.append(conv)

            # Cache the result
            self.conversation_cache[user_id] = {
                "conversations": validated_conversations,
                "total": total,
                "timestamp": datetime.now()
            }

            logger.debug(f"Cached {len(validated_conversations)} conversations for user {user_id}")
            return validated_conversations, total
        except Exception as e:
            logger.error(f"Error getting optimized conversation history for user {user_id}: {e}")

            # Try to return cached data even if expired
            if user_id in self.conversation_cache:
                cached_data = self.conversation_cache[user_id]
                return cached_data.get("conversations", []), cached_data.get("total", 0)

            return [], 0

    def _prioritize_conversations(self, conversations: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Prioritize conversations based on relevance, importance, and recency.

        Args:
            conversations: List of conversation entries

        Returns:
            Prioritized list of conversations
        """
        if not conversations:
            return []

        # First, ensure we have the most recent conversations (last 5)
        try:
            # Sort by timestamp first
            sorted_convs = sorted(
                conversations,
                key=lambda x: x.get("created_at", ""),
                reverse=True  # Newest first
            )
        except Exception:
            # If sorting fails, just use the original order
            sorted_convs = conversations

        recent_count = min(5, len(sorted_convs))
        most_recent = sorted_convs[:recent_count]
        remaining = sorted_convs[recent_count:]

        # Score the remaining conversations by importance and relevance
        scored_conversations = []
        for conv in remaining:
            # Base score starts with importance if available
            score = conv.get("importance", 0.5)

            # Adjust score based on additional factors

            # 1. Boost conversations with user preferences or interests
            if "message" in conv:
                text = conv.get("message", "").lower()
                # Check for preference-related content
                if "prefer" in text or "like" in text or "favorite" in text or "love" in text:
                    score += 0.2

                # Check for personal information
                if "name" in text or "age" in text or "live" in text or "from" in text:
                    score += 0.2

                # Check for emotional content
                if "feel" in text or "happy" in text or "sad" in text or "angry" in text:
                    score += 0.1

            # 2. Boost conversations with questions and answers
            if conv.get("message", "").endswith("?") or "?" in conv.get("response", ""):
                score += 0.15

            # 3. Penalize very short or generic exchanges
            if len(conv.get("message", "")) < 10 and len(conv.get("response", "")) < 20:
                score -= 0.1

            # Add to scored list
            scored_conversations.append((conv, score))

        # Sort by score (highest first)
        scored_conversations.sort(key=lambda x: x[1], reverse=True)

        # Combine most recent with highest scored
        result = most_recent + [conv for conv, _ in scored_conversations]

        return result

    def _get_important_older_conversations(self, user_id: int, limit: int = 5) -> List[Dict[str, Any]]:
        """
        Get important older conversations with improved selection criteria.

        Args:
            user_id: User ID
            limit: Maximum number of older conversations to retrieve

        Returns:
            List of important older conversations
        """
        try:
            # Try to get conversations marked as important first
            important_conversations = self.database.get_important_conversations(
                user_id,
                limit=limit
            ) if hasattr(self.database, 'get_important_conversations') else []

            # If we got enough important conversations, return them
            if len(important_conversations) >= limit:
                return important_conversations[:limit]

            # Otherwise, get additional older conversations to fill the gap
            remaining_limit = limit - len(important_conversations)

            # Get older conversations (skip the most recent ones)
            older_conversations = self.database.get_older_conversations(
                user_id,
                skip=self.conversation_memory_limit,
                limit=remaining_limit
            )

            # Combine important and older conversations
            combined = important_conversations + older_conversations

            # Remove duplicates if any
            unique_conversations = []
            seen_ids = set()

            for conv in combined:
                conv_id = conv.get("id")
                if conv_id and conv_id not in seen_ids:
                    unique_conversations.append(conv)
                    seen_ids.add(conv_id)

            return unique_conversations[:limit]
        except Exception as e:
            logger.error(f"Error getting important older conversations for user {user_id}: {e}")
            return []

    def _calculate_conversation_metrics(self, conversations: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Calculate metrics from conversation history.

        Args:
            conversations: List of conversation exchanges

        Returns:
            Dict containing metrics
        """
        if not conversations:
            return {
                "avg_message_length": 0,
                "avg_response_length": 0,
                "voice_percentage": 0,
                "conversation_frequency": "low"
            }

        try:
            # Calculate average message length
            total_message_length = sum(len(conv["message"]) for conv in conversations if conv.get("message"))
            avg_message_length = total_message_length / len(conversations) if conversations else 0

            # Calculate average response length
            total_response_length = sum(len(conv["response"]) for conv in conversations if conv.get("response"))
            avg_response_length = total_response_length / len(conversations) if conversations else 0

            # Calculate voice percentage
            voice_count = sum(1 for conv in conversations if conv.get("is_voice"))
            voice_percentage = (voice_count / len(conversations)) * 100 if conversations else 0

            # Determine conversation frequency
            # This is a placeholder - in a real implementation, we would analyze timestamps
            if len(conversations) < 5:
                frequency = "low"
            elif len(conversations) < 20:
                frequency = "medium"
            else:
                frequency = "high"

            return {
                "avg_message_length": avg_message_length,
                "avg_response_length": avg_response_length,
                "voice_percentage": voice_percentage,
                "conversation_frequency": frequency
            }
        except Exception as e:
            logger.error(f"Error calculating conversation metrics: {e}")
            return {
                "avg_message_length": 0,
                "avg_response_length": 0,
                "voice_percentage": 0,
                "conversation_frequency": "low"
            }

    def _format_conversations_for_context(self, conversations: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Format conversations for AI context.

        Args:
            conversations: List of conversation exchanges

        Returns:
            List of formatted conversation exchanges
        """
        formatted = []

        if not conversations:
            logger.warning("No conversations to format for context")
            return formatted

        try:
            # Sort conversations by timestamp if possible
            try:
                # First, ensure all conversations have a timestamp
                for conv in conversations:
                    if not conv.get("created_at"):
                        conv["created_at"] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

                # Sort by timestamp (oldest first)
                sorted_conversations = sorted(
                    conversations,
                    key=lambda x: x.get("created_at", ""),
                    reverse=False  # Oldest first for chronological order
                )

                conversations = sorted_conversations
            except Exception as sort_error:
                logger.error(f"Error sorting conversations for formatting: {sort_error}")
                # Continue with unsorted conversations

            # Format each conversation
            for conv in conversations:
                if not isinstance(conv, dict):
                    logger.warning(f"Skipping non-dict conversation: {type(conv)}")
                    continue

                # Get basic fields with defaults
                message = conv.get("message", "")
                response = conv.get("response", "")
                is_voice = conv.get("is_voice", False)
                timestamp = conv.get("created_at", "")

                # Skip empty conversations
                if not message and not response:
                    continue

                # Format timestamp if needed
                if isinstance(timestamp, datetime):
                    timestamp = timestamp.strftime('%Y-%m-%d %H:%M:%S')

                # Add formatted conversation with consistent field names
                formatted.append({
                    "message": message,
                    "response": response,
                    "is_voice": is_voice,
                    "timestamp": timestamp
                })

            # Limit the number of conversations to avoid token limits
            if len(formatted) > self.conversation_memory_limit:
                # Keep the most recent conversations
                formatted = formatted[-self.conversation_memory_limit:]

            logger.info(f"Formatted {len(formatted)} conversations for context")
            return formatted

        except Exception as e:
            logger.error(f"Error formatting conversations for context: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return []

    def get_or_generate_summary(self, user_id: int, conversations: List[Dict[str, Any]]) -> Optional[str]:
        """
        Get or generate a summary of the user's conversations with improved caching.

        Args:
            user_id: User ID
            conversations: List of conversation exchanges

        Returns:
            str: Summary of the conversation or None if not available
        """
        # Check if we have a cached summary that's still valid
        if user_id in self.summary_cache:
            cache_time = self.summary_cache_timestamps.get(user_id, datetime.min)
            cache_age = (datetime.now() - cache_time).total_seconds() / 60

            # Use cache if it's still valid (less than 1 hour old)
            if cache_age < 60:  # 1 hour cache for summaries
                logger.debug(f"Using cached summary for user {user_id}")
                return self.summary_cache.get(user_id)

        try:
            # Try to get existing summary from database
            summary = None
            try:
                summary = self.database.get_user_summary(user_id)
            except Exception as db_error:
                logger.error(f"Error getting user summary from database: {db_error}")
                # Continue with summary generation even if database retrieval fails

            # Check if summary needs to be updated
            if self._should_update_summary(user_id, summary):
                # If AI provider supports summarization, use it
                if self.ai_provider.supports_feature("summarization") and conversations:
                    try:
                        new_summary = self.ai_provider.summarize_conversation(conversations)

                        # Try to update database
                        try:
                            self.database.update_user_summary(user_id, new_summary)
                            logger.info(f"Updated summary for user {user_id}")
                        except Exception as update_error:
                            logger.error(f"Error updating user summary in database: {update_error}")
                            # Continue with the new summary even if database update fails

                        # Update cache
                        self.summary_cache[user_id] = new_summary
                        self.summary_cache_timestamps[user_id] = datetime.now()

                        return new_summary
                    except Exception as ai_error:
                        logger.error(f"Error generating summary with AI provider: {ai_error}")
                        # Fall back to existing summary

            # If we have a summary, cache it
            if summary:
                self.summary_cache[user_id] = summary
                self.summary_cache_timestamps[user_id] = datetime.now()

            return summary
        except Exception as e:
            logger.error(f"Error getting or generating summary for user {user_id}: {e}")
            return None

    async def update_user_summary(self, user_id: int) -> bool:
        """
        Update user summary asynchronously.

        Args:
            user_id: User ID

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Get conversation history
            conversations, _ = self._get_optimized_conversation_history(user_id)

            # If no conversations, return False
            if not conversations:
                logger.warning(f"No conversations available for user {user_id}, cannot update summary")
                return False

            # Generate summary using AI provider
            if self.ai_provider.supports_feature("summarization"):
                new_summary = self.ai_provider.summarize_conversation(conversations)

                # Update database
                success = self.database.update_user_summary(user_id, new_summary)

                if success:
                    logger.info(f"Updated summary for user {user_id}")

                    # Clear cache to ensure we get the new summary next time
                    if hasattr(self, 'summary_cache') and user_id in self.summary_cache:
                        del self.summary_cache[user_id]

                return success
            else:
                logger.warning("AI provider does not support summarization")
                return False
        except Exception as e:
            logger.error(f"Error updating summary for user {user_id}: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False

    async def score_conversation_importance(self, user_id: int, conversation_id: int, message: str, response: str) -> bool:
        """
        Score conversation importance.

        Args:
            user_id: User ID
            conversation_id: Conversation ID
            message: User message
            response: Bot response

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # If AI provider supports importance scoring, use it
            if self.ai_provider.supports_feature("importance_scoring"):
                importance_score = self.ai_provider.score_importance(message, response)

                # Update database
                if importance_score > self.importance_threshold:
                    success = self.database.set_conversation_importance(conversation_id, importance_score)
                    if success:
                        logger.info(f"Marked conversation {conversation_id} as important with score {importance_score}")
                    return success

            # Basic importance scoring based on message length and complexity
            message_length = len(message)
            response_length = len(response)

            # Simple heuristic: longer messages and responses are more likely to be important
            if message_length > 100 and response_length > 200:
                importance_score = 0.8
                success = self.database.set_conversation_importance(conversation_id, importance_score)
                if success:
                    logger.info(f"Marked conversation {conversation_id} as important with basic score {importance_score}")
                return success

            return True
        except Exception as e:
            logger.error(f"Error scoring conversation importance for user {user_id}: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False

    def _should_update_summary(self, user_id: int, current_summary: Optional[str]) -> bool:
        """
        Check if summary needs to be updated.

        Args:
            user_id: User ID
            current_summary: Current summary

        Returns:
            bool: True if summary needs to be updated, False otherwise
        """
        try:
            # If no summary exists, generate one
            if not current_summary:
                return True

            # Get last summary update time
            last_update = self.database.get_user_summary_update_time(user_id)

            # If no last update time, update summary
            if not last_update:
                return True

            # Check if enough time has passed since last update
            time_since_update = datetime.now() - last_update
            hours_since_update = time_since_update.total_seconds() / 3600

            # Check if we have new conversations since last update
            conversation_count = self.database.get_conversation_count_since(user_id, last_update)

            # Update if enough time has passed or we have enough new conversations
            return (hours_since_update >= self.summary_update_frequency or
                    conversation_count >= self.conversation_memory_limit // 2)
        except Exception as e:
            logger.error(f"Error checking if summary needs update for user {user_id}: {e}")
            return False

    def get_user_interests(self, user_id: int) -> List[str]:
        """
        Get user interests.

        Args:
            user_id: User ID

        Returns:
            List of interests
        """
        try:
            # Get user interests from database
            interests = self.database.get_user_interests(user_id)
            return interests or []
        except Exception as e:
            logger.error(f"Error getting user interests for user {user_id}: {e}")
            return []

    def get_mood_history(self, user_id: int, days: int = 7) -> List[Dict[str, Any]]:
        """
        Get mood history.

        Args:
            user_id: User ID
            days: Number of days to look back

        Returns:
            List of mood entries
        """
        try:
            # Get mood history from database
            mood_history = self.database.get_mood_history(user_id, days)
            return mood_history or []
        except Exception as e:
            logger.error(f"Error getting mood history for user {user_id}: {e}")
            return []

    def clear_conversation_cache(self, user_id: Optional[int] = None) -> None:
        """
        Clear conversation cache for a user or all users.

        Args:
            user_id: User ID or None to clear all
        """
        if user_id is None:
            self.conversation_cache = {}
            logger.info("Cleared conversation cache for all users")
        elif user_id in self.conversation_cache:
            del self.conversation_cache[user_id]
            logger.info(f"Cleared conversation cache for user {user_id}")

    def _initialize_context_preservation_patterns(self):
        """Initialize patterns for context preservation detection."""
        # Common short responses that should preserve context
        self.context_preserving_messages = [
            "lol", "haha", "yes", "no", "ok", "okay", "sure", "thanks",
            "thank you", "cool", "nice", "great", "awesome", "wow",
            "hmm", "huh", "what", "why", "how", "when", "where", "who",
            "?", "!", "...", "hi", "hello", "hey", "maybe", "perhaps",
            "good", "bad", "fine", "alright", "right", "wrong", "true", "false",
            "really", "interesting", "tell me more", "go on", "continue", "and", "then",
            "so", "but", "oh", "ah", "uh", "um", "well", "like", "actually", "basically",
            "exactly", "precisely", "definitely", "absolutely", "certainly", "indeed"
        ]

        # Phrases that indicate a continuation of conversation
        self.continuation_phrases = [
            "and then", "what about", "tell me more", "go on", "continue",
            "what else", "and", "but", "so", "also", "plus", "moreover",
            "furthermore", "additionally", "besides", "in addition"
        ]

        # Question words that almost always require context
        self.question_words = [
            "what", "why", "how", "when", "where", "who", "which", "whose",
            "whom", "is", "are", "was", "were", "will", "would", "can", "could",
            "should", "do", "does", "did", "have", "has", "had"
        ]

        # Compile regex patterns for more efficient matching
        self.emoji_pattern = re.compile(
            "[\U0001F600-\U0001F64F\U0001F300-\U0001F5FF\U0001F680-\U0001F6FF"
            "\U0001F700-\U0001F77F\U0001F780-\U0001F7FF\U0001F800-\U0001F8FF"
            "\U0001F900-\U0001F9FF\U0001FA00-\U0001FA6F\U0001FA70-\U0001FAFF"
            "\U00002702-\U000027B0\U000024C2-\U0001F251]+"
        )

        # Pattern for detecting name references (e.g., "call me John")
        self.name_reference_pattern = re.compile(
            r"(?:call\s+me|I'm|I\s+am|name(?:'s)?(?:\s+is)?)\s+([A-Z][a-z]+)"
        )

        # Create a set for faster lookups
        self.context_preserving_set = set(self.context_preserving_messages)

        # Word count threshold for short messages
        self.short_message_threshold = self.config.get("short_message_threshold", 10)

        # User-specific learned context preservation patterns
        self.user_context_patterns = {}

    def should_preserve_context_for_short_message(self, message: str, user_id: Optional[int] = None) -> bool:
        """
        Determine if a message should preserve conversation context using improved detection.

        Args:
            message: User message
            user_id: Optional user ID for personalized context preservation

        Returns:
            bool: True if context should be preserved, False otherwise
        """
        if not message:
            return True  # Empty messages should preserve context

        # Always preserve context for short messages to maintain conversation flow
        if len(message.split()) <= self.short_message_threshold:
            return True

        # Clean and normalize the message
        message_clean = message.strip()
        message_lower = message_clean.lower()
        words = message_lower.split()

        # Check if message is short
        is_short = len(words) < self.short_message_threshold

        # If message is not short, check if it contains continuation phrases
        if not is_short:
            for phrase in self.continuation_phrases:
                if phrase in message_lower:
                    return True

            # Check if it starts with a question word
            for word in self.question_words:
                if message_lower.startswith(word + " ") or message_lower == word:
                    return True

            # Not short and no continuation indicators
            return False

        # For short messages, perform more detailed checks

        # 1. Exact match with known context-preserving messages
        if message_lower in self.context_preserving_set:
            return True

        # 2. Check for question marks
        if "?" in message_clean:
            return True

        # 3. Check for emoji content
        emojis = self.emoji_pattern.findall(message_clean)
        is_mostly_emoji = len(emojis) > 0 and len(''.join(emojis)) / len(message_clean) > 0.5
        if is_mostly_emoji:
            return True

        # 4. Check for user-specific learned patterns
        if user_id and user_id in self.user_context_patterns:
            for pattern in self.user_context_patterns[user_id]:
                if pattern in message_lower:
                    return True

        # 5. Check for single-word responses (very likely to need context)
        if len(words) == 1 and len(message_clean) < 15:
            return True

        # 6. Check for starting with question words
        for question_word in self.question_words:
            if message_lower.startswith(question_word + " ") or message_lower == question_word:
                return True

        # If none of the above conditions are met, don't preserve context
        return False

    def learn_context_pattern(self, user_id: int, message: str) -> None:
        """
        Learn a new context preservation pattern from user interaction.

        Args:
            user_id: User ID
            message: Message to learn from
        """
        if not user_id or not message:
            return

        # Initialize user patterns if not exists
        if user_id not in self.user_context_patterns:
            self.user_context_patterns[user_id] = set()

        # Add the message to user-specific patterns if it's short
        message_lower = message.lower().strip()
        words = message_lower.split()

        if len(words) <= 3 and len(message_lower) < 20:
            self.user_context_patterns[user_id].add(message_lower)

        # Try to persist learned patterns to database if possible
        try:
            patterns_json = json.dumps(list(self.user_context_patterns[user_id]))
            self.database.update_user_preference(user_id, "context_patterns", patterns_json)
        except Exception as e:
            logger.warning(f"Failed to persist learned context pattern for user {user_id}: {e}")

    def get_user_name_from_context(self, user_id: int) -> str:
        """
        Get the user's name from context with caching for better performance.

        Args:
            user_id: User ID

        Returns:
            str: User's name or empty string if not found
        """
        # Check cache first
        if user_id in self.name_cache:
            # Check if cache is still valid
            cache_age = (datetime.now() - self.name_cache_timestamps.get(user_id, datetime.min)).total_seconds() / 60
            if cache_age <= self.name_cache_ttl_minutes:
                return self.name_cache[user_id]

        try:
            # Not in cache or cache expired, fetch from database
            name = self._fetch_user_name_from_database(user_id)

            # Cache the result
            if name:
                self.name_cache[user_id] = name
                self.name_cache_timestamps[user_id] = datetime.now()

            return name
        except Exception as e:
            logger.error(f"Error getting user name from context for user {user_id}: {e}")
            # Try to return cached name even if expired in case of database error
            return self.name_cache.get(user_id, "")

    def _fetch_user_name_from_database(self, user_id: int) -> str:
        """
        Fetch user name from database with proper error handling.

        Args:
            user_id: User ID

        Returns:
            str: User's name or empty string if not found
        """
        try:
            # Use user_manager if available
            if hasattr(self, 'user_manager') and self.user_manager:
                # Try to get user data from user_manager
                user_data = self.user_manager.get_user(user_id)

                if user_data:
                    # Check for preferred name in user preferences first
                    try:
                        preferences = self.user_manager.get_user_preferences(user_id)
                        preferred_name = preferences.get("preferred_name")
                        if preferred_name:
                            return preferred_name
                    except Exception as e:
                        # Log but continue with other methods
                        logger.debug(f"Error getting preferred name from preferences for user {user_id}: {e}")

                    # Check for preferred name in user data
                    preferred_name = user_data.get("preferred_name")
                    if preferred_name:
                        return preferred_name

                    # Then check for first name
                    first_name = user_data.get("first_name")
                    if first_name:
                        return first_name

                    # Then check for username
                    username = user_data.get("username")
                    if username:
                        return username

                    # Finally check for last name
                    last_name = user_data.get("last_name")
                    if last_name:
                        return last_name

            # Fallback to direct database access
            # Try to get user data
            user_data = self.database.get_user(user_id)

            if not user_data:
                return ""

            # Check for preferred name in user preferences first
            try:
                preferred_name = self.database.get_user_preference(user_id, "preferred_name")
                if preferred_name:
                    return preferred_name
            except Exception as e:
                # Log but continue with other methods
                logger.debug(f"Error getting preferred name from preferences for user {user_id}: {e}")

            # Check for preferred name in user data
            preferred_name = user_data.get("preferred_name")
            if preferred_name:
                return preferred_name

            # Then check for first name
            first_name = user_data.get("first_name")
            if first_name:
                return first_name

            # Then check for username
            username = user_data.get("username")
            if username:
                return username

            # Finally check for last name
            last_name = user_data.get("last_name")
            if last_name:
                return last_name

            return ""
        except Exception as e:
            logger.error(f"Database error fetching user name for user {user_id}: {e}")
            return ""

    def update_user_name_from_message(self, user_id: int, message: str) -> bool:
        """
        Update user's preferred name based on message content.

        Args:
            user_id: User ID
            message: Message that might contain name information

        Returns:
            bool: True if name was updated, False otherwise
        """
        if not user_id or not message:
            return False

        # Check for name reference patterns
        match = self.name_reference_pattern.search(message)
        if match:
            name = match.group(1)

            # Validate name (basic check)
            if len(name) >= 2 and len(name) <= 30 and name[0].isupper():
                try:
                    # Use user_manager if available
                    if hasattr(self, 'user_manager') and self.user_manager:
                        # Update preference using user_manager
                        success = self.user_manager.update_user_preference(user_id, "preferred_name", name)
                        if success:
                            # Update cache
                            self.name_cache[user_id] = name
                            self.name_cache_timestamps[user_id] = datetime.now()

                            logger.info(f"Updated preferred name for user {user_id} to '{name}' using user_manager")
                            return True

                    # Fallback to direct database access
                    # Update in database
                    self.database.update_user_preference(user_id, "preferred_name", name)

                    # Update in user data if possible
                    try:
                        self.database.update_user(user_id, {"preferred_name": name})
                    except Exception as e:
                        logger.debug(f"Could not update user data with preferred name: {e}")

                    # Update cache
                    self.name_cache[user_id] = name
                    self.name_cache_timestamps[user_id] = datetime.now()

                    logger.info(f"Updated preferred name for user {user_id} to '{name}'")
                    return True
                except Exception as e:
                    logger.error(f"Error updating preferred name for user {user_id}: {e}")

        return False
