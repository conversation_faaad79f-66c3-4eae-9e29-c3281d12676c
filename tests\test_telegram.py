"""
Simple test script to check if the Telegram bot is working.
"""

import os
import logging
from dotenv import load_dotenv
from telegram import Bot

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

async def main():
    """Test the Telegram bot."""
    # Load environment variables
    load_dotenv()
    
    # Get the Telegram token
    token = os.getenv("TELEGRAM_TOKEN")
    if not token:
        logger.error("TELEGRAM_TOKEN not found in environment variables")
        return
    
    logger.info(f"Testing Telegram bot with token: {token}")
    
    # Create a bot instance
    bot = Bot(token=token)
    
    # Get bot information
    bot_info = await bot.get_me()
    logger.info(f"Bot info: {bot_info.to_dict()}")
    
    logger.info("Bot is working correctly!")

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
