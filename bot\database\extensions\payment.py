"""
Payment database extension for VoicePal.

This module extends the database with payment-related functionality.
"""

import logging
import json
from datetime import datetime
from typing import Dict, Any, Optional, List

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

def extend_database_for_payment(database) -> None:
    """
    Extend database with payment methods.

    Args:
        database: Database instance
    """
    try:
        conn = database.conn
        cursor = database.cursor

        # Create invoices table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS invoices (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            package_id TEXT,
            payload TEXT UNIQUE,
            amount INTEGER,
            currency TEXT,
            status TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIG<PERSON> KEY (user_id) REFERENCES users (user_id) ON DELETE CASCADE
        )
        ''')

        # Create transactions table if it doesn't exist
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS transactions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            amount INTEGER,
            credits INTEGER,
            transaction_id TEXT UNIQUE,
            status TEXT,
            source TEXT DEFAULT 'purchase',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (user_id) ON DELETE CASCADE
        )
        ''')

        conn.commit()
        logger.info("Database schema extended for payment system")
    except Exception as e:
        logger.error(f"Error extending database schema for payment: {e}")
        if conn:
            conn.rollback()
        raise

    # Add methods to Database class
    def add_invoice(self, user_id: int, package_id: str, payload: str,
                   amount: int, currency: str, status: str = "pending") -> int:
        """
        Add an invoice.

        Args:
            user_id: User ID
            package_id: Package ID
            payload: Invoice payload
            amount: Amount in cents
            currency: Currency code
            status: Invoice status

        Returns:
            int: Invoice ID
        """
        try:
            # First, check if the user exists
            self.cursor.execute("SELECT user_id FROM users WHERE user_id = ?", (user_id,))
            user = self.cursor.fetchone()

            # If user doesn't exist, create a basic user record
            if not user:
                logger.warning(f"User {user_id} not found in database. Creating basic user record before adding invoice.")
                self.cursor.execute(
                    "INSERT INTO users (user_id, credits) VALUES (?, ?)",
                    (user_id, 0)  # Start with 0 credits
                )
                logger.info(f"Created basic user record for user {user_id}")

            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            self.cursor.execute(
                """INSERT INTO invoices
                   (user_id, package_id, payload, amount, currency, status, created_at, updated_at)
                   VALUES (?, ?, ?, ?, ?, ?, ?, ?)""",
                (user_id, package_id, payload, amount, currency, status, current_time, current_time)
            )
            self.conn.commit()
            invoice_id = self.cursor.lastrowid
            logger.info(f"Added invoice {invoice_id} for user {user_id}")
            return invoice_id
        except Exception as e:
            logger.error(f"Error adding invoice for user {user_id}: {e}")
            self.conn.rollback()
            raise

    def get_invoice(self, payload: str) -> Optional[Dict[str, Any]]:
        """
        Get invoice by payload.

        Args:
            payload: Invoice payload

        Returns:
            Dict containing invoice information or None if not found
        """
        try:
            self.cursor.execute(
                """SELECT id, user_id, package_id, payload, amount, currency, status, created_at, updated_at
                   FROM invoices
                   WHERE payload = ?""",
                (payload,)
            )
            invoice = self.cursor.fetchone()

            if invoice:
                return dict(invoice)
            return None
        except Exception as e:
            logger.error(f"Error getting invoice with payload {payload}: {e}")
            return None

    def update_invoice_status(self, payload: str, status: str) -> bool:
        """
        Update invoice status.

        Args:
            payload: Invoice payload
            status: New status

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            self.cursor.execute(
                """UPDATE invoices
                   SET status = ?, updated_at = ?
                   WHERE payload = ?""",
                (status, current_time, payload)
            )
            self.conn.commit()
            logger.info(f"Updated invoice status for payload {payload} to {status}")
            return True
        except Exception as e:
            logger.error(f"Error updating invoice status for payload {payload}: {e}")
            self.conn.rollback()
            return False

    def get_user_invoices(self, user_id: int, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Get user's invoices.

        Args:
            user_id: User ID
            limit: Maximum number of invoices to return

        Returns:
            List of invoice dictionaries
        """
        try:
            self.cursor.execute(
                """SELECT id, user_id, package_id, payload, amount, currency, status, created_at, updated_at
                   FROM invoices
                   WHERE user_id = ?
                   ORDER BY created_at DESC
                   LIMIT ?""",
                (user_id, limit)
            )
            invoices = self.cursor.fetchall()
            return [dict(i) for i in invoices]
        except Exception as e:
            logger.error(f"Error getting invoices for user {user_id}: {e}")
            return []

    def add_transaction(self, user_id: int, amount: int, credits: int,
                       transaction_id: str, status: str = "completed", source: str = "purchase") -> int:
        """
        Add a transaction.

        Args:
            user_id: User ID
            amount: Amount in cents
            credits: Number of credits
            transaction_id: Transaction ID
            status: Transaction status
            source: Source of the transaction (e.g., 'purchase', 'free', 'bonus')

        Returns:
            int: Transaction ID
        """
        try:
            # First, check if the user exists
            self.cursor.execute("SELECT user_id FROM users WHERE user_id = ?", (user_id,))
            user = self.cursor.fetchone()

            # If user doesn't exist, create a basic user record
            if not user:
                logger.warning(f"User {user_id} not found in database. Creating basic user record before adding transaction.")
                self.cursor.execute(
                    "INSERT INTO users (user_id, credits) VALUES (?, ?)",
                    (user_id, 0)  # Start with 0 credits
                )
                logger.info(f"Created basic user record for user {user_id}")

            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            # Use a transaction to ensure atomicity
            self.conn.execute("BEGIN TRANSACTION")

            try:
                self.cursor.execute(
                    """INSERT INTO transactions
                       (user_id, amount, credits, transaction_id, status, source, created_at)
                       VALUES (?, ?, ?, ?, ?, ?, ?)""",
                    (user_id, amount, credits, transaction_id, status, source, current_time)
                )

                # Get the new transaction ID
                new_transaction_id = self.cursor.lastrowid

                # Commit the transaction
                self.conn.commit()
                logger.info(f"Added transaction {new_transaction_id} for user {user_id}")
                return new_transaction_id
            except Exception as inner_e:
                # Rollback in case of error
                self.conn.rollback()
                logger.error(f"Error in transaction for user {user_id}: {inner_e}")
                raise inner_e

        except Exception as e:
            logger.error(f"Error adding transaction for user {user_id}: {e}")
            # Ensure rollback happens
            try:
                self.conn.rollback()
            except:
                pass
            raise

    def get_transaction(self, transaction_id: str) -> Optional[Dict[str, Any]]:
        """
        Get transaction by ID.

        Args:
            transaction_id: Transaction ID

        Returns:
            Dict containing transaction information or None if not found
        """
        try:
            self.cursor.execute(
                """SELECT id, user_id, amount, credits, transaction_id, status, source, created_at
                   FROM transactions
                   WHERE transaction_id = ?""",
                (transaction_id,)
            )
            transaction = self.cursor.fetchone()

            if transaction:
                return dict(transaction)
            return None
        except Exception as e:
            logger.error(f"Error getting transaction {transaction_id}: {e}")
            return None

    def update_transaction_status(self, transaction_id: str, status: str) -> bool:
        """
        Update transaction status.

        Args:
            transaction_id: Transaction ID
            status: New status

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            self.cursor.execute(
                """UPDATE transactions
                   SET status = ?
                   WHERE transaction_id = ?""",
                (status, transaction_id)
            )
            self.conn.commit()
            logger.info(f"Updated transaction status for {transaction_id} to {status}")
            return True
        except Exception as e:
            logger.error(f"Error updating transaction status for {transaction_id}: {e}")
            self.conn.rollback()
            return False

    def get_user_transactions(self, user_id: int, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Get user's transactions.

        Args:
            user_id: User ID
            limit: Maximum number of transactions to return

        Returns:
            List of transaction dictionaries
        """
        try:
            self.cursor.execute(
                """SELECT id, user_id, amount, credits, transaction_id, status, source, created_at
                   FROM transactions
                   WHERE user_id = ?
                   ORDER BY created_at DESC
                   LIMIT ?""",
                (user_id, limit)
            )
            transactions = self.cursor.fetchall()
            return [dict(t) for t in transactions]
        except Exception as e:
            logger.error(f"Error getting transactions for user {user_id}: {e}")
            return []

    # Replace or add methods to Database class
    setattr(database.__class__, 'add_invoice', add_invoice)
    setattr(database.__class__, 'get_invoice', get_invoice)
    setattr(database.__class__, 'update_invoice_status', update_invoice_status)
    setattr(database.__class__, 'get_user_invoices', get_user_invoices)
    setattr(database.__class__, 'add_transaction', add_transaction)
    setattr(database.__class__, 'get_transaction', get_transaction)
    setattr(database.__class__, 'update_transaction_status', update_transaction_status)
    setattr(database.__class__, 'get_user_transactions', get_user_transactions)

    logger.info("Database extended with methods for payment system")
