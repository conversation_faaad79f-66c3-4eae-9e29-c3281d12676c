"""
Test script for enhanced memory system.

This script tests the enhanced memory system functionality.
"""

import unittest
import asyncio
import logging
import os
import sys
from unittest.mock import MagicMock, patch
from datetime import datetime, timedelta

# Add parent directory to path to import bot modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from bot.features.enhanced_memory_manager import EnhancedMemoryManager
from bot.core.enhanced_dialog_engine import EnhancedDialogEngine
from bot.features.memory_system import initialize_memory_system, get_memory_config

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class TestEnhancedMemory(unittest.TestCase):
    """Test case for enhanced memory system."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create mock database
        self.mock_db = MagicMock()
        self.mock_db.get_user.return_value = {"user_id": 123, "first_name": "Test", "last_name": "User"}
        self.mock_db.get_conversations.return_value = [
            {"id": 1, "user_id": 123, "message": "Hello", "response": "Hi there!", "is_voice": False, "created_at": "2023-01-01 12:00:00"},
            {"id": 2, "user_id": 123, "message": "How are you?", "response": "I'm doing well, thanks!", "is_voice": False, "created_at": "2023-01-01 12:01:00"}
        ]
        self.mock_db.get_conversation_count.return_value = 2
        self.mock_db.get_user_preferences.return_value = {"personality": "friendly"}
        self.mock_db.get_user_summary.return_value = "User is interested in casual conversation."
        self.mock_db.get_user_interests.return_value = ["conversation", "greetings"]
        self.mock_db.get_mood_history.return_value = [
            {"id": 1, "user_id": 123, "sentiment": "positive", "confidence": 0.8, "source": "text", "created_at": "2023-01-01 12:00:00"}
        ]
        
        # Create mock AI provider
        self.mock_ai_provider = MagicMock()
        self.mock_ai_provider.supports_feature.return_value = True
        self.mock_ai_provider.summarize_conversation.return_value = "User is friendly and asks about well-being."
        self.mock_ai_provider.analyze_sentiment.return_value = {"sentiment": "positive", "confidence": 0.8}
        
        async def mock_generate_response(*args, **kwargs):
            return {"text": "This is a test response", "language": "en"}
        
        self.mock_ai_provider.generate_response = mock_generate_response
        
        # Create mock user manager
        self.mock_user_manager = MagicMock()
        self.mock_user_manager.database = self.mock_db
        self.mock_user_manager.get_user_profile.return_value = {
            "user_data": {"user_id": 123, "first_name": "Test", "last_name": "User"},
            "preferences": {"personality": "friendly"},
            "conversation_history": [
                {"message": "Hello", "response": "Hi there!"},
                {"message": "How are you?", "response": "I'm doing well, thanks!"}
            ]
        }
        
        # Create config
        self.config = {
            "conversation_memory": 10,
            "summary_update_frequency": 24,
            "token_limit": 2000,
            "importance_threshold": 0.5
        }
        
        # Create memory manager
        self.memory_manager = EnhancedMemoryManager(
            database=self.mock_db,
            ai_provider=self.mock_ai_provider,
            config=self.config
        )
        
        # Create dialog engine
        self.dialog_engine = EnhancedDialogEngine(
            ai_provider=self.mock_ai_provider,
            memory_manager=self.memory_manager,
            user_manager=self.mock_user_manager
        )
    
    def test_get_conversation_context(self):
        """Test get_conversation_context method."""
        context = self.memory_manager.get_conversation_context(123)
        
        # Check that context contains expected keys
        self.assertIn("user_data", context)
        self.assertIn("conversations", context)
        self.assertIn("preferences", context)
        self.assertIn("summary", context)
        self.assertIn("interests", context)
        self.assertIn("mood_history", context)
        self.assertIn("metrics", context)
        
        # Check that user data is correct
        self.assertEqual(context["user_data"]["user_id"], 123)
        
        # Check that conversations are formatted correctly
        self.assertEqual(len(context["conversations"]), 2)
        self.assertIn("message", context["conversations"][0])
        self.assertIn("response", context["conversations"][0])
        
        # Check that metrics are calculated
        self.assertIn("avg_message_length", context["metrics"])
        self.assertIn("avg_response_length", context["metrics"])
        self.assertIn("voice_percentage", context["metrics"])
    
    def test_should_update_summary(self):
        """Test _should_update_summary method."""
        # Test with no current summary
        self.assertTrue(self.memory_manager._should_update_summary(123, None))
        
        # Test with existing summary but no last update time
        self.mock_db.get_user_summary_update_time.return_value = None
        self.assertTrue(self.memory_manager._should_update_summary(123, "Existing summary"))
        
        # Test with recent update time and few new conversations
        last_update = datetime.now() - timedelta(hours=12)
        self.mock_db.get_user_summary_update_time.return_value = last_update
        self.mock_db.get_conversation_count_since.return_value = 2
        self.assertFalse(self.memory_manager._should_update_summary(123, "Existing summary"))
        
        # Test with old update time
        last_update = datetime.now() - timedelta(hours=36)
        self.mock_db.get_user_summary_update_time.return_value = last_update
        self.assertTrue(self.memory_manager._should_update_summary(123, "Existing summary"))
        
        # Test with many new conversations
        self.mock_db.get_conversation_count_since.return_value = 10
        self.assertTrue(self.memory_manager._should_update_summary(123, "Existing summary"))
    
    async def test_process_message(self):
        """Test process_message method."""
        response = await self.dialog_engine.process_message(123, "Hello there!")
        
        # Check that response is as expected
        self.assertEqual(response["text"], "This is a test response")
        self.assertEqual(response["language"], "en")
        
        # Check that conversation was added
        self.mock_user_manager.add_conversation_entry.assert_called_once()
        
        # Check that AI provider was called with context
        self.mock_ai_provider.generate_response.assert_called_once()
    
    async def test_analyze_mood(self):
        """Test analyze_mood method."""
        mood_data = await self.dialog_engine.analyze_mood(123, text="I'm feeling great today!")
        
        # Check that mood data is as expected
        self.assertEqual(mood_data["sentiment"], "positive")
        self.assertEqual(mood_data["confidence"], 0.8)
        
        # Check that AI provider was called
        self.mock_ai_provider.analyze_sentiment.assert_called_once_with("I'm feeling great today!")
        
        # Check that mood entry was added
        self.mock_db.add_mood_entry.assert_called_once()

def run_tests():
    """Run the tests."""
    unittest.main()

if __name__ == "__main__":
    run_tests()
