"""
Enhanced rate limiting module for VoicePal.

This module provides advanced rate limiting with multiple strategies and Redis support.
"""

import time
import logging
from typing import Dict, Optional, Tuple, List
from collections import defaultdict, deque
from enum import Enum
from dataclasses import dataclass

logger = logging.getLogger(__name__)

class RateLimitStrategy(Enum):
    """Rate limiting strategies."""
    FIXED_WINDOW = "fixed_window"
    SLIDING_WINDOW = "sliding_window"
    TOKEN_BUCKET = "token_bucket"
    LEAKY_BUCKET = "leaky_bucket"

@dataclass
class RateLimitRule:
    """Rate limit rule configuration."""
    requests: int
    window: int  # seconds
    strategy: RateLimitStrategy = RateLimitStrategy.SLIDING_WINDOW
    burst_allowance: int = 0  # Additional requests allowed in burst
    
class RateLimitResult:
    """Result of rate limit check."""
    
    def __init__(self, allowed: bool, remaining: int, reset_time: float, retry_after: int = 0):
        self.allowed = allowed
        self.remaining = remaining
        self.reset_time = reset_time
        self.retry_after = retry_after

class EnhancedRateLimiter:
    """Enhanced rate limiter with multiple strategies and Redis support."""
    
    def __init__(self, redis_client=None, default_rules: Optional[Dict[str, RateLimitRule]] = None):
        """
        Initialize enhanced rate limiter.
        
        Args:
            redis_client: Redis client for distributed rate limiting
            default_rules: Default rate limiting rules
        """
        self.redis_client = redis_client
        self.use_redis = redis_client is not None
        
        # In-memory storage for when Redis is not available
        self.memory_storage: Dict[str, deque] = defaultdict(deque)
        self.token_buckets: Dict[str, Dict] = defaultdict(dict)
        
        # Default rate limiting rules
        self.rules = default_rules or {
            "message": RateLimitRule(requests=30, window=60),  # 30 messages per minute
            "voice": RateLimitRule(requests=10, window=60),    # 10 voice messages per minute
            "ai_request": RateLimitRule(requests=20, window=60), # 20 AI requests per minute
            "payment": RateLimitRule(requests=5, window=300),   # 5 payment attempts per 5 minutes
            "api": RateLimitRule(requests=100, window=60),      # 100 API requests per minute
            "admin": RateLimitRule(requests=200, window=60),    # 200 admin requests per minute
        }
    
    def check_rate_limit(self, key: str, rule_name: str, user_id: Optional[int] = None) -> RateLimitResult:
        """
        Check if request is within rate limit.
        
        Args:
            key: Unique identifier for the rate limit (e.g., user_id, ip_address)
            rule_name: Name of the rate limiting rule to apply
            user_id: User ID for audit logging
            
        Returns:
            RateLimitResult indicating if request is allowed
        """
        rule = self.rules.get(rule_name)
        if not rule:
            logger.warning(f"Unknown rate limit rule: {rule_name}")
            return RateLimitResult(allowed=True, remaining=999, reset_time=time.time() + 60)
        
        rate_limit_key = f"{rule_name}:{key}"
        
        if self.use_redis:
            return self._check_rate_limit_redis(rate_limit_key, rule, user_id)
        else:
            return self._check_rate_limit_memory(rate_limit_key, rule, user_id)
    
    def _check_rate_limit_redis(self, key: str, rule: RateLimitRule, user_id: Optional[int]) -> RateLimitResult:
        """Check rate limit using Redis."""
        try:
            if rule.strategy == RateLimitStrategy.SLIDING_WINDOW:
                return self._sliding_window_redis(key, rule, user_id)
            elif rule.strategy == RateLimitStrategy.FIXED_WINDOW:
                return self._fixed_window_redis(key, rule, user_id)
            elif rule.strategy == RateLimitStrategy.TOKEN_BUCKET:
                return self._token_bucket_redis(key, rule, user_id)
            else:
                # Fallback to sliding window
                return self._sliding_window_redis(key, rule, user_id)
        except Exception as e:
            logger.error(f"Redis rate limiting failed: {e}")
            # Fallback to memory-based rate limiting
            return self._check_rate_limit_memory(key, rule, user_id)
    
    def _check_rate_limit_memory(self, key: str, rule: RateLimitRule, user_id: Optional[int]) -> RateLimitResult:
        """Check rate limit using in-memory storage."""
        if rule.strategy == RateLimitStrategy.SLIDING_WINDOW:
            return self._sliding_window_memory(key, rule, user_id)
        elif rule.strategy == RateLimitStrategy.TOKEN_BUCKET:
            return self._token_bucket_memory(key, rule, user_id)
        else:
            # Default to sliding window
            return self._sliding_window_memory(key, rule, user_id)
    
    def _sliding_window_redis(self, key: str, rule: RateLimitRule, user_id: Optional[int]) -> RateLimitResult:
        """Sliding window rate limiting with Redis."""
        now = time.time()
        window_start = now - rule.window
        
        pipe = self.redis_client.pipeline()
        
        # Remove old entries
        pipe.zremrangebyscore(key, 0, window_start)
        
        # Count current requests
        pipe.zcard(key)
        
        # Add current request
        pipe.zadd(key, {str(now): now})
        
        # Set expiration
        pipe.expire(key, rule.window)
        
        results = pipe.execute()
        current_count = results[1] + 1  # +1 for the request we just added
        
        allowed = current_count <= rule.requests
        remaining = max(0, rule.requests - current_count)
        reset_time = now + rule.window
        
        if not allowed:
            # Remove the request we just added since it's not allowed
            self.redis_client.zrem(key, str(now))
            retry_after = int(rule.window)
        else:
            retry_after = 0
        
        return RateLimitResult(allowed, remaining, reset_time, retry_after)
    
    def _sliding_window_memory(self, key: str, rule: RateLimitRule, user_id: Optional[int]) -> RateLimitResult:
        """Sliding window rate limiting with memory storage."""
        now = time.time()
        window_start = now - rule.window
        
        # Get request times for this key
        request_times = self.memory_storage[key]
        
        # Remove old requests
        while request_times and request_times[0] < window_start:
            request_times.popleft()
        
        # Check if we can add this request
        current_count = len(request_times)
        allowed = current_count < rule.requests
        
        if allowed:
            request_times.append(now)
            remaining = rule.requests - current_count - 1
            retry_after = 0
        else:
            remaining = 0
            # Calculate retry after time
            if request_times:
                oldest_request = request_times[0]
                retry_after = int(oldest_request + rule.window - now)
            else:
                retry_after = rule.window
        
        reset_time = now + rule.window
        
        return RateLimitResult(allowed, remaining, reset_time, retry_after)
    
    def _fixed_window_redis(self, key: str, rule: RateLimitRule, user_id: Optional[int]) -> RateLimitResult:
        """Fixed window rate limiting with Redis."""
        now = time.time()
        window = int(now // rule.window) * rule.window
        window_key = f"{key}:{window}"
        
        pipe = self.redis_client.pipeline()
        pipe.incr(window_key)
        pipe.expire(window_key, rule.window)
        results = pipe.execute()
        
        current_count = results[0]
        allowed = current_count <= rule.requests
        remaining = max(0, rule.requests - current_count)
        reset_time = window + rule.window
        
        retry_after = int(reset_time - now) if not allowed else 0
        
        return RateLimitResult(allowed, remaining, reset_time, retry_after)
    
    def _token_bucket_redis(self, key: str, rule: RateLimitRule, user_id: Optional[int]) -> RateLimitResult:
        """Token bucket rate limiting with Redis."""
        now = time.time()
        
        # Lua script for atomic token bucket operations
        lua_script = """
        local key = KEYS[1]
        local capacity = tonumber(ARGV[1])
        local refill_rate = tonumber(ARGV[2])
        local now = tonumber(ARGV[3])
        
        local bucket = redis.call('HMGET', key, 'tokens', 'last_refill')
        local tokens = tonumber(bucket[1]) or capacity
        local last_refill = tonumber(bucket[2]) or now
        
        -- Calculate tokens to add
        local time_passed = now - last_refill
        local tokens_to_add = time_passed * refill_rate
        tokens = math.min(capacity, tokens + tokens_to_add)
        
        local allowed = 0
        if tokens >= 1 then
            tokens = tokens - 1
            allowed = 1
        end
        
        -- Update bucket
        redis.call('HMSET', key, 'tokens', tokens, 'last_refill', now)
        redis.call('EXPIRE', key, 3600)  -- 1 hour expiration
        
        return {allowed, tokens}
        """
        
        refill_rate = rule.requests / rule.window  # tokens per second
        result = self.redis_client.eval(lua_script, 1, key, rule.requests, refill_rate, now)
        
        allowed = bool(result[0])
        remaining = int(result[1])
        reset_time = now + rule.window
        retry_after = 1 if not allowed else 0  # Try again in 1 second
        
        return RateLimitResult(allowed, remaining, reset_time, retry_after)
    
    def _token_bucket_memory(self, key: str, rule: RateLimitRule, user_id: Optional[int]) -> RateLimitResult:
        """Token bucket rate limiting with memory storage."""
        now = time.time()
        
        if key not in self.token_buckets:
            self.token_buckets[key] = {
                'tokens': rule.requests,
                'last_refill': now
            }
        
        bucket = self.token_buckets[key]
        
        # Calculate tokens to add
        time_passed = now - bucket['last_refill']
        refill_rate = rule.requests / rule.window  # tokens per second
        tokens_to_add = time_passed * refill_rate
        
        bucket['tokens'] = min(rule.requests, bucket['tokens'] + tokens_to_add)
        bucket['last_refill'] = now
        
        # Check if request is allowed
        if bucket['tokens'] >= 1:
            bucket['tokens'] -= 1
            allowed = True
            retry_after = 0
        else:
            allowed = False
            retry_after = 1  # Try again in 1 second
        
        remaining = int(bucket['tokens'])
        reset_time = now + rule.window
        
        return RateLimitResult(allowed, remaining, reset_time, retry_after)
    
    def add_rule(self, name: str, rule: RateLimitRule) -> None:
        """
        Add a new rate limiting rule.
        
        Args:
            name: Rule name
            rule: Rate limiting rule
        """
        self.rules[name] = rule
    
    def remove_rule(self, name: str) -> bool:
        """
        Remove a rate limiting rule.
        
        Args:
            name: Rule name
            
        Returns:
            True if rule was removed, False if not found
        """
        if name in self.rules:
            del self.rules[name]
            return True
        return False
    
    def get_rule(self, name: str) -> Optional[RateLimitRule]:
        """
        Get a rate limiting rule.
        
        Args:
            name: Rule name
            
        Returns:
            Rate limiting rule or None if not found
        """
        return self.rules.get(name)
    
    def clear_rate_limit(self, key: str, rule_name: str) -> bool:
        """
        Clear rate limit for a specific key and rule.
        
        Args:
            key: Rate limit key
            rule_name: Rule name
            
        Returns:
            True if cleared successfully, False otherwise
        """
        try:
            rate_limit_key = f"{rule_name}:{key}"
            
            if self.use_redis:
                self.redis_client.delete(rate_limit_key)
            else:
                if rate_limit_key in self.memory_storage:
                    del self.memory_storage[rate_limit_key]
                if rate_limit_key in self.token_buckets:
                    del self.token_buckets[rate_limit_key]
            
            return True
        except Exception as e:
            logger.error(f"Failed to clear rate limit: {e}")
            return False
