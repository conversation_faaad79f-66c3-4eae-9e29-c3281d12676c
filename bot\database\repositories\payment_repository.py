"""
Payment repositories for VoicePal.

This module provides the payment-related repositories for the VoicePal database.
"""

import logging
from typing import Dict, List, Any, Optional, Union, Tuple
from datetime import datetime

from bot.database.core.connection import DatabaseConnection
from bot.database.core.exceptions import (
    DatabaseError,
    DatabaseNotFoundError,
    DatabaseDuplicateError,
    InsufficientCreditsError
)
from bot.database.repositories.repository import Repository
from bot.database.models.payment import Transaction, PaymentPackage, Subscription

# Set up logging
logger = logging.getLogger(__name__)

class TransactionRepository(Repository[Transaction]):
    """Repository for Transaction model."""
    
    _model_class = Transaction
    
    def find_by_user(self, user_id: str, limit: Optional[int] = None, offset: Optional[int] = None) -> List[Transaction]:
        """Find transactions by user ID.
        
        Args:
            user_id: User ID
            limit: Maximum number of transactions to return
            offset: Offset for pagination
            
        Returns:
            List of transactions
            
        Raises:
            DatabaseError: If query fails
        """
        return self.find_all(
            where={"user_id": user_id},
            order_by="created_at DESC",
            limit=limit,
            offset=offset
        )
    
    def find_by_status(self, status: str, limit: Optional[int] = None, offset: Optional[int] = None) -> List[Transaction]:
        """Find transactions by status.
        
        Args:
            status: Transaction status
            limit: Maximum number of transactions to return
            offset: Offset for pagination
            
        Returns:
            List of transactions
            
        Raises:
            DatabaseError: If query fails
        """
        return self.find_all(
            where={"status": status},
            order_by="created_at DESC",
            limit=limit,
            offset=offset
        )
    
    def find_by_provider_transaction_id(self, provider_transaction_id: str) -> Optional[Transaction]:
        """Find transaction by provider transaction ID.
        
        Args:
            provider_transaction_id: Provider's transaction ID
            
        Returns:
            Transaction or None if not found
            
        Raises:
            DatabaseError: If query fails
        """
        return self.find_one({"provider_transaction_id": provider_transaction_id})
    
    def create_purchase_transaction(self, user_id: str, amount: int, provider: str) -> Transaction:
        """Create a purchase transaction.
        
        Args:
            user_id: User ID
            amount: Transaction amount
            provider: Payment provider
            
        Returns:
            Created transaction
            
        Raises:
            DatabaseError: If creation fails
        """
        try:
            transaction = Transaction(
                user_id=user_id,
                amount=amount,
                type=Transaction.TYPE_PURCHASE,
                status=Transaction.STATUS_PENDING,
                provider=provider
            )
            
            return self.create(transaction)
        except Exception as e:
            logger.error(f"Failed to create purchase transaction: {e}")
            raise DatabaseError(f"Failed to create purchase transaction: {e}") from e
    
    def create_credit_transaction(self, user_id: str, amount: int, provider: Optional[str] = None) -> Transaction:
        """Create a credit transaction.
        
        Args:
            user_id: User ID
            amount: Transaction amount
            provider: Payment provider
            
        Returns:
            Created transaction
            
        Raises:
            DatabaseError: If creation fails
        """
        try:
            transaction = Transaction(
                user_id=user_id,
                amount=amount,
                type=Transaction.TYPE_CREDIT,
                status=Transaction.STATUS_COMPLETED,
                provider=provider
            )
            
            return self.create(transaction)
        except Exception as e:
            logger.error(f"Failed to create credit transaction: {e}")
            raise DatabaseError(f"Failed to create credit transaction: {e}") from e
    
    def create_debit_transaction(self, user_id: str, amount: int) -> Transaction:
        """Create a debit transaction.
        
        Args:
            user_id: User ID
            amount: Transaction amount
            
        Returns:
            Created transaction
            
        Raises:
            DatabaseError: If creation fails
        """
        try:
            transaction = Transaction(
                user_id=user_id,
                amount=amount,
                type=Transaction.TYPE_DEBIT,
                status=Transaction.STATUS_COMPLETED
            )
            
            return self.create(transaction)
        except Exception as e:
            logger.error(f"Failed to create debit transaction: {e}")
            raise DatabaseError(f"Failed to create debit transaction: {e}") from e
    
    def complete_transaction(self, transaction_id: str, provider_transaction_id: Optional[str] = None) -> Optional[Transaction]:
        """Complete a transaction.
        
        Args:
            transaction_id: Transaction ID
            provider_transaction_id: Provider's transaction ID
            
        Returns:
            Updated transaction or None if not found
            
        Raises:
            DatabaseError: If update fails
        """
        try:
            transaction = self.find_by_id(transaction_id)
            if not transaction:
                return None
            
            transaction.complete(provider_transaction_id)
            return self.update(transaction)
        except Exception as e:
            logger.error(f"Failed to complete transaction: {e}")
            raise DatabaseError(f"Failed to complete transaction: {e}") from e
    
    def fail_transaction(self, transaction_id: str) -> Optional[Transaction]:
        """Mark a transaction as failed.
        
        Args:
            transaction_id: Transaction ID
            
        Returns:
            Updated transaction or None if not found
            
        Raises:
            DatabaseError: If update fails
        """
        try:
            transaction = self.find_by_id(transaction_id)
            if not transaction:
                return None
            
            transaction.fail()
            return self.update(transaction)
        except Exception as e:
            logger.error(f"Failed to mark transaction as failed: {e}")
            raise DatabaseError(f"Failed to mark transaction as failed: {e}") from e

class PaymentPackageRepository(Repository[PaymentPackage]):
    """Repository for PaymentPackage model."""
    
    _model_class = PaymentPackage
    
    def find_active_packages(self) -> List[PaymentPackage]:
        """Find all active payment packages.
        
        Returns:
            List of active payment packages
            
        Raises:
            DatabaseError: If query fails
        """
        return self.find_all(
            where={"is_active": True},
            order_by="price ASC"
        )
    
    def find_by_name(self, name: str) -> Optional[PaymentPackage]:
        """Find payment package by name.
        
        Args:
            name: Package name
            
        Returns:
            Payment package or None if not found
            
        Raises:
            DatabaseError: If query fails
        """
        return self.find_one({"name": name})
    
    def activate_package(self, package_id: str) -> Optional[PaymentPackage]:
        """Activate a payment package.
        
        Args:
            package_id: Package ID
            
        Returns:
            Updated payment package or None if not found
            
        Raises:
            DatabaseError: If update fails
        """
        try:
            package = self.find_by_id(package_id)
            if not package:
                return None
            
            package.activate()
            return self.update(package)
        except Exception as e:
            logger.error(f"Failed to activate payment package: {e}")
            raise DatabaseError(f"Failed to activate payment package: {e}") from e
    
    def deactivate_package(self, package_id: str) -> Optional[PaymentPackage]:
        """Deactivate a payment package.
        
        Args:
            package_id: Package ID
            
        Returns:
            Updated payment package or None if not found
            
        Raises:
            DatabaseError: If update fails
        """
        try:
            package = self.find_by_id(package_id)
            if not package:
                return None
            
            package.deactivate()
            return self.update(package)
        except Exception as e:
            logger.error(f"Failed to deactivate payment package: {e}")
            raise DatabaseError(f"Failed to deactivate payment package: {e}") from e

class SubscriptionRepository(Repository[Subscription]):
    """Repository for Subscription model."""
    
    _model_class = Subscription
    
    def find_by_user(self, user_id: str) -> List[Subscription]:
        """Find subscriptions by user ID.
        
        Args:
            user_id: User ID
            
        Returns:
            List of subscriptions
            
        Raises:
            DatabaseError: If query fails
        """
        return self.find_all(
            where={"user_id": user_id},
            order_by="created_at DESC"
        )
    
    def find_active_by_user(self, user_id: str) -> List[Subscription]:
        """Find active subscriptions by user ID.
        
        Args:
            user_id: User ID
            
        Returns:
            List of active subscriptions
            
        Raises:
            DatabaseError: If query fails
        """
        return self.find_all(
            where={"user_id": user_id, "status": Subscription.STATUS_ACTIVE},
            order_by="created_at DESC"
        )
    
    def find_by_provider_subscription_id(self, provider_subscription_id: str) -> Optional[Subscription]:
        """Find subscription by provider subscription ID.
        
        Args:
            provider_subscription_id: Provider's subscription ID
            
        Returns:
            Subscription or None if not found
            
        Raises:
            DatabaseError: If query fails
        """
        return self.find_one({"provider_subscription_id": provider_subscription_id})
    
    def create_subscription(self, user_id: str, package_id: str, provider: str,
                           provider_subscription_id: Optional[str] = None) -> Subscription:
        """Create a subscription.
        
        Args:
            user_id: User ID
            package_id: Payment package ID
            provider: Payment provider
            provider_subscription_id: Provider's subscription ID
            
        Returns:
            Created subscription
            
        Raises:
            DatabaseError: If creation fails
        """
        try:
            subscription = Subscription(
                user_id=user_id,
                package_id=package_id,
                status=Subscription.STATUS_PENDING,
                provider=provider,
                provider_subscription_id=provider_subscription_id
            )
            
            return self.create(subscription)
        except Exception as e:
            logger.error(f"Failed to create subscription: {e}")
            raise DatabaseError(f"Failed to create subscription: {e}") from e
    
    def activate_subscription(self, subscription_id: str, start_date: Optional[str] = None,
                             end_date: Optional[str] = None) -> Optional[Subscription]:
        """Activate a subscription.
        
        Args:
            subscription_id: Subscription ID
            start_date: Subscription start date
            end_date: Subscription end date
            
        Returns:
            Updated subscription or None if not found
            
        Raises:
            DatabaseError: If update fails
        """
        try:
            subscription = self.find_by_id(subscription_id)
            if not subscription:
                return None
            
            subscription.activate(start_date, end_date)
            return self.update(subscription)
        except Exception as e:
            logger.error(f"Failed to activate subscription: {e}")
            raise DatabaseError(f"Failed to activate subscription: {e}") from e
    
    def cancel_subscription(self, subscription_id: str) -> Optional[Subscription]:
        """Cancel a subscription.
        
        Args:
            subscription_id: Subscription ID
            
        Returns:
            Updated subscription or None if not found
            
        Raises:
            DatabaseError: If update fails
        """
        try:
            subscription = self.find_by_id(subscription_id)
            if not subscription:
                return None
            
            subscription.cancel()
            return self.update(subscription)
        except Exception as e:
            logger.error(f"Failed to cancel subscription: {e}")
            raise DatabaseError(f"Failed to cancel subscription: {e}") from e
    
    def has_active_subscription(self, user_id: str) -> bool:
        """Check if user has an active subscription.
        
        Args:
            user_id: User ID
            
        Returns:
            True if user has an active subscription, False otherwise
            
        Raises:
            DatabaseError: If query fails
        """
        try:
            active_subscriptions = self.find_active_by_user(user_id)
            return len(active_subscriptions) > 0
        except Exception as e:
            logger.error(f"Failed to check active subscription: {e}")
            raise DatabaseError(f"Failed to check active subscription: {e}") from e
