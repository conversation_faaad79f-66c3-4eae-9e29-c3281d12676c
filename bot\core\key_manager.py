"""
API key management utilities for VoicePal.

This module provides secure API key storage, access logging, and key rotation
capabilities for the VoicePal bot.
"""

import os
import json
import base64
import logging
import time
import hashlib
import sqlite3
from typing import Dict, Any, Optional, List, Union
from datetime import datetime, timedelta
from cryptography.fernet import <PERSON><PERSON><PERSON>, InvalidToken
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class KeyManager:
    """
    Secure API key management for VoicePal.
    
    This class provides secure storage, access, and rotation of API keys
    used by the VoicePal bot. It uses encryption to protect keys at rest
    and logs all access to keys for security auditing.
    """
    
    def __init__(self, db_connection, master_key: Optional[str] = None):
        """
        Initialize the key manager.
        
        Args:
            db_connection: SQLite database connection
            master_key: Master encryption key (if None, will use environment variable or generate)
        """
        self.db = db_connection
        
        # Set up encryption
        self._setup_encryption(master_key)
        
        # Set up database tables
        self._setup_database()
        
    def _setup_encryption(self, master_key: Optional[str] = None) -> None:
        """
        Set up encryption for API keys.
        
        Args:
            master_key: Master encryption key (if None, will use environment variable or generate)
        """
        # Get master key from parameter, environment, or generate
        if master_key:
            self.master_key = master_key
        elif os.environ.get("VOICEPAL_MASTER_KEY"):
            self.master_key = os.environ.get("VOICEPAL_MASTER_KEY")
        else:
            # Generate a new master key
            self.master_key = base64.urlsafe_b64encode(os.urandom(32)).decode()
            logger.warning("Generated new master key. Store this securely: %s", self.master_key)
            
        # Create a salt for key derivation
        self.salt = os.environ.get("VOICEPAL_KEY_SALT", "voicepal_salt").encode()
        
        # Derive a key from the master key
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=self.salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(self.master_key.encode()))
        
        # Create Fernet cipher
        self.cipher = Fernet(key)
        
    def _setup_database(self) -> None:
        """Create database tables for key management if they don't exist."""
        try:
            cursor = self.db.cursor()
            
            # Create api_keys table
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS api_keys (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                service TEXT UNIQUE,
                encrypted_key TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                expires_at TIMESTAMP,
                is_active BOOLEAN DEFAULT 1
            )
            ''')
            
            # Create key_access_logs table
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS key_access_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                service TEXT,
                access_type TEXT,
                user_id INTEGER,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                ip_address TEXT,
                success BOOLEAN
            )
            ''')
            
            # Create key_rotation_history table
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS key_rotation_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                service TEXT,
                rotation_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                reason TEXT
            )
            ''')
            
            self.db.commit()
            logger.info("Key management tables created")
        except sqlite3.Error as e:
            logger.error("Error setting up key management tables: %s", e)
            raise
            
    def store_key(self, service: str, api_key: str, expires_at: Optional[datetime] = None) -> bool:
        """
        Store an API key securely.
        
        Args:
            service: Service name (e.g., 'deepgram', 'elevenlabs')
            api_key: The API key to store
            expires_at: Expiration date for the key
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Encrypt the API key
            encrypted_key = self.cipher.encrypt(api_key.encode()).decode()
            
            # Format expiration date
            expires_str = expires_at.isoformat() if expires_at else None
            
            # Store in database
            cursor = self.db.cursor()
            
            # Check if service already exists
            cursor.execute("SELECT id FROM api_keys WHERE service = ?", (service,))
            existing = cursor.fetchone()
            
            if existing:
                # Update existing key
                cursor.execute(
                    """UPDATE api_keys SET 
                       encrypted_key = ?, 
                       updated_at = ?, 
                       expires_at = ?,
                       is_active = 1
                       WHERE service = ?""",
                    (encrypted_key, datetime.now().isoformat(), expires_str, service)
                )
                
                # Log key rotation
                cursor.execute(
                    "INSERT INTO key_rotation_history (service, reason) VALUES (?, ?)",
                    (service, "manual_update")
                )
            else:
                # Insert new key
                cursor.execute(
                    """INSERT INTO api_keys 
                       (service, encrypted_key, updated_at, expires_at) 
                       VALUES (?, ?, ?, ?)""",
                    (service, encrypted_key, datetime.now().isoformat(), expires_str)
                )
                
            self.db.commit()
            logger.info("Stored API key for service: %s", service)
            return True
        except Exception as e:
            logger.error("Error storing API key for service %s: %s", service, e)
            return False
            
    def get_key(self, service: str, user_id: Optional[int] = None, 
                ip_address: Optional[str] = None) -> Optional[str]:
        """
        Get an API key securely.
        
        Args:
            service: Service name (e.g., 'deepgram', 'elevenlabs')
            user_id: User ID requesting the key (for logging)
            ip_address: IP address requesting the key (for logging)
            
        Returns:
            str: The API key or None if not found or expired
        """
        try:
            # Get encrypted key from database
            cursor = self.db.cursor()
            cursor.execute(
                """SELECT encrypted_key, expires_at, is_active 
                   FROM api_keys 
                   WHERE service = ?""",
                (service,)
            )
            result = cursor.fetchone()
            
            success = False
            api_key = None
            
            if result:
                encrypted_key, expires_at, is_active = result
                
                # Check if key is active
                if not is_active:
                    logger.warning("API key for service %s is inactive", service)
                else:
                    # Check if key is expired
                    if expires_at and datetime.fromisoformat(expires_at) < datetime.now():
                        logger.warning("API key for service %s is expired", service)
                    else:
                        # Decrypt the key
                        try:
                            api_key = self.cipher.decrypt(encrypted_key.encode()).decode()
                            success = True
                        except InvalidToken:
                            logger.error("Failed to decrypt API key for service %s", service)
            else:
                logger.warning("No API key found for service %s", service)
                
            # Log access
            self._log_access(service, "get", user_id, ip_address, success)
            
            return api_key
        except Exception as e:
            logger.error("Error getting API key for service %s: %s", service, e)
            # Log access failure
            self._log_access(service, "get", user_id, ip_address, False)
            return None
            
    def _log_access(self, service: str, access_type: str, user_id: Optional[int] = None,
                   ip_address: Optional[str] = None, success: bool = True) -> None:
        """
        Log API key access.
        
        Args:
            service: Service name
            access_type: Type of access ('get', 'store', 'rotate')
            user_id: User ID accessing the key
            ip_address: IP address accessing the key
            success: Whether access was successful
        """
        try:
            cursor = self.db.cursor()
            cursor.execute(
                """INSERT INTO key_access_logs 
                   (service, access_type, user_id, ip_address, success) 
                   VALUES (?, ?, ?, ?, ?)""",
                (service, access_type, user_id, ip_address, success)
            )
            self.db.commit()
        except Exception as e:
            logger.error("Error logging key access: %s", e)
            
    def rotate_key(self, service: str, new_api_key: str, 
                  reason: str = "scheduled_rotation") -> bool:
        """
        Rotate an API key.
        
        Args:
            service: Service name
            new_api_key: New API key
            reason: Reason for rotation
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Store the new key
            success = self.store_key(service, new_api_key)
            
            if success:
                # Log rotation
                cursor = self.db.cursor()
                cursor.execute(
                    "INSERT INTO key_rotation_history (service, reason) VALUES (?, ?)",
                    (service, reason)
                )
                self.db.commit()
                logger.info("Rotated API key for service %s: %s", service, reason)
                
            return success
        except Exception as e:
            logger.error("Error rotating API key for service %s: %s", service, e)
            return False
            
    def deactivate_key(self, service: str) -> bool:
        """
        Deactivate an API key.
        
        Args:
            service: Service name
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            cursor = self.db.cursor()
            cursor.execute(
                "UPDATE api_keys SET is_active = 0 WHERE service = ?",
                (service,)
            )
            self.db.commit()
            logger.info("Deactivated API key for service %s", service)
            return True
        except Exception as e:
            logger.error("Error deactivating API key for service %s: %s", service, e)
            return False
            
    def get_key_info(self, service: str) -> Optional[Dict[str, Any]]:
        """
        Get information about an API key.
        
        Args:
            service: Service name
            
        Returns:
            Dict containing key information or None if not found
        """
        try:
            cursor = self.db.cursor()
            cursor.execute(
                """SELECT id, service, created_at, updated_at, expires_at, is_active 
                   FROM api_keys 
                   WHERE service = ?""",
                (service,)
            )
            result = cursor.fetchone()
            
            if result:
                return {
                    'id': result[0],
                    'service': result[1],
                    'created_at': result[2],
                    'updated_at': result[3],
                    'expires_at': result[4],
                    'is_active': bool(result[5]),
                    'is_expired': result[4] and datetime.fromisoformat(result[4]) < datetime.now(),
                }
            return None
        except Exception as e:
            logger.error("Error getting key info for service %s: %s", service, e)
            return None
            
    def get_access_logs(self, service: Optional[str] = None, 
                       limit: int = 100) -> List[Dict[str, Any]]:
        """
        Get access logs for API keys.
        
        Args:
            service: Service name (optional, if None returns logs for all services)
            limit: Maximum number of logs to return
            
        Returns:
            List of access log dictionaries
        """
        try:
            cursor = self.db.cursor()
            
            if service:
                cursor.execute(
                    """SELECT id, service, access_type, user_id, timestamp, ip_address, success 
                       FROM key_access_logs 
                       WHERE service = ? 
                       ORDER BY timestamp DESC 
                       LIMIT ?""",
                    (service, limit)
                )
            else:
                cursor.execute(
                    """SELECT id, service, access_type, user_id, timestamp, ip_address, success 
                       FROM key_access_logs 
                       ORDER BY timestamp DESC 
                       LIMIT ?""",
                    (limit,)
                )
                
            logs = cursor.fetchall()
            
            return [{
                'id': log[0],
                'service': log[1],
                'access_type': log[2],
                'user_id': log[3],
                'timestamp': log[4],
                'ip_address': log[5],
                'success': bool(log[6]),
            } for log in logs]
        except Exception as e:
            logger.error("Error getting access logs: %s", e)
            return []
