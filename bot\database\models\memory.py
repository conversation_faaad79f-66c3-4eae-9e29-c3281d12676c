"""
Memory domain models for VoicePal.

This module provides the memory-related models for the VoicePal database.
"""

import logging
from typing import Dict, List, Any, Optional, ClassVar, Type, Set, Tuple
from datetime import datetime

from bot.database.models.model import Model

# Set up logging
logger = logging.getLogger(__name__)

class Memory(Model):
    """Memory model."""
    
    _table_name = "memories"
    _primary_key = "memory_id"
    _required_columns = ["user_id", "content"]
    _defaults = {
        "importance": 1
    }
    _foreign_keys = {
        "user_id": ("users", "user_id")
    }
    
    def __init__(self,
                 memory_id: Optional[str] = None,
                 user_id: str = None,
                 content: str = None,
                 importance: int = 1,
                 created_at: Optional[str] = None,
                 updated_at: Optional[str] = None,
                 last_accessed: Optional[str] = None,
                 **kwargs):
        """Initialize memory model.
        
        Args:
            memory_id: Memory ID
            user_id: User ID
            content: Memory content
            importance: Memory importance (1-10)
            created_at: Creation timestamp
            updated_at: Last update timestamp
            last_accessed: Last access timestamp
            **kwargs: Additional attributes
        """
        super().__init__(
            memory_id=memory_id,
            user_id=user_id,
            content=content,
            importance=importance,
            created_at=created_at,
            updated_at=updated_at,
            last_accessed=last_accessed,
            **kwargs
        )
    
    def validate(self) -> List[str]:
        """Validate memory attributes.
        
        Returns:
            List of validation error messages (empty if valid)
        """
        errors = super().validate()
        
        # Validate importance
        if hasattr(self, "importance") and self.importance is not None:
            if not isinstance(self.importance, int) or self.importance < 1 or self.importance > 10:
                errors.append(f"Importance must be an integer between 1 and 10: {self.importance}")
        
        return errors
    
    def update_content(self, content: str) -> None:
        """Update memory content.
        
        Args:
            content: New content
        """
        self.content = content
        self.updated_at = datetime.now().isoformat()
    
    def update_importance(self, importance: int) -> None:
        """Update memory importance.
        
        Args:
            importance: New importance
            
        Raises:
            ValueError: If importance is not between 1 and 10
        """
        if not isinstance(importance, int) or importance < 1 or importance > 10:
            raise ValueError(f"Importance must be an integer between 1 and 10: {importance}")
        
        self.importance = importance
        self.updated_at = datetime.now().isoformat()
    
    def access(self) -> None:
        """Update last access timestamp."""
        self.last_accessed = datetime.now().isoformat()

class MemoryTag(Model):
    """Memory tag model."""
    
    _table_name = "memory_tags"
    _primary_key = "tag_id"
    _required_columns = ["memory_id", "tag"]
    _foreign_keys = {
        "memory_id": ("memories", "memory_id")
    }
    
    def __init__(self,
                 tag_id: Optional[str] = None,
                 memory_id: str = None,
                 tag: str = None,
                 created_at: Optional[str] = None,
                 **kwargs):
        """Initialize memory tag model.
        
        Args:
            tag_id: Tag ID
            memory_id: Memory ID
            tag: Tag name
            created_at: Creation timestamp
            **kwargs: Additional attributes
        """
        super().__init__(
            tag_id=tag_id,
            memory_id=memory_id,
            tag=tag,
            created_at=created_at,
            **kwargs
        )
