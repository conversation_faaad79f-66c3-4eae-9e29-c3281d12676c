"""
Test script for the database core module.

This script tests the functionality of the database core module,
focusing on the credit system and user management.
"""

import os
import unittest
import sqlite3
from datetime import datetime, timedelta
import sys
import logging

# Add the parent directory to the path so we can import the bot modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from bot.database.core import Database
from bot.core.exceptions import (
    DatabaseError, DatabaseQueryError, DatabaseIntegrityError, InsufficientCreditsError
)

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class TestDatabaseCore(unittest.TestCase):
    """Test cases for the Database core class."""
    
    def setUp(self):
        """Set up test environment."""
        # Use a test database file
        self.test_db_file = "test_voicepal.db"
        # Remove test database if it exists
        if os.path.exists(self.test_db_file):
            os.remove(self.test_db_file)
        # Create a new database instance
        self.db = Database(self.test_db_file)
    
    def tearDown(self):
        """Clean up after tests."""
        self.db.close()
        # Remove test database
        if os.path.exists(self.test_db_file):
            os.remove(self.test_db_file)
    
    def test_add_user_basic(self):
        """Test adding a new user with basic information."""
        # Add a new user
        is_new = self.db.add_user(
            user_id=123456789,
            username="testuser",
            first_name="Test",
            last_name="User"
        )
        # Should be a new user
        self.assertTrue(is_new)
        
        # Get the user
        user = self.db.get_user(123456789)
        # Check user data
        self.assertEqual(user["user_id"], 123456789)
        self.assertEqual(user["username"], "testuser")
        self.assertEqual(user["first_name"], "Test")
        self.assertEqual(user["last_name"], "User")
        self.assertEqual(user["credits"], 0)
        self.assertEqual(user["personality"], "friendly")
        self.assertEqual(user["free_credits_received"], 0)
        self.assertIsNone(user["registration_ip"])
        self.assertIsNone(user["device_id"])
    
    def test_add_user_with_tracking(self):
        """Test adding a new user with tracking information."""
        # Add a new user with IP and device ID
        is_new = self.db.add_user(
            user_id=123456789,
            username="testuser",
            first_name="Test",
            last_name="User",
            ip_address="***********",
            device_id="test_device_123"
        )
        # Should be a new user
        self.assertTrue(is_new)
        
        # Get the user
        user = self.db.get_user(123456789)
        # Check user data
        self.assertEqual(user["registration_ip"], "***********")
        self.assertEqual(user["device_id"], "test_device_123")
    
    def test_update_user(self):
        """Test updating an existing user."""
        # Add a new user
        self.db.add_user(
            user_id=123456789,
            username="testuser",
            first_name="Test",
            last_name="User"
        )
        
        # Update the user
        is_new = self.db.add_user(
            user_id=123456789,
            username="updated_user",
            first_name="Updated",
            last_name="User",
            ip_address="***********",
            device_id="updated_device_123"
        )
        # Should not be a new user
        self.assertFalse(is_new)
        
        # Get the user
        user = self.db.get_user(123456789)
        # Check user data
        self.assertEqual(user["username"], "updated_user")
        self.assertEqual(user["first_name"], "Updated")
        self.assertEqual(user["registration_ip"], "***********")
        self.assertEqual(user["device_id"], "updated_device_123")
    
    def test_add_credits_basic(self):
        """Test adding credits to a user."""
        # Add a user
        self.db.add_user(user_id=123456789)
        
        # Initial credits should be 0
        self.assertEqual(self.db.get_user_credits(123456789), 0)
        
        # Add credits
        new_balance = self.db.add_credits(123456789, 100, source="manual")
        self.assertEqual(new_balance, 100)
        self.assertEqual(self.db.get_user_credits(123456789), 100)
        
        # Add more credits
        new_balance = self.db.add_credits(123456789, 50, source="manual")
        self.assertEqual(new_balance, 150)
        self.assertEqual(self.db.get_user_credits(123456789), 150)
    
    def test_add_credits_with_source(self):
        """Test adding credits with different sources."""
        # Add a user
        self.db.add_user(user_id=123456789)
        
        # Add credits with different sources
        self.db.add_credits(123456789, 100, source="purchase")
        self.db.add_credits(123456789, 50, source="admin")
        
        # Check transactions
        transactions = self.db.get_user_transactions(123456789)
        self.assertEqual(len(transactions), 2)
        
        # Check transaction sources
        sources = [t["source"] for t in transactions]
        self.assertIn("purchase", sources)
        self.assertIn("admin", sources)
    
    def test_free_trial_credits(self):
        """Test free trial credits can only be received once."""
        # Add a user
        self.db.add_user(user_id=123456789)
        
        # Add free trial credits
        new_balance = self.db.add_credits(123456789, 100, source="free_trial")
        self.assertEqual(new_balance, 100)
        
        # Check that free_credits_received is set to 1
        user = self.db.get_user(123456789)
        self.assertEqual(user["free_credits_received"], 1)
        
        # Try to add free trial credits again
        new_balance = self.db.add_credits(123456789, 100, source="free_trial")
        # Balance should not change
        self.assertEqual(new_balance, 100)
        self.assertEqual(self.db.get_user_credits(123456789), 100)
    
    def test_use_credits(self):
        """Test using credits."""
        # Add a user
        self.db.add_user(user_id=123456789)
        
        # Add credits
        self.db.add_credits(123456789, 100, source="manual")
        
        # Use credits successfully
        result = self.db.use_credits(123456789, 50)
        self.assertTrue(result)
        self.assertEqual(self.db.get_user_credits(123456789), 50)
        
        # Try to use more credits than available
        with self.assertRaises(InsufficientCreditsError):
            self.db.use_credits(123456789, 100)
        
        # Credits should remain unchanged
        self.assertEqual(self.db.get_user_credits(123456789), 50)
    
    def test_transaction_recording(self):
        """Test that transactions are recorded for credit additions."""
        # Add a user
        self.db.add_user(user_id=123456789)
        
        # Add credits
        self.db.add_credits(123456789, 100, source="purchase")
        
        # Check transactions
        transactions = self.db.get_user_transactions(123456789)
        self.assertEqual(len(transactions), 1)
        
        # Check transaction details
        transaction = transactions[0]
        self.assertEqual(transaction["user_id"], 123456789)
        self.assertEqual(transaction["credits"], 100)
        self.assertEqual(transaction["status"], "completed")
        self.assertEqual(transaction["source"], "purchase")

if __name__ == "__main__":
    unittest.main()
