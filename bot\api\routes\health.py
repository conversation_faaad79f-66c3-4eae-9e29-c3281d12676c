"""
Health check endpoint for MoneyMule bot.
"""

from fastapi import APIRouter, HTTPException
from datetime import datetime
import psutil
import os

router = APIRouter()

@router.get("/health")
async def health_check():
    """Basic health check endpoint."""
    try:
        # Check system resources
        memory_usage = psutil.virtual_memory().percent
        disk_usage = psutil.disk_usage('/').percent
        
        # Check environment variables
        required_env_vars = ['BOT_TOKEN', 'DEEPGRAM_API_KEY', 'GOOGLE_AI_API_KEY']
        missing_env_vars = [var for var in required_env_vars if not os.getenv(var)]
        
        status = "healthy"
        if memory_usage > 90 or disk_usage > 90 or missing_env_vars:
            status = "unhealthy"
        
        return {
            "status": status,
            "timestamp": datetime.now().isoformat(),
            "memory_usage_percent": memory_usage,
            "disk_usage_percent": disk_usage,
            "missing_env_vars": missing_env_vars,
            "version": "1.0.0"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Health check failed: {str(e)}")

@router.get("/ready")
async def readiness_check():
    """Readiness check for deployment."""
    try:
        # Check if bot can start
        required_env_vars = ['BOT_TOKEN', 'DEEPGRAM_API_KEY', 'GOOGLE_AI_API_KEY']
        missing_env_vars = [var for var in required_env_vars if not os.getenv(var)]
        
        if missing_env_vars:
            raise HTTPException(
                status_code=503, 
                detail=f"Not ready: missing environment variables: {missing_env_vars}"
            )
        
        return {
            "status": "ready",
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=503, detail=f"Not ready: {str(e)}")
