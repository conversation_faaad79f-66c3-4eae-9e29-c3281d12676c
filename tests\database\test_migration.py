"""
Tests for database migration.

This module tests the database migration process.
"""

import os
import unittest
import tempfile
import sqlite3
from pathlib import Path

from bot.database.database_manager import DatabaseManager
from bot.database.schema_manager import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CURRENT_SCHEMA_VERSION
from bot.database.core.connection import DatabaseConnection
from bot.database.core.exceptions import (
    DatabaseError,
    DatabaseMigrationError
)

class TestDatabaseMigration(unittest.TestCase):
    """Test case for database migration."""
    
    def setUp(self):
        """Set up test environment."""
        # Create temporary directory for database
        self.temp_dir = tempfile.TemporaryDirectory()
        self.db_path = Path(self.temp_dir.name) / "test.db"
        
        # Initialize connection
        self.connection = DatabaseConnection(self.db_path)
        
        # Initialize schema manager
        self.schema_manager = SchemaManager(self.connection)
    
    def tearDown(self):
        """Clean up test environment."""
        # Close connection
        self.connection.close()
        
        # Remove temporary directory
        self.temp_dir.cleanup()
    
    def test_initial_migration(self):
        """Test initial migration."""
        # Initialize schema
        self.schema_manager.initialize_schema()
        
        # Check if current version is 1
        version = self.schema_manager.get_current_version()
        self.assertEqual(version, 1)
        
        # Check if schema_migrations table exists
        cursor = self.connection.execute(
            "SELECT name FROM sqlite_master WHERE type='table' AND name='schema_migrations'"
        )
        self.assertIsNotNone(cursor.fetchone())
        
        # Check if initial version is recorded
        cursor = self.connection.execute(
            "SELECT version FROM schema_migrations ORDER BY version DESC LIMIT 1"
        )
        version = cursor.fetchone()[0]
        self.assertEqual(version, 1)
    
    def test_migration_to_same_version(self):
        """Test migration to same version."""
        # Initialize schema
        self.schema_manager.initialize_schema()
        
        # Migrate to same version
        self.schema_manager.migrate(1)
        
        # Check if current version is still 1
        version = self.schema_manager.get_current_version()
        self.assertEqual(version, 1)
    
    def test_migration_to_invalid_version(self):
        """Test migration to invalid version."""
        # Initialize schema
        self.schema_manager.initialize_schema()
        
        # Try to migrate to invalid version
        with self.assertRaises(DatabaseMigrationError):
            self.schema_manager.migrate(999)
    
    def test_migration_with_database_manager(self):
        """Test migration with DatabaseManager."""
        # Initialize database
        db = DatabaseManager(self.db_path)
        
        # Check if current version is 1
        version = db.get_current_version()
        self.assertEqual(version, 1)
        
        # Migrate to same version
        db.migrate(1)
        
        # Check if current version is still 1
        version = db.get_current_version()
        self.assertEqual(version, 1)
        
        # Close database
        db.close()
    
    def test_migration_with_existing_data(self):
        """Test migration with existing data."""
        # Initialize database
        db = DatabaseManager(self.db_path)
        
        # Create a user
        db.execute(
            "INSERT INTO users (user_id, username) VALUES (?, ?)",
            ("test_user", "test_username")
        )
        
        # Close database
        db.close()
        
        # Reinitialize database
        db = DatabaseManager(self.db_path)
        
        # Check if user exists
        cursor = db.execute(
            "SELECT * FROM users WHERE user_id = ?",
            ("test_user",)
        )
        row = cursor.fetchone()
        self.assertIsNotNone(row)
        self.assertEqual(row["user_id"], "test_user")
        self.assertEqual(row["username"], "test_username")
        
        # Close database
        db.close()
    
    def test_migration_with_custom_migrations_dir(self):
        """Test migration with custom migrations directory."""
        # Create migrations directory
        migrations_dir = Path(self.temp_dir.name) / "migrations"
        os.makedirs(migrations_dir, exist_ok=True)
        
        # Initialize database with migrations directory
        db = DatabaseManager(self.db_path, migrations_dir)
        
        # Check if current version is 1
        version = db.get_current_version()
        self.assertEqual(version, 1)
        
        # Close database
        db.close()
    
    def test_migration_validation(self):
        """Test migration validation."""
        # Initialize database
        db = DatabaseManager(self.db_path)
        
        # Check if schema is valid
        self.assertTrue(db.validate_schema())
        
        # Drop a table
        db.execute("DROP TABLE users")
        
        # Check if schema is invalid
        self.assertFalse(db.validate_schema())
        
        # Close database
        db.close()
    
    def test_migration_with_transaction(self):
        """Test migration with transaction."""
        # Initialize database
        db = DatabaseManager(self.db_path)
        
        # Begin transaction
        db.connection.begin_transaction()
        
        # Drop a table
        db.execute("DROP TABLE users")
        
        # Rollback transaction
        db.connection.rollback()
        
        # Check if schema is still valid
        self.assertTrue(db.validate_schema())
        
        # Close database
        db.close()

if __name__ == "__main__":
    unittest.main()
