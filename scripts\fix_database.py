"""
Fix database constraint errors.

This script fixes foreign key constraint errors in the database by:
1. Checking for orphaned records in tables with foreign key constraints
2. Either fixing the references or removing the orphaned records
3. Adding missing indexes for better performance
"""

import os
import sys
import logging
import sqlite3
from typing import List, Dict, Any, Tuple

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

def connect_to_database(db_file: str) -> sqlite3.Connection:
    """
    Connect to the SQLite database.
    
    Args:
        db_file: Path to the database file
        
    Returns:
        SQLite connection object
    """
    try:
        # Create database directory if it doesn't exist
        db_dir = os.path.dirname(db_file)
        if db_dir and not os.path.exists(db_dir):
            os.makedirs(db_dir)
            
        # Connect to the database
        conn = sqlite3.connect(db_file)
        
        # Enable foreign keys
        conn.execute("PRAGMA foreign_keys = ON")
        
        # Use Row factory for better row access
        conn.row_factory = sqlite3.Row
        
        logger.info(f"Connected to database: {db_file}")
        return conn
    except Exception as e:
        logger.error(f"Error connecting to database: {e}")
        raise

def get_foreign_key_constraints(conn: sqlite3.Connection) -> Dict[str, List[Dict[str, Any]]]:
    """
    Get all foreign key constraints in the database.
    
    Args:
        conn: SQLite connection object
        
    Returns:
        Dict mapping table names to lists of foreign key constraints
    """
    constraints = {}
    
    # Get all tables
    cursor = conn.cursor()
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = [row[0] for row in cursor.fetchall()]
    
    # Get foreign key constraints for each table
    for table in tables:
        cursor.execute(f"PRAGMA foreign_key_list({table})")
        fk_constraints = [dict(row) for row in cursor.fetchall()]
        
        if fk_constraints:
            constraints[table] = fk_constraints
    
    return constraints

def check_orphaned_records(conn: sqlite3.Connection, constraints: Dict[str, List[Dict[str, Any]]]) -> Dict[str, List[Dict[str, Any]]]:
    """
    Check for orphaned records that violate foreign key constraints.
    
    Args:
        conn: SQLite connection object
        constraints: Dict mapping table names to lists of foreign key constraints
        
    Returns:
        Dict mapping table names to lists of orphaned records
    """
    orphaned_records = {}
    cursor = conn.cursor()
    
    for table, fk_list in constraints.items():
        for fk in fk_list:
            # Get the referenced table and column
            ref_table = fk['table']
            ref_column = fk['to']
            from_column = fk['from']
            
            # Check for orphaned records
            query = f"""
            SELECT * FROM {table} 
            WHERE {from_column} IS NOT NULL 
            AND {from_column} NOT IN (SELECT {ref_column} FROM {ref_table})
            """
            
            try:
                cursor.execute(query)
                records = [dict(row) for row in cursor.fetchall()]
                
                if records:
                    if table not in orphaned_records:
                        orphaned_records[table] = []
                    
                    for record in records:
                        orphaned_records[table].append({
                            'record': record,
                            'constraint': fk
                        })
            except sqlite3.Error as e:
                logger.error(f"Error checking orphaned records in {table}: {e}")
    
    return orphaned_records

def fix_orphaned_records(conn: sqlite3.Connection, orphaned_records: Dict[str, List[Dict[str, Any]]]) -> int:
    """
    Fix orphaned records by either fixing references or removing the records.
    
    Args:
        conn: SQLite connection object
        orphaned_records: Dict mapping table names to lists of orphaned records
        
    Returns:
        Number of fixed records
    """
    fixed_count = 0
    cursor = conn.cursor()
    
    for table, records in orphaned_records.items():
        for record_info in records:
            record = record_info['record']
            constraint = record_info['constraint']
            
            # Get primary key column for the table
            cursor.execute(f"PRAGMA table_info({table})")
            columns = [dict(row) for row in cursor.fetchall()]
            pk_column = next((col['name'] for col in columns if col['pk'] == 1), None)
            
            if not pk_column:
                logger.warning(f"Could not find primary key for table {table}, skipping record")
                continue
            
            # Get the record ID
            record_id = record[pk_column]
            from_column = constraint['from']
            
            # Try to fix the reference
            try:
                # Option 1: Set the foreign key to NULL if the column is nullable
                cursor.execute(f"PRAGMA table_info({table})")
                columns = [dict(row) for row in cursor.fetchall()]
                column_info = next((col for col in columns if col['name'] == from_column), None)
                
                if column_info and column_info['notnull'] == 0:
                    # Column is nullable, set to NULL
                    cursor.execute(f"UPDATE {table} SET {from_column} = NULL WHERE {pk_column} = ?", (record_id,))
                    logger.info(f"Set NULL for orphaned record in {table} with {pk_column}={record_id}")
                    fixed_count += 1
                else:
                    # Column is not nullable, delete the record
                    cursor.execute(f"DELETE FROM {table} WHERE {pk_column} = ?", (record_id,))
                    logger.info(f"Deleted orphaned record in {table} with {pk_column}={record_id}")
                    fixed_count += 1
            except sqlite3.Error as e:
                logger.error(f"Error fixing orphaned record in {table} with {pk_column}={record_id}: {e}")
    
    # Commit the changes
    conn.commit()
    
    return fixed_count

def add_missing_indexes(conn: sqlite3.Connection, constraints: Dict[str, List[Dict[str, Any]]]) -> int:
    """
    Add missing indexes for foreign key columns to improve performance.
    
    Args:
        conn: SQLite connection object
        constraints: Dict mapping table names to lists of foreign key constraints
        
    Returns:
        Number of indexes added
    """
    added_count = 0
    cursor = conn.cursor()
    
    for table, fk_list in constraints.items():
        for fk in fk_list:
            from_column = fk['from']
            
            # Check if an index already exists for this column
            cursor.execute(f"PRAGMA index_list({table})")
            indexes = [dict(row) for row in cursor.fetchall()]
            
            has_index = False
            for index in indexes:
                cursor.execute(f"PRAGMA index_info({index['name']})")
                index_columns = [dict(row) for row in cursor.fetchall()]
                
                if any(col['name'] == from_column for col in index_columns):
                    has_index = True
                    break
            
            if not has_index:
                # Create an index for the foreign key column
                index_name = f"idx_{table}_{from_column}"
                try:
                    cursor.execute(f"CREATE INDEX {index_name} ON {table}({from_column})")
                    logger.info(f"Created index {index_name} on {table}({from_column})")
                    added_count += 1
                except sqlite3.Error as e:
                    logger.error(f"Error creating index {index_name}: {e}")
    
    # Commit the changes
    conn.commit()
    
    return added_count

def main():
    """Main function to fix database constraint errors."""
    # Get database file path from command line or use default
    if len(sys.argv) > 1:
        db_file = sys.argv[1]
    else:
        db_file = "voicepal.db"
    
    logger.info(f"Fixing database constraint errors in {db_file}")
    
    try:
        # Connect to the database
        conn = connect_to_database(db_file)
        
        # Get foreign key constraints
        constraints = get_foreign_key_constraints(conn)
        logger.info(f"Found {sum(len(fk_list) for fk_list in constraints.values())} foreign key constraints in {len(constraints)} tables")
        
        # Check for orphaned records
        orphaned_records = check_orphaned_records(conn, constraints)
        orphaned_count = sum(len(records) for records in orphaned_records.values())
        logger.info(f"Found {orphaned_count} orphaned records in {len(orphaned_records)} tables")
        
        # Fix orphaned records
        if orphaned_count > 0:
            fixed_count = fix_orphaned_records(conn, orphaned_records)
            logger.info(f"Fixed {fixed_count} orphaned records")
        
        # Add missing indexes
        added_count = add_missing_indexes(conn, constraints)
        logger.info(f"Added {added_count} missing indexes")
        
        # Close the connection
        conn.close()
        
        logger.info("Database constraint errors fixed successfully")
        return 0
    except Exception as e:
        logger.error(f"Error fixing database constraint errors: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
