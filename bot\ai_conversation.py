"""
AI conversation module for VoicePal.

This module handles the AI conversation logic, providing responses to user messages.
It includes a simple rule-based conversation system that can be used as a fallback
when more advanced AI providers are not available.
"""

import logging
import random
from typing import Dict, List, Any
import re
from datetime import datetime

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class AIConversation:
    """AI conversation handler for VoicePal."""

    # Define personality types with emoji icons
    PERSONALITIES = {
        "friendly": {
            "name": "Friendly",
            "emoji": "😊",
            "description": "Warm, supportive, and empathetic",
            "greeting": "Hi there! It's so nice to meet you. How are you feeling today?",
            "tone": "warm and supportive"
        },
        "witty": {
            "name": "Witty",
            "emoji": "😏",
            "description": "Humorous, clever, and playful",
            "greeting": "Hey! Ready for some conversation with a side of wit? How's your day going?",
            "tone": "light-hearted and humorous"
        },
        "calm": {
            "name": "Cal<PERSON>",
            "emoji": "😌",
            "description": "Serene, peaceful, and reassuring",
            "greeting": "Hello. I'm here to provide a calm space for conversation. How are you today?",
            "tone": "peaceful and reassuring"
        },
        "motivational": {
            "name": "Motivational",
            "emoji": "💪",
            "description": "Inspiring, encouraging, and positive",
            "greeting": "Hello! I'm excited to chat with you today. What goals are you working towards?",
            "tone": "encouraging and positive"
        },
        "thoughtful": {
            "name": "Thoughtful",
            "emoji": "🤔",
            "description": "Reflective, philosophical, and insightful",
            "greeting": "Hello. I'm looking forward to our conversation. What's been on your mind lately?",
            "tone": "reflective and insightful"
        }
    }

    # Common responses for different topics
    TOPIC_RESPONSES = {
        "greeting": [
            "Hello! How are you today?",
            "Hi there! It's nice to talk with you.",
            "Hey! How's your day going?",
            "Greetings! How are you feeling today?",
            "Hello! I'm happy to chat with you."
        ],
        "feeling_good": [
            "I'm glad to hear you're doing well!",
            "That's wonderful to hear!",
            "Great! What's been going well for you?",
            "Excellent! Anything special happening today?",
            "That's fantastic! What's making you feel good?"
        ],
        "feeling_bad": [
            "I'm sorry to hear that. Would you like to talk about it?",
            "That sounds difficult. What's been going on?",
            "I'm here for you. What's troubling you?",
            "I'm sorry you're feeling that way. Is there anything specific that's bothering you?",
            "That's tough. Would sharing more about it help?"
        ],
        "weather": [
            "The weather can really affect our mood, can't it?",
            "Do you enjoy this kind of weather?",
            "What's your favorite type of weather?",
            "Weather talk - a classic conversation starter! How does this weather make you feel?",
            "Do you have any plans that depend on the weather?"
        ],
        "hobby": [
            "That sounds like a wonderful hobby! How did you get started with it?",
            "I'd love to hear more about that. What do you enjoy most about it?",
            "How long have you been interested in that?",
            "That's fascinating! What drew you to that hobby?",
            "Do you have any other hobbies or interests?"
        ],
        "work": [
            "How do you feel about your work?",
            "What aspects of your job do you find most fulfilling?",
            "Work can be such a big part of our lives. How's your work-life balance?",
            "What's been challenging at work lately?",
            "Have you always wanted to do the kind of work you're doing now?"
        ],
        "family": [
            "Family relationships can be so important. How close are you with your family?",
            "Do you have any family traditions you particularly enjoy?",
            "What values from your family have shaped who you are today?",
            "Family dynamics can be complex. How would you describe yours?",
            "What's a favorite memory you have with your family?"
        ],
        "thanks": [
            "You're welcome! I'm happy to chat anytime.",
            "It's my pleasure! I enjoy our conversations.",
            "Anytime! That's what I'm here for.",
            "You're very welcome. Is there anything else on your mind?",
            "No problem at all! I'm glad I could help."
        ],
        "goodbye": [
            "Goodbye! I hope we can chat again soon.",
            "Take care! I'll be here when you want to talk again.",
            "Farewell for now! Have a wonderful day.",
            "Until next time! Take good care of yourself.",
            "Goodbye! Remember, I'm always here if you need someone to talk to."
        ],
        "fallback": [
            "That's interesting. Tell me more about that.",
            "I'd like to hear more about your thoughts on that.",
            "How does that make you feel?",
            "What else comes to mind when you think about that?",
            "Could you share more about what that means to you?"
        ]
    }

    def __init__(self, personality: str = "friendly", conversation_memory: int = 5):
        """
        Initialize the AI conversation handler.

        Args:
            personality: Personality type (friendly, witty, calm, etc.)
            conversation_memory: Number of previous exchanges to remember
        """
        self.set_personality(personality)
        self.conversation_memory = conversation_memory
        self.conversation_history = []
        logger.info(f"Initialized AI conversation with {personality} personality")

    def set_personality(self, personality: str) -> None:
        """
        Set the AI personality.

        Args:
            personality: Personality type (friendly, witty, calm, etc.)
        """
        if personality in self.PERSONALITIES:
            self.personality = personality
            self.personality_data = self.PERSONALITIES[personality]
            logger.info(f"Set AI personality to {personality}")
        else:
            self.personality = "friendly"
            self.personality_data = self.PERSONALITIES["friendly"]
            logger.warning(f"Unknown personality '{personality}'. Defaulting to 'friendly'")

    def get_greeting(self) -> str:
        """
        Get a greeting message based on the current personality.

        Returns:
            str: Greeting message
        """
        return self.personality_data["greeting"]

    def get_available_personalities(self) -> Dict[str, Dict[str, str]]:
        """
        Get available personality types.

        Returns:
            Dict of personality types and their descriptions
        """
        return self.PERSONALITIES

    def _detect_topic(self, message: str) -> str:
        """
        Detect the topic of a message.

        Args:
            message: User message

        Returns:
            str: Detected topic
        """
        message = message.lower()

        # Check for greetings
        if re.search(r'\b(hi|hello|hey|greetings|howdy)\b', message):
            return "greeting"

        # Check for positive feelings
        if re.search(r'\b(good|great|happy|excellent|wonderful|fantastic|amazing)\b', message) and \
           re.search(r'\b(i am|i\'m|im|i feel|feeling)\b', message):
            return "feeling_good"

        # Check for negative feelings
        if re.search(r'\b(bad|sad|depressed|unhappy|terrible|awful|miserable)\b', message) and \
           re.search(r'\b(i am|i\'m|im|i feel|feeling)\b', message):
            return "feeling_bad"

        # Check for weather talk
        if re.search(r'\b(weather|rain|sunny|snow|cold|hot|warm|temperature)\b', message):
            return "weather"

        # Check for hobby talk
        if re.search(r'\b(hobby|hobbies|interest|interests|passion|enjoy|like to)\b', message):
            return "hobby"

        # Check for work talk
        if re.search(r'\b(work|job|career|profession|office|boss|colleague)\b', message):
            return "work"

        # Check for family talk
        if re.search(r'\b(family|parent|mother|father|mom|dad|brother|sister|sibling)\b', message):
            return "family"

        # Check for thanks
        if re.search(r'\b(thanks|thank you|appreciate|grateful)\b', message):
            return "thanks"

        # Check for goodbye
        if re.search(r'\b(bye|goodbye|farewell|see you|talk to you later)\b', message):
            return "goodbye"

        # Default fallback
        return "fallback"

    def get_response(self, message: str) -> str:
        """
        Get a response to a user message.

        Args:
            message: User message

        Returns:
            str: AI response
        """
        # Add message to conversation history
        self.conversation_history.append({"role": "user", "message": message, "timestamp": datetime.now().isoformat()})

        # Trim conversation history if needed
        if len(self.conversation_history) > self.conversation_memory * 2:
            self.conversation_history = self.conversation_history[-self.conversation_memory * 2:]

        # Detect topic
        topic = self._detect_topic(message)

        # Get response based on topic and personality
        responses = self.TOPIC_RESPONSES.get(topic, self.TOPIC_RESPONSES["fallback"])
        response = random.choice(responses)

        # Adjust response based on personality
        if self.personality == "witty":
            response = self._add_wit(response)
        elif self.personality == "calm":
            response = self._add_calm(response)
        elif self.personality == "motivational":
            response = self._add_motivation(response)
        elif self.personality == "thoughtful":
            response = self._add_thoughtfulness(response)

        # Add response to conversation history
        self.conversation_history.append({"role": "assistant", "message": response, "timestamp": datetime.now().isoformat()})

        return response

    def _add_wit(self, response: str) -> str:
        """Add wit to a response."""
        witty_additions = [
            " I'd give that a solid 10 on the conversation scale!",
            " Not to brag, but I'm pretty good at this talking thing.",
            " If I had a penny for every great conversation like this... I'd have exactly one penny so far!",
            " Just between us, I think we're having the best chat in the digital universe right now.",
            " I'd high-five you if I had hands!"
        ]

        # 50% chance to add wit
        if random.random() < 0.5:
            return response + random.choice(witty_additions)
        return response

    def _add_calm(self, response: str) -> str:
        """Add calmness to a response."""
        calm_additions = [
            " Take a deep breath and notice how you feel right now.",
            " Remember to be gentle with yourself today.",
            " It's okay to take things one moment at a time.",
            " Finding moments of peace in each day is so important.",
            " I'm here with you in this moment."
        ]

        # 50% chance to add calmness
        if random.random() < 0.5:
            return response + random.choice(calm_additions)
        return response

    def _add_motivation(self, response: str) -> str:
        """Add motivation to a response."""
        motivational_additions = [
            " You've got this!",
            " I believe in your ability to overcome any challenge.",
            " Every step forward is progress, no matter how small.",
            " Your potential is limitless!",
            " Today is full of possibilities waiting for you."
        ]

        # 50% chance to add motivation
        if random.random() < 0.5:
            return response + random.choice(motivational_additions)
        return response

    def _add_thoughtfulness(self, response: str) -> str:
        """Add thoughtfulness to a response."""
        thoughtful_additions = [
            " It's fascinating how our conversations shape our understanding of ourselves.",
            " I wonder how this moment fits into the broader tapestry of your day.",
            " The questions we ask often reveal more than the answers we seek.",
            " Sometimes the most meaningful insights come from everyday conversations.",
            " I find it remarkable how words can bridge the gap between different experiences."
        ]

        # 50% chance to add thoughtfulness
        if random.random() < 0.5:
            return response + random.choice(thoughtful_additions)
        return response

    def get_conversation_history(self) -> List[Dict[str, Any]]:
        """
        Get the conversation history.

        Returns:
            List of conversation exchanges
        """
        return self.conversation_history

    def clear_conversation_history(self) -> None:
        """Clear the conversation history."""
        self.conversation_history = []
        logger.info("Cleared conversation history")


