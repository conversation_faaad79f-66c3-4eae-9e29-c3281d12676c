"""
<PERSON><PERSON><PERSON> to update imports in the codebase to use enhanced implementations.

This script updates import statements in the codebase to use the enhanced
implementations of memory manager and dialog engine.
"""

import os
import sys
import re
import logging
from typing import Dict, List, Set, Tuple, Any
import importlib
import inspect

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# Define import replacements
IMPORT_REPLACEMENTS = {
    # Memory manager replacements
    "from bot.features.enhanced_memory_manager import EnhancedMemoryManager as MemoryManager": 
        "from bot.features.enhanced_memory_manager import EnhancedMemoryManager as MemoryManager",
    "import bot.features.memory_manager": 
        "import bot.features.enhanced_memory_manager as memory_manager",
    "from bot.features import memory_manager": 
        "from bot.features import enhanced_memory_manager as memory_manager",
    
    # Dialog engine replacements
    "from bot.core.enhanced_dialog_engine import EnhancedDialogEngine as DialogEngine": 
        "from bot.core.enhanced_dialog_engine import EnhancedDialogEngine as DialogEngine",
    "import bot.core.dialog_engine": 
        "import bot.core.enhanced_dialog_engine as dialog_engine",
    "from bot.core import dialog_engine": 
        "from bot.core import enhanced_dialog_engine as dialog_engine",
}

# Define class replacements
CLASS_REPLACEMENTS = {
    "MemoryManager": "EnhancedMemoryManager",
    "DialogEngine": "EnhancedDialogEngine",
}

def find_python_files(directory: str) -> List[str]:
    """
    Find all Python files in a directory.
    
    Args:
        directory: Directory to search
        
    Returns:
        List of file paths
    """
    python_files = []
    
    for root, _, files in os.walk(directory):
        for file in files:
            if file.endswith(".py"):
                python_files.append(os.path.join(root, file))
    
    return python_files

def update_imports_in_file(file_path: str, replacements: Dict[str, str]) -> bool:
    """
    Update imports in a file.
    
    Args:
        file_path: Path to the file
        replacements: Dict mapping old imports to new imports
        
    Returns:
        bool: True if file was updated, False otherwise
    """
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()
        
        updated_content = content
        for old_import, new_import in replacements.items():
            updated_content = updated_content.replace(old_import, new_import)
        
        if updated_content != content:
            # Write updated content
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(updated_content)
            
            logger.info(f"Updated imports in {file_path}")
            return True
        
        return False
    except Exception as e:
        logger.error(f"Error updating imports in {file_path}: {e}")
        return False

def update_class_references(file_path: str, replacements: Dict[str, str]) -> bool:
    """
    Update class references in a file.
    
    Args:
        file_path: Path to the file
        replacements: Dict mapping old class names to new class names
        
    Returns:
        bool: True if file was updated, False otherwise
    """
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()
        
        updated_content = content
        for old_class, new_class in replacements.items():
            # Replace class instantiations
            pattern = rf"(\s|=|,|\(|^){old_class}\("
            replacement = rf"\1{new_class}("
            updated_content = re.sub(pattern, replacement, updated_content)
            
            # Replace isinstance checks
            pattern = rf"isinstance\(\s*\w+\s*,\s*{old_class}\s*\)"
            replacement = f"isinstance(\\1, {new_class})"
            updated_content = re.sub(pattern, replacement, updated_content)
        
        if updated_content != content:
            # Write updated content
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(updated_content)
            
            logger.info(f"Updated class references in {file_path}")
            return True
        
        return False
    except Exception as e:
        logger.error(f"Error updating class references in {file_path}: {e}")
        return False

def update_imports_in_directory(directory: str) -> Tuple[int, int]:
    """
    Update imports in all Python files in a directory.
    
    Args:
        directory: Directory to search
        
    Returns:
        Tuple containing counts of files updated and errors
    """
    files_updated = 0
    errors = 0
    
    try:
        # Find all Python files
        python_files = find_python_files(directory)
        logger.info(f"Found {len(python_files)} Python files in {directory}")
        
        # Update imports in each file
        for file_path in python_files:
            try:
                # Skip the enhanced implementations themselves
                if "enhanced_memory_manager.py" in file_path or "enhanced_dialog_engine.py" in file_path:
                    continue
                
                # Update imports
                if update_imports_in_file(file_path, IMPORT_REPLACEMENTS):
                    files_updated += 1
                
                # Update class references
                if update_class_references(file_path, CLASS_REPLACEMENTS):
                    files_updated += 1
            except Exception as e:
                logger.error(f"Error processing {file_path}: {e}")
                errors += 1
        
        return files_updated, errors
    except Exception as e:
        logger.error(f"Error updating imports in {directory}: {e}")
        return files_updated, errors + 1

def main():
    """Main function."""
    logger.info("Starting import updates for enhanced implementations")
    
    # Update imports in bot directory
    files_updated, errors = update_imports_in_directory("bot")
    
    logger.info(f"Import updates completed:")
    logger.info(f"  - Files updated: {files_updated}")
    logger.info(f"  - Errors: {errors}")
    
    if errors > 0:
        logger.warning("Updates completed with errors. Check the log for details.")
        return 1
    
    logger.info("Updates completed successfully")
    return 0

if __name__ == "__main__":
    sys.exit(main())
