"""
Telegram Stars payment provider for VoicePal.

This module implements the Telegram Stars payment system for VoicePal.
"""

import logging
import uuid
import traceback
from typing import Dict, List, Optional, Any, Tuple, Union
from datetime import datetime

from telegram import (
    LabeledPrice,
    InlineKeyboardButton,
    InlineKeyboardMarkup,
    Update,
    PreCheckoutQuery,
    SuccessfulPayment
)
from telegram.ext import ContextTypes
from telegram.error import TelegramError

from bot.database.transaction import Transaction
from bot.payment.payment_provider_interface import PaymentProviderInterface

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class TelegramStarsPayment(PaymentProviderInterface):
    """Telegram Stars payment handler for VoicePal."""

    # Define credit packages
    CREDIT_PACKAGES = {
        "small": {
            "id": "small",
            "title": "100 Credits",
            "description": "100 credits for conversations",
            "credits": 100,
            "price": 100,  # in Stars
            "currency": "XTR"  # Telegram Stars currency code
        },
        "medium": {
            "id": "medium",
            "title": "300 Credits",
            "description": "300 credits for conversations",
            "credits": 300,
            "price": 250,  # in Stars
            "currency": "XTR"
        },
        "large": {
            "id": "large",
            "title": "1000 Credits",
            "description": "1000 credits for conversations",
            "credits": 1000,
            "price": 750,  # in Stars
            "currency": "XTR"
        }
    }

    def __init__(self, database, config_manager=None, provider_token=None, digital_goods_mode=False):
        """
        Initialize the Telegram Stars payment system.

        Args:
            database: Database instance for storing transactions
            config_manager: Configuration manager instance
            provider_token: Optional provider token for payments
            digital_goods_mode: Whether to operate in digital goods mode (no provider token)
        """
        self.database = database
        self.config_manager = config_manager
        self.digital_goods_mode = digital_goods_mode

        # For Telegram Stars (digital goods), we don't need a provider token
        # It can be left empty according to Telegram documentation
        # However, we'll store it if provided for flexibility
        self.provider_token = provider_token or ""

        if provider_token:
            logger.info("Initialized Telegram Stars payment system with provider token")
        elif digital_goods_mode:
            logger.info("Initialized Telegram Stars payment system for digital goods - no provider token needed")
        else:
            logger.warning("Initialized Telegram Stars payment system without provider token and not in digital goods mode")

    def get_credit_packages(self) -> Dict[str, Dict[str, Any]]:
        """
        Get available credit packages.

        Returns:
            Dict of credit packages
        """
        return self.CREDIT_PACKAGES

    def get_credit_package(self, package_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a specific credit package.

        Args:
            package_id: ID of the credit package

        Returns:
            Dict containing credit package details or None if not found
        """
        return self.CREDIT_PACKAGES.get(package_id)

    def get_payment_keyboard(self) -> InlineKeyboardMarkup:
        """
        Get keyboard with payment options.

        Returns:
            InlineKeyboardMarkup with payment options
        """
        keyboard = []

        # Add a button for each credit package
        for package_id, package in self.CREDIT_PACKAGES.items():
            button_text = f"Buy {package['credits']} credits - {package['price']} Stars"
            keyboard.append([InlineKeyboardButton(button_text, callback_data=f"buy_credits_{package_id}")])

        # Add back button
        keyboard.append([InlineKeyboardButton("🔙 Back to Menu", callback_data="back_to_main")])

        return InlineKeyboardMarkup(keyboard)

    async def start_payment(self, update: Update, context: ContextTypes.DEFAULT_TYPE, package_id: str) -> None:
        """
        Start the payment process for a credit package.

        Args:
            update: Telegram update
            context: Callback context
            package_id: ID of the credit package
        """
        package = self.get_credit_package(package_id)
        if not package:
            await update.callback_query.message.reply_text(
                "Invalid package. Please choose a valid package."
            )
            return

        # Create a unique payload for this transaction
        payload = f"credits-{package_id}-{uuid.uuid4()}"

        # Get package details
        title = package["title"]
        description = package["description"]
        price = package["price"]
        currency = package["currency"]

        # Create labeled price
        prices = [LabeledPrice(title, price)]

        try:
            # Ensure user exists in the database
            user_id = update.callback_query.from_user.id
            user = self.database.get_user(user_id)

            if not user:
                # Add user to database if not exists
                self.database.add_user(
                    user_id=user_id,
                    username=update.callback_query.from_user.username,
                    first_name=update.callback_query.from_user.first_name,
                    last_name=update.callback_query.from_user.last_name
                )
                logger.info(f"Added user {user_id} to database before processing payment")

            # Record the invoice in the database
            self.database.add_invoice(
                user_id=user_id,
                package_id=package_id,
                payload=payload,
                amount=price,
                currency=currency,
                status="pending"
            )

            # For Telegram Stars (digital goods), we don't need a provider token
            logger.info("Using Telegram Stars for digital goods payment")

            try:
                # Send invoice - for digital goods (Telegram Stars), we use XTR currency
                # Prepare invoice parameters
                invoice_params = {
                    "chat_id": update.callback_query.from_user.id,
                    "title": title,
                    "description": description,
                    "payload": payload,
                    "currency": "XTR",  # Telegram Stars currency code
                    "prices": prices,
                    "start_parameter": f"credits-{package_id}",
                    "photo_url": "https://i.imgur.com/Nh9Pzw6.png",  # VoicePal Stars payment image
                    "photo_width": 512,
                    "photo_height": 512,
                    "need_name": False,
                    "need_phone_number": False,
                    "need_email": False,
                    "need_shipping_address": False,
                    "is_flexible": False
                }

                # Add provider_token only if we're not in digital_goods_mode or if it's explicitly provided
                if not self.digital_goods_mode or self.provider_token:
                    invoice_params["provider_token"] = self.provider_token
                    logger.info(f"Using provider token for payment: {self.provider_token[:5]}...")
                else:
                    logger.info("Using digital goods mode without provider token")

                # Send the invoice with the prepared parameters
                await context.bot.send_invoice(**invoice_params)
                logger.info(f"Invoice sent successfully to user {update.callback_query.from_user.id}")
            except Exception as e:
                logger.error(f"Error sending invoice: {e}")
                await update.callback_query.message.reply_text(
                    "Sorry, there was an error processing your payment request. Please try again later."
                )
            logger.info(f"Payment invoice sent to user {update.callback_query.from_user.id} for package {package_id}")
        except TelegramError as e:
            logger.error(f"Error sending invoice: {e}")
            await update.callback_query.message.reply_text(
                "Sorry, there was an error processing your payment request. Please try again later."
            )

    def validate_pre_checkout(self, query: PreCheckoutQuery) -> bool:
        """
        Validate pre-checkout query.

        Args:
            query: Pre-checkout query

        Returns:
            bool: True if valid, False otherwise
        """
        try:
            # Extract package_id from payload
            payload_parts = query.invoice_payload.split('-')
            if len(payload_parts) < 2 or payload_parts[0] != "credits":
                logger.error("Invalid payment payload: %s", query.invoice_payload)
                return False

            package_id = payload_parts[1]

            # Check if package exists
            if package_id not in self.CREDIT_PACKAGES:
                logger.error("Invalid package_id in pre-checkout: %s", package_id)
                return False

            return True
        except Exception as e:
            logger.error("Error validating pre-checkout: %s", e)
            return False

    async def precheckout_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """
        Answer the PreCheckoutQuery.

        Args:
            update: Telegram update
            context: Callback context
        """
        query = update.pre_checkout_query

        # Check if the invoice payload is valid
        try:
            # Extract package_id from payload
            payload_parts = query.invoice_payload.split('-')
            if len(payload_parts) < 2 or payload_parts[0] != "credits":
                await query.answer(ok=False, error_message="Invalid payment payload.")
                return

            package_id = payload_parts[1]

            # Check if package exists
            if package_id not in self.CREDIT_PACKAGES:
                await query.answer(ok=False, error_message="Invalid credit package.")
                return

            # Update invoice status in database
            self.database.update_invoice_status(
                payload=query.invoice_payload,
                status="pre_checkout"
            )

            # Answer pre-checkout query
            await query.answer(ok=True)
            logger.info(f"Pre-checkout approved for user {query.from_user.id}, payload: {query.invoice_payload}")
        except Exception as e:
            logger.error(f"Error in pre-checkout: {e}")
            await query.answer(ok=False, error_message="An error occurred. Please try again later.")

    def process_payment(self, user_id: int, payment: SuccessfulPayment) -> int:
        """
        Process successful payment.

        Args:
            user_id: User ID
            payment: Successful payment

        Returns:
            int: Number of credits added
        """
        try:
            # Extract package_id from payload
            payload = payment.invoice_payload
            payload_parts = payload.split('-')

            if len(payload_parts) < 2 or payload_parts[0] != "credits":
                logger.error("Invalid payload format: %s", payload)
                return 0

            package_id = payload_parts[1]

            if package_id not in self.CREDIT_PACKAGES:
                logger.error("Invalid package ID: %s", package_id)
                return 0

            package = self.CREDIT_PACKAGES[package_id]

            # Use transaction context manager to ensure atomicity
            with Transaction(self.database.conn):
                # Add credits to user account
                new_credits = self.database.add_credits(user_id, package["credits"], source="purchase")

                # Record transaction
                self.database.add_transaction(
                    user_id=user_id,
                    amount=payment.total_amount,
                    credits=package["credits"],
                    transaction_id=payment.telegram_payment_charge_id,
                    status="completed"
                )

                # Update invoice status
                self.database.update_invoice_status(
                    payload=payload,
                    status="completed"
                )

            logger.info("Payment successful for user %s, package: %s, credits: %s",
                       user_id, package_id, package["credits"])

            return package["credits"]
        except Exception as e:
            logger.error("Error processing payment: %s", e)
            logger.error(traceback.format_exc())
            return 0

    async def successful_payment_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """
        Handle successful payment.

        Args:
            update: Telegram update
            context: Callback context
        """
        payment = update.message.successful_payment
        payload = payment.invoice_payload
        user_id = update.effective_user.id

        try:
            # Extract package_id from payload
            payload_parts = payload.split('-')
            if len(payload_parts) < 2 or payload_parts[0] != "credits":
                logger.error(f"Invalid payload format: {payload}")
                await update.message.reply_text(
                    "There was an error processing your payment. Please contact support."
                )
                return

            package_id = payload_parts[1]

            if package_id in self.CREDIT_PACKAGES:
                package = self.CREDIT_PACKAGES[package_id]

                try:
                    # Use transaction context manager to ensure atomicity
                    with Transaction(self.database.conn):
                        # Add credits to user account
                        new_credits = self.database.add_credits(user_id, package["credits"], source="purchase")

                        # Record transaction
                        self.database.add_transaction(
                            user_id=user_id,
                            amount=payment.total_amount,
                            credits=package["credits"],
                            transaction_id=payment.telegram_payment_charge_id,
                            status="completed"
                        )

                        # Update invoice status
                        self.database.update_invoice_status(
                            payload=payload,
                            status="completed"
                        )

                    await update.message.reply_text(
                        f"🎉 Payment successful! {package['credits']} credits have been added to your account.\n\n"
                        f"Your current balance: {new_credits} credits\n\n"
                        f"Thank you for supporting VoicePal! Enjoy your conversations."
                    )

                    logger.info(f"Payment successful for user {user_id}, package: {package_id}, "
                               f"credits: {package['credits']}, charge_id: {payment.telegram_payment_charge_id}")
                except Exception as e:
                    logger.error(f"Database error processing payment: {e}")
                    logger.error(traceback.format_exc())
                    await update.message.reply_text(
                        "There was an error processing your payment. Our team has been notified and will resolve this issue. "
                        "Please contact support with your payment ID: " + payment.telegram_payment_charge_id
                    )
            else:
                logger.error(f"Invalid package ID: {package_id}")
                await update.message.reply_text(
                    "There was an error processing your payment. Please contact support."
                )
        except Exception as e:
            logger.error(f"Error processing successful payment: {e}")
            logger.error(traceback.format_exc())
            await update.message.reply_text(
                "There was an error processing your payment. Please contact support."
            )

    async def create_invoice(self, chat_id: int, title: str, description: str, payload: str, price_cents: int, context: ContextTypes.DEFAULT_TYPE) -> bool:
        """
        Create and send an invoice.

        Args:
            chat_id: Chat ID to send the invoice to
            title: Invoice title
            description: Invoice description
            payload: Invoice payload
            price_cents: Price in cents
            context: Callback context

        Returns:
            bool: True if invoice was sent successfully, False otherwise
        """
        try:
            # For Telegram Stars (digital goods), we don't need a provider token
            # Create labeled price
            prices = [LabeledPrice(title, price_cents)]

            # Send invoice - for digital goods (Telegram Stars), we use XTR currency
            # Prepare invoice parameters
            invoice_params = {
                "chat_id": chat_id,
                "title": title,
                "description": description,
                "payload": payload,
                "currency": "XTR",  # Telegram Stars currency code
                "prices": prices,
                "start_parameter": f"credits_{price_cents}",
                "photo_url": "https://i.imgur.com/Nh9Pzw6.png",  # VoicePal Stars payment image
                "photo_width": 512,
                "photo_height": 512,
                "need_name": False,
                "need_phone_number": False,
                "need_email": False,
                "need_shipping_address": False,
                "is_flexible": False
            }

            # Add provider_token only if we're not in digital_goods_mode or if it's explicitly provided
            if not self.digital_goods_mode or self.provider_token:
                invoice_params["provider_token"] = self.provider_token
                logger.info(f"Using provider token for payment: {self.provider_token[:5] if self.provider_token else 'empty'}...")
            else:
                logger.info("Using digital goods mode without provider token")

            # Send the invoice with the prepared parameters
            await context.bot.send_invoice(**invoice_params)
            logger.info("Invoice sent successfully to user %s", chat_id)
            return True
        except Exception as e:
            logger.error("Error sending invoice: %s", e)
            return False

    async def refund_payment(self, user_id: int, transaction_id: str) -> bool:
        """
        Refund a payment.

        Args:
            user_id: User ID
            transaction_id: Transaction ID

        Returns:
            bool: True if refund was successful, False otherwise
        """
        try:
            # Get transaction details
            transaction = self.database.get_transaction(transaction_id)
            if not transaction:
                logger.error("Transaction not found: %s", transaction_id)
                return False

            # Check if transaction belongs to user
            if transaction["user_id"] != user_id:
                logger.error("Transaction %s does not belong to user %s", transaction_id, user_id)
                return False

            # Check if transaction is completed
            if transaction["status"] != "completed":
                logger.error("Cannot refund transaction %s with status %s",
                            transaction_id, transaction["status"])
                return False

            # Check if user has enough credits
            user = self.database.get_user(user_id)
            if user["credits"] < transaction["credits"]:
                logger.error("User %s does not have enough credits for refund", user_id)
                return False

            try:
                # Use transaction context manager to ensure atomicity
                with Transaction(self.database.conn):
                    # Deduct credits from user
                    new_credits = self.database.deduct_credits(user_id, transaction["credits"])

                    # Record refund transaction
                    self.database.add_transaction(
                        user_id=user_id,
                        amount=-transaction["amount"],
                        credits=-transaction["credits"],
                        transaction_id=f"refund-{transaction_id}",
                        status="refunded"
                    )

                    # Update original transaction status
                    self.database.update_transaction_status(
                        transaction_id=transaction_id,
                        status="refunded"
                    )

                logger.info("Payment refunded for user %s, transaction: %s", user_id, transaction_id)
                return True
            except Exception as db_error:
                logger.error("Database error during refund: %s", db_error)
                logger.error(traceback.format_exc())
                return False
        except Exception as e:
            logger.error("Error refunding payment: %s", e)
            logger.error(traceback.format_exc())
            return False
