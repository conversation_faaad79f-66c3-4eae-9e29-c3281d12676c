"""
<PERSON><PERSON><PERSON> to verify imports of the modules we've modified.
"""

import os
import sys
import logging
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

def verify_imports():
    """Verify imports of the modules we've modified."""
    logger.info("Verifying imports...")
    
    # List of modules to verify
    modules = [
        "bot.providers.voice.processor",
        "bot.providers.tts.deepgram_provider",
        "bot.features.mood_entry",
        "bot.core.navigation_router",
        "bot.providers.ai.google_ai_provider",
        "bot.core.enhanced_dialog_engine",
        "bot.features.enhanced_memory_manager"
    ]
    
    # Verify each module
    for module_name in modules:
        try:
            logger.info(f"Importing {module_name}...")
            __import__(module_name)
            logger.info(f"Successfully imported {module_name}")
        except Exception as e:
            logger.error(f"Error importing {module_name}: {e}")
    
    logger.info("Import verification completed")

if __name__ == "__main__":
    verify_imports()
