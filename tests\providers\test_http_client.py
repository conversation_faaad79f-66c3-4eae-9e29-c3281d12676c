"""
Tests for HTTP client.

This module tests the HTTPClient class.
"""

import pytest
import httpx
from unittest.mock import MagicMock, patch, AsyncMock

from bot.providers.core.http_client import HTTPClient
from bot.providers.core.exceptions import (
    ProviderAPIError,
    ProviderTimeoutError,
    ProviderAuthenticationError,
    ProviderRateLimitError,
    ProviderServiceUnavailableError
)

@pytest.mark.asyncio
async def test_init():
    """Test HTTP client initialization."""
    client = HTTPClient(base_url="https://api.example.com", timeout=60.0)
    
    assert client.base_url == "https://api.example.com"
    assert client.timeout == 60.0
    assert "User-Agent" in client.default_headers
    assert "Accept" in client.default_headers

@pytest.mark.asyncio
async def test_build_url():
    """Test URL building."""
    client = HTTPClient(base_url="https://api.example.com")
    
    # Test with base URL
    url = client._build_url("endpoint")
    assert url == "https://api.example.com/endpoint"
    
    # Test with absolute URL
    url = client._build_url("https://other.example.com/endpoint")
    assert url == "https://other.example.com/endpoint"
    
    # Test without base URL
    client = HTTPClient()
    url = client._build_url("https://api.example.com/endpoint")
    assert url == "https://api.example.com/endpoint"

@pytest.mark.asyncio
async def test_request_success(mock_httpx_client):
    """Test successful request."""
    client = HTTPClient(base_url="https://api.example.com")
    
    with patch("httpx.AsyncClient", return_value=mock_httpx_client):
        response = await client.request(
            method="GET",
            endpoint="test",
            headers={"X-Test": "test"},
            params={"param": "value"}
        )
    
    assert response == {"result": "success"}
    mock_httpx_client.request.assert_called_once_with(
        method="GET",
        url="https://api.example.com/test",
        headers={"User-Agent": "VoicePal/1.0", "Accept": "application/json", "X-Test": "test"},
        params={"param": "value"},
        json=None,
        data=None
    )

@pytest.mark.asyncio
async def test_request_authentication_error(mock_httpx_client):
    """Test authentication error."""
    client = HTTPClient()
    
    # Set up mock response
    mock_response = MagicMock()
    mock_response.status_code = 401
    mock_response.text = "Unauthorized"
    mock_response.headers = {}
    mock_httpx_client.request.return_value = mock_response
    
    with patch("httpx.AsyncClient", return_value=mock_httpx_client):
        with pytest.raises(ProviderAuthenticationError):
            await client.request(method="GET", endpoint="test")

@pytest.mark.asyncio
async def test_request_rate_limit_error(mock_httpx_client):
    """Test rate limit error with retry."""
    client = HTTPClient()
    
    # Set up mock responses
    rate_limit_response = MagicMock()
    rate_limit_response.status_code = 429
    rate_limit_response.text = "Rate limit exceeded"
    rate_limit_response.headers = {"Retry-After": "1"}
    
    success_response = MagicMock()
    success_response.status_code = 200
    success_response.text = '{"result": "success"}'
    success_response.json.return_value = {"result": "success"}
    success_response.headers = {}
    
    # First call returns rate limit, second call succeeds
    mock_httpx_client.request.side_effect = [rate_limit_response, success_response]
    
    with patch("httpx.AsyncClient", return_value=mock_httpx_client):
        with patch("asyncio.sleep", AsyncMock()) as mock_sleep:
            response = await client.request(method="GET", endpoint="test")
    
    assert response == {"result": "success"}
    assert mock_httpx_client.request.call_count == 2
    mock_sleep.assert_called_once_with(1)

@pytest.mark.asyncio
async def test_request_rate_limit_error_max_retries(mock_httpx_client):
    """Test rate limit error with max retries exceeded."""
    client = HTTPClient()
    
    # Set up mock response
    rate_limit_response = MagicMock()
    rate_limit_response.status_code = 429
    rate_limit_response.text = "Rate limit exceeded"
    rate_limit_response.headers = {"Retry-After": "1"}
    
    # All calls return rate limit
    mock_httpx_client.request.return_value = rate_limit_response
    
    with patch("httpx.AsyncClient", return_value=mock_httpx_client):
        with patch("asyncio.sleep", AsyncMock()):
            with pytest.raises(ProviderRateLimitError):
                await client.request(method="GET", endpoint="test", retries=3)
    
    assert mock_httpx_client.request.call_count == 3

@pytest.mark.asyncio
async def test_request_server_error(mock_httpx_client):
    """Test server error with retry."""
    client = HTTPClient()
    
    # Set up mock responses
    server_error_response = MagicMock()
    server_error_response.status_code = 500
    server_error_response.text = "Internal server error"
    server_error_response.headers = {}
    
    success_response = MagicMock()
    success_response.status_code = 200
    success_response.text = '{"result": "success"}'
    success_response.json.return_value = {"result": "success"}
    success_response.headers = {}
    
    # First call returns server error, second call succeeds
    mock_httpx_client.request.side_effect = [server_error_response, success_response]
    
    with patch("httpx.AsyncClient", return_value=mock_httpx_client):
        with patch("asyncio.sleep", AsyncMock()) as mock_sleep:
            response = await client.request(method="GET", endpoint="test")
    
    assert response == {"result": "success"}
    assert mock_httpx_client.request.call_count == 2
    mock_sleep.assert_called_once_with(1)

@pytest.mark.asyncio
async def test_request_client_error(mock_httpx_client):
    """Test client error."""
    client = HTTPClient()
    
    # Set up mock response
    client_error_response = MagicMock()
    client_error_response.status_code = 400
    client_error_response.text = "Bad request"
    client_error_response.headers = {}
    
    mock_httpx_client.request.return_value = client_error_response
    
    with patch("httpx.AsyncClient", return_value=mock_httpx_client):
        with pytest.raises(ProviderAPIError):
            await client.request(method="GET", endpoint="test")

@pytest.mark.asyncio
async def test_request_timeout(mock_httpx_client):
    """Test request timeout."""
    client = HTTPClient()
    
    # Set up mock to raise timeout
    mock_httpx_client.request.side_effect = httpx.TimeoutException("Timeout")
    
    with patch("httpx.AsyncClient", return_value=mock_httpx_client):
        with patch("asyncio.sleep", AsyncMock()):
            with pytest.raises(ProviderTimeoutError):
                await client.request(method="GET", endpoint="test", retries=2)
    
    assert mock_httpx_client.request.call_count == 2

@pytest.mark.asyncio
async def test_request_non_json_response(mock_httpx_client):
    """Test non-JSON response."""
    client = HTTPClient()
    
    # Set up mock response
    text_response = MagicMock()
    text_response.status_code = 200
    text_response.text = "Plain text response"
    text_response.json.side_effect = ValueError("Invalid JSON")
    text_response.headers = {}
    
    mock_httpx_client.request.return_value = text_response
    
    with patch("httpx.AsyncClient", return_value=mock_httpx_client):
        response = await client.request(method="GET", endpoint="test")
    
    assert response == {"text": "Plain text response"}

@pytest.mark.asyncio
async def test_convenience_methods(mock_httpx_client):
    """Test convenience methods."""
    client = HTTPClient()
    
    with patch("httpx.AsyncClient", return_value=mock_httpx_client):
        # Test GET
        await client.get("test", params={"param": "value"})
        mock_httpx_client.request.assert_called_with(
            method="GET",
            url="test",
            headers={"User-Agent": "VoicePal/1.0", "Accept": "application/json"},
            params={"param": "value"},
            json=None,
            data=None
        )
        
        # Test POST
        await client.post("test", json={"data": "value"})
        mock_httpx_client.request.assert_called_with(
            method="POST",
            url="test",
            headers={"User-Agent": "VoicePal/1.0", "Accept": "application/json"},
            params=None,
            json={"data": "value"},
            data=None
        )
        
        # Test PUT
        await client.put("test", data="raw data")
        mock_httpx_client.request.assert_called_with(
            method="PUT",
            url="test",
            headers={"User-Agent": "VoicePal/1.0", "Accept": "application/json"},
            params=None,
            json=None,
            data="raw data"
        )
        
        # Test DELETE
        await client.delete("test")
        mock_httpx_client.request.assert_called_with(
            method="DELETE",
            url="test",
            headers={"User-Agent": "VoicePal/1.0", "Accept": "application/json"},
            params=None,
            json=None,
            data=None
        )
