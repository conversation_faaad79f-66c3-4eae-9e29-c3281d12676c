"""
Integration tests for API endpoints.
"""

import pytest
import tempfile
import os
from fastapi.testclient import Test<PERSON>lient
from unittest.mock import Mock, patch

from bot.api.main import create_app
from bot.database.database_manager import DatabaseManager


@pytest.fixture
def temp_db_path():
    """Create temporary database file."""
    fd, path = tempfile.mkstemp(suffix='.db')
    os.close(fd)
    yield path
    try:
        os.unlink(path)
    except OSError:
        pass


@pytest.fixture
def test_app(temp_db_path):
    """Create test FastAPI application."""
    # Mock configuration
    config = {
        'database': {
            'url': f'sqlite:///{temp_db_path}',
            'pool_size': 5
        },
        'redis': {
            'url': None
        },
        'security': {
            'enable_rate_limiting': False,
            'enable_encryption': False
        }
    }
    
    app = create_app(config)
    return app


@pytest.fixture
def client(test_app):
    """Create test client."""
    return TestClient(test_app)


@pytest.fixture
def mock_database_manager():
    """Create mock database manager."""
    db_manager = Mock(spec=DatabaseManager)
    
    # Mock user data
    db_manager.get_user.return_value = {
        'user_id': 123,
        'username': 'testuser',
        'first_name': 'Test',
        'credits': 100,
        'created_at': '2024-01-01T00:00:00'
    }
    
    db_manager.get_all_users.return_value = [
        {
            'user_id': 123,
            'username': 'testuser',
            'first_name': 'Test',
            'credits': 100,
            'created_at': '2024-01-01T00:00:00'
        }
    ]
    
    # Mock conversation data
    db_manager.get_user_conversations.return_value = [
        {
            'conversation_id': 'conv_123',
            'user_id': 123,
            'created_at': '2024-01-01T00:00:00',
            'message_count': 5
        }
    ]
    
    return db_manager


class TestHealthEndpoints:
    """Test health and status endpoints."""
    
    def test_health_check(self, client):
        """Test health check endpoint."""
        response = client.get("/health")
        assert response.status_code == 200
        
        data = response.json()
        assert data["status"] == "healthy"
        assert "timestamp" in data
        assert "uptime" in data
    
    def test_status_endpoint(self, client):
        """Test detailed status endpoint."""
        with patch('bot.api.endpoints.health.get_system_status') as mock_status:
            mock_status.return_value = {
                "system": {"cpu_percent": 45.0, "memory_percent": 60.0},
                "database": {"status": "connected", "pool_size": 5},
                "redis": {"status": "disconnected"},
                "features": {"analytics": True, "ab_testing": True}
            }
            
            response = client.get("/status")
            assert response.status_code == 200
            
            data = response.json()
            assert "system" in data
            assert "database" in data
            assert "features" in data


class TestUserEndpoints:
    """Test user management endpoints."""
    
    def test_list_users(self, client, mock_database_manager):
        """Test listing users."""
        with patch('bot.api.dependencies.get_database_manager', return_value=mock_database_manager):
            response = client.get("/api/v1/users")
            assert response.status_code == 200
            
            data = response.json()
            assert "users" in data
            assert len(data["users"]) == 1
            assert data["users"][0]["user_id"] == 123
    
    def test_get_user(self, client, mock_database_manager):
        """Test getting specific user."""
        with patch('bot.api.dependencies.get_database_manager', return_value=mock_database_manager):
            response = client.get("/api/v1/users/123")
            assert response.status_code == 200
            
            data = response.json()
            assert data["user_id"] == 123
            assert data["username"] == "testuser"
            assert data["credits"] == 100
    
    def test_get_user_not_found(self, client, mock_database_manager):
        """Test getting non-existent user."""
        mock_database_manager.get_user.return_value = None
        
        with patch('bot.api.dependencies.get_database_manager', return_value=mock_database_manager):
            response = client.get("/api/v1/users/999")
            assert response.status_code == 404
            
            data = response.json()
            assert "User not found" in data["detail"]
    
    def test_update_user(self, client, mock_database_manager):
        """Test updating user."""
        mock_database_manager.update_user.return_value = True
        
        with patch('bot.api.dependencies.get_database_manager', return_value=mock_database_manager):
            update_data = {
                "credits": 150,
                "first_name": "Updated Name"
            }
            
            response = client.put("/api/v1/users/123", json=update_data)
            assert response.status_code == 200
            
            data = response.json()
            assert data["message"] == "User updated successfully"
    
    def test_delete_user(self, client, mock_database_manager):
        """Test deleting user."""
        mock_database_manager.delete_user.return_value = True
        
        with patch('bot.api.dependencies.get_database_manager', return_value=mock_database_manager):
            response = client.delete("/api/v1/users/123")
            assert response.status_code == 200
            
            data = response.json()
            assert data["message"] == "User deleted successfully"


class TestAnalyticsEndpoints:
    """Test analytics endpoints."""
    
    def test_analytics_dashboard(self, client):
        """Test analytics dashboard endpoint."""
        mock_dashboard = {
            "timestamp": "2024-01-01T00:00:00",
            "period_days": 30,
            "user_metrics": {"total_users": 100, "active_users_daily": 20},
            "conversation_metrics": {"total_conversations": 500},
            "business_metrics": {"total_revenue": 1000.0},
            "insights": []
        }
        
        with patch('bot.api.dependencies.get_analytics_manager') as mock_analytics:
            mock_analytics.return_value.get_comprehensive_dashboard.return_value = mock_dashboard
            
            response = client.get("/api/v1/analytics/dashboard")
            assert response.status_code == 200
            
            data = response.json()
            assert data["user_metrics"]["total_users"] == 100
            assert data["business_metrics"]["total_revenue"] == 1000.0
    
    def test_user_analytics(self, client):
        """Test user analytics endpoint."""
        mock_metrics = {
            "total_users": 100,
            "active_users_daily": 20,
            "retention_rate_7d": 0.8,
            "churn_rate": 0.1
        }
        
        with patch('bot.api.dependencies.get_analytics_manager') as mock_analytics:
            mock_analytics.return_value.user_analytics.get_user_metrics.return_value = Mock(**mock_metrics)
            
            response = client.get("/api/v1/analytics/users")
            assert response.status_code == 200
            
            data = response.json()
            assert data["total_users"] == 100
            assert data["active_users_daily"] == 20
    
    def test_conversation_analytics(self, client):
        """Test conversation analytics endpoint."""
        mock_metrics = {
            "total_conversations": 500,
            "avg_conversation_length": 10.5,
            "user_engagement_score": 0.75
        }
        
        with patch('bot.api.dependencies.get_analytics_manager') as mock_analytics:
            mock_analytics.return_value.conversation_analytics.analyze_conversation_patterns.return_value = Mock(**mock_metrics)
            
            response = client.get("/api/v1/analytics/conversations")
            assert response.status_code == 200
            
            data = response.json()
            assert data["total_conversations"] == 500
            assert data["avg_conversation_length"] == 10.5
    
    def test_revenue_analytics(self, client):
        """Test revenue analytics endpoint."""
        mock_metrics = {
            "total_revenue": 1000.0,
            "monthly_recurring_revenue": 500.0,
            "conversion_rate": 0.15,
            "customer_lifetime_value": 300.0
        }
        
        with patch('bot.api.dependencies.get_analytics_manager') as mock_analytics:
            mock_analytics.return_value.business_analytics.get_revenue_metrics.return_value = Mock(**mock_metrics)
            
            response = client.get("/api/v1/analytics/revenue")
            assert response.status_code == 200
            
            data = response.json()
            assert data["total_revenue"] == 1000.0
            assert data["conversion_rate"] == 0.15


class TestABTestEndpoints:
    """Test A/B testing endpoints."""
    
    def test_create_ab_test(self, client):
        """Test creating A/B test."""
        test_data = {
            "name": "Test Feature",
            "description": "Testing new feature",
            "test_type": "feature_flag",
            "variants": [
                {
                    "variant_id": "control",
                    "name": "Control",
                    "description": "Control variant",
                    "traffic_allocation": 0.5,
                    "configuration": {},
                    "is_control": True
                },
                {
                    "variant_id": "treatment",
                    "name": "Treatment",
                    "description": "Treatment variant",
                    "traffic_allocation": 0.5,
                    "configuration": {"feature_enabled": True},
                    "is_control": False
                }
            ],
            "start_date": "2024-01-01T00:00:00",
            "target_audience": {}
        }
        
        with patch('bot.api.dependencies.get_advanced_features_manager') as mock_manager:
            mock_manager.return_value.create_ab_test.return_value = "test_123"
            
            response = client.post("/api/v1/ab-tests", json=test_data)
            assert response.status_code == 201
            
            data = response.json()
            assert data["test_id"] == "test_123"
            assert data["message"] == "A/B test created successfully"
    
    def test_list_ab_tests(self, client):
        """Test listing A/B tests."""
        mock_tests = [
            {
                "test_id": "test_123",
                "name": "Test Feature",
                "status": "active",
                "created_at": "2024-01-01T00:00:00"
            }
        ]
        
        with patch('bot.api.dependencies.get_advanced_features_manager') as mock_manager:
            mock_manager.return_value.ab_test_manager.list_active_tests.return_value = [Mock(**test) for test in mock_tests]
            
            response = client.get("/api/v1/ab-tests")
            assert response.status_code == 200
            
            data = response.json()
            assert "tests" in data
            assert len(data["tests"]) == 1
    
    def test_get_ab_test_results(self, client):
        """Test getting A/B test results."""
        mock_results = [
            {
                "variant_id": "control",
                "participants": 100,
                "conversions": 15,
                "conversion_rate": 0.15
            },
            {
                "variant_id": "treatment",
                "participants": 95,
                "conversions": 20,
                "conversion_rate": 0.21
            }
        ]
        
        with patch('bot.api.dependencies.get_advanced_features_manager') as mock_manager:
            mock_manager.return_value.get_ab_test_results.return_value = mock_results
            
            response = client.get("/api/v1/ab-tests/test_123/results")
            assert response.status_code == 200
            
            data = response.json()
            assert "results" in data
            assert len(data["results"]) == 2
            assert data["results"][0]["variant_id"] == "control"


class TestFeatureFlagEndpoints:
    """Test feature flag endpoints."""
    
    def test_create_feature_flag(self, client):
        """Test creating feature flag."""
        flag_data = {
            "name": "New Feature",
            "description": "Testing new feature",
            "flag_type": "boolean",
            "default_value": True,
            "rollout_strategy": "percentage",
            "rollout_config": {"percentage": 50}
        }
        
        with patch('bot.api.dependencies.get_advanced_features_manager') as mock_manager:
            mock_manager.return_value.create_feature_flag.return_value = "flag_123"
            
            response = client.post("/api/v1/feature-flags", json=flag_data)
            assert response.status_code == 201
            
            data = response.json()
            assert data["flag_id"] == "flag_123"
            assert data["message"] == "Feature flag created successfully"
    
    def test_list_feature_flags(self, client):
        """Test listing feature flags."""
        mock_flags = [
            {
                "flag_id": "flag_123",
                "name": "New Feature",
                "enabled": True,
                "rollout_strategy": "percentage"
            }
        ]
        
        with patch('bot.api.dependencies.get_advanced_features_manager') as mock_manager:
            mock_manager.return_value.feature_flag_manager.list_flags.return_value = [Mock(**flag) for flag in mock_flags]
            
            response = client.get("/api/v1/feature-flags")
            assert response.status_code == 200
            
            data = response.json()
            assert "flags" in data
            assert len(data["flags"]) == 1
    
    def test_update_feature_flag(self, client):
        """Test updating feature flag."""
        update_data = {
            "enabled": False,
            "rollout_config": {"percentage": 25}
        }
        
        with patch('bot.api.dependencies.get_advanced_features_manager') as mock_manager:
            mock_manager.return_value.feature_flag_manager.update_flag.return_value = True
            
            response = client.put("/api/v1/feature-flags/flag_123", json=update_data)
            assert response.status_code == 200
            
            data = response.json()
            assert data["message"] == "Feature flag updated successfully"


class TestWebSocketEndpoints:
    """Test WebSocket endpoints."""
    
    def test_websocket_connection(self, client):
        """Test WebSocket connection."""
        with client.websocket_connect("/ws") as websocket:
            # Send test message
            websocket.send_json({"type": "ping"})
            
            # Receive response
            data = websocket.receive_json()
            assert data["type"] == "pong"
    
    def test_websocket_analytics_subscription(self, client):
        """Test WebSocket analytics subscription."""
        with client.websocket_connect("/ws") as websocket:
            # Subscribe to analytics
            websocket.send_json({
                "type": "subscribe",
                "channel": "analytics"
            })
            
            # Should receive confirmation
            data = websocket.receive_json()
            assert data["type"] == "subscribed"
            assert data["channel"] == "analytics"
