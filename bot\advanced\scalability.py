"""
Scalability features for VoicePal.

This module provides scalability improvements including load balancing,
service registry, and horizontal scaling capabilities.
"""

import logging
import json
import time
import random
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
import threading
import hashlib

logger = logging.getLogger(__name__)

class ServiceStatus(Enum):
    """Service status."""
    HEALTHY = "healthy"
    UNHEALTHY = "unhealthy"
    MAINTENANCE = "maintenance"
    UNKNOWN = "unknown"

class LoadBalancingStrategy(Enum):
    """Load balancing strategies."""
    ROUND_ROBIN = "round_robin"
    LEAST_CONNECTIONS = "least_connections"
    WEIGHTED_ROUND_ROBIN = "weighted_round_robin"
    CONSISTENT_HASH = "consistent_hash"
    RANDOM = "random"

@dataclass
class ServiceInstance:
    """Service instance definition."""
    instance_id: str
    service_name: str
    host: str
    port: int
    weight: int
    status: ServiceStatus
    health_check_url: str
    metadata: Dict[str, Any]
    last_health_check: datetime
    active_connections: int
    total_requests: int
    avg_response_time: float

@dataclass
class LoadBalancerConfig:
    """Load balancer configuration."""
    strategy: LoadBalancingStrategy
    health_check_interval: int
    health_check_timeout: int
    max_retries: int
    circuit_breaker_threshold: int
    circuit_breaker_timeout: int

class ServiceRegistry:
    """Service registry for microservices."""
    
    def __init__(self, redis_client=None):
        """
        Initialize service registry.
        
        Args:
            redis_client: Redis client for distributed registry
        """
        self.redis_client = redis_client
        self.services: Dict[str, List[ServiceInstance]] = {}
        self.lock = threading.RLock()
        
        logger.info("Service registry initialized")
    
    def register_service(
        self,
        service_name: str,
        host: str,
        port: int,
        weight: int = 1,
        health_check_url: str = "/health",
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Register a service instance.
        
        Args:
            service_name: Name of the service
            host: Service host
            port: Service port
            weight: Service weight for load balancing
            health_check_url: Health check endpoint
            metadata: Additional metadata
            
        Returns:
            Instance ID
        """
        try:
            instance_id = f"{service_name}_{host}_{port}_{int(time.time())}"
            
            instance = ServiceInstance(
                instance_id=instance_id,
                service_name=service_name,
                host=host,
                port=port,
                weight=weight,
                status=ServiceStatus.UNKNOWN,
                health_check_url=health_check_url,
                metadata=metadata or {},
                last_health_check=datetime.utcnow(),
                active_connections=0,
                total_requests=0,
                avg_response_time=0.0
            )
            
            with self.lock:
                if service_name not in self.services:
                    self.services[service_name] = []
                self.services[service_name].append(instance)
            
            # Store in Redis if available
            if self.redis_client:
                try:
                    key = f"services:{service_name}:{instance_id}"
                    self.redis_client.hset(key, mapping=asdict(instance))
                    self.redis_client.expire(key, 300)  # 5 minutes TTL
                    
                    # Add to service list
                    self.redis_client.sadd(f"service_instances:{service_name}", instance_id)
                except Exception as e:
                    logger.warning(f"Failed to store service in Redis: {e}")
            
            logger.info(f"Registered service instance: {instance_id}")
            return instance_id
            
        except Exception as e:
            logger.error(f"Failed to register service: {e}")
            raise
    
    def deregister_service(self, instance_id: str) -> bool:
        """
        Deregister a service instance.
        
        Args:
            instance_id: Instance ID to deregister
            
        Returns:
            True if successful, False otherwise
        """
        try:
            with self.lock:
                for service_name, instances in self.services.items():
                    for i, instance in enumerate(instances):
                        if instance.instance_id == instance_id:
                            del instances[i]
                            
                            # Remove from Redis
                            if self.redis_client:
                                try:
                                    self.redis_client.delete(f"services:{service_name}:{instance_id}")
                                    self.redis_client.srem(f"service_instances:{service_name}", instance_id)
                                except Exception as e:
                                    logger.warning(f"Failed to remove service from Redis: {e}")
                            
                            logger.info(f"Deregistered service instance: {instance_id}")
                            return True
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to deregister service: {e}")
            return False
    
    def get_service_instances(self, service_name: str) -> List[ServiceInstance]:
        """
        Get all instances of a service.
        
        Args:
            service_name: Service name
            
        Returns:
            List of service instances
        """
        try:
            with self.lock:
                return self.services.get(service_name, []).copy()
        except Exception as e:
            logger.error(f"Failed to get service instances: {e}")
            return []
    
    def get_healthy_instances(self, service_name: str) -> List[ServiceInstance]:
        """
        Get healthy instances of a service.
        
        Args:
            service_name: Service name
            
        Returns:
            List of healthy service instances
        """
        instances = self.get_service_instances(service_name)
        return [inst for inst in instances if inst.status == ServiceStatus.HEALTHY]
    
    def update_instance_status(self, instance_id: str, status: ServiceStatus):
        """
        Update instance status.
        
        Args:
            instance_id: Instance ID
            status: New status
        """
        try:
            with self.lock:
                for instances in self.services.values():
                    for instance in instances:
                        if instance.instance_id == instance_id:
                            instance.status = status
                            instance.last_health_check = datetime.utcnow()
                            
                            # Update in Redis
                            if self.redis_client:
                                try:
                                    key = f"services:{instance.service_name}:{instance_id}"
                                    self.redis_client.hset(key, "status", status.value)
                                    self.redis_client.hset(key, "last_health_check", datetime.utcnow().isoformat())
                                except Exception as e:
                                    logger.warning(f"Failed to update status in Redis: {e}")
                            
                            return
        except Exception as e:
            logger.error(f"Failed to update instance status: {e}")

class LoadBalancer:
    """Load balancer for distributing requests across service instances."""
    
    def __init__(
        self,
        service_registry: ServiceRegistry,
        config: LoadBalancerConfig
    ):
        """
        Initialize load balancer.
        
        Args:
            service_registry: Service registry instance
            config: Load balancer configuration
        """
        self.service_registry = service_registry
        self.config = config
        
        # State for different strategies
        self.round_robin_counters: Dict[str, int] = {}
        self.circuit_breakers: Dict[str, Dict[str, Any]] = {}
        
        # Health checker
        self.health_checker_thread: Optional[threading.Thread] = None
        self.is_health_checking = False
        
        logger.info("Load balancer initialized")
    
    def start_health_checker(self):
        """Start health checker thread."""
        if self.is_health_checking:
            return
        
        self.is_health_checking = True
        self.health_checker_thread = threading.Thread(
            target=self._health_check_loop,
            daemon=True,
            name="HealthChecker"
        )
        self.health_checker_thread.start()
        
        logger.info("Health checker started")
    
    def stop_health_checker(self):
        """Stop health checker thread."""
        self.is_health_checking = False
        if self.health_checker_thread:
            self.health_checker_thread.join(timeout=5)
        
        logger.info("Health checker stopped")
    
    def _health_check_loop(self):
        """Health check loop."""
        while self.is_health_checking:
            try:
                self._perform_health_checks()
                time.sleep(self.config.health_check_interval)
            except Exception as e:
                logger.error(f"Error in health check loop: {e}")
                time.sleep(self.config.health_check_interval)
    
    def _perform_health_checks(self):
        """Perform health checks on all service instances."""
        try:
            for service_name, instances in self.service_registry.services.items():
                for instance in instances:
                    try:
                        # Simplified health check (in production, use HTTP requests)
                        # For now, randomly mark some as healthy/unhealthy for testing
                        if random.random() > 0.1:  # 90% healthy
                            status = ServiceStatus.HEALTHY
                        else:
                            status = ServiceStatus.UNHEALTHY
                        
                        self.service_registry.update_instance_status(instance.instance_id, status)
                        
                    except Exception as e:
                        logger.warning(f"Health check failed for {instance.instance_id}: {e}")
                        self.service_registry.update_instance_status(instance.instance_id, ServiceStatus.UNHEALTHY)
        except Exception as e:
            logger.error(f"Failed to perform health checks: {e}")
    
    def get_instance(self, service_name: str, request_id: Optional[str] = None) -> Optional[ServiceInstance]:
        """
        Get service instance using configured load balancing strategy.
        
        Args:
            service_name: Service name
            request_id: Request ID for consistent hashing
            
        Returns:
            Selected service instance or None
        """
        try:
            healthy_instances = self.service_registry.get_healthy_instances(service_name)
            
            if not healthy_instances:
                logger.warning(f"No healthy instances available for service: {service_name}")
                return None
            
            # Check circuit breakers
            available_instances = []
            for instance in healthy_instances:
                if not self._is_circuit_breaker_open(instance.instance_id):
                    available_instances.append(instance)
            
            if not available_instances:
                logger.warning(f"All instances circuit breaker open for service: {service_name}")
                return None
            
            # Apply load balancing strategy
            if self.config.strategy == LoadBalancingStrategy.ROUND_ROBIN:
                return self._round_robin_select(service_name, available_instances)
            elif self.config.strategy == LoadBalancingStrategy.LEAST_CONNECTIONS:
                return self._least_connections_select(available_instances)
            elif self.config.strategy == LoadBalancingStrategy.WEIGHTED_ROUND_ROBIN:
                return self._weighted_round_robin_select(service_name, available_instances)
            elif self.config.strategy == LoadBalancingStrategy.CONSISTENT_HASH:
                return self._consistent_hash_select(available_instances, request_id or "")
            elif self.config.strategy == LoadBalancingStrategy.RANDOM:
                return random.choice(available_instances)
            else:
                return available_instances[0]
                
        except Exception as e:
            logger.error(f"Failed to get instance for service {service_name}: {e}")
            return None
    
    def _round_robin_select(self, service_name: str, instances: List[ServiceInstance]) -> ServiceInstance:
        """Round robin selection."""
        if service_name not in self.round_robin_counters:
            self.round_robin_counters[service_name] = 0
        
        index = self.round_robin_counters[service_name] % len(instances)
        self.round_robin_counters[service_name] += 1
        
        return instances[index]
    
    def _least_connections_select(self, instances: List[ServiceInstance]) -> ServiceInstance:
        """Least connections selection."""
        return min(instances, key=lambda x: x.active_connections)
    
    def _weighted_round_robin_select(self, service_name: str, instances: List[ServiceInstance]) -> ServiceInstance:
        """Weighted round robin selection."""
        # Create weighted list
        weighted_instances = []
        for instance in instances:
            weighted_instances.extend([instance] * instance.weight)
        
        if not weighted_instances:
            return instances[0]
        
        if service_name not in self.round_robin_counters:
            self.round_robin_counters[service_name] = 0
        
        index = self.round_robin_counters[service_name] % len(weighted_instances)
        self.round_robin_counters[service_name] += 1
        
        return weighted_instances[index]
    
    def _consistent_hash_select(self, instances: List[ServiceInstance], key: str) -> ServiceInstance:
        """Consistent hash selection."""
        if not key:
            return random.choice(instances)
        
        # Simple consistent hashing
        hash_value = int(hashlib.md5(key.encode()).hexdigest(), 16)
        index = hash_value % len(instances)
        
        return instances[index]
    
    def _is_circuit_breaker_open(self, instance_id: str) -> bool:
        """Check if circuit breaker is open for instance."""
        if instance_id not in self.circuit_breakers:
            self.circuit_breakers[instance_id] = {
                "failure_count": 0,
                "last_failure": None,
                "state": "closed"  # closed, open, half_open
            }
        
        breaker = self.circuit_breakers[instance_id]
        
        if breaker["state"] == "open":
            # Check if timeout has passed
            if breaker["last_failure"]:
                time_since_failure = (datetime.utcnow() - breaker["last_failure"]).total_seconds()
                if time_since_failure > self.config.circuit_breaker_timeout:
                    breaker["state"] = "half_open"
                    return False
            return True
        
        return False
    
    def record_request_success(self, instance_id: str):
        """Record successful request."""
        if instance_id in self.circuit_breakers:
            self.circuit_breakers[instance_id]["failure_count"] = 0
            self.circuit_breakers[instance_id]["state"] = "closed"
    
    def record_request_failure(self, instance_id: str):
        """Record failed request."""
        if instance_id not in self.circuit_breakers:
            self.circuit_breakers[instance_id] = {
                "failure_count": 0,
                "last_failure": None,
                "state": "closed"
            }
        
        breaker = self.circuit_breakers[instance_id]
        breaker["failure_count"] += 1
        breaker["last_failure"] = datetime.utcnow()
        
        if breaker["failure_count"] >= self.config.circuit_breaker_threshold:
            breaker["state"] = "open"
            logger.warning(f"Circuit breaker opened for instance: {instance_id}")

class ScalabilityManager:
    """Manager for scalability features."""
    
    def __init__(
        self,
        redis_client=None,
        enable_service_registry: bool = True,
        enable_load_balancer: bool = True
    ):
        """
        Initialize scalability manager.
        
        Args:
            redis_client: Redis client for distributed features
            enable_service_registry: Whether to enable service registry
            enable_load_balancer: Whether to enable load balancer
        """
        self.redis_client = redis_client
        
        # Initialize components
        self.service_registry: Optional[ServiceRegistry] = None
        self.load_balancer: Optional[LoadBalancer] = None
        
        if enable_service_registry:
            self.service_registry = ServiceRegistry(redis_client)
        
        if enable_load_balancer and self.service_registry:
            config = LoadBalancerConfig(
                strategy=LoadBalancingStrategy.ROUND_ROBIN,
                health_check_interval=30,
                health_check_timeout=5,
                max_retries=3,
                circuit_breaker_threshold=5,
                circuit_breaker_timeout=60
            )
            self.load_balancer = LoadBalancer(self.service_registry, config)
        
        logger.info("Scalability manager initialized")
    
    def start_services(self):
        """Start scalability services."""
        try:
            if self.load_balancer:
                self.load_balancer.start_health_checker()
            
            logger.info("Scalability services started")
            
        except Exception as e:
            logger.error(f"Failed to start scalability services: {e}")
    
    def stop_services(self):
        """Stop scalability services."""
        try:
            if self.load_balancer:
                self.load_balancer.stop_health_checker()
            
            logger.info("Scalability services stopped")
            
        except Exception as e:
            logger.error(f"Failed to stop scalability services: {e}")
    
    def register_bot_instance(
        self,
        host: str,
        port: int,
        instance_metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Register bot instance for load balancing.
        
        Args:
            host: Instance host
            port: Instance port
            instance_metadata: Additional metadata
            
        Returns:
            Instance ID
        """
        if not self.service_registry:
            raise RuntimeError("Service registry not enabled")
        
        return self.service_registry.register_service(
            service_name="voicepal_bot",
            host=host,
            port=port,
            metadata=instance_metadata
        )
    
    def get_bot_instance(self, request_id: Optional[str] = None) -> Optional[ServiceInstance]:
        """
        Get bot instance for request handling.
        
        Args:
            request_id: Request ID for consistent routing
            
        Returns:
            Selected bot instance or None
        """
        if not self.load_balancer:
            return None
        
        return self.load_balancer.get_instance("voicepal_bot", request_id)
    
    def get_scalability_status(self) -> Dict[str, Any]:
        """
        Get scalability system status.
        
        Returns:
            Status information
        """
        status = {
            "service_registry": {
                "enabled": self.service_registry is not None,
                "services": {}
            },
            "load_balancer": {
                "enabled": self.load_balancer is not None,
                "strategy": None,
                "health_checking": False
            }
        }
        
        if self.service_registry:
            for service_name, instances in self.service_registry.services.items():
                status["service_registry"]["services"][service_name] = {
                    "total_instances": len(instances),
                    "healthy_instances": len([i for i in instances if i.status == ServiceStatus.HEALTHY]),
                    "instances": [
                        {
                            "instance_id": inst.instance_id,
                            "host": inst.host,
                            "port": inst.port,
                            "status": inst.status.value,
                            "active_connections": inst.active_connections
                        }
                        for inst in instances
                    ]
                }
        
        if self.load_balancer:
            status["load_balancer"]["strategy"] = self.load_balancer.config.strategy.value
            status["load_balancer"]["health_checking"] = self.load_balancer.is_health_checking
        
        return status
    
    def scale_service(self, service_name: str, target_instances: int) -> bool:
        """
        Scale service to target number of instances.
        
        Args:
            service_name: Service to scale
            target_instances: Target number of instances
            
        Returns:
            True if scaling initiated, False otherwise
        """
        try:
            if not self.service_registry:
                return False
            
            current_instances = self.service_registry.get_healthy_instances(service_name)
            current_count = len(current_instances)
            
            if current_count == target_instances:
                logger.info(f"Service {service_name} already at target scale: {target_instances}")
                return True
            
            if current_count < target_instances:
                # Scale up
                logger.info(f"Scaling up {service_name} from {current_count} to {target_instances}")
                # In production, this would trigger container orchestration
                return True
            else:
                # Scale down
                logger.info(f"Scaling down {service_name} from {current_count} to {target_instances}")
                # In production, this would gracefully shutdown excess instances
                return True
                
        except Exception as e:
            logger.error(f"Failed to scale service {service_name}: {e}")
            return False
