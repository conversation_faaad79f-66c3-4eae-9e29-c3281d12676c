#!/usr/bin/env python3
"""
Fix the database by removing the login_count column.
"""

import sqlite3
import os
import shutil
from datetime import datetime

def fix_database(db_path: str) -> None:
    """
    Fix the database by removing the login_count column.
    
    Args:
        db_path: Path to the database file
    """
    # Backup the database
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = f"{db_path}.backup_{timestamp}"
    shutil.copy2(db_path, backup_path)
    print(f"Database backed up to: {backup_path}")
    
    # Connect to the database
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Get the current schema of the users table
        cursor.execute("PRAGMA table_info(users)")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]
        column_types = {column[1]: column[2] for column in columns}
        
        print(f"Current columns in users table: {column_names}")
        
        # Check if login_count column exists
        if 'login_count' in column_names:
            print("login_count column exists, creating a new users table without it")
            
            # Create a temporary table without login_count
            create_table_sql = "CREATE TABLE users_temp ("
            column_defs = []
            
            for column in columns:
                name = column[1]
                if name != 'login_count':
                    type_def = column[2]
                    not_null = "NOT NULL" if column[3] == 1 else ""
                    default = f"DEFAULT {column[4]}" if column[4] is not None else ""
                    pk = "PRIMARY KEY" if column[5] == 1 else ""
                    column_defs.append(f"{name} {type_def} {not_null} {default} {pk}".strip())
            
            create_table_sql += ", ".join(column_defs) + ")"
            print(f"Creating temporary table with SQL: {create_table_sql}")
            cursor.execute(create_table_sql)
            
            # Copy data from users to users_temp
            copy_columns = [col for col in column_names if col != 'login_count']
            copy_sql = f"INSERT INTO users_temp SELECT {', '.join(copy_columns)} FROM users"
            print(f"Copying data with SQL: {copy_sql}")
            cursor.execute(copy_sql)
            
            # Drop the original table
            cursor.execute("DROP TABLE users")
            
            # Rename the temporary table
            cursor.execute("ALTER TABLE users_temp RENAME TO users")
            
            # Commit changes
            conn.commit()
            print("Created new users table without login_count column")
            
            # Verify the new schema
            cursor.execute("PRAGMA table_info(users)")
            new_columns = cursor.fetchall()
            new_column_names = [column[1] for column in new_columns]
            print(f"New columns in users table: {new_column_names}")
            
            if 'login_count' not in new_column_names:
                print("Successfully removed login_count column")
            else:
                print("Failed to remove login_count column")
        else:
            print("login_count column does not exist, no changes needed")
    
    except Exception as e:
        print(f"Error fixing database: {e}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    # Fix the database
    fix_database("voicepal.db")
