"""
Payment security integration for VoicePal.

This module integrates the payment security monitoring system with the VoicePal bot.
"""

import logging
import asyncio
import functools
import json
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime, timedelta

from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import Context<PERSON>ypes, CommandHandler, CallbackQueryHandler

from bot.core.payment_security import PaymentSecurityMonitor
from bot.core.security_monitor import (
    SEVERITY_INFO, SEVERITY_LOW, SEVERITY_MEDIUM, SEVERITY_HIGH, SEVERITY_CRITICAL,
    CATEGORY_PAYMENT
)

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

def integrate_payment_security(bot_instance):
    """
    Integrate payment security monitoring with the VoicePal bot.

    Args:
        bot_instance: VoicePalBot instance
    """
    # Initialize payment security monitor
    bot_instance.payment_security = PaymentSecurityMonitor(
        database=bot_instance.database,
        security_monitor=bot_instance.security_monitor,
        config_manager=bot_instance.config_manager
    )

    # Register payment security command handlers
    bot_instance.application.add_handler(
        CommandHandler("paymentsecurity", payment_security_command, filters=bot_instance.admin_filter)
    )

    # Register callback query handlers for payment security
    bot_instance.application.add_handler(
        CallbackQueryHandler(handle_payment_security_callback, pattern=r"^payment_security_")
    )

    # Monkey patch payment methods to add security monitoring
    patch_payment_methods(bot_instance)

    logger.info("Payment security monitoring integrated with VoicePal bot")

def patch_payment_methods(bot_instance):
    """
    Patch payment methods to add security monitoring.

    Args:
        bot_instance: VoicePalBot instance
    """
    # Store original methods
    if hasattr(bot_instance, "handle_pre_checkout"):
        original_handle_pre_checkout = bot_instance.handle_pre_checkout

        # Patch handle_pre_checkout method
        async def patched_handle_pre_checkout(update: Update, context: ContextTypes.DEFAULT_TYPE):
            """Patched handle_pre_checkout method with payment security monitoring."""
            user_id = update.pre_checkout_query.from_user.id
            invoice_payload = update.pre_checkout_query.invoice_payload
            total_amount = update.pre_checkout_query.total_amount / 100  # Convert to dollars

            # Extract payment details from payload
            try:
                payload_parts = invoice_payload.split("_")
                credits = int(payload_parts[1])
                payment_method = "telegram"
            except (IndexError, ValueError):
                credits = 0
                payment_method = "telegram"

            # Check if payment is suspicious
            is_suspicious, reason = bot_instance.payment_security.check_payment_suspicious(
                user_id, payment_method, total_amount, credits
            )

            # Block suspicious payments
            if is_suspicious:
                logger.warning(f"Blocking suspicious payment from user {user_id}: {reason}")

                # Log the blocked payment attempt
                bot_instance.payment_security.log_payment_attempt(
                    user_id=user_id,
                    payment_method=payment_method,
                    payment_id=invoice_payload,
                    amount=total_amount,
                    credits=credits,
                    is_successful=False,
                    details={
                        "reason": reason,
                        "is_blocked": True,
                        "currency": update.pre_checkout_query.currency
                    }
                )

                # Reject the payment
                await update.pre_checkout_query.answer(
                    ok=False,
                    error_message=f"Payment rejected for security reasons. Please contact support."
                )
                return

            # Log the payment attempt
            bot_instance.payment_security.log_payment_attempt(
                user_id=user_id,
                payment_method=payment_method,
                payment_id=invoice_payload,
                amount=total_amount,
                credits=credits,
                is_successful=True,
                details={
                    "currency": update.pre_checkout_query.currency
                }
            )

            # Call original method
            return await original_handle_pre_checkout(update, context)

        # Replace method with patched version
        bot_instance.handle_pre_checkout = patched_handle_pre_checkout

    # Patch handle_successful_payment method if it exists
    if hasattr(bot_instance, "handle_successful_payment"):
        original_handle_successful_payment = bot_instance.handle_successful_payment

        # Patch handle_successful_payment method
        async def patched_handle_successful_payment(update: Update, context: ContextTypes.DEFAULT_TYPE):
            """Patched handle_successful_payment method with payment security monitoring."""
            user_id = update.effective_user.id
            payment = update.message.successful_payment
            invoice_payload = payment.invoice_payload
            total_amount = payment.total_amount / 100  # Convert to dollars

            # Extract payment details from payload
            try:
                payload_parts = invoice_payload.split("_")
                credits = int(payload_parts[1])
                payment_method = "telegram"
            except (IndexError, ValueError):
                credits = 0
                payment_method = "telegram"

            # Log the successful payment
            bot_instance.payment_security.log_payment_attempt(
                user_id=user_id,
                payment_method=payment_method,
                payment_id=payment.telegram_payment_charge_id,
                amount=total_amount,
                credits=credits,
                is_successful=True,
                details={
                    "currency": payment.currency,
                    "provider_payment_charge_id": payment.provider_payment_charge_id,
                    "telegram_payment_charge_id": payment.telegram_payment_charge_id,
                    "invoice_payload": invoice_payload
                }
            )

            # Call original method
            return await original_handle_successful_payment(update, context)

        # Replace method with patched version
        bot_instance.handle_successful_payment = patched_handle_successful_payment

    # Patch buy_command method if it exists
    if hasattr(bot_instance, "buy_command"):
        original_buy_command = bot_instance.buy_command

        # Patch buy_command method
        async def patched_buy_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
            """Patched buy_command method with payment security checks."""
            user_id = update.effective_user.id

            # Check if user is blacklisted for payments
            if bot_instance.payment_security.is_blacklisted(user_id):
                await update.message.reply_text(
                    "Sorry, you are currently unable to make payments. Please contact support for assistance."
                )

                # Log the blocked payment attempt
                bot_instance.payment_security.log_payment_attempt(
                    user_id=user_id,
                    payment_method="any",
                    payment_id="buy_command_blocked",
                    amount=0,
                    credits=0,
                    is_successful=False,
                    details={
                        "reason": "User is blacklisted",
                        "is_blocked": True
                    }
                )

                return

            # Call original method
            return await original_buy_command(update, context)

        # Replace method with patched version
        bot_instance.buy_command = patched_buy_command

async def payment_security_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """
    Handle the /paymentsecurity command (admin only).

    Args:
        update: Update object
        context: Context object
    """
    user_id = update.effective_user.id
    bot_instance = context.bot_data.get("bot_instance")

    if not bot_instance or not hasattr(bot_instance, "payment_security"):
        await update.message.reply_text("Payment security monitoring is not available.")
        return

    # Check if user is admin
    admin_ids = bot_instance.config_manager.get_admin_user_ids()
    if user_id not in admin_ids:
        await update.message.reply_text("This command is only available to administrators.")
        return

    # Store bot instance in context for dashboard access
    context.bot_data["bot_instance"] = bot_instance

    # Show payment security dashboard
    await show_payment_security_dashboard(update, context)

async def show_payment_security_dashboard(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """
    Show the payment security dashboard.

    Args:
        update: Update object
        context: Context object
    """
    bot_instance = context.bot_data.get("bot_instance")

    if not bot_instance or not hasattr(bot_instance, "payment_security"):
        await update.message.reply_text("Payment security monitoring is not available.")
        return

    # Get payment security statistics
    stats = bot_instance.payment_security.get_payment_security_stats(days=7)

    # Format dashboard message
    message = (
        "💳 *Payment Security Dashboard* 💳\n\n"
        "*Payment Statistics (Last 7 Days):*\n"
    )

    if stats:
        success_rate = 0
        if stats["total_payments"] > 0:
            success_rate = (stats["successful_payments"] / stats["total_payments"]) * 100

        message += (
            f"Total Payments: {stats['total_payments']}\n"
            f"Successful: {stats['successful_payments']}\n"
            f"Failed: {stats['failed_payments']}\n"
            f"Suspicious: {stats['suspicious_payments']}\n"
            f"Success Rate: {success_rate:.1f}%\n"
            f"Total Amount: ${stats['total_amount']:.2f}\n"
            f"Unique Users: {stats['unique_users']}\n"
            f"Blacklisted Users: {stats['blacklist_count']}\n\n"
        )

        # Add payment method breakdown
        message += "*Payment Methods:*\n"
        for method in stats.get("payment_methods", [])[:3]:  # Show top 3
            method_name = method["payment_method"] or "Unknown"
            success_rate = 0
            if method["count"] > 0:
                success_rate = (method["successful"] / method["count"]) * 100

            message += f"- {method_name}: {method['count']} payments ({success_rate:.1f}% success)\n"

        # Add daily trend
        message += "\n*Daily Trend:*\n"
        for day_data in stats.get("daily_counts", [])[-5:]:  # Show last 5 days
            day = day_data["day"]
            count = day_data["count"]
            suspicious = day_data["suspicious"]

            message += f"- {day}: {count} payments"
            if suspicious > 0:
                message += f" ({suspicious} suspicious)"
            message += "\n"
    else:
        message += "No payment data available.\n"

    # Create dashboard keyboard
    keyboard = [
        [
            InlineKeyboardButton("View Suspicious Payments", callback_data="payment_security_suspicious"),
            InlineKeyboardButton("View Blacklist", callback_data="payment_security_blacklist")
        ],
        [
            InlineKeyboardButton("Payment Thresholds", callback_data="payment_security_thresholds"),
            InlineKeyboardButton("Refresh", callback_data="payment_security_refresh")
        ],
        [
            InlineKeyboardButton("🔙 Back to Security", callback_data="security_section_overview")
        ]
    ]

    reply_markup = InlineKeyboardMarkup(keyboard)

    # Send or edit message
    if update.callback_query:
        await update.callback_query.edit_message_text(
            text=message,
            reply_markup=reply_markup,
            parse_mode="Markdown"
        )
    else:
        await update.message.reply_text(
            text=message,
            reply_markup=reply_markup,
            parse_mode="Markdown"
        )

async def show_suspicious_payments(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """
    Show suspicious payments.

    Args:
        update: Update object
        context: Context object
    """
    bot_instance = context.bot_data.get("bot_instance")

    if not bot_instance or not hasattr(bot_instance, "payment_security"):
        await update.message.reply_text("Payment security monitoring is not available.")
        return

    # Get suspicious payments
    cursor = bot_instance.database.conn.cursor()
    cursor.execute("""
        SELECT id, timestamp, user_id, event_type, payment_method, amount, credits, details
        FROM payment_security_logs
        WHERE is_suspicious = 1
        ORDER BY timestamp DESC
        LIMIT 10
    """)

    suspicious_payments = cursor.fetchall()

    # Format message
    message = (
        "⚠️ *Suspicious Payments* ⚠️\n\n"
    )

    if suspicious_payments:
        for payment in suspicious_payments:
            payment_time = datetime.fromisoformat(payment["timestamp"]).strftime("%Y-%m-%d %H:%M")

            # Extract reason from details
            reason = "Unknown"
            if payment["details"]:
                try:
                    details = json.loads(payment["details"])
                    reason = details.get("reason", "Unknown")
                except:
                    pass

            message += (
                f"🕒 {payment_time}\n"
                f"👤 User: {payment['user_id']}\n"
                f"💳 Method: {payment['payment_method'] or 'Unknown'}\n"
                f"💰 Amount: ${payment['amount']:.2f} ({payment['credits']} credits)\n"
                f"⚠️ Reason: {reason}\n\n"
            )
    else:
        message += "No suspicious payments found.\n"

    # Create keyboard
    keyboard = [
        [
            InlineKeyboardButton("Blacklist Management", callback_data="payment_security_blacklist"),
            InlineKeyboardButton("Refresh", callback_data="payment_security_suspicious")
        ],
        [
            InlineKeyboardButton("🔙 Back to Payment Security", callback_data="payment_security_refresh")
        ]
    ]

    reply_markup = InlineKeyboardMarkup(keyboard)

    # Send or edit message
    if update.callback_query:
        await update.callback_query.edit_message_text(
            text=message,
            reply_markup=reply_markup,
            parse_mode="Markdown"
        )
    else:
        await update.message.reply_text(
            text=message,
            reply_markup=reply_markup,
            parse_mode="Markdown"
        )

async def show_payment_blacklist(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """
    Show payment blacklist.

    Args:
        update: Update object
        context: Context object
    """
    bot_instance = context.bot_data.get("bot_instance")

    if not bot_instance or not hasattr(bot_instance, "payment_security"):
        await update.message.reply_text("Payment security monitoring is not available.")
        return

    # Get blacklist
    cursor = bot_instance.database.conn.cursor()
    cursor.execute("""
        SELECT id, user_id, payment_method, reason, added_at, expiry_date, is_permanent
        FROM payment_blacklist
        WHERE is_permanent = 1 OR expiry_date > ?
        ORDER BY added_at DESC
    """, (datetime.now().isoformat(),))

    blacklist = cursor.fetchall()

    # Format message
    message = (
        "🚫 *Payment Blacklist* 🚫\n\n"
    )

    if blacklist:
        for entry in blacklist:
            added_at = datetime.fromisoformat(entry["added_at"]).strftime("%Y-%m-%d")

            expiry = "Permanent" if entry["is_permanent"] else "Unknown"
            if not entry["is_permanent"] and entry["expiry_date"]:
                expiry_date = datetime.fromisoformat(entry["expiry_date"])
                days_left = (expiry_date - datetime.now()).days
                expiry = f"Expires in {days_left} days"

            message += (
                f"👤 User: {entry['user_id']}\n"
                f"💳 Method: {entry['payment_method'] or 'All methods'}\n"
                f"⚠️ Reason: {entry['reason']}\n"
                f"📅 Added: {added_at}\n"
                f"⏱️ Status: {expiry}\n\n"
            )
    else:
        message += "No blacklisted users found.\n"

    # Create keyboard
    keyboard = [
        [
            InlineKeyboardButton("Add to Blacklist", callback_data="payment_security_add_blacklist"),
            InlineKeyboardButton("Remove from Blacklist", callback_data="payment_security_remove_blacklist")
        ],
        [
            InlineKeyboardButton("🔙 Back to Payment Security", callback_data="payment_security_refresh")
        ]
    ]

    reply_markup = InlineKeyboardMarkup(keyboard)

    # Send or edit message
    if update.callback_query:
        await update.callback_query.edit_message_text(
            text=message,
            reply_markup=reply_markup,
            parse_mode="Markdown"
        )
    else:
        await update.message.reply_text(
            text=message,
            reply_markup=reply_markup,
            parse_mode="Markdown"
        )

async def handle_payment_security_callback(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """
    Handle payment security callback queries.

    Args:
        update: Update object
        context: Context object
    """
    query = update.callback_query
    await query.answer()

    callback_data = query.data
    bot_instance = context.bot_data.get("bot_instance")

    if not bot_instance or not hasattr(bot_instance, "payment_security"):
        await query.edit_message_text("Payment security monitoring is not available.")
        return

    if callback_data == "payment_security_refresh":
        await show_payment_security_dashboard(update, context)
    elif callback_data == "payment_security_suspicious":
        await show_suspicious_payments(update, context)
    elif callback_data == "payment_security_blacklist":
        await show_payment_blacklist(update, context)
    elif callback_data == "payment_security_thresholds":
        await show_payment_thresholds(update, context)
    else:
        await query.edit_message_text(
            "Unknown payment security option.",
            reply_markup=InlineKeyboardMarkup([[
                InlineKeyboardButton("Back to Payment Security", callback_data="payment_security_refresh")
            ]])
        )

async def show_payment_thresholds(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """
    Show payment security thresholds.

    Args:
        update: Update object
        context: Context object
    """
    bot_instance = context.bot_data.get("bot_instance")

    if not bot_instance or not hasattr(bot_instance, "payment_security"):
        await update.message.reply_text("Payment security monitoring is not available.")
        return

    # Get thresholds
    thresholds = bot_instance.payment_security.thresholds

    # Format message
    message = (
        "⚙️ *Payment Security Thresholds* ⚙️\n\n"
        "These thresholds determine when payments are flagged as suspicious:\n\n"
    )

    if thresholds:
        for key, value in thresholds.items():
            # Format key for display
            display_key = key.replace("_", " ").title()

            message += f"• {display_key}: {value}\n"
    else:
        message += "No thresholds configured.\n"

    # Create keyboard
    keyboard = [
        [
            InlineKeyboardButton("🔙 Back to Payment Security", callback_data="payment_security_refresh")
        ]
    ]

    reply_markup = InlineKeyboardMarkup(keyboard)

    # Send or edit message
    if update.callback_query:
        await update.callback_query.edit_message_text(
            text=message,
            reply_markup=reply_markup,
            parse_mode="Markdown"
        )
    else:
        await update.message.reply_text(
            text=message,
            reply_markup=reply_markup,
            parse_mode="Markdown"
        )
