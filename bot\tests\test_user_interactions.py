"""
Test script to simulate user interactions with the VoicePal bot.

This script tests:
1. Menu navigation
2. Mood tracking
3. Conversation flow
4. Voice processing
"""

import os
import sys
import asyncio
import logging
from pathlib import Path
from unittest.mock import MagicMock, patch

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# Import telegram types for mocking
from telegram import Update, User, Message, Chat, CallbackQuery
from telegram.ext import ContextTypes

# Import bot modules
from bot.main import VoicePalBot
from bot.config_manager import ConfigManager

class MockContext:
    """Mock context for testing."""
    
    def __init__(self):
        self.bot = MagicMock()
        self.args = []
        self.user_data = {}
        self.chat_data = {}
        self.bot_data = {}

async def create_mock_update(user_id=123456789, username="test_user", 
                            first_name="Test", last_name="User", 
                            message_text=None, callback_data=None):
    """Create a mock update object."""
    user = User(id=user_id, first_name=first_name, is_bot=False, 
                last_name=last_name, username=username)
    chat = Chat(id=user_id, type="private")
    
    if message_text:
        message = Message(
            message_id=1,
            date=None,
            chat=chat,
            from_user=user,
            text=message_text
        )
        return Update(update_id=1, message=message)
    
    if callback_data:
        message = Message(
            message_id=1,
            date=None,
            chat=chat,
            from_user=user
        )
        callback_query = CallbackQuery(
            id="1",
            from_user=user,
            chat_instance="1",
            message=message,
            data=callback_data
        )
        return Update(update_id=1, callback_query=callback_query)
    
    return Update(update_id=1)

async def test_start_command(bot):
    """Test the /start command."""
    logger.info("Testing /start command...")
    
    update = await create_mock_update(message_text="/start")
    context = MockContext()
    
    await bot.start_command(update, context)
    logger.info("Start command test completed")

async def test_help_command(bot):
    """Test the /help command."""
    logger.info("Testing /help command...")
    
    update = await create_mock_update(message_text="/help")
    context = MockContext()
    
    await bot.help_command(update, context)
    logger.info("Help command test completed")

async def test_menu_navigation(bot):
    """Test menu navigation."""
    logger.info("Testing menu navigation...")
    
    # Test main menu
    update = await create_mock_update(callback_data="main_menu")
    context = MockContext()
    
    await bot.handle_callback_query(update, context)
    
    # Test mood diary
    update = await create_mock_update(callback_data="show_mood_diary")
    await bot.handle_callback_query(update, context)
    
    # Test add mood entry
    update = await create_mock_update(callback_data="add_mood_entry")
    await bot.handle_callback_query(update, context)
    
    # Test mood selection
    update = await create_mock_update(callback_data="mood_entry_happy")
    await bot.handle_callback_query(update, context)
    
    logger.info("Menu navigation test completed")

async def test_conversation_flow(bot):
    """Test conversation flow."""
    logger.info("Testing conversation flow...")
    
    # Test greeting
    update = await create_mock_update(message_text="Hello")
    context = MockContext()
    
    await bot.handle_message(update, context)
    
    # Test short message
    update = await create_mock_update(message_text="Hi")
    await bot.handle_message(update, context)
    
    # Test longer message
    update = await create_mock_update(
        message_text="I'm feeling great today! How are you doing?"
    )
    await bot.handle_message(update, context)
    
    # Test question
    update = await create_mock_update(
        message_text="What's the weather like today?"
    )
    await bot.handle_message(update, context)
    
    logger.info("Conversation flow test completed")

async def run_tests():
    """Run all tests."""
    logger.info("Starting user interaction tests...")
    
    # Create mock config
    config = {
        "providers": {
            "stt": {
                "type": "deepgram",
                "api_key": "test_key"
            },
            "ai": {
                "type": "google_ai",
                "api_key": "test_key",
                "model_name": "gemini-1.5-flash"
            },
            "tts": {
                "type": "deepgram",
                "api_key": "test_key",
                "voice_id": "aura-thalia-en"
            }
        },
        "features": {
            "memory": {
                "enabled": True,
                "conversation_memory": 10
            },
            "mood_tracking": {
                "enabled": True
            },
            "personalization": {
                "enabled": True,
                "default_personality": "friendly"
            }
        }
    }
    
    # Create mock config manager
    config_manager = ConfigManager()
    config_manager.config = config
    
    # Create bot instance with mocked dependencies
    with patch('bot.main.Database'), \
         patch('bot.main.ConfigManager', return_value=config_manager), \
         patch('bot.main.VoiceProcessor'), \
         patch('bot.main.GoogleAIProvider'), \
         patch('bot.main.EnhancedMemoryManager'), \
         patch('bot.main.EnhancedDialogEngine'), \
         patch('bot.main.MoodEntry'), \
         patch('bot.main.NavigationRouter'):
        
        bot = VoicePalBot()
        bot.initialize()
        
        # Run tests
        await test_start_command(bot)
        await test_help_command(bot)
        await test_menu_navigation(bot)
        await test_conversation_flow(bot)
    
    logger.info("All user interaction tests completed")

if __name__ == '__main__':
    asyncio.run(run_tests())
