"""
Security manager for VoicePal.

This module coordinates all security features and provides a unified interface.
"""

import logging
from typing import Optional, Dict, Any, List
from datetime import datetime

from bot.security.encryption import DataEncryption, FieldEncryption
from bot.security.audit_logger import <PERSON>tLogger, AuditEvent, AuditEventType, AuditSeverity
from bot.security.rate_limiter import EnhancedRateLimiter, RateLimitRule, RateLimitStrategy
from bot.security.data_protection import DataProtection, GDPR

logger = logging.getLogger(__name__)

class SecurityManager:
    """Centralized security manager for VoicePal."""
    
    def __init__(
        self,
        database,
        redis_client=None,
        master_key: Optional[str] = None,
        enable_encryption: bool = True,
        enable_audit_logging: bool = True,
        enable_rate_limiting: bool = True
    ):
        """
        Initialize security manager.
        
        Args:
            database: Database instance
            redis_client: Redis client for distributed features
            master_key: Master encryption key
            enable_encryption: Whether to enable encryption
            enable_audit_logging: Whether to enable audit logging
            enable_rate_limiting: Whether to enable rate limiting
        """
        self.database = database
        self.redis_client = redis_client
        
        # Initialize components
        self.encryption = None
        self.field_encryption = None
        self.audit_logger = None
        self.rate_limiter = None
        self.data_protection = None
        
        if enable_encryption:
            self.encryption = DataEncryption(master_key)
            self.field_encryption = FieldEncryption(self.encryption)
        
        if enable_audit_logging:
            self.audit_logger = AuditLogger(database, log_to_file=True)
        
        if enable_rate_limiting:
            self.rate_limiter = EnhancedRateLimiter(redis_client)
        
        self.data_protection = DataProtection(database, self.encryption, self.audit_logger)
        
        # Security configuration
        self.config = {
            "encryption_enabled": enable_encryption,
            "audit_logging_enabled": enable_audit_logging,
            "rate_limiting_enabled": enable_rate_limiting,
            "max_login_attempts": 5,
            "session_timeout": 3600,  # 1 hour
            "password_min_length": 8,
            "require_2fa": False,
        }
        
        logger.info("Security manager initialized")
    
    def encrypt_sensitive_data(self, data: Dict[str, Any], table_name: str) -> Dict[str, Any]:
        """
        Encrypt sensitive data before storing.
        
        Args:
            data: Data to encrypt
            table_name: Database table name
            
        Returns:
            Data with encrypted fields
        """
        if not self.field_encryption:
            return data
        
        try:
            return self.field_encryption.encrypt_model_data(table_name, data)
        except Exception as e:
            logger.error(f"Failed to encrypt data: {e}")
            return data
    
    def decrypt_sensitive_data(self, data: Dict[str, Any], table_name: str) -> Dict[str, Any]:
        """
        Decrypt sensitive data after retrieving.
        
        Args:
            data: Data to decrypt
            table_name: Database table name
            
        Returns:
            Data with decrypted fields
        """
        if not self.field_encryption:
            return data
        
        try:
            return self.field_encryption.decrypt_model_data(table_name, data)
        except Exception as e:
            logger.error(f"Failed to decrypt data: {e}")
            return data
    
    def check_rate_limit(self, user_id: int, action: str, ip_address: Optional[str] = None) -> bool:
        """
        Check if user action is within rate limits.
        
        Args:
            user_id: User ID
            action: Action being performed
            ip_address: User's IP address
            
        Returns:
            True if action is allowed, False otherwise
        """
        if not self.rate_limiter:
            return True
        
        try:
            # Check user-based rate limit
            user_result = self.rate_limiter.check_rate_limit(str(user_id), action, user_id)
            
            if not user_result.allowed:
                # Log rate limit violation
                if self.audit_logger:
                    self.audit_logger.log_security_event(
                        event_type=AuditEventType.RATE_LIMIT_EXCEEDED,
                        description=f"Rate limit exceeded for action: {action}",
                        details={
                            "action": action,
                            "remaining": user_result.remaining,
                            "retry_after": user_result.retry_after
                        },
                        user_id=user_id,
                        severity=AuditSeverity.MEDIUM,
                        ip_address=ip_address
                    )
                return False
            
            # Check IP-based rate limit if IP is provided
            if ip_address:
                ip_result = self.rate_limiter.check_rate_limit(ip_address, f"ip_{action}", user_id)
                if not ip_result.allowed:
                    if self.audit_logger:
                        self.audit_logger.log_security_event(
                            event_type=AuditEventType.RATE_LIMIT_EXCEEDED,
                            description=f"IP rate limit exceeded for action: {action}",
                            details={
                                "action": action,
                                "ip_address": ip_address,
                                "remaining": ip_result.remaining,
                                "retry_after": ip_result.retry_after
                            },
                            user_id=user_id,
                            severity=AuditSeverity.HIGH,
                            ip_address=ip_address
                        )
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Rate limit check failed: {e}")
            # Allow action if rate limiting fails
            return True
    
    def log_user_action(
        self,
        event_type: AuditEventType,
        user_id: int,
        description: str,
        details: Optional[Dict[str, Any]] = None,
        severity: AuditSeverity = AuditSeverity.LOW,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> bool:
        """
        Log user action for audit purposes.
        
        Args:
            event_type: Type of event
            user_id: User ID
            description: Event description
            details: Additional event details
            severity: Event severity
            ip_address: User's IP address
            user_agent: User's user agent
            
        Returns:
            True if logged successfully, False otherwise
        """
        if not self.audit_logger:
            return True
        
        return self.audit_logger.log_user_action(
            event_type=event_type,
            user_id=user_id,
            description=description,
            details=details,
            severity=severity,
            ip_address=ip_address,
            user_agent=user_agent
        )
    
    def log_security_event(
        self,
        event_type: AuditEventType,
        description: str,
        details: Optional[Dict[str, Any]] = None,
        user_id: Optional[int] = None,
        severity: AuditSeverity = AuditSeverity.HIGH,
        ip_address: Optional[str] = None
    ) -> bool:
        """
        Log security event.
        
        Args:
            event_type: Type of event
            description: Event description
            details: Additional event details
            user_id: User ID (if applicable)
            severity: Event severity
            ip_address: IP address
            
        Returns:
            True if logged successfully, False otherwise
        """
        if not self.audit_logger:
            return True
        
        return self.audit_logger.log_security_event(
            event_type=event_type,
            description=description,
            details=details,
            user_id=user_id,
            severity=severity,
            ip_address=ip_address
        )
    
    def export_user_data(self, user_id: int) -> Optional[Dict[str, Any]]:
        """
        Export user data for GDPR compliance.
        
        Args:
            user_id: User ID
            
        Returns:
            Exported user data or None if failed
        """
        try:
            # Log data export request
            self.log_user_action(
                event_type=AuditEventType.DATA_EXPORT,
                user_id=user_id,
                description="User data export requested",
                severity=AuditSeverity.MEDIUM
            )
            
            return self.data_protection.export_user_data(user_id)
            
        except Exception as e:
            logger.error(f"Failed to export user data: {e}")
            return None
    
    def delete_user_data(self, user_id: int, deletion_type: str = "complete") -> bool:
        """
        Delete user data for GDPR compliance.
        
        Args:
            user_id: User ID
            deletion_type: Type of deletion (partial, complete)
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Log data deletion request
            self.log_user_action(
                event_type=AuditEventType.DATA_DELETION,
                user_id=user_id,
                description=f"User data deletion requested: {deletion_type}",
                details={"deletion_type": deletion_type},
                severity=AuditSeverity.HIGH
            )
            
            return self.data_protection.delete_user_data(user_id, deletion_type)
            
        except Exception as e:
            logger.error(f"Failed to delete user data: {e}")
            return False
    
    def validate_user_input(self, input_data: str, max_length: int = 1000) -> bool:
        """
        Validate user input for security.
        
        Args:
            input_data: User input to validate
            max_length: Maximum allowed length
            
        Returns:
            True if input is valid, False otherwise
        """
        try:
            # Check length
            if len(input_data) > max_length:
                return False
            
            # Check for potential injection attacks
            suspicious_patterns = [
                "javascript:",
                "<script",
                "eval(",
                "exec(",
                "system(",
                "shell_exec(",
                "DROP TABLE",
                "DELETE FROM",
                "INSERT INTO",
                "UPDATE SET"
            ]
            
            input_lower = input_data.lower()
            for pattern in suspicious_patterns:
                if pattern in input_lower:
                    # Log security violation
                    self.log_security_event(
                        event_type=AuditEventType.SECURITY_VIOLATION,
                        description="Suspicious input detected",
                        details={
                            "input_length": len(input_data),
                            "pattern_detected": pattern,
                            "input_preview": input_data[:100]
                        },
                        severity=AuditSeverity.HIGH
                    )
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Input validation failed: {e}")
            return False
    
    def get_security_status(self) -> Dict[str, Any]:
        """
        Get security system status.
        
        Returns:
            Security status information
        """
        return {
            "encryption": {
                "enabled": self.encryption is not None,
                "field_encryption": self.field_encryption is not None
            },
            "audit_logging": {
                "enabled": self.audit_logger is not None
            },
            "rate_limiting": {
                "enabled": self.rate_limiter is not None,
                "redis_available": self.redis_client is not None
            },
            "data_protection": {
                "enabled": self.data_protection is not None
            },
            "config": self.config
        }
    
    def cleanup_expired_data(self) -> Dict[str, int]:
        """
        Clean up expired data.
        
        Returns:
            Cleanup statistics
        """
        if not self.data_protection:
            return {}
        
        return self.data_protection.cleanup_expired_data()
