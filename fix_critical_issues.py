#!/usr/bin/env python3
"""
Critical Issues Fix Script for MoneyMule Bot

This script addresses the most critical production readiness issues
identified in the comprehensive testing.
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CriticalIssuesFixer:
    """Fix critical production readiness issues."""
    
    def __init__(self):
        self.issues_fixed = []
        self.issues_failed = []
        
    def fix_dependencies(self):
        """Fix missing dependencies."""
        logger.info("🔧 Fixing missing dependencies...")
        
        critical_packages = [
            'python-telegram-bot>=20.6',
            'python-dotenv>=1.0.0',
            'deepgram-sdk>=3.0.0',
            'google-generativeai>=0.3.0',
            'elevenlabs>=0.2.0',
            'fastapi>=0.100.0',
            'httpx>=0.25.0',
            'upstash-redis>=0.15.0',
            'qdrant-client>=1.6.0',
            'sentence-transformers>=2.2.0',
            'ffmpeg-python>=0.2.0',
            'gTTS>=2.3.0',
            'groq>=0.4.0'
        ]
        
        for package in critical_packages:
            try:
                logger.info(f"Installing {package}...")
                subprocess.check_call([
                    sys.executable, '-m', 'pip', 'install', package
                ])
                self.issues_fixed.append(f"Installed {package}")
            except subprocess.CalledProcessError as e:
                logger.error(f"Failed to install {package}: {e}")
                self.issues_failed.append(f"Failed to install {package}")
    
    def create_env_template(self):
        """Create environment variable template."""
        logger.info("📝 Creating environment variable template...")
        
        env_template = """# MoneyMule Bot Environment Variables
# Copy this file to .env and fill in your actual values

# Telegram Bot Configuration
BOT_TOKEN=your_telegram_bot_token_here

# AI Service API Keys
DEEPGRAM_API_KEY=your_deepgram_api_key_here
GOOGLE_AI_API_KEY=your_google_ai_api_key_here
ELEVENLABS_API_KEY=your_elevenlabs_api_key_here
GROQ_API_KEY=your_groq_api_key_here

# Database Configuration
DATABASE_URL=sqlite:///bot_database.db

# Redis Configuration (Optional)
REDIS_URL=redis://localhost:6379

# Security
SECRET_KEY=your_secret_key_here

# Deployment
ENVIRONMENT=development
DEBUG=true

# Monitoring
SENTRY_DSN=your_sentry_dsn_here

# Payment Configuration
STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key_here
STRIPE_SECRET_KEY=your_stripe_secret_key_here
"""
        
        try:
            with open('.env.template', 'w') as f:
                f.write(env_template)
            self.issues_fixed.append("Created .env.template file")
            logger.info("✅ Created .env.template - copy to .env and fill in values")
        except Exception as e:
            logger.error(f"Failed to create .env.template: {e}")
            self.issues_failed.append("Failed to create .env.template")
    
    def fix_deepgram_imports(self):
        """Fix Deepgram SDK import issues."""
        logger.info("🔧 Fixing Deepgram imports...")
        
        # Find files with Deepgram imports
        deepgram_files = [
            'bot/providers/stt/deepgram_provider.py',
            'bot/providers/tts/deepgram_provider.py',
            'bot/providers/voice/processor.py'
        ]
        
        for file_path in deepgram_files:
            if os.path.exists(file_path):
                try:
                    with open(file_path, 'r') as f:
                        content = f.read()
                    
                    # Fix old import to new import
                    old_import = "from deepgram import Deepgram"
                    new_import = "from deepgram import DeepgramClient, PrerecordedOptions, SpeakOptions"
                    
                    if old_import in content:
                        content = content.replace(old_import, new_import)
                        
                        with open(file_path, 'w') as f:
                            f.write(content)
                        
                        self.issues_fixed.append(f"Fixed Deepgram imports in {file_path}")
                        logger.info(f"✅ Fixed Deepgram imports in {file_path}")
                    
                except Exception as e:
                    logger.error(f"Failed to fix {file_path}: {e}")
                    self.issues_failed.append(f"Failed to fix {file_path}")
    
    def create_health_check(self):
        """Create basic health check endpoint."""
        logger.info("🏥 Creating health check endpoint...")
        
        health_check_code = '''"""
Health check endpoint for MoneyMule bot.
"""

from fastapi import APIRouter, HTTPException
from datetime import datetime
import psutil
import os

router = APIRouter()

@router.get("/health")
async def health_check():
    """Basic health check endpoint."""
    try:
        # Check system resources
        memory_usage = psutil.virtual_memory().percent
        disk_usage = psutil.disk_usage('/').percent
        
        # Check environment variables
        required_env_vars = ['BOT_TOKEN', 'DEEPGRAM_API_KEY', 'GOOGLE_AI_API_KEY']
        missing_env_vars = [var for var in required_env_vars if not os.getenv(var)]
        
        status = "healthy"
        if memory_usage > 90 or disk_usage > 90 or missing_env_vars:
            status = "unhealthy"
        
        return {
            "status": status,
            "timestamp": datetime.now().isoformat(),
            "memory_usage_percent": memory_usage,
            "disk_usage_percent": disk_usage,
            "missing_env_vars": missing_env_vars,
            "version": "1.0.0"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Health check failed: {str(e)}")

@router.get("/ready")
async def readiness_check():
    """Readiness check for deployment."""
    try:
        # Check if bot can start
        required_env_vars = ['BOT_TOKEN', 'DEEPGRAM_API_KEY', 'GOOGLE_AI_API_KEY']
        missing_env_vars = [var for var in required_env_vars if not os.getenv(var)]
        
        if missing_env_vars:
            raise HTTPException(
                status_code=503, 
                detail=f"Not ready: missing environment variables: {missing_env_vars}"
            )
        
        return {
            "status": "ready",
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=503, detail=f"Not ready: {str(e)}")
'''
        
        try:
            # Create health check directory if it doesn't exist
            health_dir = Path('bot/api/routes')
            health_dir.mkdir(parents=True, exist_ok=True)
            
            # Write health check file
            with open(health_dir / 'health.py', 'w') as f:
                f.write(health_check_code)
            
            self.issues_fixed.append("Created health check endpoint")
            logger.info("✅ Created health check endpoint")
        except Exception as e:
            logger.error(f"Failed to create health check: {e}")
            self.issues_failed.append("Failed to create health check")
    
    def create_privacy_docs(self):
        """Create basic privacy and terms documents."""
        logger.info("📄 Creating privacy documentation...")
        
        privacy_policy = """# Privacy Policy

## Data Collection
We collect minimal data necessary for bot functionality:
- Telegram user ID and username
- Voice messages for processing
- Usage statistics

## Data Usage
- Voice data is processed for AI responses and deleted after processing
- User preferences are stored to improve experience
- No data is shared with third parties without consent

## Data Retention
- Voice messages: Processed and deleted immediately
- User preferences: Stored until account deletion
- Logs: Retained for 30 days for debugging

## User Rights
- Request data deletion
- Access stored data
- Opt out of data collection

Contact: <EMAIL>
"""
        
        terms_of_service = """# Terms of Service

## Service Description
MoneyMule is a voice-enabled Telegram bot providing AI conversation services.

## Usage Rules
- Use bot responsibly and legally
- No harassment or inappropriate content
- Respect rate limits and fair usage

## Payment Terms
- Credits are non-refundable
- Subscription fees are charged monthly
- Service may be suspended for non-payment

## Limitations
- Service provided "as is"
- No guarantee of uptime
- Features may change without notice

## Contact
For support: <EMAIL>
"""
        
        try:
            with open('privacy_policy.md', 'w') as f:
                f.write(privacy_policy)
            
            with open('terms_of_service.md', 'w') as f:
                f.write(terms_of_service)
            
            self.issues_fixed.append("Created privacy documentation")
            logger.info("✅ Created privacy and terms documentation")
        except Exception as e:
            logger.error(f"Failed to create privacy docs: {e}")
            self.issues_failed.append("Failed to create privacy docs")
    
    def run_fixes(self):
        """Run all critical fixes."""
        logger.info("🚀 Starting critical issues fix...")
        
        # Run all fixes
        self.fix_dependencies()
        self.create_env_template()
        self.fix_deepgram_imports()
        self.create_health_check()
        self.create_privacy_docs()
        
        # Generate report
        self.generate_report()
    
    def generate_report(self):
        """Generate fix report."""
        logger.info("\n" + "="*60)
        logger.info("CRITICAL ISSUES FIX REPORT")
        logger.info("="*60)
        
        logger.info(f"✅ Issues Fixed ({len(self.issues_fixed)}):")
        for issue in self.issues_fixed:
            logger.info(f"  • {issue}")
        
        if self.issues_failed:
            logger.info(f"\n❌ Issues Failed ({len(self.issues_failed)}):")
            for issue in self.issues_failed:
                logger.info(f"  • {issue}")
        
        logger.info("\n📋 NEXT STEPS:")
        logger.info("1. Copy .env.template to .env and fill in your API keys")
        logger.info("2. Test the bot with: python run_bot.py")
        logger.info("3. Run production tests: python run_production_tests.py")
        logger.info("4. Deploy when all tests pass")
        
        if len(self.issues_failed) == 0:
            logger.info("\n🎉 All critical issues have been addressed!")
        else:
            logger.info(f"\n⚠️ {len(self.issues_failed)} issues still need manual attention")

def main():
    """Main function."""
    fixer = CriticalIssuesFixer()
    fixer.run_fixes()

if __name__ == "__main__":
    main()
