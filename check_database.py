#!/usr/bin/env python3
"""
Check database schema for VoicePal.
"""

import sqlite3
import sys

def check_database(db_path: str) -> None:
    """
    Check database schema.
    
    Args:
        db_path: Path to the database file
    """
    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # Get list of tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print(f"Tables in database: {[table['name'] for table in tables]}")
        
        # Check users table schema
        cursor.execute("PRAGMA table_info(users)")
        columns = cursor.fetchall()
        
        print("\nColumns in users table:")
        for column in columns:
            print(f"  {column['name']} ({column['type']})")
        
        # Check if login_count column exists
        login_count_column = next((col for col in columns if col['name'] == 'login_count'), None)
        if login_count_column:
            print(f"\nlogin_count column exists with type: {login_count_column['type']}")
        else:
            print("\nlogin_count column does not exist")
        
        # Check sample data
        cursor.execute("SELECT * FROM users LIMIT 1")
        user = cursor.fetchone()
        
        if user:
            print("\nSample user data:")
            for key in user.keys():
                print(f"  {key}: {user[key]}")
        else:
            print("\nNo users found in database")
        
    except Exception as e:
        print(f"Error checking database: {e}")
    finally:
        if conn:
            conn.close()

def main():
    """Main entry point."""
    # Get database path from command line or use default
    db_path = sys.argv[1] if len(sys.argv) > 1 else "voicepal.db"
    
    # Check database schema
    check_database(db_path)

if __name__ == "__main__":
    main()
