#!/usr/bin/env python3
"""
Fix database schema for VoicePal.

This script adds missing columns to the users table.
"""

import sqlite3
import logging
import os
import sys

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

def fix_database_schema(db_path: str) -> None:
    """
    Fix database schema by adding missing columns.
    
    Args:
        db_path: Path to the database file
    """
    if not os.path.exists(db_path):
        logger.error(f"Database file not found: {db_path}")
        return
    
    logger.info(f"Fixing database schema for: {db_path}")
    
    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # Check current schema
        cursor.execute("PRAGMA table_info(users)")
        columns = cursor.fetchall()
        column_names = [column['name'] for column in columns]
        
        logger.info(f"Current columns in users table: {column_names}")
        
        # Add missing columns
        missing_columns = []
        
        # Check for login_count column
        if 'login_count' not in column_names:
            missing_columns.append(('login_count', 'INTEGER DEFAULT 1'))
        
        # Check for visit_count column
        if 'visit_count' not in column_names:
            missing_columns.append(('visit_count', 'INTEGER DEFAULT 0'))
        
        # Check for timezone column
        if 'timezone' not in column_names:
            missing_columns.append(('timezone', 'TEXT'))
        
        # Check for preferred_name column
        if 'preferred_name' not in column_names:
            missing_columns.append(('preferred_name', 'TEXT'))
        
        # Add missing columns
        for column_name, column_type in missing_columns:
            try:
                logger.info(f"Adding {column_name} column to users table")
                cursor.execute(f"ALTER TABLE users ADD COLUMN {column_name} {column_type}")
                conn.commit()
                logger.info(f"Successfully added {column_name} column")
            except Exception as e:
                logger.error(f"Error adding {column_name} column: {e}")
                conn.rollback()
        
        # Verify schema after changes
        cursor.execute("PRAGMA table_info(users)")
        columns_after = cursor.fetchall()
        column_names_after = [column['name'] for column in columns_after]
        
        logger.info(f"Columns in users table after fixes: {column_names_after}")
        
        # Check if all missing columns were added
        still_missing = [col for col, _ in missing_columns if col not in column_names_after]
        
        if still_missing:
            logger.error(f"Failed to add columns: {still_missing}")
        else:
            logger.info("All missing columns added successfully")
        
    except Exception as e:
        logger.error(f"Error fixing database schema: {e}")
    finally:
        if conn:
            conn.close()

def main():
    """Main entry point."""
    # Get database path from command line or use default
    db_path = sys.argv[1] if len(sys.argv) > 1 else "voicepal.db"
    
    # Fix database schema
    fix_database_schema(db_path)

if __name__ == "__main__":
    main()
