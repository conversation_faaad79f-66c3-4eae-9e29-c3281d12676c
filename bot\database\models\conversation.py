"""
Conversation domain models for VoicePal.

This module provides the conversation-related models for the VoicePal database.
"""

import logging
from typing import Dict, List, Any, Optional, ClassVar, Type, Set, Tuple
from datetime import datetime

from bot.database.models.model import Model

# Set up logging
logger = logging.getLogger(__name__)

class Conversation(Model):
    """Conversation model."""
    
    _table_name = "conversations"
    _primary_key = "conversation_id"
    _required_columns = ["user_id"]
    _defaults = {
        "is_active": True
    }
    _foreign_keys = {
        "user_id": ("users", "user_id")
    }
    
    def __init__(self,
                 conversation_id: Optional[str] = None,
                 user_id: str = None,
                 title: Optional[str] = None,
                 created_at: Optional[str] = None,
                 updated_at: Optional[str] = None,
                 is_active: bool = True,
                 **kwargs):
        """Initialize conversation model.
        
        Args:
            conversation_id: Conversation ID
            user_id: User ID
            title: Conversation title
            created_at: Creation timestamp
            updated_at: Last update timestamp
            is_active: Whether conversation is active
            **kwargs: Additional attributes
        """
        super().__init__(
            conversation_id=conversation_id,
            user_id=user_id,
            title=title,
            created_at=created_at,
            updated_at=updated_at,
            is_active=is_active,
            **kwargs
        )
    
    def update_title(self, title: str) -> None:
        """Update conversation title.
        
        Args:
            title: New title
        """
        self.title = title
        self.updated_at = datetime.now().isoformat()
    
    def archive(self) -> None:
        """Archive conversation."""
        self.is_active = False
        self.updated_at = datetime.now().isoformat()
    
    def activate(self) -> None:
        """Activate conversation."""
        self.is_active = True
        self.updated_at = datetime.now().isoformat()

class Message(Model):
    """Message model."""
    
    _table_name = "messages"
    _primary_key = "message_id"
    _required_columns = ["conversation_id", "user_id", "content", "role"]
    _foreign_keys = {
        "conversation_id": ("conversations", "conversation_id"),
        "user_id": ("users", "user_id")
    }
    
    # Valid roles
    ROLE_USER = "user"
    ROLE_ASSISTANT = "assistant"
    ROLE_SYSTEM = "system"
    
    VALID_ROLES = [ROLE_USER, ROLE_ASSISTANT, ROLE_SYSTEM]
    
    def __init__(self,
                 message_id: Optional[str] = None,
                 conversation_id: str = None,
                 user_id: str = None,
                 content: str = None,
                 role: str = None,
                 created_at: Optional[str] = None,
                 **kwargs):
        """Initialize message model.
        
        Args:
            message_id: Message ID
            conversation_id: Conversation ID
            user_id: User ID
            content: Message content
            role: Message role (user, assistant, system)
            created_at: Creation timestamp
            **kwargs: Additional attributes
        """
        super().__init__(
            message_id=message_id,
            conversation_id=conversation_id,
            user_id=user_id,
            content=content,
            role=role,
            created_at=created_at,
            **kwargs
        )
    
    def validate(self) -> List[str]:
        """Validate message attributes.
        
        Returns:
            List of validation error messages (empty if valid)
        """
        errors = super().validate()
        
        # Validate role
        if hasattr(self, "role") and self.role not in self.VALID_ROLES:
            errors.append(f"Invalid role: {self.role}. Must be one of {self.VALID_ROLES}")
        
        return errors
    
    def is_user_message(self) -> bool:
        """Check if message is from user.
        
        Returns:
            True if message is from user, False otherwise
        """
        return self.role == self.ROLE_USER
    
    def is_assistant_message(self) -> bool:
        """Check if message is from assistant.
        
        Returns:
            True if message is from assistant, False otherwise
        """
        return self.role == self.ROLE_ASSISTANT
    
    def is_system_message(self) -> bool:
        """Check if message is system message.
        
        Returns:
            True if message is system message, False otherwise
        """
        return self.role == self.ROLE_SYSTEM

class MessageMetadata(Model):
    """Message metadata model."""
    
    _table_name = "message_metadata"
    _primary_key = "metadata_id"
    _required_columns = ["message_id", "key"]
    _foreign_keys = {
        "message_id": ("messages", "message_id")
    }
    
    def __init__(self,
                 metadata_id: Optional[str] = None,
                 message_id: str = None,
                 key: str = None,
                 value: Optional[str] = None,
                 created_at: Optional[str] = None,
                 **kwargs):
        """Initialize message metadata model.
        
        Args:
            metadata_id: Metadata ID
            message_id: Message ID
            key: Metadata key
            value: Metadata value
            created_at: Creation timestamp
            **kwargs: Additional attributes
        """
        super().__init__(
            metadata_id=metadata_id,
            message_id=message_id,
            key=key,
            value=value,
            created_at=created_at,
            **kwargs
        )
