"""
Telegram payment provider for VoicePal.

This module provides a provider for Telegram payment services.
"""

import os
import logging
import asyncio
import json
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass, field

from telegram import LabeledPrice, Bot
from telegram.error import TelegramError

from bot.providers.core.provider import PaymentProvider
from bot.providers.core.config import PaymentProviderConfig
from bot.providers.core.exceptions import (
    ProviderError,
    ProviderConfigError,
    ProviderAuthError,
    ProviderRateLimitError,
    ProviderTimeoutError,
    ProviderNotFoundError,
    ProviderValidationError,
    ProviderNotInitializedError
)

# Set up logging
logger = logging.getLogger(__name__)

@dataclass
class TelegramPaymentConfig(PaymentProviderConfig):
    """Configuration for Telegram payment provider."""
    
    api_key: str = ""  # Bot token
    provider_token: str = ""  # Payment provider token
    currency: str = "USD"
    timeout: int = 30
    
    def __post_init__(self):
        """Post-initialization."""
        self.provider_type = "payment"
        self.provider_name = "telegram_payment"
    
    def validate(self) -> List[str]:
        """Validate configuration.
        
        Returns:
            List of validation error messages (empty if valid)
        """
        errors = super().validate()
        
        # Validate provider token
        if not self.provider_token:
            errors.append("Provider token is required")
        
        # Validate currency
        valid_currencies = ["USD", "EUR", "GBP", "JPY", "CAD", "AUD", "CHF", "CNY", "SEK", "NZD", "MXN", "SGD", "HKD", "NOK", "KRW", "TRY", "RUB", "INR", "BRL", "ZAR"]
        if self.currency not in valid_currencies:
            errors.append(f"Invalid currency: {self.currency}. Must be one of {valid_currencies}")
        
        return errors

class TelegramPaymentProvider(PaymentProvider[TelegramPaymentConfig]):
    """Provider for Telegram payment services."""
    
    provider_type = "payment"
    provider_name = "telegram_payment"
    provider_version = "1.0.0"
    provider_description = "Provider for Telegram payment services"
    config_class = TelegramPaymentConfig
    
    def __init__(self, config: TelegramPaymentConfig):
        """Initialize provider.
        
        Args:
            config: Provider configuration
        """
        super().__init__(config)
        self.bot = None
        self.payments = {}
    
    def validate_config(self) -> None:
        """Validate provider configuration.
        
        Raises:
            ProviderConfigError: If configuration is invalid
        """
        errors = self.config.validate()
        if errors:
            error_message = "; ".join(errors)
            raise ProviderConfigError(f"Invalid configuration: {error_message}")
    
    def initialize(self) -> None:
        """Initialize provider.
        
        Raises:
            ProviderInitializationError: If initialization fails
        """
        try:
            # Initialize bot
            self.bot = Bot(token=self.config.api_key)
            
            self.initialized = True
            logger.info(f"Initialized {self.provider_name} provider")
        except Exception as e:
            logger.error(f"Failed to initialize {self.provider_name} provider: {e}")
            raise ProviderInitializationError(f"Failed to initialize {self.provider_name} provider: {e}") from e
    
    def shutdown(self) -> None:
        """Shutdown provider.
        
        Raises:
            ProviderShutdownError: If shutdown fails
        """
        self.bot = None
        self.payments = {}
        self.initialized = False
        logger.info(f"Shutdown {self.provider_name} provider")
    
    async def create_payment(self, amount: int, currency: str, description: str, **kwargs) -> Dict[str, Any]:
        """Create a payment.
        
        Args:
            amount: Payment amount in cents
            currency: Payment currency
            description: Payment description
            **kwargs: Additional arguments
            
        Returns:
            Payment information
            
        Raises:
            ProviderError: If payment creation fails
        """
        if not self.initialized:
            raise ProviderNotInitializedError(f"{self.provider_name} provider is not initialized")
        
        try:
            # Get chat ID
            chat_id = kwargs.get("chat_id")
            if not chat_id:
                raise ProviderValidationError("Chat ID is required")
            
            # Get title
            title = kwargs.get("title", "Payment")
            
            # Get payload
            payload = kwargs.get("payload", f"payment_{chat_id}_{int(asyncio.get_event_loop().time())}")
            
            # Get photo URL and dimensions
            photo_url = kwargs.get("photo_url")
            photo_width = kwargs.get("photo_width")
            photo_height = kwargs.get("photo_height")
            
            # Get need name, phone, email, shipping address
            need_name = kwargs.get("need_name", False)
            need_phone_number = kwargs.get("need_phone_number", False)
            need_email = kwargs.get("need_email", False)
            need_shipping_address = kwargs.get("need_shipping_address", False)
            
            # Get is flexible
            is_flexible = kwargs.get("is_flexible", False)
            
            # Get prices
            prices = kwargs.get("prices", [LabeledPrice(label=title, amount=amount)])
            if not prices:
                prices = [LabeledPrice(label=title, amount=amount)]
            
            # Create invoice
            message = await self.bot.send_invoice(
                chat_id=chat_id,
                title=title,
                description=description,
                payload=payload,
                provider_token=self.config.provider_token,
                currency=currency or self.config.currency,
                prices=prices,
                photo_url=photo_url,
                photo_width=photo_width,
                photo_height=photo_height,
                need_name=need_name,
                need_phone_number=need_phone_number,
                need_email=need_email,
                need_shipping_address=need_shipping_address,
                is_flexible=is_flexible
            )
            
            # Store payment information
            payment_info = {
                "id": payload,
                "chat_id": chat_id,
                "message_id": message.message_id,
                "amount": amount,
                "currency": currency or self.config.currency,
                "description": description,
                "title": title,
                "status": "pending",
                "created": int(asyncio.get_event_loop().time())
            }
            
            self.payments[payload] = payment_info
            
            return payment_info
        except TelegramError as e:
            logger.error(f"Failed to create payment: {e}")
            
            # Map Telegram exceptions to provider exceptions
            if "unauthorized" in str(e).lower():
                raise ProviderAuthError(f"Authentication failed: {e}") from e
            elif "flood" in str(e).lower():
                raise ProviderRateLimitError(f"Rate limit exceeded: {e}") from e
            elif "not found" in str(e).lower():
                raise ProviderNotFoundError(f"Resource not found: {e}") from e
            elif "timeout" in str(e).lower():
                raise ProviderTimeoutError(f"Request timed out: {e}") from e
            elif "invalid" in str(e).lower():
                raise ProviderValidationError(f"Invalid request: {e}") from e
            else:
                raise ProviderError(f"Failed to create payment: {e}") from e
        except Exception as e:
            logger.error(f"Failed to create payment: {e}")
            raise ProviderError(f"Failed to create payment: {e}") from e
    
    async def get_payment_status(self, payment_id: str) -> Dict[str, Any]:
        """Get payment status.
        
        Args:
            payment_id: Payment ID
            
        Returns:
            Payment status
            
        Raises:
            ProviderError: If getting payment status fails
        """
        if not self.initialized:
            raise ProviderNotInitializedError(f"{self.provider_name} provider is not initialized")
        
        try:
            # Get payment information
            payment_info = self.payments.get(payment_id)
            if not payment_info:
                raise ProviderNotFoundError(f"Payment not found: {payment_id}")
            
            return payment_info
        except Exception as e:
            logger.error(f"Failed to get payment status: {e}")
            
            # Re-raise provider exceptions
            if isinstance(e, ProviderError):
                raise
            
            raise ProviderError(f"Failed to get payment status: {e}") from e
    
    async def update_payment_status(self, payment_id: str, status: str, **kwargs) -> Dict[str, Any]:
        """Update payment status.
        
        Args:
            payment_id: Payment ID
            status: Payment status
            **kwargs: Additional arguments
            
        Returns:
            Updated payment information
            
        Raises:
            ProviderError: If updating payment status fails
        """
        if not self.initialized:
            raise ProviderNotInitializedError(f"{self.provider_name} provider is not initialized")
        
        try:
            # Get payment information
            payment_info = self.payments.get(payment_id)
            if not payment_info:
                raise ProviderNotFoundError(f"Payment not found: {payment_id}")
            
            # Update status
            payment_info["status"] = status
            
            # Update additional fields
            for key, value in kwargs.items():
                payment_info[key] = value
            
            # Store updated payment information
            self.payments[payment_id] = payment_info
            
            return payment_info
        except Exception as e:
            logger.error(f"Failed to update payment status: {e}")
            
            # Re-raise provider exceptions
            if isinstance(e, ProviderError):
                raise
            
            raise ProviderError(f"Failed to update payment status: {e}") from e
    
    async def create_subscription(self, customer_id: str, price_id: str, **kwargs) -> Dict[str, Any]:
        """Create a subscription.
        
        Args:
            customer_id: Customer ID
            price_id: Price ID
            **kwargs: Additional arguments
            
        Returns:
            Subscription information
            
        Raises:
            ProviderError: If subscription creation fails
        """
        # Telegram doesn't support subscriptions directly
        raise ProviderUnsupportedError("Telegram doesn't support subscriptions directly")
    
    async def cancel_subscription(self, subscription_id: str) -> Dict[str, Any]:
        """Cancel a subscription.
        
        Args:
            subscription_id: Subscription ID
            
        Returns:
            Cancellation result
            
        Raises:
            ProviderError: If subscription cancellation fails
        """
        # Telegram doesn't support subscriptions directly
        raise ProviderUnsupportedError("Telegram doesn't support subscriptions directly")
