"""
Feature flags endpoints for VoicePal API.
"""

import logging
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from bot.api.dependencies import get_advanced_features_manager

logger = logging.getLogger(__name__)
router = APIRouter()

class FeatureFlagCreate(BaseModel):
    name: str
    description: str
    flag_type: str
    default_value: bool
    rollout_strategy: str
    rollout_config: dict

class FeatureFlagUpdate(BaseModel):
    enabled: bool = None
    rollout_config: dict = None

@router.post("/feature-flags", status_code=201)
async def create_feature_flag(
    flag_data: FeatureFlagCreate,
    advanced=Depends(get_advanced_features_manager)
):
    """Create a new feature flag."""
    try:
        flag_id = advanced.create_feature_flag(**flag_data.dict())
        return {"flag_id": flag_id, "message": "Feature flag created successfully"}
    except Exception as e:
        logger.error(f"Error creating feature flag: {e}")
        raise HTTPException(status_code=500, detail="Error creating feature flag")

@router.get("/feature-flags")
async def list_feature_flags(advanced=Depends(get_advanced_features_manager)):
    """List all feature flags."""
    try:
        flags = advanced.feature_flag_manager.list_flags()
        return {"flags": [flag.__dict__ if hasattr(flag, '__dict__') else flag for flag in flags]}
    except Exception as e:
        logger.error(f"Error listing feature flags: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving feature flags")

@router.put("/feature-flags/{flag_id}")
async def update_feature_flag(
    flag_id: str,
    flag_update: FeatureFlagUpdate,
    advanced=Depends(get_advanced_features_manager)
):
    """Update a feature flag."""
    try:
        success = advanced.feature_flag_manager.update_flag(flag_id, **flag_update.dict(exclude_unset=True))
        if not success:
            raise HTTPException(status_code=404, detail="Feature flag not found")
        return {"message": "Feature flag updated successfully"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating feature flag: {e}")
        raise HTTPException(status_code=500, detail="Error updating feature flag")
