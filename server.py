"""
Simple server script for Render.com deployment.

This script helps <PERSON><PERSON> understand that this is a web service.
It simply imports and runs the main function from the bot module.
"""

import os
import logging
from bot.main import main
import asyncio

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

if __name__ == "__main__":
    logger.info("Starting VoicePal bot server...")
    
    # Set environment variables for Render
    os.environ["RENDER"] = "true"
    
    # Get the port from environment or use default
    port = os.environ.get("PORT", "8443")
    logger.info(f"Using port: {port}")
    
    # Run the bot
    asyncio.run(main())
