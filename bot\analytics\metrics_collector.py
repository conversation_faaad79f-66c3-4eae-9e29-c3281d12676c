"""
Metrics collector for VoicePal analytics.

This module collects and aggregates various metrics for analytics and monitoring.
"""

import logging
import time
import threading
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass
from collections import defaultdict, deque
import statistics

logger = logging.getLogger(__name__)

@dataclass
class Metric:
    """Metric data point."""
    name: str
    value: float
    timestamp: datetime
    tags: Dict[str, str]
    unit: str

@dataclass
class MetricSummary:
    """Metric summary statistics."""
    name: str
    count: int
    min_value: float
    max_value: float
    avg_value: float
    sum_value: float
    last_value: float
    unit: str

class MetricsCollector:
    """Metrics collection and aggregation system."""
    
    def __init__(
        self,
        database,
        redis_client=None,
        collection_interval: int = 60,
        retention_days: int = 30
    ):
        """
        Initialize metrics collector.
        
        Args:
            database: Database instance
            redis_client: Redis client for real-time metrics
            collection_interval: Collection interval in seconds
            retention_days: Number of days to retain metrics
        """
        self.database = database
        self.redis_client = redis_client
        self.collection_interval = collection_interval
        self.retention_days = retention_days
        
        # In-memory metrics storage
        self.metrics_buffer: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self.metric_callbacks: Dict[str, List[Callable]] = defaultdict(list)
        
        # Collection thread
        self.collection_thread: Optional[threading.Thread] = None
        self.is_collecting = False
        
        # Initialize metrics table
        self._initialize_metrics_table()
        
        logger.info("Metrics collector initialized")
    
    def _initialize_metrics_table(self):
        """Initialize metrics table if it doesn't exist."""
        try:
            self.database.execute("""
                CREATE TABLE IF NOT EXISTS metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    value REAL NOT NULL,
                    timestamp TEXT NOT NULL,
                    tags TEXT,
                    unit TEXT,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Create indexes
            self.database.execute("CREATE INDEX IF NOT EXISTS idx_metrics_name ON metrics(name)")
            self.database.execute("CREATE INDEX IF NOT EXISTS idx_metrics_timestamp ON metrics(timestamp)")
            
            self.database.commit()
            
        except Exception as e:
            logger.error(f"Failed to initialize metrics table: {e}")
    
    def start_collection(self):
        """Start metrics collection."""
        if self.is_collecting:
            return
        
        self.is_collecting = True
        self.collection_thread = threading.Thread(
            target=self._collection_loop,
            daemon=True,
            name="MetricsCollector"
        )
        self.collection_thread.start()
        
        logger.info("Metrics collection started")
    
    def stop_collection(self):
        """Stop metrics collection."""
        self.is_collecting = False
        if self.collection_thread:
            self.collection_thread.join(timeout=5)
        
        # Flush remaining metrics
        self.flush_metrics()
        
        logger.info("Metrics collection stopped")
    
    def _collection_loop(self):
        """Main collection loop."""
        while self.is_collecting:
            try:
                # Collect system metrics
                self._collect_system_metrics()
                
                # Collect application metrics
                self._collect_application_metrics()
                
                # Flush metrics to database
                self.flush_metrics()
                
                # Sleep until next collection
                time.sleep(self.collection_interval)
                
            except Exception as e:
                logger.error(f"Error in metrics collection loop: {e}")
                time.sleep(self.collection_interval)
    
    def _collect_system_metrics(self):
        """Collect system-level metrics."""
        try:
            import psutil
            
            # CPU metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            self.record_metric("system.cpu.percent", cpu_percent, unit="percent")
            
            # Memory metrics
            memory = psutil.virtual_memory()
            self.record_metric("system.memory.percent", memory.percent, unit="percent")
            self.record_metric("system.memory.used", memory.used / 1024 / 1024, unit="MB")
            self.record_metric("system.memory.available", memory.available / 1024 / 1024, unit="MB")
            
            # Disk metrics
            disk = psutil.disk_usage('/')
            self.record_metric("system.disk.percent", disk.percent, unit="percent")
            self.record_metric("system.disk.used", disk.used / 1024 / 1024 / 1024, unit="GB")
            self.record_metric("system.disk.free", disk.free / 1024 / 1024 / 1024, unit="GB")
            
            # Network metrics
            network = psutil.net_io_counters()
            self.record_metric("system.network.bytes_sent", network.bytes_sent, unit="bytes")
            self.record_metric("system.network.bytes_recv", network.bytes_recv, unit="bytes")
            
        except Exception as e:
            logger.error(f"Failed to collect system metrics: {e}")
    
    def _collect_application_metrics(self):
        """Collect application-level metrics."""
        try:
            # Database metrics
            self._collect_database_metrics()
            
            # User metrics
            self._collect_user_metrics()
            
            # Conversation metrics
            self._collect_conversation_metrics()
            
            # Revenue metrics
            self._collect_revenue_metrics()
            
        except Exception as e:
            logger.error(f"Failed to collect application metrics: {e}")
    
    def _collect_database_metrics(self):
        """Collect database-related metrics."""
        try:
            # Total users
            total_users = self.database.execute("SELECT COUNT(*) FROM users").fetchone()[0]
            self.record_metric("app.users.total", total_users, unit="count")
            
            # Active users (last 24 hours)
            yesterday = datetime.utcnow() - timedelta(days=1)
            active_users = self.database.execute("""
                SELECT COUNT(DISTINCT user_id) FROM conversations
                WHERE created_at >= ?
            """, (yesterday.isoformat(),)).fetchone()[0]
            self.record_metric("app.users.active_24h", active_users, unit="count")
            
            # Total conversations
            total_conversations = self.database.execute("SELECT COUNT(*) FROM conversations").fetchone()[0]
            self.record_metric("app.conversations.total", total_conversations, unit="count")
            
            # Total messages
            total_messages = self.database.execute("SELECT COUNT(*) FROM messages").fetchone()[0]
            self.record_metric("app.messages.total", total_messages, unit="count")
            
        except Exception as e:
            logger.error(f"Failed to collect database metrics: {e}")
    
    def _collect_user_metrics(self):
        """Collect user-related metrics."""
        try:
            # New users today
            today = datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0)
            new_users_today = self.database.execute("""
                SELECT COUNT(*) FROM users
                WHERE created_at >= ?
            """, (today.isoformat(),)).fetchone()[0]
            self.record_metric("app.users.new_today", new_users_today, unit="count")
            
            # Average credits per user
            avg_credits = self.database.execute("""
                SELECT AVG(credits) FROM users WHERE credits > 0
            """).fetchone()[0] or 0
            self.record_metric("app.users.avg_credits", avg_credits, unit="credits")
            
        except Exception as e:
            logger.error(f"Failed to collect user metrics: {e}")
    
    def _collect_conversation_metrics(self):
        """Collect conversation-related metrics."""
        try:
            # Conversations today
            today = datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0)
            conversations_today = self.database.execute("""
                SELECT COUNT(*) FROM conversations
                WHERE created_at >= ?
            """, (today.isoformat(),)).fetchone()[0]
            self.record_metric("app.conversations.today", conversations_today, unit="count")
            
            # Messages today
            messages_today = self.database.execute("""
                SELECT COUNT(*) FROM messages m
                JOIN conversations c ON m.conversation_id = c.conversation_id
                WHERE c.created_at >= ?
            """, (today.isoformat(),)).fetchone()[0]
            self.record_metric("app.messages.today", messages_today, unit="count")
            
            # Average messages per conversation (last 7 days)
            week_ago = datetime.utcnow() - timedelta(days=7)
            avg_messages = self.database.execute("""
                SELECT AVG(message_count) FROM (
                    SELECT COUNT(m.message_id) as message_count
                    FROM conversations c
                    LEFT JOIN messages m ON c.conversation_id = m.conversation_id
                    WHERE c.created_at >= ?
                    GROUP BY c.conversation_id
                )
            """, (week_ago.isoformat(),)).fetchone()[0] or 0
            self.record_metric("app.conversations.avg_messages", avg_messages, unit="count")
            
        except Exception as e:
            logger.error(f"Failed to collect conversation metrics: {e}")
    
    def _collect_revenue_metrics(self):
        """Collect revenue-related metrics."""
        try:
            # Revenue today
            today = datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0)
            revenue_today = self.database.execute("""
                SELECT COALESCE(SUM(amount), 0) FROM transactions
                WHERE status = 'completed' AND created_at >= ?
            """, (today.isoformat(),)).fetchone()[0]
            self.record_metric("app.revenue.today", revenue_today, unit="USD")
            
            # Successful payments today
            payments_today = self.database.execute("""
                SELECT COUNT(*) FROM transactions
                WHERE status = 'completed' AND created_at >= ?
            """, (today.isoformat(),)).fetchone()[0]
            self.record_metric("app.payments.successful_today", payments_today, unit="count")
            
        except Exception as e:
            logger.error(f"Failed to collect revenue metrics: {e}")
    
    def record_metric(
        self,
        name: str,
        value: float,
        tags: Optional[Dict[str, str]] = None,
        unit: str = "count",
        timestamp: Optional[datetime] = None
    ):
        """
        Record a metric.
        
        Args:
            name: Metric name
            value: Metric value
            tags: Optional tags
            unit: Metric unit
            timestamp: Optional timestamp (defaults to now)
        """
        try:
            metric = Metric(
                name=name,
                value=value,
                timestamp=timestamp or datetime.utcnow(),
                tags=tags or {},
                unit=unit
            )
            
            # Add to buffer
            self.metrics_buffer[name].append(metric)
            
            # Store in Redis for real-time access
            if self.redis_client:
                try:
                    key = f"metrics:{name}"
                    self.redis_client.lpush(key, f"{metric.timestamp.isoformat()}:{value}")
                    self.redis_client.expire(key, 86400)  # 24 hours TTL
                except Exception as e:
                    logger.warning(f"Failed to store metric in Redis: {e}")
            
            # Trigger callbacks
            for callback in self.metric_callbacks[name]:
                try:
                    callback(metric)
                except Exception as e:
                    logger.warning(f"Metric callback failed: {e}")
            
        except Exception as e:
            logger.error(f"Failed to record metric {name}: {e}")
    
    def flush_metrics(self):
        """Flush metrics buffer to database."""
        if not any(self.metrics_buffer.values()):
            return
        
        try:
            metrics_data = []
            
            for name, metrics in self.metrics_buffer.items():
                while metrics:
                    metric = metrics.popleft()
                    metrics_data.append((
                        metric.name,
                        metric.value,
                        metric.timestamp.isoformat(),
                        str(metric.tags) if metric.tags else None,
                        metric.unit
                    ))
            
            if metrics_data:
                self.database.executemany("""
                    INSERT INTO metrics (name, value, timestamp, tags, unit)
                    VALUES (?, ?, ?, ?, ?)
                """, metrics_data)
                
                self.database.commit()
                
                logger.debug(f"Flushed {len(metrics_data)} metrics to database")
            
        except Exception as e:
            logger.error(f"Failed to flush metrics: {e}")
    
    def get_metric_summary(self, name: str, hours: int = 24) -> Optional[MetricSummary]:
        """
        Get metric summary for the specified time period.
        
        Args:
            name: Metric name
            hours: Number of hours to analyze
            
        Returns:
            Metric summary or None
        """
        try:
            self.flush_metrics()
            
            cutoff_time = datetime.utcnow() - timedelta(hours=hours)
            
            metrics = self.database.execute("""
                SELECT value, unit FROM metrics
                WHERE name = ? AND timestamp >= ?
                ORDER BY timestamp
            """, (name, cutoff_time.isoformat())).fetchall()
            
            if not metrics:
                return None
            
            values = [m['value'] for m in metrics]
            unit = metrics[0]['unit']
            
            return MetricSummary(
                name=name,
                count=len(values),
                min_value=min(values),
                max_value=max(values),
                avg_value=statistics.mean(values),
                sum_value=sum(values),
                last_value=values[-1],
                unit=unit
            )
            
        except Exception as e:
            logger.error(f"Failed to get metric summary: {e}")
            return None
    
    def get_metric_trends(self, name: str, hours: int = 24, interval_minutes: int = 60) -> List[Dict[str, Any]]:
        """
        Get metric trends over time.
        
        Args:
            name: Metric name
            hours: Number of hours to analyze
            interval_minutes: Interval for aggregation in minutes
            
        Returns:
            List of trend data points
        """
        try:
            self.flush_metrics()
            
            trends = []
            end_time = datetime.utcnow()
            start_time = end_time - timedelta(hours=hours)
            
            # Calculate number of intervals
            total_minutes = hours * 60
            num_intervals = total_minutes // interval_minutes
            
            for i in range(num_intervals):
                interval_start = start_time + timedelta(minutes=i * interval_minutes)
                interval_end = interval_start + timedelta(minutes=interval_minutes)
                
                # Get metrics for this interval
                metrics = self.database.execute("""
                    SELECT value FROM metrics
                    WHERE name = ? AND timestamp >= ? AND timestamp < ?
                """, (name, interval_start.isoformat(), interval_end.isoformat())).fetchall()
                
                if metrics:
                    values = [m['value'] for m in metrics]
                    trends.append({
                        "timestamp": interval_start.isoformat(),
                        "count": len(values),
                        "avg_value": statistics.mean(values),
                        "min_value": min(values),
                        "max_value": max(values),
                        "sum_value": sum(values)
                    })
                else:
                    trends.append({
                        "timestamp": interval_start.isoformat(),
                        "count": 0,
                        "avg_value": 0,
                        "min_value": 0,
                        "max_value": 0,
                        "sum_value": 0
                    })
            
            return trends
            
        except Exception as e:
            logger.error(f"Failed to get metric trends: {e}")
            return []
    
    def add_metric_callback(self, metric_name: str, callback: Callable[[Metric], None]):
        """
        Add callback for metric updates.
        
        Args:
            metric_name: Metric name to watch
            callback: Callback function
        """
        self.metric_callbacks[metric_name].append(callback)
    
    def remove_metric_callback(self, metric_name: str, callback: Callable):
        """
        Remove metric callback.
        
        Args:
            metric_name: Metric name
            callback: Callback function to remove
        """
        if callback in self.metric_callbacks[metric_name]:
            self.metric_callbacks[metric_name].remove(callback)
    
    def get_all_metric_names(self) -> List[str]:
        """Get list of all metric names."""
        try:
            self.flush_metrics()
            
            result = self.database.execute("""
                SELECT DISTINCT name FROM metrics
                ORDER BY name
            """).fetchall()
            
            return [row['name'] for row in result]
            
        except Exception as e:
            logger.error(f"Failed to get metric names: {e}")
            return []
    
    def cleanup_old_metrics(self):
        """Clean up old metrics beyond retention period."""
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=self.retention_days)
            
            result = self.database.execute("""
                DELETE FROM metrics
                WHERE timestamp < ?
            """, (cutoff_date.isoformat(),))
            
            self.database.commit()
            
            logger.info(f"Cleaned up {result.rowcount} old metrics")
            
        except Exception as e:
            logger.error(f"Failed to cleanup old metrics: {e}")
    
    def get_dashboard_metrics(self) -> Dict[str, Any]:
        """Get key metrics for dashboard display."""
        try:
            dashboard = {}
            
            # Key metrics for the last 24 hours
            key_metrics = [
                "app.users.active_24h",
                "app.conversations.today",
                "app.messages.today",
                "app.revenue.today",
                "system.cpu.percent",
                "system.memory.percent"
            ]
            
            for metric_name in key_metrics:
                summary = self.get_metric_summary(metric_name, hours=24)
                if summary:
                    dashboard[metric_name] = {
                        "current": summary.last_value,
                        "average": summary.avg_value,
                        "min": summary.min_value,
                        "max": summary.max_value,
                        "unit": summary.unit
                    }
            
            return dashboard
            
        except Exception as e:
            logger.error(f"Failed to get dashboard metrics: {e}")
            return {}
