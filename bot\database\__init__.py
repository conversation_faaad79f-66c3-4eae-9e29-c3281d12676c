"""
Database module for VoicePal.

This module provides database functionality for VoicePal.
"""

from bot.database.core import (
    DatabaseError,
    DatabaseConnectionError,
    DatabaseQueryError,
    DatabaseIntegrityError,
    DatabaseMigrationError,
    DatabaseSchemaError,
    DatabaseTransactionError,
    DatabaseTimeoutError,
    DatabaseNotFoundError,
    DatabaseDuplicateError,
    InsufficientCreditsError,
    DatabaseConnection
)
from bot.database.schema_manager import SchemaManager

# Import models
from bot.database.models import (
    Model,
    User,
    UserPreference,
    UserStat,
    Conversation,
    Message,
    MessageMetadata,
    Transaction,
    PaymentPackage,
    Subscription,
    Memory,
    MemoryTag,
    VoiceSetting,
    VoiceRecording
)

# Import repositories
from bot.database.repositories import (
    Repository,
    UserRepository,
    UserPreferenceRepository,
    UserStatRepository,
    ConversationRepository,
    MessageRepository,
    MessageMetadataRepository,
    TransactionRepository,
    PaymentPackageRepository,
    SubscriptionRepository,
    MemoryRepository,
    MemoryTagRepository,
    VoiceSettingRepository,
    VoiceRecordingRepository
)

# Legacy imports (for backward compatibility)
from bot.database.database_manager import DatabaseManager as Database
from bot.database.extensions.payment import extend_database_for_payment
from bot.database.extensions.sentiment import extend_database_for_sentiment
from bot.database.extensions.memory import extend_database_for_memory
from bot.database.extensions.user_preferences import extend_database_for_user_preferences
from bot.database.extensions.stats import extend_database_for_stats
from bot.database.extensions.verification import extend_database_for_verification

__all__ = [
    # Core
    'DatabaseError',
    'DatabaseConnectionError',
    'DatabaseQueryError',
    'DatabaseIntegrityError',
    'DatabaseMigrationError',
    'DatabaseSchemaError',
    'DatabaseTransactionError',
    'DatabaseTimeoutError',
    'DatabaseNotFoundError',
    'DatabaseDuplicateError',
    'InsufficientCreditsError',
    'DatabaseConnection',
    'SchemaManager',

    # Models
    'Model',
    'User',
    'UserPreference',
    'UserStat',
    'Conversation',
    'Message',
    'MessageMetadata',
    'Transaction',
    'PaymentPackage',
    'Subscription',
    'Memory',
    'MemoryTag',
    'VoiceSetting',
    'VoiceRecording',

    # Repositories
    'Repository',
    'UserRepository',
    'UserPreferenceRepository',
    'UserStatRepository',
    'ConversationRepository',
    'MessageRepository',
    'MessageMetadataRepository',
    'TransactionRepository',
    'PaymentPackageRepository',
    'SubscriptionRepository',
    'MemoryRepository',
    'MemoryTagRepository',
    'VoiceSettingRepository',
    'VoiceRecordingRepository',

    # Legacy (to be removed)
    'Database',
    'extend_database_for_payment',
    'extend_database_for_sentiment',
    'extend_database_for_memory',
    'extend_database_for_user_preferences',
    'extend_database_for_stats',
    'extend_database_for_verification'
]
