"""
Tests for the SchemaManager class.

This module provides tests for the SchemaManager class.
"""

import os
import unittest
import tempfile
from pathlib import Path

from bot.database.core.connection import DatabaseConnection
from bot.database.schema_manager import <PERSON>hema<PERSON>ana<PERSON>, CURRENT_SCHEMA_VERSION
from bot.database.core.exceptions import DatabaseSchemaError, DatabaseMigrationError

class TestSchemaManager(unittest.TestCase):
    """Test case for SchemaManager."""
    
    def setUp(self):
        """Set up test environment."""
        # Create temporary directory for database
        self.temp_dir = tempfile.TemporaryDirectory()
        self.db_path = Path(self.temp_dir.name) / "test.db"
        
        # Initialize connection
        self.connection = DatabaseConnection(self.db_path)
        
        # Initialize schema manager
        self.schema_manager = SchemaManager(self.connection)
    
    def tearDown(self):
        """Clean up test environment."""
        # Close connection
        self.connection.close()
        
        # Remove temporary directory
        self.temp_dir.cleanup()
    
    def test_initialization(self):
        """Test schema initialization."""
        # Initialize schema
        self.schema_manager.initialize_schema()
        
        # Check if schema_migrations table exists
        cursor = self.connection.execute(
            "SELECT name FROM sqlite_master WHERE type='table' AND name='schema_migrations'"
        )
        self.assertIsNotNone(cursor.fetchone())
        
        # Check if initial version is recorded
        cursor = self.connection.execute(
            "SELECT version FROM schema_migrations ORDER BY version DESC LIMIT 1"
        )
        version = cursor.fetchone()[0]
        self.assertEqual(version, 1)
    
    def test_get_current_version(self):
        """Test getting current schema version."""
        # Initialize schema
        self.schema_manager.initialize_schema()
        
        # Get current version
        version = self.schema_manager.get_current_version()
        self.assertEqual(version, 1)
    
    def test_validate_schema(self):
        """Test schema validation."""
        # Initialize schema
        self.schema_manager.initialize_schema()
        
        # Validate schema
        self.assertTrue(self.schema_manager.validate_schema())
        
        # Drop a table
        self.connection.execute("DROP TABLE users")
        
        # Validate schema again
        self.assertFalse(self.schema_manager.validate_schema())
    
    def test_migrate(self):
        """Test schema migration."""
        # Initialize schema
        self.schema_manager.initialize_schema()
        
        # Get current version
        version = self.schema_manager.get_current_version()
        self.assertEqual(version, 1)
        
        # Migrate to same version (no-op)
        self.schema_manager.migrate(1)
        version = self.schema_manager.get_current_version()
        self.assertEqual(version, 1)
        
        # Try to migrate to invalid version
        with self.assertRaises(DatabaseMigrationError):
            self.schema_manager.migrate(999)
    
    def test_table_creation(self):
        """Test table creation."""
        # Initialize schema
        self.schema_manager.initialize_schema()
        
        # Check if all tables exist
        cursor = self.connection.execute(
            "SELECT name FROM sqlite_master WHERE type='table'"
        )
        tables = [row[0] for row in cursor.fetchall()]
        
        expected_tables = [
            "users",
            "user_preferences",
            "user_stats",
            "conversations",
            "messages",
            "message_metadata",
            "memories",
            "memory_tags",
            "transactions",
            "payment_packages",
            "subscriptions",
            "voice_settings",
            "voice_recordings",
            "schema_migrations",
            "system_settings"
        ]
        
        for table in expected_tables:
            self.assertIn(table, tables)
    
    def test_index_creation(self):
        """Test index creation."""
        # Initialize schema
        self.schema_manager.initialize_schema()
        
        # Check if indexes exist
        cursor = self.connection.execute(
            "SELECT name FROM sqlite_master WHERE type='index'"
        )
        indexes = [row[0] for row in cursor.fetchall()]
        
        expected_indexes = [
            "idx_users_username",
            "idx_user_preferences_user_id",
            "idx_user_stats_user_id",
            "idx_conversations_user_id",
            "idx_messages_conversation_id",
            "idx_messages_user_id",
            "idx_message_metadata_message_id",
            "idx_memories_user_id",
            "idx_memory_tags_memory_id",
            "idx_memory_tags_tag",
            "idx_transactions_user_id",
            "idx_transactions_status",
            "idx_payment_packages_is_active",
            "idx_subscriptions_user_id",
            "idx_subscriptions_status",
            "idx_voice_settings_user_id",
            "idx_voice_recordings_user_id",
            "idx_voice_recordings_message_id"
        ]
        
        for index in expected_indexes:
            self.assertIn(index, indexes)
    
    def test_foreign_keys(self):
        """Test foreign key constraints."""
        # Initialize schema
        self.schema_manager.initialize_schema()
        
        # Enable foreign keys
        self.connection.execute("PRAGMA foreign_keys = ON")
        
        # Create a user
        self.connection.execute(
            "INSERT INTO users (user_id, username) VALUES (?, ?)",
            ("test_user", "test_username")
        )
        
        # Create a conversation
        self.connection.execute(
            "INSERT INTO conversations (conversation_id, user_id) VALUES (?, ?)",
            ("test_conversation", "test_user")
        )
        
        # Try to create a message with invalid conversation_id
        with self.assertRaises(sqlite3.IntegrityError):
            self.connection.execute(
                "INSERT INTO messages (message_id, conversation_id, user_id, content, role) VALUES (?, ?, ?, ?, ?)",
                ("test_message", "invalid_conversation", "test_user", "Test message", "user")
            )
        
        # Try to create a message with invalid user_id
        with self.assertRaises(sqlite3.IntegrityError):
            self.connection.execute(
                "INSERT INTO messages (message_id, conversation_id, user_id, content, role) VALUES (?, ?, ?, ?, ?)",
                ("test_message", "test_conversation", "invalid_user", "Test message", "user")
            )
        
        # Create a valid message
        self.connection.execute(
            "INSERT INTO messages (message_id, conversation_id, user_id, content, role) VALUES (?, ?, ?, ?, ?)",
            ("test_message", "test_conversation", "test_user", "Test message", "user")
        )
        
        # Check if message exists
        cursor = self.connection.execute(
            "SELECT * FROM messages WHERE message_id = ?",
            ("test_message",)
        )
        self.assertIsNotNone(cursor.fetchone())
        
        # Delete user and check if related records are deleted
        self.connection.execute(
            "DELETE FROM users WHERE user_id = ?",
            ("test_user",)
        )
        
        # Check if conversation is deleted
        cursor = self.connection.execute(
            "SELECT * FROM conversations WHERE conversation_id = ?",
            ("test_conversation",)
        )
        self.assertIsNone(cursor.fetchone())
        
        # Check if message is deleted
        cursor = self.connection.execute(
            "SELECT * FROM messages WHERE message_id = ?",
            ("test_message",)
        )
        self.assertIsNone(cursor.fetchone())

if __name__ == "__main__":
    unittest.main()
