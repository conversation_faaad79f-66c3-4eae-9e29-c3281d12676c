# VoicePal: Deployment Guide

This guide provides step-by-step instructions for deploying VoicePal on Render.com's free tier, which doesn't require a credit card.

## 1. Prerequisites

Before you begin, you'll need:

- A GitHub, GitLab, or Bitbucket account to host your code
- A Render.com account (free signup at [render.com](https://render.com))
- A Telegram Bot Token (from BotFather)
- A Deepgram API Key
- A Telegram Payments Provider Token (for monetization)

## 2. Preparing Your Repository

### 2.1 Create a GitHub Repository

1. Create a new repository on GitHub (or GitLab/Bitbucket)
2. Push your VoicePal code to the repository
3. Make sure your repository includes:
   - All the bot code
   - requirements.txt file
   - A Procfile for Render (see below)

### 2.2 Create a Procfile

Create a file named `Procfile` (no extension) in the root of your repository with the following content:

```
web: python -m bot.main
```

This tells <PERSON>der how to run your application.

### 2.3 Update Your Code for Render

Render assigns a dynamic port to your application through the `PORT` environment variable. Make sure your application listens on this port by adding the following to your main.py file:

```python
import os
port = int(os.environ.get("PORT", 10000))
# Use this port in your application
```

## 3. Deploying to Render

### 3.1 Create a Web Service

1. Sign in to your Render account at [dashboard.render.com](https://dashboard.render.com)
2. Click **New** and select **Web Service**
3. Connect your GitHub/GitLab/Bitbucket account if you haven't already
4. Select the repository containing your VoicePal code
5. Configure your web service:
   - **Name**: voicepal-bot
   - **Region**: Choose the region closest to your users
   - **Branch**: main (or your preferred branch)
   - **Runtime**: Python 3
   - **Build Command**: `pip install -r requirements.txt`
   - **Start Command**: `python -m bot.main`
6. Under **Advanced** settings, add the following environment variables:
   - `TELEGRAM_TOKEN`: Your Telegram bot token
   - `PAYMENT_PROVIDER_TOKEN`: Your Telegram payments provider token
   - `DEEPGRAM_API_KEY`: Your Deepgram API key
   - `ADMIN_USER_IDS`: Comma-separated list of admin Telegram user IDs
   - `DATABASE_FILE`: `/var/data/voicepal.db` (to store in Render's ephemeral storage)
   - `WEBHOOK_URL`: `https://your-service-name.onrender.com` (replace with your actual Render URL)
7. Select the **Free** instance type
8. Click **Create Web Service**

### 3.2 Understanding Free Tier Limitations

Render's free tier has some limitations to be aware of:

1. **Spin Down on Idle**: Your service will spin down after 15 minutes of inactivity and spin back up when a new request comes in. This causes a delay of up to 1 minute for the first request after inactivity.
2. **Monthly Usage Limits**: 750 free instance hours per month.
3. **No Persistent Storage**: Data is stored in ephemeral storage, which means it will be lost when the service restarts. For a production deployment, you would need to use a paid plan with a persistent disk or an external database.

## 4. Setting Up a Database (Optional)

For a more persistent database solution, you can use Render's free PostgreSQL database:

1. In the Render dashboard, click **New** and select **PostgreSQL**
2. Configure your database:
   - **Name**: voicepal-db
   - **Database**: voicepal
   - **User**: voicepal
   - **Region**: Same as your web service
3. Select the **Free** instance type
4. Click **Create Database**

Note that free PostgreSQL databases on Render:
- Have a 1GB storage limit
- Expire after 90 days
- Are limited to one per account

### 4.1 Connecting to PostgreSQL

If you choose to use PostgreSQL, you'll need to modify your database.py file to use PostgreSQL instead of SQLite. Add the following environment variable to your web service:

- `DATABASE_URL`: The internal connection string provided by Render after database creation

## 5. Setting Up the Admin Dashboard (Optional)

For the admin dashboard, you can create a separate web service:

1. In the Render dashboard, click **New** and select **Web Service**
2. Connect to the same repository
3. Configure your web service:
   - **Name**: voicepal-admin
   - **Region**: Same as your bot service
   - **Branch**: main (or your preferred branch)
   - **Runtime**: Python 3
   - **Build Command**: `pip install -r requirements.txt`
   - **Start Command**: `python -m reflex run --env prod --host 0.0.0.0 --port $PORT`
4. Add the same environment variables as your bot service
5. Select the **Free** instance type
6. Click **Create Web Service**

## 6. Monitoring and Maintenance

### 6.1 View Logs

1. In the Render dashboard, select your web service
2. Click on the **Logs** tab to view real-time logs
3. Use the dropdown to filter logs by type (Build, Deploy, Runtime)

### 6.2 Manual Deployment

1. In the Render dashboard, select your web service
2. Click the **Manual Deploy** button and select **Deploy latest commit**
3. Render will pull the latest code from your repository and deploy it

### 6.3 Automatic Deployment

By default, Render automatically deploys your service when you push changes to your connected repository branch.

## 7. Troubleshooting

### 7.1 Common Issues

#### Service Not Starting
- Check the logs in the Render dashboard for error messages
- Verify that your Start Command is correct
- Make sure all required environment variables are set

#### Bot Not Responding
- Check if your service is spun down due to inactivity (it will take up to a minute to respond)
- Verify your Telegram token in the environment variables
- Check the logs for errors

#### Database Issues
- If using SQLite, remember that data will be lost when the service restarts
- If using PostgreSQL, check the connection string and database credentials

### 7.2 Getting Help

If you encounter issues not covered in this guide, please:
- Check the [Render documentation](https://render.com/docs)
- Check the project's GitHub repository for known issues
- Consult the documentation for the specific component causing issues
- Reach out to the community for support

## 8. Production Considerations

For a production deployment, consider:

1. **Upgrading to a paid plan** to avoid spin-down and get more resources
2. **Using a persistent disk** or external database for data storage
3. **Setting up a custom domain** for your admin dashboard
4. **Implementing regular database backups** to prevent data loss

Remember that the free tier is great for testing and development but has limitations that make it less suitable for production use.
