"""
Base STT provider interface for VoicePal.

This module defines the base STT provider interface that all STT providers must implement.
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, Any

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class BaseSTTProvider(ABC):
    """Base class for STT providers."""

    @abstractmethod
    async def transcribe_audio(self, audio_file_path: str, **kwargs) -> Dict[str, Any]:
        """
        Transcribe audio to text.
        
        Args:
            audio_file_path: Path to audio file
            **kwargs: Additional provider-specific parameters
            
        Returns:
            Dict containing transcript, language, confidence, etc.
        """
        pass
    
    @abstractmethod
    def supports_feature(self, feature_name: str) -> bool:
        """
        Check if provider supports a specific feature.
        
        Args:
            feature_name: Name of the feature to check
            
        Returns:
            bool: True if the feature is supported, False otherwise
        """
        pass
    
    @abstractmethod
    def get_available_languages(self) -> Dict[str, str]:
        """
        Get available languages.
        
        Returns:
            Dictionary of language codes and names
        """
        pass
    
    @abstractmethod
    async def transcribe_with_sentiment(self, audio_file_path: str, **kwargs) -> Dict[str, Any]:
        """
        Transcribe audio to text with sentiment analysis.
        
        Args:
            audio_file_path: Path to audio file
            **kwargs: Additional provider-specific parameters
            
        Returns:
            Dict containing transcript, language, confidence, sentiment, etc.
        """
        pass
