"""
Test script for the Deepgram TTS provider caching system.

This script tests the caching functionality of the Deepgram TTS provider,
focusing on cache expiration, file management, and performance.
"""

import os
import sys
import logging
import time
import argparse
from pathlib import Path
from datetime import datetime, timedelta

# Add the parent directory to the path so we can import the bot modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from bot.providers.tts.deepgram_provider import DeepgramTT<PERSON>rovider

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

def test_cache_basic(api_key=None):
    """Test basic caching functionality."""
    logger.info("Testing basic caching functionality")
    
    # Create provider with short cache TTL
    provider = DeepgramTTSProvider(
        api_key=api_key,
        cache_ttl=5,  # 5 seconds TTL
        use_cache=True
    )
    
    # Force API initialization for testing
    provider.api_initialized = True
    
    try:
        # Generate speech for the first time
        logger.info("Generating speech for the first time")
        start_time = time.time()
        result1 = provider.generate_speech("Hello, this is a test of the caching system.")
        first_gen_time = time.time() - start_time
        logger.info(f"First generation took {first_gen_time:.2f} seconds")
        
        if result1:
            logger.info(f"Generated audio file: {result1}")
            
            # Check cache
            cache_size = len(provider.cache)
            logger.info(f"Cache size: {cache_size}")
            
            if cache_size > 0:
                logger.info("✅ Audio was cached")
            else:
                logger.error("❌ Audio was not cached")
            
            # Generate speech again with the same text
            logger.info("Generating speech again with the same text")
            start_time = time.time()
            result2 = provider.generate_speech("Hello, this is a test of the caching system.")
            second_gen_time = time.time() - start_time
            logger.info(f"Second generation took {second_gen_time:.2f} seconds")
            
            # Check if the same file was returned
            if result1 == result2:
                logger.info("✅ Same audio file returned from cache")
            else:
                logger.error("❌ Different audio file returned")
            
            # Check if second generation was faster (should be if using cache)
            if second_gen_time < first_gen_time:
                logger.info("✅ Cached generation was faster")
            else:
                logger.warning("⚠️ Cached generation was not faster")
            
            return True
        else:
            logger.error("❌ Failed to generate speech")
            return False
    except Exception as e:
        logger.error(f"Error testing basic caching: {e}")
        return False

def test_cache_expiration(api_key=None):
    """Test cache expiration."""
    logger.info("Testing cache expiration")
    
    # Create provider with very short cache TTL
    provider = DeepgramTTSProvider(
        api_key=api_key,
        cache_ttl=2,  # 2 seconds TTL
        use_cache=True
    )
    
    # Force API initialization for testing
    provider.api_initialized = True
    
    try:
        # Generate speech
        logger.info("Generating speech")
        result1 = provider.generate_speech("Testing cache expiration.")
        
        if result1:
            # Check cache
            cache_size_before = len(provider.cache)
            logger.info(f"Cache size before expiration: {cache_size_before}")
            
            # Wait for cache to expire
            logger.info("Waiting for cache to expire (3 seconds)...")
            time.sleep(3)
            
            # Clean expired cache
            logger.info("Cleaning expired cache")
            provider._clean_expired_cache()
            
            # Check cache again
            cache_size_after = len(provider.cache)
            logger.info(f"Cache size after expiration: {cache_size_after}")
            
            if cache_size_after < cache_size_before:
                logger.info("✅ Cache entries expired and were removed")
            else:
                logger.error("❌ Cache entries were not removed after expiration")
            
            # Generate speech again
            logger.info("Generating speech again after cache expiration")
            result2 = provider.generate_speech("Testing cache expiration.")
            
            # Check if a new file was generated
            if result1 != result2:
                logger.info("✅ New audio file generated after cache expiration")
            else:
                logger.error("❌ Same audio file returned after cache expiration")
            
            return True
        else:
            logger.error("❌ Failed to generate speech")
            return False
    except Exception as e:
        logger.error(f"Error testing cache expiration: {e}")
        return False

def test_cache_directory(api_key=None):
    """Test cache directory management."""
    logger.info("Testing cache directory management")
    
    # Create provider
    provider = DeepgramTTSProvider(
        api_key=api_key,
        cache_ttl=60,  # 60 seconds TTL
        use_cache=True
    )
    
    # Force API initialization for testing
    provider.api_initialized = True
    
    try:
        # Check if cache directory exists
        cache_dir = provider.cache_dir
        logger.info(f"Cache directory: {cache_dir}")
        
        if os.path.exists(cache_dir):
            logger.info("✅ Cache directory exists")
        else:
            logger.error("❌ Cache directory does not exist")
            return False
        
        # Generate multiple speech files
        test_texts = [
            "This is the first test text.",
            "Here is a second test text.",
            "And this is a third test text."
        ]
        
        for text in test_texts:
            result = provider.generate_speech(text)
            if result:
                logger.info(f"Generated audio file for '{text[:20]}...'")
            else:
                logger.error(f"Failed to generate audio for '{text[:20]}...'")
        
        # Check cache directory contents
        cache_files = list(Path(cache_dir).glob("*.mp3"))
        logger.info(f"Number of files in cache directory: {len(cache_files)}")
        
        if len(cache_files) >= len(test_texts):
            logger.info("✅ Cache directory contains expected number of files")
        else:
            logger.error("❌ Cache directory missing files")
        
        return True
    except Exception as e:
        logger.error(f"Error testing cache directory: {e}")
        return False

def main():
    """Run Deepgram TTS caching tests."""
    parser = argparse.ArgumentParser(description="Test the Deepgram TTS caching system")
    parser.add_argument("--api-key", help="Deepgram API key")
    parser.add_argument("--test", choices=["basic", "expiration", "directory", "all"], 
                        default="all", help="Test to run")
    args = parser.parse_args()
    
    # Get API key from arguments or environment
    api_key = args.api_key
    if not api_key:
        api_key = os.environ.get("DEEPGRAM_API_KEY")
    
    logger.info("Starting Deepgram TTS caching tests")
    
    if args.test == "basic" or args.test == "all":
        basic_success = test_cache_basic(api_key)
        logger.info(f"Basic caching test: {'Success' if basic_success else 'Failed'}")
    
    if args.test == "expiration" or args.test == "all":
        expiration_success = test_cache_expiration(api_key)
        logger.info(f"Cache expiration test: {'Success' if expiration_success else 'Failed'}")
    
    if args.test == "directory" or args.test == "all":
        directory_success = test_cache_directory(api_key)
        logger.info(f"Cache directory test: {'Success' if directory_success else 'Failed'}")
    
    logger.info("Deepgram TTS caching tests completed")

if __name__ == "__main__":
    main()
