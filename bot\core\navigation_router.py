"""
Unified Navigation Router for VoicePal.

This module provides a centralized navigation system that handles all callback queries
and ensures consistent UI behavior across the bot.
"""

import logging
from typing import Dict, Any, Optional, Tuple, Callable, List
from telegram import Update, InlineKeyboardMarkup
from telegram.ext import ContextTypes

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class NavigationRouter:
    """Centralized navigation router for handling all callback queries."""

    def __init__(self, bot_instance):
        """
        Initialize the navigation router.

        Args:
            bot_instance: VoicePalBot instance
        """
        self.bot_instance = bot_instance
        self.handlers: Dict[str, Callable] = {}
        self.pattern_handlers: List[Tuple[str, Callable]] = []
        self.fallback_handler: Optional[Callable] = None

        # Register core navigation handlers
        self._register_core_handlers()

        logger.info("Navigation router initialized")

    def register_handler(self, callback_data: str, handler: Callable) -> None:
        """
        Register a callback handler for specific callback data.

        Args:
            callback_data: Exact callback data to match
            handler: Handler function
        """
        self.handlers[callback_data] = handler
        logger.debug(f"Registered handler for callback: {callback_data}")

    def register_pattern_handler(self, pattern: str, handler: Callable) -> None:
        """
        Register a callback handler for callback data patterns.

        Args:
            pattern: Pattern to match (e.g., "buy_credits_")
            handler: Handler function
        """
        self.pattern_handlers.append((pattern, handler))
        logger.debug(f"Registered pattern handler for: {pattern}")

    def register_fallback_handler(self, handler: Callable) -> None:
        """
        Register a fallback handler for unmatched callbacks.

        Args:
            handler: Fallback handler function
        """
        self.fallback_handler = handler
        logger.debug("Registered fallback handler")

    def _register_core_handlers(self) -> None:
        """Register core navigation handlers."""
        # Main menu navigation
        self.register_handler("back_to_main", self._handle_back_to_main)
        self.register_handler("start_conversation", self._handle_start_conversation)

        # Profile and credits
        self.register_handler("show_profile", self._handle_show_profile)
        self.register_handler("show_credits", self._handle_show_credits)

        # Settings
        self.register_handler("show_settings", self._handle_show_settings)
        self.register_handler("show_voice_settings", self._handle_show_voice_settings)

        # Info and help
        self.register_handler("show_info", self._handle_show_info)
        self.register_handler("show_help", self._handle_show_help)

        # Mood and diary
        self.register_handler("show_mood_diary", self._handle_show_mood_diary)

        # Additional menu handlers
        self._register_additional_handlers()

        logger.info("Core navigation handlers registered")

    async def route_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> bool:
        """
        Route callback query to appropriate handler.

        Args:
            update: Telegram update
            context: Callback context

        Returns:
            bool: True if callback was handled, False otherwise
        """
        query = update.callback_query
        callback_data = query.data
        user_id = query.from_user.id

        logger.info(f"Routing callback: {callback_data} for user {user_id}")

        try:
            # Check exact match handlers first
            if callback_data in self.handlers:
                return await self._execute_handler(self.handlers[callback_data], update, context, callback_data, "exact")

            # Check pattern handlers
            for pattern, handler in self.pattern_handlers:
                if callback_data.startswith(pattern):
                    return await self._execute_handler(handler, update, context, callback_data, f"pattern:{pattern}")

            # Use fallback handler if available
            if self.fallback_handler:
                return await self._execute_handler(self.fallback_handler, update, context, callback_data, "fallback")

            logger.warning(f"No handler found for callback: {callback_data}")
            return False

        except Exception as e:
            return await self._handle_routing_error(update, context, callback_data, e)

    async def _execute_handler(self, handler: Callable, update: Update, context: ContextTypes.DEFAULT_TYPE,
                              callback_data: str, handler_type: str) -> bool:
        """
        Execute a callback handler and process its result.

        Args:
            handler: The handler function to execute
            update: Telegram update
            context: Callback context
            callback_data: The callback data being processed
            handler_type: Type of handler (exact, pattern, fallback)

        Returns:
            bool: True if callback was handled successfully, False otherwise
        """
        query = update.callback_query
        user_id = query.from_user.id

        try:
            # Execute the handler
            result = await handler(update, context)

            # If no result, assume it was handled internally
            if not result:
                logger.info(f"Handler for {callback_data} ({handler_type}) returned no result, assuming handled internally")
                return True

            # Process the result
            if isinstance(result, tuple) and len(result) == 2:
                response_text, reply_markup = result

                if response_text:
                    try:
                        await query.edit_message_text(
                            response_text,
                            reply_markup=reply_markup,
                            parse_mode="Markdown"
                        )
                    except Exception as edit_error:
                        # Handle "Message is not modified" error gracefully
                        if "Message is not modified" in str(edit_error):
                            logger.info(f"Message content unchanged for {handler_type} callback {callback_data}")
                            # Just acknowledge the query without changing the message
                            await query.answer("Settings updated")
                        else:
                            logger.error(f"Error editing message for {handler_type} callback {callback_data}: {edit_error}")
                            raise
                return True
            else:
                logger.warning(f"Handler for {callback_data} ({handler_type}) returned unexpected result format: {result}")
                return False

        except Exception as e:
            logger.error(f"Error executing {handler_type} handler for {callback_data}: {e}")
            import traceback
            logger.error(traceback.format_exc())

            # Save error in button state manager if available
            if hasattr(self.bot_instance, 'button_state_manager') and self.bot_instance.button_state_manager:
                self.bot_instance.button_state_manager.register_error(user_id, callback_data, e)

            # Re-raise to be handled by the central error handler
            raise

    async def _handle_routing_error(self, update: Update, context: ContextTypes.DEFAULT_TYPE,
                                   callback_data: str, error: Exception) -> bool:
        """
        Handle errors that occur during callback routing.

        Args:
            update: Telegram update
            context: Callback context
            callback_data: The callback data being processed
            error: The exception that occurred

        Returns:
            bool: True if error was handled, False otherwise
        """
        query = update.callback_query
        user_id = query.from_user.id

        logger.error(f"Error routing callback {callback_data}: {error}")
        import traceback
        logger.error(traceback.format_exc())

        # Save error in button state manager if available
        if hasattr(self.bot_instance, 'button_state_manager') and self.bot_instance.button_state_manager:
            self.bot_instance.button_state_manager.register_error(user_id, callback_data, error)

        # Send error message to user
        try:
            await query.edit_message_text(
                "Sorry, there was an error processing your request. Please try again.",
                reply_markup=self.bot_instance.menu_manager.get_main_menu_keyboard()
            )
        except Exception as edit_error:
            logger.error(f"Error sending error message: {edit_error}")

            # Try sending a new message if editing fails
            try:
                await query.message.reply_text(
                    "Sorry, there was an error processing your request. Please try again.",
                    reply_markup=self.bot_instance.menu_manager.get_main_menu_keyboard()
                )
            except Exception as reply_error:
                logger.error(f"Error sending fallback error message: {reply_error}")

        return True  # We handled the error

    # Core handler implementations
    async def _handle_back_to_main(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> Tuple[str, InlineKeyboardMarkup]:
        """Handle back to main menu."""
        return "📋 Main Menu:", self.bot_instance.menu_manager.get_main_menu_keyboard()

    async def _handle_start_conversation(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> Tuple[str, Optional[InlineKeyboardMarkup]]:
        """Handle start conversation."""
        return "I'm here to chat! What would you like to talk about today?", None

    async def _handle_show_profile(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> Tuple[str, InlineKeyboardMarkup]:
        """Handle show profile."""
        user_id = update.callback_query.from_user.id
        return "👤 Your Profile:", self.bot_instance.menu_manager.get_profile_keyboard(user_id)

    async def _handle_show_credits(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> Tuple[str, InlineKeyboardMarkup]:
        """Handle show credits."""
        user_id = update.callback_query.from_user.id
        credits = self.bot_instance.database.get_user_credits(user_id)
        return f"💰 Your Credits: {credits}", self.bot_instance.menu_manager.get_credits_keyboard()

    async def _handle_show_settings(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> Tuple[str, InlineKeyboardMarkup]:
        """Handle show settings."""
        return "⚙️ Settings:", self.bot_instance.menu_manager.get_settings_keyboard()

    async def _handle_show_voice_settings(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> Tuple[str, InlineKeyboardMarkup]:
        """Handle show voice settings."""
        return "🎤 Voice Settings:", self.bot_instance.menu_manager.get_voice_settings_keyboard()

    async def _handle_show_info(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> Tuple[str, InlineKeyboardMarkup]:
        """Handle show info."""
        return "ℹ️ Information:", self.bot_instance.menu_manager.get_info_keyboard()

    async def _handle_show_help(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> Tuple[str, InlineKeyboardMarkup]:
        """Handle show help."""
        return "❓ Help:", self.bot_instance.menu_manager.get_help_keyboard()

    async def _handle_show_mood_diary(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> Tuple[str, InlineKeyboardMarkup]:
        """Handle show mood diary."""
        return "📊 Mood Diary:", self.bot_instance.menu_manager.get_mood_diary_keyboard()

    def _register_additional_handlers(self) -> None:
        """Register additional menu handlers for all menu options."""
        # Profile and personality handlers
        self.register_handler("show_personality", self._handle_show_personality)
        self.register_handler("free_credits", self._handle_free_credits)
        self.register_handler("show_usage_history", self._handle_show_usage_history)

        # Settings handlers
        self.register_handler("show_language_settings", self._handle_show_language_settings)
        self.register_handler("show_notification_settings", self._handle_show_notification_settings)
        self.register_handler("show_privacy_settings", self._handle_show_privacy_settings)
        self.register_handler("show_clear_data", self._handle_show_clear_data)

        # Voice settings handlers are now imported from bot.handlers.voice_settings_handlers

        # Info handlers
        self.register_handler("show_about", self._handle_show_about)
        self.register_handler("show_whats_new", self._handle_show_whats_new)
        self.register_handler("show_terms", self._handle_show_terms)
        self.register_handler("show_privacy", self._handle_show_privacy_policy)
        self.register_handler("show_support", self._handle_show_support)
        self.register_handler("show_feedback", self._handle_show_feedback)

        # Mood diary handlers
        self.register_handler("add_mood_entry", self._handle_add_mood_entry)
        self.register_handler("view_mood_history", self._handle_view_mood_history)
        self.register_handler("mood_trends", self._handle_mood_trends)
        self.register_handler("mood_insights", self._handle_mood_insights)

        logger.info("Additional navigation handlers registered")

    # Additional handler implementations
    async def _handle_show_personality(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> Tuple[str, InlineKeyboardMarkup]:
        """Handle show personality."""
        message = "👤 Choose your personality:\n\nSelect how you'd like VoicePal to interact with you."
        # This would typically show personality options
        keyboard = [[InlineKeyboardButton("🔙 Back to Profile", callback_data="show_profile")]]
        return message, InlineKeyboardMarkup(keyboard)

    async def _handle_free_credits(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> Tuple[str, InlineKeyboardMarkup]:
        """Handle free credits."""
        message = "🎁 Free Credits:\n\nWatch ads or complete tasks to earn free credits!"
        keyboard = [[InlineKeyboardButton("🔙 Back to Profile", callback_data="show_profile")]]
        return message, InlineKeyboardMarkup(keyboard)

    async def _handle_show_usage_history(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> Tuple[str, InlineKeyboardMarkup]:
        """Handle show usage history."""
        message = "📊 Usage History:\n\nYour recent credit usage and activity."
        keyboard = [[InlineKeyboardButton("🔙 Back to Credits", callback_data="show_credits")]]
        return message, InlineKeyboardMarkup(keyboard)

    async def _handle_show_language_settings(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> Tuple[str, InlineKeyboardMarkup]:
        """Handle show language settings."""
        message = "🌐 Language Settings:\n\nChoose your preferred language for VoicePal."
        keyboard = [[InlineKeyboardButton("🔙 Back to Settings", callback_data="show_settings")]]
        return message, InlineKeyboardMarkup(keyboard)

    async def _handle_show_notification_settings(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> Tuple[str, InlineKeyboardMarkup]:
        """Handle show notification settings."""
        message = "🔔 Notification Settings:\n\nManage your notification preferences."
        keyboard = [[InlineKeyboardButton("🔙 Back to Settings", callback_data="show_settings")]]
        return message, InlineKeyboardMarkup(keyboard)

    async def _handle_show_privacy_settings(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> Tuple[str, InlineKeyboardMarkup]:
        """Handle show privacy settings."""
        message = "🔐 Privacy Settings:\n\nControl your data and privacy preferences."
        keyboard = [[InlineKeyboardButton("🔙 Back to Settings", callback_data="show_settings")]]
        return message, InlineKeyboardMarkup(keyboard)

    async def _handle_show_clear_data(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> Tuple[str, InlineKeyboardMarkup]:
        """Handle show clear data."""
        message = "🗑️ Clear Data:\n\n⚠️ This will permanently delete your conversation history and preferences."
        keyboard = [
            [InlineKeyboardButton("❌ Confirm Delete", callback_data="confirm_clear_data")],
            [InlineKeyboardButton("🔙 Back to Settings", callback_data="show_settings")]
        ]
        return message, InlineKeyboardMarkup(keyboard)

    async def _handle_show_about(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> Tuple[str, InlineKeyboardMarkup]:
        """Handle show about."""
        message = "📖 About VoicePal:\n\nVoicePal is your AI companion for meaningful conversations and emotional support."
        keyboard = [[InlineKeyboardButton("🔙 Back to Info", callback_data="show_info")]]
        return message, InlineKeyboardMarkup(keyboard)

    async def _handle_show_whats_new(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> Tuple[str, InlineKeyboardMarkup]:
        """Handle show what's new."""
        message = "🆕 What's New:\n\n• Improved voice quality\n• Better memory system\n• Enhanced mood tracking"
        keyboard = [[InlineKeyboardButton("🔙 Back to Info", callback_data="show_info")]]
        return message, InlineKeyboardMarkup(keyboard)

    async def _handle_show_terms(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> Tuple[str, InlineKeyboardMarkup]:
        """Handle show terms."""
        message = "📜 Terms of Service:\n\nBy using VoicePal, you agree to our terms and conditions."
        keyboard = [[InlineKeyboardButton("🔙 Back to Info", callback_data="show_info")]]
        return message, InlineKeyboardMarkup(keyboard)

    async def _handle_show_privacy_policy(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> Tuple[str, InlineKeyboardMarkup]:
        """Handle show privacy policy."""
        message = "🔒 Privacy Policy:\n\nWe protect your privacy and data according to our privacy policy."
        keyboard = [[InlineKeyboardButton("🔙 Back to Info", callback_data="show_info")]]
        return message, InlineKeyboardMarkup(keyboard)

    async def _handle_show_support(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> Tuple[str, InlineKeyboardMarkup]:
        """Handle show support."""
        message = "🆘 Support:\n\nNeed help? Contact our support team or check the FAQ."
        keyboard = [[InlineKeyboardButton("🔙 Back to Info", callback_data="show_info")]]
        return message, InlineKeyboardMarkup(keyboard)

    async def _handle_show_feedback(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> Tuple[str, InlineKeyboardMarkup]:
        """Handle show feedback."""
        message = "📝 Feedback:\n\nWe'd love to hear your thoughts! Send us your feedback."
        keyboard = [[InlineKeyboardButton("🔙 Back to Info", callback_data="show_info")]]
        return message, InlineKeyboardMarkup(keyboard)

    async def _handle_add_mood_entry(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> Tuple[str, InlineKeyboardMarkup]:
        """Handle add mood entry."""
        # Check if mood_entry is available
        if hasattr(self.bot_instance, 'mood_entry') and self.bot_instance.mood_entry:
            # Get mood entry keyboard from mood_entry
            reply_markup = self.bot_instance.mood_entry.get_mood_entry_keyboard()
            message = "📝 How are you feeling today?"
            return message, reply_markup
        else:
            # Fallback if mood_entry is not available
            message = "📝 Add Mood Entry:\n\nMood tracking is not available."
            keyboard = [[InlineKeyboardButton("🔙 Back to Mood Diary", callback_data="show_mood_diary")]]
            return message, InlineKeyboardMarkup(keyboard)

    async def _handle_view_mood_history(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> Tuple[str, InlineKeyboardMarkup]:
        """Handle view mood history."""
        user_id = update.callback_query.from_user.id
        try:
            if hasattr(self.bot_instance, 'mood_tracker') and self.bot_instance.mood_tracker:
                summary = self.bot_instance.mood_tracker.get_daily_mood_summary(user_id)
                if summary.get("status") == "success":
                    message = f"📊 Mood History:\n\nToday: {summary.get('entries_count', 0)} entries\nDominant mood: {summary.get('dominant_mood', 'N/A')}"
                else:
                    message = "📊 Mood History:\n\nNo mood data available yet."
            else:
                message = "📊 Mood History:\n\nMood tracking is not available."
        except Exception as e:
            logger.error(f"Error getting mood history: {e}")
            message = "📊 Mood History:\n\nError retrieving mood data."

        keyboard = [[InlineKeyboardButton("🔙 Back to Mood Diary", callback_data="show_mood_diary")]]
        return message, InlineKeyboardMarkup(keyboard)

    async def _handle_mood_trends(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> Tuple[str, InlineKeyboardMarkup]:
        """Handle mood trends."""
        message = "📈 Mood Trends:\n\nYour mood patterns and trends over time."
        keyboard = [[InlineKeyboardButton("🔙 Back to Mood Diary", callback_data="show_mood_diary")]]
        return message, InlineKeyboardMarkup(keyboard)

    async def _handle_mood_insights(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> Tuple[str, InlineKeyboardMarkup]:
        """Handle mood insights."""
        message = "💡 Mood Insights:\n\nPersonalized insights based on your mood patterns."
        keyboard = [[InlineKeyboardButton("🔙 Back to Mood Diary", callback_data="show_mood_diary")]]
        return message, InlineKeyboardMarkup(keyboard)

    # Voice settings handlers are now imported from bot.handlers.voice_settings_handlers
    # This ensures a single source of truth for voice settings UI and logic

    def register_module_handlers(self, module_name: str, handlers: Dict[str, Callable]) -> None:
        """
        Register handlers from a specific module.

        Args:
            module_name: Name of the module registering handlers
            handlers: Dictionary of callback_data -> handler mappings
        """
        for callback_data, handler in handlers.items():
            self.register_handler(callback_data, handler)

        logger.info("Registered %d handlers from module: %s", len(handlers), module_name)

    def register_module_pattern_handlers(self, module_name: str,
                                       pattern_handlers: List[Tuple[str, Callable]]) -> None:
        """
        Register pattern handlers from a specific module.

        Args:
            module_name: Name of the module registering handlers
            pattern_handlers: List of (pattern, handler) tuples
        """
        for pattern, handler in pattern_handlers:
            self.register_pattern_handler(pattern, handler)

        logger.info("Registered %d pattern handlers from module: %s",
                   len(pattern_handlers), module_name)

    def get_registered_handlers(self) -> Dict[str, str]:
        """
        Get a summary of all registered handlers for debugging.

        Returns:
            Dict mapping callback data to handler names
        """
        result = {}

        # Exact match handlers
        for callback_data, handler in self.handlers.items():
            result[callback_data] = f"{handler.__module__}.{handler.__name__}"

        # Pattern handlers
        for pattern, handler in self.pattern_handlers:
            result[f"PATTERN:{pattern}"] = f"{handler.__module__}.{handler.__name__}"

        return result
