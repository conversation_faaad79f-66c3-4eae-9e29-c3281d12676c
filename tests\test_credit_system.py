"""
Test script for the credit system.

This script tests the credit system functionality in isolation,
focusing on the free trial credits and transaction tracking.
"""

import os
import sys
import logging
import argparse
from datetime import datetime

# Add the parent directory to the path so we can import the bot modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from bot.database.core import Database
from bot.core.exceptions import InsufficientCreditsError

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

def test_free_trial_credits(db_file="test_credits.db"):
    """Test free trial credits functionality."""
    logger.info("Testing free trial credits functionality")
    
    # Remove test database if it exists
    if os.path.exists(db_file):
        os.remove(db_file)
    
    # Create database
    db = Database(db_file)
    
    try:
        # Create test user
        user_id = 123456789
        db.add_user(user_id=user_id, username="test_user")
        logger.info(f"Created test user with ID {user_id}")
        
        # Check initial credits
        initial_credits = db.get_user_credits(user_id)
        logger.info(f"Initial credits: {initial_credits}")
        
        # Add free trial credits
        free_credits = 100
        new_balance = db.add_credits(user_id, free_credits, source="free_trial")
        logger.info(f"Added {free_credits} free trial credits. New balance: {new_balance}")
        
        # Check that free_credits_received flag is set
        user = db.get_user(user_id)
        logger.info(f"Free credits received flag: {user['free_credits_received']}")
        
        # Try to add free trial credits again
        logger.info("Attempting to add free trial credits again...")
        new_balance = db.add_credits(user_id, free_credits, source="free_trial")
        logger.info(f"Balance after second attempt: {new_balance}")
        
        # Check if balance changed
        if new_balance == free_credits:
            logger.info("✅ Free trial credits can only be received once")
        else:
            logger.error("❌ Free trial credits were added multiple times")
        
        # Check transactions
        transactions = db.get_user_transactions(user_id)
        logger.info(f"Number of transactions: {len(transactions)}")
        
        # Check transaction sources
        sources = [t["source"] for t in transactions]
        logger.info(f"Transaction sources: {sources}")
        
        if "free_trial" in sources and sources.count("free_trial") == 1:
            logger.info("✅ Free trial transaction recorded correctly")
        else:
            logger.error("❌ Free trial transaction not recorded correctly")
        
        return True
    except Exception as e:
        logger.error(f"Error testing free trial credits: {e}")
        return False
    finally:
        # Close database
        db.close()
        
        # Remove test database
        if os.path.exists(db_file):
            os.remove(db_file)

def test_credit_sources(db_file="test_credits.db"):
    """Test credit sources and transaction tracking."""
    logger.info("Testing credit sources and transaction tracking")
    
    # Remove test database if it exists
    if os.path.exists(db_file):
        os.remove(db_file)
    
    # Create database
    db = Database(db_file)
    
    try:
        # Create test user
        user_id = 123456789
        db.add_user(user_id=user_id, username="test_user")
        logger.info(f"Created test user with ID {user_id}")
        
        # Add credits with different sources
        sources = ["free_trial", "purchase", "admin", "manual"]
        for source in sources:
            db.add_credits(user_id, 50, source=source)
            logger.info(f"Added 50 credits with source '{source}'")
        
        # Check total credits
        total_credits = db.get_user_credits(user_id)
        logger.info(f"Total credits: {total_credits}")
        
        # Check transactions
        transactions = db.get_user_transactions(user_id)
        logger.info(f"Number of transactions: {len(transactions)}")
        
        # Check transaction sources
        transaction_sources = [t["source"] for t in transactions]
        logger.info(f"Transaction sources: {transaction_sources}")
        
        # Check if all sources are recorded
        all_sources_recorded = all(source in transaction_sources for source in sources)
        if all_sources_recorded:
            logger.info("✅ All credit sources recorded correctly")
        else:
            logger.error("❌ Not all credit sources were recorded")
        
        return True
    except Exception as e:
        logger.error(f"Error testing credit sources: {e}")
        return False
    finally:
        # Close database
        db.close()
        
        # Remove test database
        if os.path.exists(db_file):
            os.remove(db_file)

def test_credit_usage(db_file="test_credits.db"):
    """Test credit usage and insufficient credits handling."""
    logger.info("Testing credit usage and insufficient credits handling")
    
    # Remove test database if it exists
    if os.path.exists(db_file):
        os.remove(db_file)
    
    # Create database
    db = Database(db_file)
    
    try:
        # Create test user
        user_id = 123456789
        db.add_user(user_id=user_id, username="test_user")
        logger.info(f"Created test user with ID {user_id}")
        
        # Add credits
        db.add_credits(user_id, 100, source="manual")
        logger.info("Added 100 credits")
        
        # Use credits successfully
        db.use_credits(user_id, 50)
        logger.info("Used 50 credits")
        
        # Check remaining credits
        remaining_credits = db.get_user_credits(user_id)
        logger.info(f"Remaining credits: {remaining_credits}")
        
        # Try to use more credits than available
        try:
            db.use_credits(user_id, 100)
            logger.error("❌ Used more credits than available")
            success = False
        except InsufficientCreditsError:
            logger.info("✅ InsufficientCreditsError raised correctly")
            success = True
        
        # Check credits didn't change
        final_credits = db.get_user_credits(user_id)
        logger.info(f"Final credits: {final_credits}")
        
        if final_credits == remaining_credits:
            logger.info("✅ Credits unchanged after insufficient credits error")
        else:
            logger.error("❌ Credits changed after insufficient credits error")
            success = False
        
        return success
    except Exception as e:
        logger.error(f"Error testing credit usage: {e}")
        return False
    finally:
        # Close database
        db.close()
        
        # Remove test database
        if os.path.exists(db_file):
            os.remove(db_file)

def main():
    """Run credit system tests."""
    parser = argparse.ArgumentParser(description="Test the VoicePal credit system")
    parser.add_argument("--test", choices=["free_trial", "sources", "usage", "all"], 
                        default="all", help="Test to run")
    args = parser.parse_args()
    
    logger.info("Starting credit system tests")
    
    if args.test == "free_trial" or args.test == "all":
        free_trial_success = test_free_trial_credits()
        logger.info(f"Free trial credits test: {'Success' if free_trial_success else 'Failed'}")
    
    if args.test == "sources" or args.test == "all":
        sources_success = test_credit_sources()
        logger.info(f"Credit sources test: {'Success' if sources_success else 'Failed'}")
    
    if args.test == "usage" or args.test == "all":
        usage_success = test_credit_usage()
        logger.info(f"Credit usage test: {'Success' if usage_success else 'Failed'}")
    
    logger.info("Credit system tests completed")

if __name__ == "__main__":
    main()
