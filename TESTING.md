# Testing VoicePal Improvements

This document provides instructions for testing the memory and voice improvements made to the VoicePal bot.

## Prerequisites

Before running the tests, make sure you have the following:

1. Python 3.10 or higher installed
2. Required API keys:
   - Deepgram API key (for TTS testing)
   - Google AI API key (optional, for real AI responses)
3. Required dependencies installed:
   - `ffplay` (part of FFmpeg) for audio playback

## Setting Up Environment Variables

Set the following environment variables:

```bash
# Windows
set DEEPGRAM_API_KEY=your_deepgram_api_key
set GOOGLE_AI_API_KEY=your_google_ai_api_key

# Linux/macOS
export DEEPGRAM_API_KEY=your_deepgram_api_key
export GOOGLE_AI_API_KEY=your_google_ai_api_key
```

## Test Scripts

We've provided three test scripts to verify different aspects of the improvements:

1. `test_deepgram_tts.py` - Tests the Deepgram TTS provider
2. `test_memory_improvements.py` - Tests the memory improvements
3. `test_bot_improvements.py` - Tests the entire bot with all improvements

### 1. Testing Deepgram TTS

This script tests the Deepgram TTS provider with various voices, text lengths, and personalities.

```bash
python test_deepgram_tts.py
```

Options:
- `--no-play` - Don't play audio files (just generate them)
- `--save-dir DIR` - Directory to save generated audio files (default: test_output)

Example:
```bash
python test_deepgram_tts.py --save-dir my_test_output
```

What to look for:
- The script should successfully generate audio files for different voices and text lengths
- The audio should sound natural and human-like
- The voice should adapt to different personalities
- The script should not fall back to Google TTS unless there's an issue with the Deepgram API

### 2. Testing Memory Improvements

This script tests the memory improvements, including short message handling and user name remembering.

```bash
python test_memory_improvements.py
```

What to look for:
- The script should correctly identify short messages that should preserve context
- The script should correctly retrieve user names from the database
- The message analyzer should adapt response length based on message complexity

### 3. Testing the Entire Bot

This script tests the entire bot with all improvements, simulating a conversation.

```bash
python test_bot_improvements.py
```

Options:
- `--no-voice` - Don't generate voice responses (text only)

What to look for:
- The bot should maintain context across short messages
- The bot should use the user's name in responses
- Voice responses should be generated using Deepgram TTS
- The bot should adapt response length based on message complexity

## Manual Testing

For a more thorough test, you can run the actual bot and interact with it:

1. Make sure you have the required API keys set as environment variables
2. Run the bot:
   ```bash
   python -m bot.main
   ```
3. Interact with the bot through Telegram
4. Test the following scenarios:

### Short Message Handling

1. Start a conversation with the bot
2. Ask a question that requires context, like "What's the capital of France?"
3. After the bot responds, send a short message like "Why?" or "Cool!"
4. The bot should maintain context and provide a relevant response

### User Name Remembering

1. Start a conversation with the bot
2. The bot should greet you using your Telegram name
3. Continue the conversation and see if the bot consistently uses your name

### Voice Quality

1. Send a voice message to the bot
2. The bot should respond with a voice message
3. The voice should sound natural and human-like
4. Try different types of messages (short, long, questions, statements)

## Troubleshooting

### Audio Playback Issues

If you have issues with audio playback:

1. Make sure FFmpeg is installed and `ffplay` is in your PATH
2. Try running the test with `--no-play` option to skip audio playback
3. Check the generated audio files manually

### API Key Issues

If you have issues with API keys:

1. Verify that the API keys are correctly set as environment variables
2. Check that the API keys are valid and have the necessary permissions
3. For Deepgram, make sure your account has TTS capabilities enabled

### Database Issues

If you encounter database errors:

1. Delete any test database files (test_memory.db, test_bot.db)
2. Make sure you have write permissions in the current directory

## Conclusion

These tests should verify that the memory and voice improvements are working correctly. If you encounter any issues, please check the logs for error messages and troubleshoot accordingly.

The improvements should result in:
1. Better conversation flow with short messages
2. More consistent use of user names
3. Higher quality voice responses using Deepgram TTS
