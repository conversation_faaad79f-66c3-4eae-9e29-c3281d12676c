#!/usr/bin/env python3
"""
Backup and recreate the database for VoicePal.
"""

import sqlite3
import os
import shutil
import datetime
import sys

def backup_and_recreate_db(db_path: str) -> None:
    """
    Backup and recreate the database.
    
    Args:
        db_path: Path to the database file
    """
    if not os.path.exists(db_path):
        print(f"Database file not found: {db_path}")
        return
    
    # Create backup
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = f"{db_path}.backup_{timestamp}"
    
    try:
        # Copy database file to backup
        shutil.copy2(db_path, backup_path)
        print(f"Database backed up to: {backup_path}")
        
        # Connect to original database
        conn_src = sqlite3.connect(db_path)
        conn_src.row_factory = sqlite3.Row
        
        # Get schema
        cursor_src = conn_src.cursor()
        cursor_src.execute("SELECT sql FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
        tables = cursor_src.fetchall()
        
        # Rename original database
        conn_src.close()
        temp_path = f"{db_path}.old"
        os.rename(db_path, temp_path)
        print(f"Original database renamed to: {temp_path}")
        
        # Create new database
        conn_dest = sqlite3.connect(db_path)
        cursor_dest = conn_dest.cursor()
        
        # Create tables in new database
        for table in tables:
            if table['sql']:
                cursor_dest.execute(table['sql'])
        
        conn_dest.commit()
        print("Schema recreated in new database")
        
        # Connect to old database
        conn_src = sqlite3.connect(temp_path)
        conn_src.row_factory = sqlite3.Row
        cursor_src = conn_src.cursor()
        
        # Get list of tables
        cursor_src.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
        tables = cursor_src.fetchall()
        
        # Copy data from old to new database
        for table in tables:
            table_name = table['name']
            
            # Get column names
            cursor_src.execute(f"PRAGMA table_info({table_name})")
            columns = cursor_src.fetchall()
            column_names = [column['name'] for column in columns]
            
            # Skip empty tables
            cursor_src.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor_src.fetchone()[0]
            if count == 0:
                print(f"Skipping empty table: {table_name}")
                continue
            
            # Get data from old database
            cursor_src.execute(f"SELECT * FROM {table_name}")
            rows = cursor_src.fetchall()
            
            # Check if table exists in new database
            cursor_dest.execute(f"PRAGMA table_info({table_name})")
            new_columns = cursor_dest.fetchall()
            new_column_names = [column['name'] for column in new_columns]
            
            # Find common columns
            common_columns = [col for col in column_names if col in new_column_names]
            
            if not common_columns:
                print(f"No common columns found for table: {table_name}")
                continue
            
            # Insert data into new database
            placeholders = ", ".join(["?" for _ in common_columns])
            columns_str = ", ".join(common_columns)
            
            for row in rows:
                values = [row[col] for col in common_columns]
                cursor_dest.execute(f"INSERT INTO {table_name} ({columns_str}) VALUES ({placeholders})", values)
            
            print(f"Copied {len(rows)} rows to table: {table_name}")
        
        conn_dest.commit()
        print("Data copied to new database")
        
        # Close connections
        conn_src.close()
        conn_dest.close()
        
        # Delete old database
        os.remove(temp_path)
        print(f"Old database deleted: {temp_path}")
        
        print("Database recreated successfully")
        
    except Exception as e:
        print(f"Error recreating database: {e}")
        
        # Try to restore original database if something went wrong
        if os.path.exists(temp_path) and not os.path.exists(db_path):
            os.rename(temp_path, db_path)
            print(f"Original database restored from: {temp_path}")

def main():
    """Main entry point."""
    # Get database path from command line or use default
    db_path = sys.argv[1] if len(sys.argv) > 1 else "voicepal.db"
    
    # Backup and recreate database
    backup_and_recreate_db(db_path)

if __name__ == "__main__":
    main()
