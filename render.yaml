# VoicePal Bot - Render.com Deployment Configuration
# ================================================
# Infrastructure as Code for Render.com deployment

services:
  - type: web
    name: voicepal-bot
    env: python
    region: oregon
    plan: starter

    # Build configuration
    buildCommand: |
      pip install --upgrade pip setuptools wheel
      pip install -r requirements.txt

    # Start command
    startCommand: python run.py --webhook

    # Health check
    healthCheckPath: /health

    # Environment variables
    envVars:
      - key: PYTHON_VERSION
        value: 3.11.0
      - key: PYTHONPATH
        value: /opt/render/project/src
      - key: RENDER
        value: true
      - key: PORT
        value: 8443
      - key: LOG_LEVEL
        value: INFO
      - key: ENABLE_MEMORY
        value: true
      - key: ENABLE_MOOD_TRACKING
        value: true
      - key: ENABLE_PERSONALIZATION
        value: true
      - key: ENABLE_SECURITY_MONITORING
        value: true
      - key: PAYMENT_PROVIDER
        value: telegram_stars
      - key: DEFAULT_TTS_PROVIDER
        value: deepgram
      - key: DEFAULT_VOICE_ID
        value: aura-thalia-en
      - key: DEFAULT_LANGUAGE
        value: en
      - key: DEFAULT_AI_MODEL
        value: gemini-2.0-flash
      - key: MAX_WORKERS
        value: 2
      - key: CONVERSATION_MEMORY_LIMIT
        value: 20
      - key: CACHE_TTL_MINUTES
        value: 5
      - key: RATE_LIMIT_ENABLED
        value: true
      - key: MAX_REQUESTS_PER_MINUTE
        value: 30

      # Secret environment variables (set these in Render dashboard)
      - key: TELEGRAM_TOKEN
        sync: false
      - key: TELEGRAM_ADMIN_ID
        sync: false
      - key: DEEPGRAM_API_KEY
        sync: false
      - key: GOOGLE_AI_API_KEY
        sync: false
      - key: ELEVENLABS_API_KEY
        sync: false
      - key: GROQ_API_KEY
        sync: false
      - key: PAYMENT_PROVIDER_TOKEN
        sync: false

    # Auto-deploy configuration
    autoDeploy: true

    # Disk configuration for SQLite database
    disk:
      name: voicepal-data
      mountPath: /opt/render/project/src/data
      sizeGB: 1
