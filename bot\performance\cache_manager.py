"""
Cache manager for VoicePal.

This module provides intelligent caching strategies for improved performance.
"""

import time
import logging
import hashlib
from typing import Any, Optional, Dict, List, Callable, Union
from dataclasses import dataclass
from enum import Enum
import json

logger = logging.getLogger(__name__)

class CacheStrategy(Enum):
    """Cache strategies."""
    LRU = "lru"  # Least Recently Used
    LFU = "lfu"  # Least Frequently Used
    TTL = "ttl"  # Time To Live
    FIFO = "fifo"  # First In First Out

@dataclass
class CacheEntry:
    """Cache entry with metadata."""
    key: str
    value: Any
    created_at: float
    last_accessed: float
    access_count: int
    ttl: Optional[float] = None
    
    @property
    def is_expired(self) -> bool:
        """Check if entry is expired."""
        if self.ttl is None:
            return False
        return time.time() > (self.created_at + self.ttl)
    
    @property
    def age(self) -> float:
        """Get entry age in seconds."""
        return time.time() - self.created_at

@dataclass
class CacheStats:
    """Cache statistics."""
    total_entries: int
    hits: int
    misses: int
    hit_rate: float
    memory_usage: int
    evictions: int
    expired_entries: int

class CacheManager:
    """Intelligent cache manager with multiple strategies."""
    
    def __init__(
        self,
        max_size: int = 1000,
        default_ttl: Optional[int] = 3600,
        strategy: CacheStrategy = CacheStrategy.LRU,
        redis_client=None
    ):
        """
        Initialize cache manager.
        
        Args:
            max_size: Maximum number of cache entries
            default_ttl: Default time to live in seconds
            strategy: Cache eviction strategy
            redis_client: Redis client for distributed caching
        """
        self.max_size = max_size
        self.default_ttl = default_ttl
        self.strategy = strategy
        self.redis_client = redis_client
        
        # Local cache storage
        self.cache: Dict[str, CacheEntry] = {}
        
        # Statistics
        self.stats = CacheStats(
            total_entries=0,
            hits=0,
            misses=0,
            hit_rate=0.0,
            memory_usage=0,
            evictions=0,
            expired_entries=0
        )
        
        # Cache prefixes for different data types
        self.prefixes = {
            "user": "user:",
            "conversation": "conv:",
            "ai_response": "ai:",
            "voice_result": "voice:",
            "translation": "trans:",
            "embedding": "embed:",
            "session": "sess:"
        }
        
        logger.info(f"Cache manager initialized: {max_size} entries, {strategy.value} strategy")
    
    def _generate_key(self, prefix: str, identifier: str, params: Optional[Dict] = None) -> str:
        """
        Generate cache key.
        
        Args:
            prefix: Key prefix
            identifier: Key identifier
            params: Additional parameters for key generation
            
        Returns:
            Generated cache key
        """
        base_key = f"{self.prefixes.get(prefix, prefix)}{identifier}"
        
        if params:
            # Create deterministic hash of parameters
            params_str = json.dumps(params, sort_keys=True)
            params_hash = hashlib.md5(params_str.encode()).hexdigest()[:8]
            base_key += f":{params_hash}"
        
        return base_key
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        Get value from cache.
        
        Args:
            key: Cache key
            default: Default value if not found
            
        Returns:
            Cached value or default
        """
        # Try Redis first if available
        if self.redis_client:
            try:
                value = self.redis_client.get(key)
                if value is not None:
                    self.stats.hits += 1
                    self._update_hit_rate()
                    try:
                        return json.loads(value.decode('utf-8'))
                    except (json.JSONDecodeError, UnicodeDecodeError):
                        return value.decode('utf-8')
            except Exception as e:
                logger.warning(f"Redis cache get failed: {e}")
        
        # Try local cache
        entry = self.cache.get(key)
        if entry is None:
            self.stats.misses += 1
            self._update_hit_rate()
            return default
        
        # Check if expired
        if entry.is_expired:
            self._remove_entry(key)
            self.stats.misses += 1
            self.stats.expired_entries += 1
            self._update_hit_rate()
            return default
        
        # Update access metadata
        entry.last_accessed = time.time()
        entry.access_count += 1
        
        self.stats.hits += 1
        self._update_hit_rate()
        
        return entry.value
    
    def set(
        self,
        key: str,
        value: Any,
        ttl: Optional[int] = None,
        force_local: bool = False
    ) -> bool:
        """
        Set value in cache.
        
        Args:
            key: Cache key
            value: Value to cache
            ttl: Time to live in seconds
            force_local: Force storage in local cache only
            
        Returns:
            True if successful, False otherwise
        """
        ttl = ttl or self.default_ttl
        
        # Store in Redis if available and not forced local
        if self.redis_client and not force_local:
            try:
                serialized_value = json.dumps(value) if isinstance(value, (dict, list)) else str(value)
                self.redis_client.set(key, serialized_value, ex=ttl)
            except Exception as e:
                logger.warning(f"Redis cache set failed: {e}")
        
        # Store in local cache
        current_time = time.time()
        
        # Check if we need to evict entries
        if len(self.cache) >= self.max_size and key not in self.cache:
            self._evict_entry()
        
        # Create cache entry
        entry = CacheEntry(
            key=key,
            value=value,
            created_at=current_time,
            last_accessed=current_time,
            access_count=1,
            ttl=ttl
        )
        
        self.cache[key] = entry
        self.stats.total_entries = len(self.cache)
        
        return True
    
    def delete(self, key: str) -> bool:
        """
        Delete value from cache.
        
        Args:
            key: Cache key
            
        Returns:
            True if deleted, False if not found
        """
        # Delete from Redis
        if self.redis_client:
            try:
                self.redis_client.delete(key)
            except Exception as e:
                logger.warning(f"Redis cache delete failed: {e}")
        
        # Delete from local cache
        if key in self.cache:
            self._remove_entry(key)
            return True
        
        return False
    
    def clear(self) -> None:
        """Clear all cache entries."""
        # Clear Redis (be careful with this in production)
        if self.redis_client:
            try:
                # Only clear keys with our prefixes
                for prefix in self.prefixes.values():
                    pattern = f"{prefix}*"
                    keys = self.redis_client.keys(pattern)
                    if keys:
                        self.redis_client.delete(*keys)
            except Exception as e:
                logger.warning(f"Redis cache clear failed: {e}")
        
        # Clear local cache
        self.cache.clear()
        self.stats.total_entries = 0
    
    def _remove_entry(self, key: str) -> None:
        """Remove entry from local cache."""
        if key in self.cache:
            del self.cache[key]
            self.stats.total_entries = len(self.cache)
    
    def _evict_entry(self) -> None:
        """Evict an entry based on the configured strategy."""
        if not self.cache:
            return
        
        if self.strategy == CacheStrategy.LRU:
            # Remove least recently used
            oldest_key = min(self.cache.keys(), key=lambda k: self.cache[k].last_accessed)
        elif self.strategy == CacheStrategy.LFU:
            # Remove least frequently used
            oldest_key = min(self.cache.keys(), key=lambda k: self.cache[k].access_count)
        elif self.strategy == CacheStrategy.FIFO:
            # Remove oldest entry
            oldest_key = min(self.cache.keys(), key=lambda k: self.cache[k].created_at)
        else:  # TTL or default
            # Remove entry closest to expiration
            oldest_key = min(
                self.cache.keys(),
                key=lambda k: self.cache[k].created_at + (self.cache[k].ttl or self.default_ttl or 3600)
            )
        
        self._remove_entry(oldest_key)
        self.stats.evictions += 1
    
    def _update_hit_rate(self) -> None:
        """Update cache hit rate."""
        total_requests = self.stats.hits + self.stats.misses
        if total_requests > 0:
            self.stats.hit_rate = self.stats.hits / total_requests
    
    def cleanup_expired(self) -> int:
        """
        Clean up expired entries.
        
        Returns:
            Number of entries cleaned up
        """
        expired_keys = []
        current_time = time.time()
        
        for key, entry in self.cache.items():
            if entry.is_expired:
                expired_keys.append(key)
        
        for key in expired_keys:
            self._remove_entry(key)
        
        self.stats.expired_entries += len(expired_keys)
        
        if expired_keys:
            logger.debug(f"Cleaned up {len(expired_keys)} expired cache entries")
        
        return len(expired_keys)
    
    def get_stats(self) -> CacheStats:
        """Get cache statistics."""
        # Update memory usage estimate
        self.stats.memory_usage = sum(
            len(str(entry.value)) + len(entry.key) + 100  # Rough estimate
            for entry in self.cache.values()
        )
        
        return self.stats
    
    # Convenience methods for common cache operations
    
    def cache_user_data(self, user_id: int, data: Dict[str, Any], ttl: int = 3600) -> bool:
        """Cache user data."""
        key = self._generate_key("user", str(user_id))
        return self.set(key, data, ttl)
    
    def get_cached_user_data(self, user_id: int) -> Optional[Dict[str, Any]]:
        """Get cached user data."""
        key = self._generate_key("user", str(user_id))
        return self.get(key)
    
    def cache_ai_response(
        self,
        prompt: str,
        response: str,
        model: str = "default",
        ttl: int = 1800
    ) -> bool:
        """Cache AI response."""
        key = self._generate_key("ai_response", prompt, {"model": model})
        return self.set(key, response, ttl)
    
    def get_cached_ai_response(self, prompt: str, model: str = "default") -> Optional[str]:
        """Get cached AI response."""
        key = self._generate_key("ai_response", prompt, {"model": model})
        return self.get(key)
    
    def cache_voice_result(
        self,
        voice_hash: str,
        result: Dict[str, Any],
        ttl: int = 3600
    ) -> bool:
        """Cache voice processing result."""
        key = self._generate_key("voice_result", voice_hash)
        return self.set(key, result, ttl)
    
    def get_cached_voice_result(self, voice_hash: str) -> Optional[Dict[str, Any]]:
        """Get cached voice processing result."""
        key = self._generate_key("voice_result", voice_hash)
        return self.get(key)
    
    def cache_embedding(
        self,
        text: str,
        embedding: List[float],
        model: str = "default",
        ttl: int = 86400  # 24 hours
    ) -> bool:
        """Cache text embedding."""
        text_hash = hashlib.md5(text.encode()).hexdigest()
        key = self._generate_key("embedding", text_hash, {"model": model})
        return self.set(key, embedding, ttl)
    
    def get_cached_embedding(self, text: str, model: str = "default") -> Optional[List[float]]:
        """Get cached text embedding."""
        text_hash = hashlib.md5(text.encode()).hexdigest()
        key = self._generate_key("embedding", text_hash, {"model": model})
        return self.get(key)

# Decorator for caching function results
def cached(
    cache_manager: CacheManager,
    ttl: Optional[int] = None,
    key_prefix: str = "func",
    include_args: bool = True
):
    """
    Decorator for caching function results.
    
    Args:
        cache_manager: Cache manager instance
        ttl: Time to live in seconds
        key_prefix: Cache key prefix
        include_args: Whether to include function arguments in cache key
    """
    def decorator(func: Callable) -> Callable:
        def wrapper(*args, **kwargs):
            # Generate cache key
            if include_args:
                args_str = str(args) + str(sorted(kwargs.items()))
                args_hash = hashlib.md5(args_str.encode()).hexdigest()[:8]
                cache_key = f"{key_prefix}:{func.__name__}:{args_hash}"
            else:
                cache_key = f"{key_prefix}:{func.__name__}"
            
            # Try to get from cache
            result = cache_manager.get(cache_key)
            if result is not None:
                return result
            
            # Execute function and cache result
            result = func(*args, **kwargs)
            cache_manager.set(cache_key, result, ttl)
            
            return result
        
        return wrapper
    return decorator
