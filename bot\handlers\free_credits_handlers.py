"""
Free credits verification handlers for VoicePal.

This module provides handlers for the free credits verification process.
"""

import logging
from typing import Dict, Any, Optional, Tuple, List
from datetime import datetime, timedelta

from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes

from bot.core.verification import VerificationManager
from bot.core.rate_limiter import rate_limit
from bot.core.security import extract_ip_info, generate_device_id

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# Constants
FREE_CREDITS_AMOUNT = 100  # Number of free credits to give
VERIFICATION_EXPIRES_MINUTES = 30  # Minutes until verification code expires

@rate_limit(rate=0.05, max_tokens=3, action="free_credits_command")
async def free_credits_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """
    Handle the /freecredits command.
    
    This command allows users to request free trial credits.
    
    Args:
        update: Telegram update
        context: Callback context
    """
    # Get bot instance
    bot_instance = context.application.bot_data.get("bot_instance")
    if not bot_instance:
        await update.message.reply_text("Bot instance not found.")
        return
    
    user = update.effective_user
    user_id = user.id
    
    # Get IP and device info for tracking
    ip_info = extract_ip_info(update)
    device_id = generate_device_id(user, update)
    
    # Initialize verification manager
    verification_manager = VerificationManager(bot_instance.database.conn)
    
    # Check if user has already received free credits
    if verification_manager.has_received_free_credits(user_id):
        await update.message.reply_text(
            "🚫 You have already received your free trial credits.\n\n"
            "You can purchase more credits using the /buy command."
        )
        return
    
    # Check if device has been used for free credits before
    if verification_manager.check_device_for_free_credits(device_id):
        # Blacklist device for attempting to get multiple free credits
        verification_manager.blacklist_device(device_id, ip_info, "multiple_free_credits_attempt")
        
        await update.message.reply_text(
            "🚫 This device has already been used to receive free trial credits.\n\n"
            "You can purchase more credits using the /buy command."
        )
        return
    
    # Check if device is blacklisted
    if verification_manager.is_device_blacklisted(device_id):
        await update.message.reply_text(
            "🚫 Your device has been blacklisted due to suspicious activity.\n\n"
            "If you believe this is an error, please contact support using the /support command."
        )
        return
    
    # Generate verification code
    verification_code = verification_manager.generate_verification_code(
        user_id, "free_credits", VERIFICATION_EXPIRES_MINUTES
    )
    
    # Send verification instructions
    await update.message.reply_text(
        f"🎁 *Free Trial Credits Verification*\n\n"
        f"To verify your account and receive {FREE_CREDITS_AMOUNT} free trial credits, "
        f"please enter the following command:\n\n"
        f"`/verify {verification_code}`\n\n"
        f"This code will expire in {VERIFICATION_EXPIRES_MINUTES} minutes.\n\n"
        f"*Note:* Free trial credits can only be claimed once per user.",
        parse_mode="Markdown"
    )

@rate_limit(rate=0.05, max_tokens=3, action="verify_command")
async def verify_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """
    Handle the /verify command.
    
    This command allows users to verify their account and receive free trial credits.
    
    Args:
        update: Telegram update
        context: Callback context
    """
    # Get bot instance
    bot_instance = context.application.bot_data.get("bot_instance")
    if not bot_instance:
        await update.message.reply_text("Bot instance not found.")
        return
    
    user = update.effective_user
    user_id = user.id
    
    # Get IP and device info for tracking
    ip_info = extract_ip_info(update)
    device_id = generate_device_id(user, update)
    
    # Parse command arguments
    args = context.args
    
    if not args:
        await update.message.reply_text(
            "Please provide your verification code.\n\n"
            "Example: `/verify 123456`",
            parse_mode="Markdown"
        )
        return
    
    verification_code = args[0]
    
    # Initialize verification manager
    verification_manager = VerificationManager(bot_instance.database.conn)
    
    # Check if user has already received free credits
    if verification_manager.has_received_free_credits(user_id):
        await update.message.reply_text(
            "🚫 You have already received your free trial credits.\n\n"
            "You can purchase more credits using the /buy command."
        )
        return
    
    # Check if device has been used for free credits before
    if verification_manager.check_device_for_free_credits(device_id):
        # Blacklist device for attempting to get multiple free credits
        verification_manager.blacklist_device(device_id, ip_info, "multiple_free_credits_attempt")
        
        await update.message.reply_text(
            "🚫 This device has already been used to receive free trial credits.\n\n"
            "You can purchase more credits using the /buy command."
        )
        return
    
    # Check if device is blacklisted
    if verification_manager.is_device_blacklisted(device_id):
        await update.message.reply_text(
            "🚫 Your device has been blacklisted due to suspicious activity.\n\n"
            "If you believe this is an error, please contact support using the /support command."
        )
        return
    
    # Verify code
    is_verified = verification_manager.verify_code(
        user_id, verification_code, "free_credits", ip_info, device_id
    )
    
    if is_verified:
        # Add free credits to user
        success = bot_instance.database.add_user_credits(user_id, FREE_CREDITS_AMOUNT)
        
        if success:
            # Track free credits
            verification_manager.track_free_credits(user_id, FREE_CREDITS_AMOUNT, ip_info, device_id)
            
            # Get user's current credits
            user_data = bot_instance.database.get_user(user_id)
            current_credits = user_data.get("credits", FREE_CREDITS_AMOUNT) if user_data else FREE_CREDITS_AMOUNT
            
            await update.message.reply_text(
                f"🎉 *Verification Successful!*\n\n"
                f"You have received {FREE_CREDITS_AMOUNT} free trial credits.\n\n"
                f"Your current balance: {current_credits} credits\n\n"
                f"Enjoy your conversations with VoicePal! You can use the /help command to learn more.",
                parse_mode="Markdown"
            )
            
            # Log successful verification
            logger.info(f"User {user_id} verified and received {FREE_CREDITS_AMOUNT} free credits")
        else:
            await update.message.reply_text(
                "❌ There was an error adding credits to your account. Please try again later or contact support."
            )
    else:
        # Check if we should blacklist the device for too many failed attempts
        cursor = bot_instance.database.conn.cursor()
        cursor.execute(
            """SELECT COUNT(*) FROM verification_attempts 
               WHERE device_id = ? AND verification_type = 'free_credits' AND success = 0 
               AND attempt_time > datetime('now', '-1 hour')""",
            (device_id,)
        )
        failed_attempts = cursor.fetchone()[0]
        
        if failed_attempts >= 5:
            # Blacklist device for too many failed attempts
            verification_manager.blacklist_device(device_id, ip_info, "too_many_verification_attempts")
            
            await update.message.reply_text(
                "🚫 Too many failed verification attempts. Your device has been temporarily blocked.\n\n"
                "If you believe this is an error, please contact support using the /support command."
            )
        else:
            await update.message.reply_text(
                "❌ Invalid or expired verification code. Please try again or request a new code with /freecredits."
            )

async def handle_free_credits_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> Optional[Tuple[str, InlineKeyboardMarkup]]:
    """
    Handle callback queries for free credits.
    
    Args:
        update: Telegram update
        context: Callback context
        
    Returns:
        Optional[Tuple[str, InlineKeyboardMarkup]]: Response text and reply markup, or None if not handled
    """
    query = update.callback_query
    callback_data = query.data
    
    if not callback_data.startswith("free_credits_"):
        return None
    
    # Get bot instance
    bot_instance = context.application.bot_data.get("bot_instance")
    if not bot_instance:
        await query.answer("Bot instance not found.")
        return None
    
    user = query.from_user
    user_id = user.id
    
    # Get IP and device info for tracking
    ip_info = extract_ip_info(update)
    device_id = generate_device_id(user, update)
    
    # Initialize verification manager
    verification_manager = VerificationManager(bot_instance.database.conn)
    
    action = callback_data.replace("free_credits_", "")
    
    if action == "request":
        # Check if user has already received free credits
        if verification_manager.has_received_free_credits(user_id):
            await query.answer("You have already received your free trial credits.")
            
            message = (
                "🚫 You have already received your free trial credits.\n\n"
                "You can purchase more credits using the /buy command."
            )
            
            keyboard = [[InlineKeyboardButton("Buy Credits", callback_data="buy_credits")]]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            return message, reply_markup
        
        # Check if device has been used for free credits before
        if verification_manager.check_device_for_free_credits(device_id):
            # Blacklist device for attempting to get multiple free credits
            verification_manager.blacklist_device(device_id, ip_info, "multiple_free_credits_attempt")
            
            await query.answer("This device has already been used to receive free trial credits.")
            
            message = (
                "🚫 This device has already been used to receive free trial credits.\n\n"
                "You can purchase more credits using the /buy command."
            )
            
            keyboard = [[InlineKeyboardButton("Buy Credits", callback_data="buy_credits")]]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            return message, reply_markup
        
        # Check if device is blacklisted
        if verification_manager.is_device_blacklisted(device_id):
            await query.answer("Your device has been blacklisted due to suspicious activity.")
            
            message = (
                "🚫 Your device has been blacklisted due to suspicious activity.\n\n"
                "If you believe this is an error, please contact support using the /support command."
            )
            
            keyboard = [[InlineKeyboardButton("Contact Support", callback_data="support")]]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            return message, reply_markup
        
        # Generate verification code
        verification_code = verification_manager.generate_verification_code(
            user_id, "free_credits", VERIFICATION_EXPIRES_MINUTES
        )
        
        await query.answer("Verification code generated.")
        
        message = (
            f"🎁 *Free Trial Credits Verification*\n\n"
            f"To verify your account and receive {FREE_CREDITS_AMOUNT} free trial credits, "
            f"please enter the following command:\n\n"
            f"`/verify {verification_code}`\n\n"
            f"This code will expire in {VERIFICATION_EXPIRES_MINUTES} minutes.\n\n"
            f"*Note:* Free trial credits can only be claimed once per user."
        )
        
        keyboard = [[InlineKeyboardButton("I've entered the code", callback_data="free_credits_check")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        return message, reply_markup
    
    elif action == "check":
        # Check if user has received free credits
        if verification_manager.has_received_free_credits(user_id):
            await query.answer("Verification successful!")
            
            # Get user's current credits
            user_data = bot_instance.database.get_user(user_id)
            current_credits = user_data.get("credits", 0) if user_data else 0
            
            message = (
                f"🎉 *Verification Successful!*\n\n"
                f"You have received {FREE_CREDITS_AMOUNT} free trial credits.\n\n"
                f"Your current balance: {current_credits} credits\n\n"
                f"Enjoy your conversations with VoicePal! You can use the /help command to learn more."
            )
            
            keyboard = [[InlineKeyboardButton("Start Chatting", callback_data="start_chat")]]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            return message, reply_markup
        else:
            await query.answer("You haven't verified your code yet.")
            
            message = (
                "❓ *Verification Pending*\n\n"
                "You haven't verified your code yet. Please enter the verification command:\n\n"
                "`/verify YOUR_CODE`\n\n"
                "Replace YOUR_CODE with the verification code you received."
            )
            
            keyboard = [
                [InlineKeyboardButton("Check Again", callback_data="free_credits_check")],
                [InlineKeyboardButton("Request New Code", callback_data="free_credits_request")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            return message, reply_markup
    
    return None
