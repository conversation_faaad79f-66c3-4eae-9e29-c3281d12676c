"""
Payment simulation script for VoicePal.

This script simulates payment flows for testing purposes.
"""

import os
import sys
import logging
import argparse
import asyncio
import uuid
from datetime import datetime
from typing import Dict, Any, Optional

# Add parent directory to path to import bot modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import bot modules
from bot.database.core import Database
from bot.payment.telegram_stars_payment import TelegramStarsPayment
from bot.payment.mock_payment import MockPayment

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class MockSuccessfulPayment:
    """Mock SuccessfulPayment object for testing."""
    
    def __init__(self, invoice_payload, total_amount, telegram_payment_charge_id):
        self.invoice_payload = invoice_payload
        self.total_amount = total_amount
        self.telegram_payment_charge_id = telegram_payment_charge_id

async def simulate_payment_flow(database_path: str, user_id: int, package_id: str,
                               payment_provider: str = "telegram_stars") -> Dict[str, Any]:
    """
    Simulate a payment flow.
    
    Args:
        database_path: Path to the database file
        user_id: User ID
        package_id: Credit package ID
        payment_provider: Payment provider to use ("telegram_stars" or "mock")
        
    Returns:
        Dict containing simulation results
    """
    # Connect to database
    database = Database(database_path)
    
    # Create payment provider
    if payment_provider == "telegram_stars":
        payment = TelegramStarsPayment(database)
    elif payment_provider == "mock":
        payment = MockPayment(database)
    else:
        raise ValueError(f"Invalid payment provider: {payment_provider}")
    
    # Get credit package
    package = payment.get_credit_package(package_id)
    if not package:
        logger.error(f"Invalid package ID: {package_id}")
        return {
            "success": False,
            "error": f"Invalid package ID: {package_id}"
        }
    
    # Get user
    user = database.get_user(user_id)
    if not user:
        logger.info(f"User {user_id} not found, creating...")
        database.add_user(
            user_id=user_id,
            username=f"user_{user_id}",
            first_name="Test",
            last_name="User"
        )
        user = database.get_user(user_id)
    
    # Get initial credits
    initial_credits = user["credits"]
    logger.info(f"Initial credits: {initial_credits}")
    
    # Create invoice payload
    payload = f"credits-{package_id}-{uuid.uuid4()}"
    
    # Record invoice in database
    try:
        database.add_invoice(
            user_id=user_id,
            package_id=package_id,
            payload=payload,
            amount=package["price"],
            currency=package["currency"],
            status="pending"
        )
        logger.info(f"Invoice created: {payload}")
    except Exception as e:
        logger.error(f"Error creating invoice: {e}")
        return {
            "success": False,
            "error": f"Error creating invoice: {e}"
        }
    
    # Create mock payment
    payment_obj = MockSuccessfulPayment(
        invoice_payload=payload,
        total_amount=package["price"],
        telegram_payment_charge_id=f"sim_{uuid.uuid4()}"
    )
    
    # Process payment
    try:
        credits_added = payment.process_payment(user_id, payment_obj)
        logger.info(f"Payment processed: {credits_added} credits added")
    except Exception as e:
        logger.error(f"Error processing payment: {e}")
        return {
            "success": False,
            "error": f"Error processing payment: {e}"
        }
    
    # Get final credits
    user = database.get_user(user_id)
    final_credits = user["credits"]
    logger.info(f"Final credits: {final_credits}")
    
    # Get transactions
    transactions = database.get_user_transactions(user_id)
    
    return {
        "success": True,
        "user_id": user_id,
        "package_id": package_id,
        "package": package,
        "initial_credits": initial_credits,
        "credits_added": credits_added,
        "final_credits": final_credits,
        "transactions": transactions
    }

async def main():
    """Main function to simulate payment flows."""
    parser = argparse.ArgumentParser(description="Simulate payment flows")
    parser.add_argument("--database", default="voicepal.db", help="Path to database file")
    parser.add_argument("--user-id", type=int, required=True, help="User ID")
    parser.add_argument("--package-id", default="small", help="Credit package ID")
    parser.add_argument("--provider", default="telegram_stars", help="Payment provider")
    args = parser.parse_args()
    
    logger.info(f"Simulating payment flow for user {args.user_id}, package {args.package_id}")
    
    result = await simulate_payment_flow(
        database_path=args.database,
        user_id=args.user_id,
        package_id=args.package_id,
        payment_provider=args.provider
    )
    
    if result["success"]:
        logger.info("Payment simulation successful!")
        logger.info(f"User ID: {result['user_id']}")
        logger.info(f"Package: {result['package_id']}")
        logger.info(f"Initial credits: {result['initial_credits']}")
        logger.info(f"Credits added: {result['credits_added']}")
        logger.info(f"Final credits: {result['final_credits']}")
        logger.info(f"Transactions: {len(result['transactions'])}")
    else:
        logger.error(f"Payment simulation failed: {result['error']}")

if __name__ == "__main__":
    asyncio.run(main())
