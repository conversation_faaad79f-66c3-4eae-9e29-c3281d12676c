"""
Optimization manager for VoicePal.

This module coordinates all performance optimization features.
"""

import logging
from typing import Optional, Dict, Any, List
from datetime import datetime

from bot.performance.database_pool import DatabaseConnectionPool, optimize_database_settings, create_database_indexes
from bot.performance.redis_optimizer import RedisOptimizer
from bot.performance.async_processor import AsyncProcessor
from bot.performance.cache_manager import CacheManager
from bot.performance.performance_monitor import PerformanceMonitor

logger = logging.getLogger(__name__)

class OptimizationManager:
    """Centralized optimization manager for VoicePal."""
    
    def __init__(
        self,
        database_path: str,
        redis_config: Optional[Dict[str, Any]] = None,
        enable_database_pool: bool = True,
        enable_redis: bool = True,
        enable_async_processing: bool = True,
        enable_caching: bool = True,
        enable_monitoring: bool = True
    ):
        """
        Initialize optimization manager.
        
        Args:
            database_path: Path to SQLite database
            redis_config: Redis configuration
            enable_database_pool: Whether to enable database connection pooling
            enable_redis: Whether to enable Redis optimization
            enable_async_processing: Whether to enable async processing
            enable_caching: Whether to enable caching
            enable_monitoring: Whether to enable performance monitoring
        """
        self.database_path = database_path
        self.redis_config = redis_config or {}
        
        # Initialize components
        self.database_pool: Optional[DatabaseConnectionPool] = None
        self.redis_optimizer: Optional[RedisOptimizer] = None
        self.async_processor: Optional[AsyncProcessor] = None
        self.cache_manager: Optional[CacheManager] = None
        self.performance_monitor: Optional[PerformanceMonitor] = None
        
        # Configuration
        self.config = {
            "database_pool_enabled": enable_database_pool,
            "redis_enabled": enable_redis,
            "async_processing_enabled": enable_async_processing,
            "caching_enabled": enable_caching,
            "monitoring_enabled": enable_monitoring,
            "optimization_level": "balanced"  # conservative, balanced, aggressive
        }
        
        # Initialize components
        if enable_database_pool:
            self._initialize_database_pool()
        
        if enable_redis:
            self._initialize_redis()
        
        if enable_async_processing:
            self._initialize_async_processor()
        
        if enable_caching:
            self._initialize_cache_manager()
        
        if enable_monitoring:
            self._initialize_performance_monitor()
        
        logger.info("Optimization manager initialized")
    
    def _initialize_database_pool(self):
        """Initialize database connection pool."""
        try:
            self.database_pool = DatabaseConnectionPool(
                database_path=self.database_path,
                min_connections=2,
                max_connections=10,
                max_idle_time=300,
                connection_timeout=30
            )
            
            # Apply database optimizations
            with self.database_pool.get_connection() as conn:
                optimize_database_settings(conn.connection)
                create_database_indexes(conn.connection)
                conn.commit()
            
            logger.info("Database connection pool initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize database pool: {e}")
            self.database_pool = None
    
    def _initialize_redis(self):
        """Initialize Redis optimizer."""
        try:
            self.redis_optimizer = RedisOptimizer(
                host=self.redis_config.get("host", "localhost"),
                port=self.redis_config.get("port", 6379),
                password=self.redis_config.get("password"),
                db=self.redis_config.get("db", 0),
                max_connections=self.redis_config.get("max_connections", 20)
            )
            
            if self.redis_optimizer.is_available():
                logger.info("Redis optimizer initialized")
            else:
                logger.warning("Redis not available, disabling Redis optimization")
                self.redis_optimizer = None
                
        except Exception as e:
            logger.error(f"Failed to initialize Redis optimizer: {e}")
            self.redis_optimizer = None
    
    def _initialize_async_processor(self):
        """Initialize async processor."""
        try:
            self.async_processor = AsyncProcessor(
                max_workers=4,
                max_processes=2,
                queue_size=1000,
                enable_process_pool=True
            )
            logger.info("Async processor initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize async processor: {e}")
            self.async_processor = None
    
    def _initialize_cache_manager(self):
        """Initialize cache manager."""
        try:
            redis_client = self.redis_optimizer.redis_client if self.redis_optimizer else None
            
            self.cache_manager = CacheManager(
                max_size=1000,
                default_ttl=3600,
                redis_client=redis_client
            )
            logger.info("Cache manager initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize cache manager: {e}")
            self.cache_manager = None
    
    def _initialize_performance_monitor(self):
        """Initialize performance monitor."""
        try:
            self.performance_monitor = PerformanceMonitor(
                collection_interval=60,
                retention_period=86400,
                alert_thresholds={
                    "cpu_percent": 80.0,
                    "memory_percent": 85.0,
                    "disk_percent": 90.0,
                    "response_time_p95": 5.0,
                    "error_rate": 0.05,
                    "cache_hit_rate": 0.7
                }
            )
            
            # Add alert callback
            self.performance_monitor.add_alert_callback(self._handle_performance_alert)
            
            logger.info("Performance monitor initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize performance monitor: {e}")
            self.performance_monitor = None
    
    async def start_optimization(self):
        """Start all optimization components."""
        try:
            # Start async processor
            if self.async_processor:
                await self.async_processor.start()
            
            # Start performance monitoring
            if self.performance_monitor:
                self.performance_monitor.start_monitoring()
            
            logger.info("Optimization components started")
            
        except Exception as e:
            logger.error(f"Failed to start optimization components: {e}")
    
    async def stop_optimization(self):
        """Stop all optimization components."""
        try:
            # Stop async processor
            if self.async_processor:
                await self.async_processor.stop()
            
            # Stop performance monitoring
            if self.performance_monitor:
                self.performance_monitor.stop_monitoring()
            
            # Close database pool
            if self.database_pool:
                self.database_pool.close_all()
            
            # Close Redis connections
            if self.redis_optimizer:
                self.redis_optimizer.close()
            
            logger.info("Optimization components stopped")
            
        except Exception as e:
            logger.error(f"Failed to stop optimization components: {e}")
    
    def _handle_performance_alert(self, alert: Dict[str, Any]):
        """Handle performance alerts."""
        logger.warning(f"Performance alert: {alert['message']}")
        
        # Implement automatic optimization based on alert type
        alert_type = alert["type"]
        
        if alert_type == "memory_high" and self.cache_manager:
            # Clear some cache entries to free memory
            self.cache_manager.cleanup_expired()
            logger.info("Cleared expired cache entries due to high memory usage")
        
        elif alert_type == "response_time_high" and self.async_processor:
            # Check if we need more workers
            stats = self.async_processor.get_stats()
            if stats.pending_tasks > stats.worker_threads * 2:
                logger.info("High response time detected, consider scaling up workers")
        
        elif alert_type == "cache_hit_rate_low" and self.cache_manager:
            # Analyze cache performance
            cache_stats = self.cache_manager.get_stats()
            logger.info(f"Cache hit rate low: {cache_stats.hit_rate:.2%}")
    
    def get_database_connection(self):
        """Get database connection from pool or create new one."""
        if self.database_pool:
            return self.database_pool.get_connection()
        else:
            # Fallback to direct connection
            import sqlite3
            return sqlite3.connect(self.database_path)
    
    def get_redis_client(self):
        """Get Redis client."""
        return self.redis_optimizer.redis_client if self.redis_optimizer else None
    
    def get_cache_manager(self):
        """Get cache manager."""
        return self.cache_manager
    
    def get_async_processor(self):
        """Get async processor."""
        return self.async_processor
    
    def get_performance_monitor(self):
        """Get performance monitor."""
        return self.performance_monitor
    
    def record_request_performance(self, response_time: float, success: bool = True):
        """Record request performance metrics."""
        if self.performance_monitor:
            self.performance_monitor.record_request(response_time, success)
    
    def get_optimization_status(self) -> Dict[str, Any]:
        """
        Get optimization system status.
        
        Returns:
            Optimization status information
        """
        status = {
            "database_pool": {
                "enabled": self.database_pool is not None,
                "stats": self.database_pool.get_stats().__dict__ if self.database_pool else {}
            },
            "redis": {
                "enabled": self.redis_optimizer is not None,
                "available": self.redis_optimizer.is_available() if self.redis_optimizer else False,
                "stats": self.redis_optimizer.get_stats().__dict__ if self.redis_optimizer else {}
            },
            "async_processor": {
                "enabled": self.async_processor is not None,
                "stats": self.async_processor.get_stats().__dict__ if self.async_processor else {}
            },
            "cache_manager": {
                "enabled": self.cache_manager is not None,
                "stats": self.cache_manager.get_stats().__dict__ if self.cache_manager else {}
            },
            "performance_monitor": {
                "enabled": self.performance_monitor is not None,
                "current_metrics": self.performance_monitor.get_current_metrics() if self.performance_monitor else {}
            },
            "config": self.config
        }
        
        return status
    
    def optimize_for_workload(self, workload_type: str):
        """
        Optimize configuration for specific workload type.
        
        Args:
            workload_type: Type of workload (cpu_intensive, io_intensive, balanced)
        """
        try:
            if workload_type == "cpu_intensive":
                # Optimize for CPU-intensive tasks
                if self.async_processor:
                    # Increase process pool size
                    logger.info("Optimizing for CPU-intensive workload")
                
            elif workload_type == "io_intensive":
                # Optimize for I/O-intensive tasks
                if self.database_pool:
                    # Increase connection pool size
                    logger.info("Optimizing for I/O-intensive workload")
                
            elif workload_type == "balanced":
                # Balanced optimization
                logger.info("Using balanced optimization")
            
            self.config["optimization_level"] = workload_type
            
        except Exception as e:
            logger.error(f"Failed to optimize for workload {workload_type}: {e}")
    
    def cleanup_resources(self):
        """Clean up resources and perform maintenance."""
        try:
            # Clean up expired cache entries
            if self.cache_manager:
                expired_count = self.cache_manager.cleanup_expired()
                logger.debug(f"Cleaned up {expired_count} expired cache entries")
            
            # Clean up completed async tasks
            if self.async_processor:
                self.async_processor.cleanup_completed_tasks()
                logger.debug("Cleaned up completed async tasks")
            
            # Clean up Redis expired keys
            if self.redis_optimizer:
                self.redis_optimizer.cleanup_expired_keys()
                logger.debug("Cleaned up expired Redis keys")
            
        except Exception as e:
            logger.error(f"Failed to cleanup resources: {e}")
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """
        Get comprehensive performance summary.
        
        Returns:
            Performance summary dictionary
        """
        summary = {
            "timestamp": datetime.utcnow().isoformat(),
            "optimization_enabled": True,
            "components": {}
        }
        
        # Database performance
        if self.database_pool:
            db_stats = self.database_pool.get_stats()
            summary["components"]["database"] = {
                "connection_pool": True,
                "total_connections": db_stats.total_connections,
                "active_connections": db_stats.active_connections,
                "average_wait_time": db_stats.average_wait_time,
                "failed_requests": db_stats.failed_requests
            }
        
        # Redis performance
        if self.redis_optimizer:
            redis_stats = self.redis_optimizer.get_stats()
            summary["components"]["redis"] = {
                "available": self.redis_optimizer.is_available(),
                "hit_rate": redis_stats.hit_rate,
                "total_commands": redis_stats.total_commands,
                "memory_usage": redis_stats.memory_usage
            }
        
        # Cache performance
        if self.cache_manager:
            cache_stats = self.cache_manager.get_stats()
            summary["components"]["cache"] = {
                "hit_rate": cache_stats.hit_rate,
                "total_entries": cache_stats.total_entries,
                "memory_usage": cache_stats.memory_usage,
                "evictions": cache_stats.evictions
            }
        
        # Async processing performance
        if self.async_processor:
            async_stats = self.async_processor.get_stats()
            summary["components"]["async_processor"] = {
                "total_tasks": async_stats.total_tasks,
                "completed_tasks": async_stats.completed_tasks,
                "failed_tasks": async_stats.failed_tasks,
                "average_execution_time": async_stats.average_execution_time
            }
        
        # System performance
        if self.performance_monitor:
            perf_summary = self.performance_monitor.get_performance_summary()
            summary["components"]["system"] = perf_summary
        
        return summary
