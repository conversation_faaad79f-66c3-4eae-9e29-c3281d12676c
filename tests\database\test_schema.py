"""
Tests for database schema management.

This module tests the SchemaManager class.
"""

import pytest
import sqlite3

from bot.database.core.schema import SchemaManager
from bot.database.core.exceptions import DatabaseSchemaError

def test_initialize_schema(db_connection):
    """Test schema initialization."""
    schema_manager = SchemaManager(db_connection)
    schema_manager.initialize_schema()
    
    # Verify tables were created
    cursor = db_connection.execute(
        "SELECT name FROM sqlite_master WHERE type='table'"
    )
    tables = [row["name"] for row in cursor.fetchall()]
    
    # Check required tables
    assert "users" in tables
    assert "conversations" in tables
    assert "messages" in tables
    assert "transactions" in tables
    assert "user_preferences" in tables
    assert "schema_migrations" in tables
    
    # Verify initial schema version
    cursor = db_connection.execute("SELECT version FROM schema_migrations")
    version = cursor.fetchone()["version"]
    assert version == 1

def test_get_current_version(initialized_db, schema_manager):
    """Test getting current schema version."""
    version = schema_manager.get_current_version()
    assert version == 1
    
    # Test with empty migrations table
    initialized_db.execute("DELETE FROM schema_migrations")
    version = schema_manager.get_current_version()
    assert version == 0

def test_validate_schema(initialized_db, schema_manager):
    """Test schema validation."""
    # Valid schema
    assert schema_manager.validate_schema() is True
    
    # Invalid schema (missing table)
    initialized_db.execute("DROP TABLE users")
    assert schema_manager.validate_schema() is False
    
    # Invalid schema (wrong version)
    initialized_db.execute("UPDATE schema_migrations SET version = 999")
    assert schema_manager.validate_schema() is False
