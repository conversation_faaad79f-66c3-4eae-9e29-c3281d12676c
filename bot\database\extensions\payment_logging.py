"""
Payment logging extension for the database.

This module provides methods to log payment-related events.
"""

import logging
import json
from datetime import datetime
from typing import Dict, Any, Optional, List

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

def extend_database_for_payment_logging(database) -> None:
    """
    Extend database with payment logging methods.

    Args:
        database: Database instance
    """
    try:
        conn = database.conn
        cursor = database.cursor

        # Create payment_logs table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS payment_logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            package_id TEXT,
            status TEXT,
            error_details TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (user_id) ON DELETE CASCADE
        )
        ''')

        conn.commit()
        logger.info("Database schema extended for payment logging")
    except Exception as e:
        logger.error(f"Error extending database schema for payment logging: {e}")
        if conn:
            conn.rollback()
        raise

    # Add methods to Database class
    def add_payment_log(self, user_id: int, package_id: str, status: str, error_details: Optional[Dict[str, Any]] = None) -> int:
        """
        Add a payment log entry.

        Args:
            user_id: User ID
            package_id: Package ID
            status: Payment status
            error_details: Error details (if any)

        Returns:
            int: Log ID
        """
        try:
            # First, check if the user exists
            self.cursor.execute("SELECT user_id FROM users WHERE user_id = ?", (user_id,))
            user = self.cursor.fetchone()
            
            # If user doesn't exist, create a basic user record
            if not user:
                logger.warning(f"User {user_id} not found in database. Creating basic user record before adding payment log.")
                self.cursor.execute(
                    "INSERT INTO users (user_id, credits) VALUES (?, ?)",
                    (user_id, 0)  # Start with 0 credits
                )
                logger.info(f"Created basic user record for user {user_id}")
            
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            # Convert error_details to JSON if provided
            error_json = json.dumps(error_details) if error_details else None

            self.cursor.execute(
                """INSERT INTO payment_logs
                   (user_id, package_id, status, error_details, created_at)
                   VALUES (?, ?, ?, ?, ?)""",
                (user_id, package_id, status, error_json, current_time)
            )
            self.conn.commit()
            log_id = self.cursor.lastrowid
            logger.info(f"Added payment log {log_id} for user {user_id}")
            return log_id
        except Exception as e:
            logger.error(f"Error adding payment log for user {user_id}: {e}")
            self.conn.rollback()
            raise

    def get_payment_logs(self, user_id: int, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Get payment logs for a user.

        Args:
            user_id: User ID
            limit: Maximum number of logs to retrieve

        Returns:
            List of payment logs
        """
        try:
            self.cursor.execute(
                """SELECT id, user_id, package_id, status, error_details, created_at
                   FROM payment_logs
                   WHERE user_id = ?
                   ORDER BY created_at DESC
                   LIMIT ?""",
                (user_id, limit)
            )
            logs = self.cursor.fetchall()
            
            # Parse error_details JSON if present
            result = []
            for log in logs:
                log_dict = dict(log)
                if log_dict.get('error_details'):
                    try:
                        log_dict['error_details'] = json.loads(log_dict['error_details'])
                    except json.JSONDecodeError:
                        pass
                result.append(log_dict)
                
            return result
        except Exception as e:
            logger.error(f"Error getting payment logs for user {user_id}: {e}")
            return []

    # Replace or add methods to Database class
    setattr(database.__class__, 'add_payment_log', add_payment_log)
    setattr(database.__class__, 'get_payment_logs', get_payment_logs)

    logger.info("Database extended with methods for payment logging")
