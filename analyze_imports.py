"""
<PERSON><PERSON><PERSON> to analyze import statements in the project.

This script:
1. Scans all Python files in the project
2. Extracts and analyzes import statements
3. Identifies inconsistent imports
4. Detects potential circular dependencies
5. Finds unused imports
6. Checks for multiple versions of the same dependency
"""

import os
import re
import sys
from collections import defaultdict
from pathlib import Path

# Define directories to ignore
IGNORE_DIRS = [
    '.git',
    'venv',
    '__pycache__',
    '.idea',
    '.vscode',
    'node_modules',
]

# Regular expressions for import statements
IMPORT_PATTERN = re.compile(r'^import\s+([^#\n]+)')
FROM_IMPORT_PATTERN = re.compile(r'^from\s+([^#\n]+)\s+import\s+([^#\n]+)')
COMMENT_PATTERN = re.compile(r'#.*$')
TRY_IMPORT_PATTERN = re.compile(r'try:\s*(?:import|from)')

def extract_imports(file_path):
    """Extract import statements from a Python file."""
    imports = []
    from_imports = []
    try_imports = []
    
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
            
            # Check for try-except import blocks
            if TRY_IMPORT_PATTERN.search(content):
                try_imports.append(file_path)
            
            # Process line by line
            lines = content.split('\n')
            for line in lines:
                # Remove comments
                line = COMMENT_PATTERN.sub('', line).strip()
                
                # Check for 'import' statements
                import_match = IMPORT_PATTERN.match(line)
                if import_match:
                    modules = import_match.group(1).strip()
                    for module in modules.split(','):
                        module = module.strip()
                        if module:
                            if ' as ' in module:
                                module_name, alias = module.split(' as ')
                                imports.append((module_name.strip(), alias.strip()))
                            else:
                                imports.append((module, None))
                
                # Check for 'from ... import' statements
                from_import_match = FROM_IMPORT_PATTERN.match(line)
                if from_import_match:
                    module = from_import_match.group(1).strip()
                    imports_list = from_import_match.group(2).strip()
                    
                    for imp in imports_list.split(','):
                        imp = imp.strip()
                        if imp:
                            if ' as ' in imp:
                                name, alias = imp.split(' as ')
                                from_imports.append((module, name.strip(), alias.strip()))
                            else:
                                from_imports.append((module, imp, None))
    
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
    
    return imports, from_imports, try_imports

def find_python_files(base_dir):
    """Find all Python files in the project."""
    python_files = []
    
    for root, dirs, files in os.walk(base_dir):
        # Skip ignored directories
        dirs[:] = [d for d in dirs if d not in IGNORE_DIRS]
        
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                rel_path = os.path.relpath(file_path, base_dir)
                python_files.append(rel_path)
    
    return python_files

def analyze_imports(base_dir, python_files):
    """Analyze import statements in all Python files."""
    all_imports = {}
    all_from_imports = {}
    all_try_imports = []
    module_dependencies = defaultdict(set)
    
    for file_path in python_files:
        full_path = os.path.join(base_dir, file_path)
        imports, from_imports, try_imports = extract_imports(full_path)
        
        all_imports[file_path] = imports
        all_from_imports[file_path] = from_imports
        if try_imports:
            all_try_imports.extend(try_imports)
        
        # Build dependency graph
        module_name = file_path_to_module_name(file_path)
        
        for imp, _ in imports:
            if imp.startswith('bot.'):
                module_dependencies[module_name].add(imp)
        
        for module, _, _ in from_imports:
            if module.startswith('bot.'):
                module_dependencies[module_name].add(module)
    
    return all_imports, all_from_imports, all_try_imports, module_dependencies

def file_path_to_module_name(file_path):
    """Convert a file path to a module name."""
    # Remove .py extension
    if file_path.endswith('.py'):
        file_path = file_path[:-3]
    
    # Replace directory separators with dots
    module_name = file_path.replace(os.path.sep, '.')
    
    # Add 'bot.' prefix if not already present
    if not module_name.startswith('bot.') and not module_name == 'bot':
        module_name = 'bot.' + module_name
    
    return module_name

def find_circular_dependencies(module_dependencies):
    """Find potential circular dependencies."""
    circular_deps = []
    
    for module, deps in module_dependencies.items():
        for dep in deps:
            if dep in module_dependencies and module in module_dependencies[dep]:
                circular_deps.append((module, dep))
    
    return circular_deps

def find_inconsistent_imports(all_imports, all_from_imports):
    """Find inconsistent import patterns."""
    inconsistent_imports = []
    
    # Check for modules imported both with 'import' and 'from ... import'
    import_modules = set()
    from_import_modules = set()
    
    for file_path, imports in all_imports.items():
        for module, _ in imports:
            import_modules.add(module)
    
    for file_path, from_imports in all_from_imports.items():
        for module, _, _ in from_imports:
            from_import_modules.add(module)
    
    # Find modules that are imported both ways
    both_ways = import_modules.intersection(from_import_modules)
    
    # Find files with inconsistent imports
    for module in both_ways:
        files_with_import = []
        files_with_from_import = []
        
        for file_path, imports in all_imports.items():
            if any(imp == module for imp, _ in imports):
                files_with_import.append(file_path)
        
        for file_path, from_imports in all_from_imports.items():
            if any(mod == module for mod, _, _ in from_imports):
                files_with_from_import.append(file_path)
        
        # Check if the same file uses both import styles
        for file_path in set(files_with_import).intersection(files_with_from_import):
            inconsistent_imports.append((file_path, module))
    
    return inconsistent_imports

def find_multiple_dependency_versions(all_imports, all_from_imports, all_try_imports):
    """Find multiple versions of the same dependency being used."""
    multiple_versions = []
    
    # Check for try-except blocks that might indicate version handling
    if all_try_imports:
        multiple_versions.append(("Multiple versions handled with try-except", all_try_imports))
    
    # Look for specific patterns in imports
    version_patterns = {
        'google.generativeai': ['google_generativeai'],
        'deepgram': ['deepgram.v1', 'deepgram.v2'],
    }
    
    for pattern, alternatives in version_patterns.items():
        files_using_pattern = []
        files_using_alternatives = defaultdict(list)
        
        # Check regular imports
        for file_path, imports in all_imports.items():
            for module, _ in imports:
                if module == pattern:
                    files_using_pattern.append(file_path)
                for alt in alternatives:
                    if module == alt:
                        files_using_alternatives[alt].append(file_path)
        
        # Check from imports
        for file_path, from_imports in all_from_imports.items():
            for module, _, _ in from_imports:
                if module == pattern:
                    files_using_pattern.append(file_path)
                for alt in alternatives:
                    if module == alt:
                        files_using_alternatives[alt].append(file_path)
        
        # If both the main pattern and alternatives are used, report it
        if files_using_pattern and any(files_using_alternatives.values()):
            multiple_versions.append((
                f"Multiple versions of {pattern}",
                {
                    pattern: files_using_pattern,
                    **{alt: files for alt, files in files_using_alternatives.items() if files}
                }
            ))
    
    return multiple_versions

def main():
    """Main function."""
    base_dir = os.path.dirname(os.path.abspath(__file__))
    print(f"Analyzing imports in {base_dir}")
    
    # Find all Python files
    python_files = find_python_files(base_dir)
    print(f"Found {len(python_files)} Python files")
    
    # Analyze imports
    all_imports, all_from_imports, all_try_imports, module_dependencies = analyze_imports(base_dir, python_files)
    
    # Find circular dependencies
    circular_deps = find_circular_dependencies(module_dependencies)
    
    # Find inconsistent imports
    inconsistent_imports = find_inconsistent_imports(all_imports, all_from_imports)
    
    # Find multiple dependency versions
    multiple_versions = find_multiple_dependency_versions(all_imports, all_from_imports, all_try_imports)
    
    # Print results
    print("\n=== IMPORT ANALYSIS REPORT ===\n")
    
    print("1. Potential Circular Dependencies:")
    if circular_deps:
        for module1, module2 in circular_deps:
            print(f"  - {module1} <-> {module2}")
    else:
        print("  No circular dependencies found")
    
    print("\n2. Inconsistent Import Patterns:")
    if inconsistent_imports:
        for file_path, module in inconsistent_imports:
            print(f"  - {file_path}: Uses both 'import {module}' and 'from {module} import ...'")
    else:
        print("  No inconsistent import patterns found")
    
    print("\n3. Multiple Dependency Versions:")
    if multiple_versions:
        for desc, details in multiple_versions:
            print(f"  - {desc}:")
            if isinstance(details, list):
                for file_path in details:
                    print(f"    * {file_path}")
            else:
                for version, files in details.items():
                    print(f"    * {version}: {', '.join(files)}")
    else:
        print("  No multiple dependency versions found")
    
    print("\n=== END OF REPORT ===")

if __name__ == "__main__":
    main()
