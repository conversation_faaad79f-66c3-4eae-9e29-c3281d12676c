"""
Tests for database models.

This module tests the database model classes.
"""

import unittest
from datetime import datetime

from bot.database.models import (
    Model,
    User,
    UserPreference,
    UserStat,
    Conversation,
    Message,
    MessageMetadata,
    Transaction,
    PaymentPackage,
    Subscription,
    Memory,
    MemoryTag,
    VoiceSetting,
    VoiceRecording
)

class TestDatabaseModels(unittest.TestCase):
    """Test case for database models."""
    
    def test_base_model(self):
        """Test base Model class."""
        # Create model
        model = Model(id="test_id", name="Test")
        
        # Check attributes
        self.assertEqual(model.id, "test_id")
        self.assertEqual(model.name, "Test")
        
        # Check to_dict
        data = model.to_dict()
        self.assertEqual(data["id"], "test_id")
        self.assertEqual(data["name"], "Test")
        
        # Check from_dict
        model2 = Model.from_dict(data)
        self.assertEqual(model2.id, "test_id")
        self.assertEqual(model2.name, "Test")
        
        # Check validation
        errors = model.validate()
        self.assertEqual(len(errors), 0)
        
        # Check is_valid
        self.assertTrue(model.is_valid())
        
        # Check update
        model.update(name="New Name")
        self.assertEqual(model.name, "New Name")
        
        # Check equality
        model3 = Model(id="test_id", name="Different Name")
        self.assertEqual(model, model3)
        
        model4 = Model(id="different_id", name="New Name")
        self.assertNotEqual(model, model4)
    
    def test_user_model(self):
        """Test User model."""
        # Create user
        user = User(
            user_id="test_user",
            username="test_username",
            first_name="Test",
            last_name="User",
            language_code="en",
            credits=100,
            is_active=True,
            personality="friendly"
        )
        
        # Check attributes
        self.assertEqual(user.user_id, "test_user")
        self.assertEqual(user.username, "test_username")
        self.assertEqual(user.first_name, "Test")
        self.assertEqual(user.last_name, "User")
        self.assertEqual(user.language_code, "en")
        self.assertEqual(user.credits, 100)
        self.assertTrue(user.is_active)
        self.assertEqual(user.personality, "friendly")
        
        # Check get_full_name
        self.assertEqual(user.get_full_name(), "Test User")
        
        # Check add_credits
        initial_credits = user.credits
        added_credits = 50
        new_credits = user.add_credits(added_credits)
        self.assertEqual(new_credits, initial_credits + added_credits)
        self.assertEqual(user.credits, initial_credits + added_credits)
        
        # Check use_credits
        used_credits = 30
        new_credits = user.use_credits(used_credits)
        self.assertEqual(new_credits, initial_credits + added_credits - used_credits)
        self.assertEqual(user.credits, initial_credits + added_credits - used_credits)
        
        # Check update_last_interaction
        user.update_last_interaction()
        self.assertIsNotNone(user.last_interaction)
        self.assertIsNotNone(user.updated_at)
    
    def test_user_preference_model(self):
        """Test UserPreference model."""
        # Create user preference
        preference = UserPreference(
            preference_id="test_preference",
            user_id="test_user",
            key="theme",
            value="dark"
        )
        
        # Check attributes
        self.assertEqual(preference.preference_id, "test_preference")
        self.assertEqual(preference.user_id, "test_user")
        self.assertEqual(preference.key, "theme")
        self.assertEqual(preference.value, "dark")
    
    def test_user_stat_model(self):
        """Test UserStat model."""
        # Create user stat
        stat = UserStat(
            stat_id="test_stat",
            user_id="test_user",
            key="messages_sent",
            value=10
        )
        
        # Check attributes
        self.assertEqual(stat.stat_id, "test_stat")
        self.assertEqual(stat.user_id, "test_user")
        self.assertEqual(stat.key, "messages_sent")
        self.assertEqual(stat.value, 10)
        
        # Check increment
        initial_value = stat.value
        increment = 5
        new_value = stat.increment(increment)
        self.assertEqual(new_value, initial_value + increment)
        self.assertEqual(stat.value, initial_value + increment)
        
        # Check reset
        stat.reset()
        self.assertEqual(stat.value, 0)
    
    def test_conversation_model(self):
        """Test Conversation model."""
        # Create conversation
        conversation = Conversation(
            conversation_id="test_conversation",
            user_id="test_user",
            title="Test Conversation",
            is_active=True
        )
        
        # Check attributes
        self.assertEqual(conversation.conversation_id, "test_conversation")
        self.assertEqual(conversation.user_id, "test_user")
        self.assertEqual(conversation.title, "Test Conversation")
        self.assertTrue(conversation.is_active)
        
        # Check update_title
        new_title = "New Title"
        conversation.update_title(new_title)
        self.assertEqual(conversation.title, new_title)
        
        # Check archive
        conversation.archive()
        self.assertFalse(conversation.is_active)
        
        # Check activate
        conversation.activate()
        self.assertTrue(conversation.is_active)
    
    def test_message_model(self):
        """Test Message model."""
        # Create message
        message = Message(
            message_id="test_message",
            conversation_id="test_conversation",
            user_id="test_user",
            content="Hello, world!",
            role=Message.ROLE_USER
        )
        
        # Check attributes
        self.assertEqual(message.message_id, "test_message")
        self.assertEqual(message.conversation_id, "test_conversation")
        self.assertEqual(message.user_id, "test_user")
        self.assertEqual(message.content, "Hello, world!")
        self.assertEqual(message.role, Message.ROLE_USER)
        
        # Check is_user_message
        self.assertTrue(message.is_user_message())
        self.assertFalse(message.is_assistant_message())
        self.assertFalse(message.is_system_message())
        
        # Check validation
        errors = message.validate()
        self.assertEqual(len(errors), 0)
        
        # Check validation with invalid role
        message.role = "invalid_role"
        errors = message.validate()
        self.assertGreater(len(errors), 0)
    
    def test_message_metadata_model(self):
        """Test MessageMetadata model."""
        # Create message metadata
        metadata = MessageMetadata(
            metadata_id="test_metadata",
            message_id="test_message",
            key="sentiment",
            value="positive"
        )
        
        # Check attributes
        self.assertEqual(metadata.metadata_id, "test_metadata")
        self.assertEqual(metadata.message_id, "test_message")
        self.assertEqual(metadata.key, "sentiment")
        self.assertEqual(metadata.value, "positive")
    
    def test_transaction_model(self):
        """Test Transaction model."""
        # Create transaction
        transaction = Transaction(
            transaction_id="test_transaction",
            user_id="test_user",
            amount=100,
            type=Transaction.TYPE_PURCHASE,
            status=Transaction.STATUS_PENDING,
            provider="stripe"
        )
        
        # Check attributes
        self.assertEqual(transaction.transaction_id, "test_transaction")
        self.assertEqual(transaction.user_id, "test_user")
        self.assertEqual(transaction.amount, 100)
        self.assertEqual(transaction.type, Transaction.TYPE_PURCHASE)
        self.assertEqual(transaction.status, Transaction.STATUS_PENDING)
        self.assertEqual(transaction.provider, "stripe")
        
        # Check complete
        provider_transaction_id = "txn_123456"
        transaction.complete(provider_transaction_id)
        self.assertEqual(transaction.status, Transaction.STATUS_COMPLETED)
        self.assertEqual(transaction.provider_transaction_id, provider_transaction_id)
        
        # Check is_completed
        self.assertTrue(transaction.is_completed())
        self.assertFalse(transaction.is_pending())
        
        # Check fail
        transaction.fail()
        self.assertEqual(transaction.status, Transaction.STATUS_FAILED)
        
        # Check refund
        transaction.refund()
        self.assertEqual(transaction.status, Transaction.STATUS_REFUNDED)
        
        # Check cancel
        transaction.cancel()
        self.assertEqual(transaction.status, Transaction.STATUS_CANCELLED)
        
        # Check validation
        errors = transaction.validate()
        self.assertEqual(len(errors), 0)
        
        # Check validation with invalid type
        transaction.type = "invalid_type"
        errors = transaction.validate()
        self.assertGreater(len(errors), 0)
    
    def test_payment_package_model(self):
        """Test PaymentPackage model."""
        # Create payment package
        package = PaymentPackage(
            package_id="test_package",
            name="Basic",
            description="Basic package",
            credits=100,
            price=9.99,
            currency="USD",
            is_active=True
        )
        
        # Check attributes
        self.assertEqual(package.package_id, "test_package")
        self.assertEqual(package.name, "Basic")
        self.assertEqual(package.description, "Basic package")
        self.assertEqual(package.credits, 100)
        self.assertEqual(package.price, 9.99)
        self.assertEqual(package.currency, "USD")
        self.assertTrue(package.is_active)
        
        # Check activate
        package.deactivate()
        self.assertFalse(package.is_active)
        
        package.activate()
        self.assertTrue(package.is_active)
        
        # Check validation
        errors = package.validate()
        self.assertEqual(len(errors), 0)
        
        # Check validation with invalid credits
        package.credits = -1
        errors = package.validate()
        self.assertGreater(len(errors), 0)
    
    def test_subscription_model(self):
        """Test Subscription model."""
        # Create subscription
        subscription = Subscription(
            subscription_id="test_subscription",
            user_id="test_user",
            package_id="test_package",
            status=Subscription.STATUS_PENDING,
            provider="stripe",
            provider_subscription_id="sub_123456"
        )
        
        # Check attributes
        self.assertEqual(subscription.subscription_id, "test_subscription")
        self.assertEqual(subscription.user_id, "test_user")
        self.assertEqual(subscription.package_id, "test_package")
        self.assertEqual(subscription.status, Subscription.STATUS_PENDING)
        self.assertEqual(subscription.provider, "stripe")
        self.assertEqual(subscription.provider_subscription_id, "sub_123456")
        
        # Check activate
        start_date = datetime.now().isoformat()
        end_date = datetime.now().isoformat()
        subscription.activate(start_date, end_date)
        self.assertEqual(subscription.status, Subscription.STATUS_ACTIVE)
        self.assertEqual(subscription.start_date, start_date)
        self.assertEqual(subscription.end_date, end_date)
        
        # Check is_active
        self.assertTrue(subscription.is_active())
        
        # Check deactivate
        subscription.deactivate()
        self.assertEqual(subscription.status, Subscription.STATUS_INACTIVE)
        
        # Check cancel
        subscription.cancel()
        self.assertEqual(subscription.status, Subscription.STATUS_CANCELLED)
        
        # Check expire
        subscription.expire()
        self.assertEqual(subscription.status, Subscription.STATUS_EXPIRED)
        
        # Check validation
        errors = subscription.validate()
        self.assertEqual(len(errors), 0)
        
        # Check validation with invalid status
        subscription.status = "invalid_status"
        errors = subscription.validate()
        self.assertGreater(len(errors), 0)
    
    def test_memory_model(self):
        """Test Memory model."""
        # Create memory
        memory = Memory(
            memory_id="test_memory",
            user_id="test_user",
            content="User likes coffee",
            importance=5
        )
        
        # Check attributes
        self.assertEqual(memory.memory_id, "test_memory")
        self.assertEqual(memory.user_id, "test_user")
        self.assertEqual(memory.content, "User likes coffee")
        self.assertEqual(memory.importance, 5)
        
        # Check update_content
        new_content = "User prefers tea over coffee"
        memory.update_content(new_content)
        self.assertEqual(memory.content, new_content)
        
        # Check update_importance
        new_importance = 8
        memory.update_importance(new_importance)
        self.assertEqual(memory.importance, new_importance)
        
        # Check access
        memory.access()
        self.assertIsNotNone(memory.last_accessed)
        
        # Check validation
        errors = memory.validate()
        self.assertEqual(len(errors), 0)
        
        # Check validation with invalid importance
        memory.importance = 11
        errors = memory.validate()
        self.assertGreater(len(errors), 0)
    
    def test_memory_tag_model(self):
        """Test MemoryTag model."""
        # Create memory tag
        tag = MemoryTag(
            tag_id="test_tag",
            memory_id="test_memory",
            tag="preferences"
        )
        
        # Check attributes
        self.assertEqual(tag.tag_id, "test_tag")
        self.assertEqual(tag.memory_id, "test_memory")
        self.assertEqual(tag.tag, "preferences")
    
    def test_voice_setting_model(self):
        """Test VoiceSetting model."""
        # Create voice setting
        setting = VoiceSetting(
            setting_id="test_setting",
            user_id="test_user",
            provider="deepgram",
            voice_id="aura",
            pitch=1.0,
            rate=1.0,
            volume=1.0
        )
        
        # Check attributes
        self.assertEqual(setting.setting_id, "test_setting")
        self.assertEqual(setting.user_id, "test_user")
        self.assertEqual(setting.provider, "deepgram")
        self.assertEqual(setting.voice_id, "aura")
        self.assertEqual(setting.pitch, 1.0)
        self.assertEqual(setting.rate, 1.0)
        self.assertEqual(setting.volume, 1.0)
        
        # Check update_voice
        new_provider = "elevenlabs"
        new_voice_id = "bella"
        setting.update_voice(new_provider, new_voice_id)
        self.assertEqual(setting.provider, new_provider)
        self.assertEqual(setting.voice_id, new_voice_id)
        
        # Check update_settings
        new_pitch = 1.2
        new_rate = 0.9
        new_volume = 0.8
        setting.update_settings(new_pitch, new_rate, new_volume)
        self.assertEqual(setting.pitch, new_pitch)
        self.assertEqual(setting.rate, new_rate)
        self.assertEqual(setting.volume, new_volume)
        
        # Check validation
        errors = setting.validate()
        self.assertEqual(len(errors), 0)
        
        # Check validation with invalid pitch
        setting.pitch = 3.0
        errors = setting.validate()
        self.assertGreater(len(errors), 0)
    
    def test_voice_recording_model(self):
        """Test VoiceRecording model."""
        # Create voice recording
        recording = VoiceRecording(
            recording_id="test_recording",
            user_id="test_user",
            message_id="test_message",
            file_path="/tmp/recording.mp3",
            duration=5.5
        )
        
        # Check attributes
        self.assertEqual(recording.recording_id, "test_recording")
        self.assertEqual(recording.user_id, "test_user")
        self.assertEqual(recording.message_id, "test_message")
        self.assertEqual(recording.file_path, "/tmp/recording.mp3")
        self.assertEqual(recording.duration, 5.5)
        
        # Check update_duration
        new_duration = 6.0
        recording.update_duration(new_duration)
        self.assertEqual(recording.duration, new_duration)

if __name__ == "__main__":
    unittest.main()
