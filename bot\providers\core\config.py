"""
Provider configuration for VoicePal.

This module provides classes and functions for loading and validating provider configurations.
"""

import os
import json
import logging
import yaml
from typing import Dict, Any, Optional, List, Type, TypeVar, Generic, Union, get_type_hints
from dataclasses import dataclass, field, asdict, is_dataclass
from pathlib import Path

from bot.providers.core.exceptions import (
    ProviderConfigError,
    ProviderNotFoundError
)

# Set up logging
logger = logging.getLogger(__name__)

# Type variable for provider configuration
T = TypeVar('T')

@dataclass
class ProviderConfig:
    """Base class for provider configurations."""
    
    provider_type: str
    provider_name: str
    enabled: bool = True
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary.
        
        Returns:
            Configuration as dictionary
        """
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ProviderConfig':
        """Create configuration from dictionary.
        
        Args:
            data: Configuration data
            
        Returns:
            Configuration instance
            
        Raises:
            ProviderConfigError: If configuration is invalid
        """
        try:
            # Get type hints for the class
            hints = get_type_hints(cls)
            
            # Filter data to only include fields defined in the class
            filtered_data = {}
            for key, value in data.items():
                if key in hints:
                    filtered_data[key] = value
            
            return cls(**filtered_data)
        except Exception as e:
            raise ProviderConfigError(f"Failed to create configuration from dictionary: {e}") from e
    
    def validate(self) -> List[str]:
        """Validate configuration.
        
        Returns:
            List of validation error messages (empty if valid)
        """
        errors = []
        
        if not self.provider_type:
            errors.append("Provider type is required")
        
        if not self.provider_name:
            errors.append("Provider name is required")
        
        return errors
    
    def is_valid(self) -> bool:
        """Check if configuration is valid.
        
        Returns:
            True if configuration is valid, False otherwise
        """
        return len(self.validate()) == 0

@dataclass
class AIProviderConfig(ProviderConfig):
    """Configuration for AI providers."""
    
    api_key: str = ""
    model: str = ""
    max_tokens: int = 1000
    temperature: float = 0.7
    top_p: float = 1.0
    frequency_penalty: float = 0.0
    presence_penalty: float = 0.0
    timeout: int = 30
    
    def __post_init__(self):
        """Post-initialization."""
        self.provider_type = "ai"
    
    def validate(self) -> List[str]:
        """Validate configuration.
        
        Returns:
            List of validation error messages (empty if valid)
        """
        errors = super().validate()
        
        if not self.api_key:
            errors.append("API key is required")
        
        if not self.model:
            errors.append("Model is required")
        
        if self.max_tokens <= 0:
            errors.append("Max tokens must be positive")
        
        if self.temperature < 0 or self.temperature > 1:
            errors.append("Temperature must be between 0 and 1")
        
        if self.top_p <= 0 or self.top_p > 1:
            errors.append("Top P must be between 0 and 1")
        
        if self.timeout <= 0:
            errors.append("Timeout must be positive")
        
        return errors

@dataclass
class TTSProviderConfig(ProviderConfig):
    """Configuration for TTS providers."""
    
    api_key: str = ""
    voice_id: str = ""
    sample_rate: int = 24000
    audio_format: str = "mp3"
    timeout: int = 30
    
    def __post_init__(self):
        """Post-initialization."""
        self.provider_type = "tts"
    
    def validate(self) -> List[str]:
        """Validate configuration.
        
        Returns:
            List of validation error messages (empty if valid)
        """
        errors = super().validate()
        
        if not self.api_key:
            errors.append("API key is required")
        
        if not self.voice_id:
            errors.append("Voice ID is required")
        
        if self.sample_rate <= 0:
            errors.append("Sample rate must be positive")
        
        if not self.audio_format:
            errors.append("Audio format is required")
        
        if self.timeout <= 0:
            errors.append("Timeout must be positive")
        
        return errors

@dataclass
class STTProviderConfig(ProviderConfig):
    """Configuration for STT providers."""
    
    api_key: str = ""
    language: str = "en"
    model: str = ""
    timeout: int = 30
    
    def __post_init__(self):
        """Post-initialization."""
        self.provider_type = "stt"
    
    def validate(self) -> List[str]:
        """Validate configuration.
        
        Returns:
            List of validation error messages (empty if valid)
        """
        errors = super().validate()
        
        if not self.api_key:
            errors.append("API key is required")
        
        if not self.language:
            errors.append("Language is required")
        
        if self.timeout <= 0:
            errors.append("Timeout must be positive")
        
        return errors

@dataclass
class PaymentProviderConfig(ProviderConfig):
    """Configuration for payment providers."""
    
    api_key: str = ""
    secret_key: str = ""
    webhook_secret: str = ""
    currency: str = "USD"
    timeout: int = 30
    
    def __post_init__(self):
        """Post-initialization."""
        self.provider_type = "payment"
    
    def validate(self) -> List[str]:
        """Validate configuration.
        
        Returns:
            List of validation error messages (empty if valid)
        """
        errors = super().validate()
        
        if not self.api_key:
            errors.append("API key is required")
        
        if not self.secret_key:
            errors.append("Secret key is required")
        
        if not self.currency:
            errors.append("Currency is required")
        
        if self.timeout <= 0:
            errors.append("Timeout must be positive")
        
        return errors

class ConfigLoader:
    """Configuration loader.
    
    This class provides methods for loading provider configurations from various sources.
    """
    
    def __init__(self, config_dir: Optional[Union[str, Path]] = None):
        """Initialize configuration loader.
        
        Args:
            config_dir: Configuration directory
        """
        self.config_dir = Path(config_dir) if config_dir else None
    
    def load_from_dict(self, data: Dict[str, Any], config_class: Type[T]) -> T:
        """Load configuration from dictionary.
        
        Args:
            data: Configuration data
            config_class: Configuration class
            
        Returns:
            Configuration instance
            
        Raises:
            ProviderConfigError: If configuration is invalid
        """
        try:
            config = config_class.from_dict(data)
            errors = config.validate()
            
            if errors:
                error_message = "; ".join(errors)
                raise ProviderConfigError(f"Invalid configuration: {error_message}")
            
            return config
        except Exception as e:
            if isinstance(e, ProviderConfigError):
                # Re-raise without wrapping
                raise
            
            raise ProviderConfigError(f"Failed to load configuration from dictionary: {e}") from e
    
    def load_from_json(self, json_str: str, config_class: Type[T]) -> T:
        """Load configuration from JSON string.
        
        Args:
            json_str: JSON string
            config_class: Configuration class
            
        Returns:
            Configuration instance
            
        Raises:
            ProviderConfigError: If configuration is invalid
        """
        try:
            data = json.loads(json_str)
            return self.load_from_dict(data, config_class)
        except json.JSONDecodeError as e:
            raise ProviderConfigError(f"Failed to parse JSON: {e}") from e
        except Exception as e:
            if isinstance(e, ProviderConfigError):
                # Re-raise without wrapping
                raise
            
            raise ProviderConfigError(f"Failed to load configuration from JSON: {e}") from e
    
    def load_from_yaml(self, yaml_str: str, config_class: Type[T]) -> T:
        """Load configuration from YAML string.
        
        Args:
            yaml_str: YAML string
            config_class: Configuration class
            
        Returns:
            Configuration instance
            
        Raises:
            ProviderConfigError: If configuration is invalid
        """
        try:
            data = yaml.safe_load(yaml_str)
            return self.load_from_dict(data, config_class)
        except yaml.YAMLError as e:
            raise ProviderConfigError(f"Failed to parse YAML: {e}") from e
        except Exception as e:
            if isinstance(e, ProviderConfigError):
                # Re-raise without wrapping
                raise
            
            raise ProviderConfigError(f"Failed to load configuration from YAML: {e}") from e
    
    def load_from_file(self, file_path: Union[str, Path], config_class: Type[T]) -> T:
        """Load configuration from file.
        
        Args:
            file_path: File path
            config_class: Configuration class
            
        Returns:
            Configuration instance
            
        Raises:
            ProviderConfigError: If configuration is invalid
        """
        file_path = Path(file_path)
        
        try:
            with open(file_path, "r") as f:
                content = f.read()
            
            if file_path.suffix.lower() in [".json"]:
                return self.load_from_json(content, config_class)
            elif file_path.suffix.lower() in [".yaml", ".yml"]:
                return self.load_from_yaml(content, config_class)
            else:
                raise ProviderConfigError(f"Unsupported file format: {file_path.suffix}")
        except FileNotFoundError as e:
            raise ProviderConfigError(f"Configuration file not found: {file_path}") from e
        except Exception as e:
            if isinstance(e, ProviderConfigError):
                # Re-raise without wrapping
                raise
            
            raise ProviderConfigError(f"Failed to load configuration from file: {e}") from e
    
    def load_from_env(self, prefix: str, config_class: Type[T]) -> T:
        """Load configuration from environment variables.
        
        Args:
            prefix: Environment variable prefix
            config_class: Configuration class
            
        Returns:
            Configuration instance
            
        Raises:
            ProviderConfigError: If configuration is invalid
        """
        try:
            # Get type hints for the class
            hints = get_type_hints(config_class)
            
            # Build data dictionary from environment variables
            data = {}
            for key in hints.keys():
                env_key = f"{prefix}_{key.upper()}"
                if env_key in os.environ:
                    value = os.environ[env_key]
                    
                    # Convert value to appropriate type
                    if hints[key] == bool:
                        value = value.lower() in ["true", "1", "yes", "y"]
                    elif hints[key] == int:
                        value = int(value)
                    elif hints[key] == float:
                        value = float(value)
                    
                    data[key] = value
            
            return self.load_from_dict(data, config_class)
        except Exception as e:
            if isinstance(e, ProviderConfigError):
                # Re-raise without wrapping
                raise
            
            raise ProviderConfigError(f"Failed to load configuration from environment variables: {e}") from e
