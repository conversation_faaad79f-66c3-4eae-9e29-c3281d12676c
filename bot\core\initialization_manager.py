"""
Initialization Manager for VoicePal Bot.

This module provides a centralized initialization manager for the VoicePal bot,
ensuring a clean and consistent startup sequence.
"""

import os
import logging
import asyncio
from typing import Dict, Any, Optional, List, Callable, Awaitable, Tuple

from bot.config_manager import ConfigManager
from bot.database.database import Database
from bot.database.extensions import extend_database
from bot.core.key_manager import KeyManager
from bot.core.secure_provider_factory import SecureProviderFactory
from bot.providers.voice.processor import VoiceProcessor
from bot.core.user_manager import UserManager
from bot.core.feature_registry import FeatureRegistry
from bot.features.button_state_manager import ButtonStateManager
from bot.features.welcome_manager import WelcomeManager
from bot.payment.telegram_stars_payment import TelegramStarsPayment
from bot.core.enhanced_dialog_engine import EnhancedDialogEngine as DialogEngine
from bot.core.menu_manager import MenuManager
from bot.core.navigation_router import NavigationRouter

# Import centralized logging configuration
from bot.core.logging_config import get_logger

# Set up logger
logger = get_logger(__name__)


class InitializationManager:
    """
    Centralized initialization manager for VoicePal bot.

    This class handles the initialization of all components in a consistent way,
    with proper error handling and dependency injection.
    """

    def __init__(self):
        """Initialize the initialization manager."""
        self.components = {}
        self.initialization_steps = []
        self.integration_steps = []
        self.error_handlers = {}

    def register_component(self, name: str, component: Any) -> None:
        """
        Register a component with the initialization manager.

        Args:
            name: Component name
            component: Component instance
        """
        self.components[name] = component

    def get_component(self, name: str) -> Any:
        """
        Get a component by name.

        Args:
            name: Component name

        Returns:
            Component instance
        """
        return self.components.get(name)

    def register_initialization_step(
        self,
        name: str,
        func: Callable[[], Awaitable[Tuple[bool, Optional[str]]]],
        dependencies: List[str] = None,
        required: bool = True
    ) -> None:
        """
        Register an initialization step.

        Args:
            name: Step name
            func: Async function to execute
            dependencies: List of step names that must be executed before this step
            required: Whether this step is required for successful initialization
        """
        self.initialization_steps.append({
            "name": name,
            "func": func,
            "dependencies": dependencies or [],
            "required": required,
            "executed": False,
            "success": False,
            "error": None
        })

    def register_integration_step(
        self,
        name: str,
        func: Callable[[], Awaitable[Tuple[bool, Optional[str]]]],
        dependencies: List[str] = None,
        required: bool = False
    ) -> None:
        """
        Register an integration step.

        Args:
            name: Step name
            func: Async function to execute
            dependencies: List of step names that must be executed before this step
            required: Whether this step is required for successful initialization
        """
        self.integration_steps.append({
            "name": name,
            "func": func,
            "dependencies": dependencies or [],
            "required": required,
            "executed": False,
            "success": False,
            "error": None
        })

    def register_error_handler(self, step_name: str, handler: Callable[[Exception], Awaitable[None]]) -> None:
        """
        Register an error handler for a specific initialization step.

        Args:
            step_name: Step name
            handler: Async function to handle errors
        """
        self.error_handlers[step_name] = handler

    async def _execute_step(self, step: Dict[str, Any]) -> bool:
        """
        Execute an initialization step.

        Args:
            step: Step configuration

        Returns:
            bool: True if step was successful, False otherwise
        """
        if step["executed"]:
            return step["success"]

        # Check dependencies
        for dependency in step["dependencies"]:
            # Find dependency step
            dependency_step = None
            for s in self.initialization_steps:
                if s["name"] == dependency:
                    dependency_step = s
                    break

            if not dependency_step:
                for s in self.integration_steps:
                    if s["name"] == dependency:
                        dependency_step = s
                        break

            if not dependency_step:
                logger.error(f"Dependency {dependency} not found for step {step['name']}")
                step["executed"] = True
                step["success"] = False
                step["error"] = f"Dependency {dependency} not found"
                return False

            # Execute dependency if not already executed
            if not dependency_step["executed"]:
                success = await self._execute_step(dependency_step)
                if not success and step["required"]:
                    logger.error(f"Required dependency {dependency} failed for step {step['name']}")
                    step["executed"] = True
                    step["success"] = False
                    step["error"] = f"Required dependency {dependency} failed"
                    return False

        # Execute step
        try:
            logger.info(f"Executing initialization step: {step['name']}")
            success, error = await step["func"]()
            step["executed"] = True
            step["success"] = success
            step["error"] = error

            if success:
                logger.info(f"Initialization step {step['name']} completed successfully")
            else:
                logger.error(f"Initialization step {step['name']} failed: {error}")

                # Call error handler if registered
                if step["name"] in self.error_handlers:
                    try:
                        await self.error_handlers[step["name"]](Exception(error))
                    except Exception as e:
                        logger.error(f"Error handler for step {step['name']} failed: {e}")

            return success
        except Exception as e:
            logger.error(f"Error executing initialization step {step['name']}: {e}")
            step["executed"] = True
            step["success"] = False
            step["error"] = str(e)

            # Call error handler if registered
            if step["name"] in self.error_handlers:
                try:
                    await self.error_handlers[step["name"]](e)
                except Exception as handler_error:
                    logger.error(f"Error handler for step {step['name']} failed: {handler_error}")

            return False

    async def initialize(self) -> bool:
        """
        Initialize all components.

        Returns:
            bool: True if initialization was successful, False otherwise
        """
        # Execute all initialization steps
        all_required_steps_successful = True

        for step in self.initialization_steps:
            if step["required"]:
                success = await self._execute_step(step)
                if not success:
                    all_required_steps_successful = False

        if not all_required_steps_successful:
            logger.error("Initialization failed: One or more required steps failed")
            return False

        # Execute all integration steps
        for step in self.integration_steps:
            if step["required"]:
                success = await self._execute_step(step)
                if not success:
                    logger.error(f"Required integration step {step['name']} failed")
                    return False
            else:
                # Execute non-required integration steps in the background
                asyncio.create_task(self._execute_step(step))

        logger.info("Initialization completed successfully")
        return True

    def get_initialization_status(self) -> Dict[str, Any]:
        """
        Get the status of all initialization steps.

        Returns:
            Dict with initialization status
        """
        return {
            "initialization_steps": self.initialization_steps,
            "integration_steps": self.integration_steps
        }
