"""
Import handler for VoicePal.

This script provides utilities for handling import errors gracefully,
with detailed error messages and fallback mechanisms.
"""

import sys
import importlib
import logging
import traceback
from typing import Dict, Any, Optional, List, Tuple, Callable

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# Define fallback modules for critical components
FALLBACK_MODULES = {
    "bot.providers.ai.google_ai_provider": "bot.providers.ai.mock_ai_provider",
    "bot.providers.tts.deepgram_provider": "bot.providers.tts.gtts_provider",
    "bot.providers.tts.elevenlabs_provider": "bot.providers.tts.gtts_provider",
    "bot.payment.telegram_stars_payment": "bot.payment.mock_payment"
}

def safe_import(module_name: str, fallback_module: Optional[str] = None) -> Tuple[Any, bool, Optional[str]]:
    """
    Safely import a module with detailed error handling and optional fallback.

    Args:
        module_name: Name of the module to import
        fallback_module: Optional fallback module to import if the main one fails

    Returns:
        Tuple containing:
        - The imported module or None if import failed
        - Boolean indicating if import was successful
        - Error message if import failed, None otherwise
    """
    try:
        module = importlib.import_module(module_name)
        return module, True, None
    except ImportError as e:
        error_msg = f"Error importing {module_name}: {str(e)}"
        logger.error(error_msg)
        logger.error(traceback.format_exc())

        # Try fallback if provided
        if fallback_module:
            logger.warning(f"Attempting to use fallback module: {fallback_module}")
            try:
                fallback = importlib.import_module(fallback_module)
                logger.info(f"Successfully imported fallback module: {fallback_module}")
                return fallback, False, error_msg
            except ImportError as fallback_error:
                fallback_error_msg = f"Error importing fallback module {fallback_module}: {str(fallback_error)}"
                logger.error(fallback_error_msg)
                logger.error(traceback.format_exc())
                return None, False, f"{error_msg}\nFallback error: {fallback_error_msg}"

        return None, False, error_msg

def patch_import_system():
    """
    Patch the import system to provide better error messages and fallbacks.
    This should be called at the start of the application.
    """
    original_import = __builtins__['__import__']

    def patched_import(name, globals=None, locals=None, fromlist=(), level=0):
        try:
            return original_import(name, globals, locals, fromlist, level)
        except ImportError as e:
            # Check if this is a known module with a fallback
            if name in FALLBACK_MODULES:
                fallback_name = FALLBACK_MODULES[name]
                logger.warning(f"Import error for {name}: {e}. Trying fallback: {fallback_name}")
                try:
                    return original_import(fallback_name, globals, locals, fromlist, level)
                except ImportError as fallback_e:
                    logger.error(f"Fallback import also failed for {fallback_name}: {fallback_e}")

            # Provide detailed error information
            logger.error(f"Import error for {name}: {e}")
            logger.error(f"Module search path: {sys.path}")
            logger.error(f"Traceback: {traceback.format_exc()}")

            # Re-raise the original exception
            raise

    # Replace the built-in import function
    __builtins__['__import__'] = patched_import
    logger.info("Import system patched for better error handling")

def verify_critical_imports() -> Dict[str, Dict[str, Any]]:
    """
    Verify that all critical modules can be imported.

    Returns:
        Dict mapping module names to import status information
    """
    critical_modules = [
        "bot.config_manager",
        "bot.database",
        "bot.providers.provider_factory",
        "bot.core.user_manager",
        "bot.core.enhanced_dialog_engine",
        "bot.core.feature_registry",
        "bot.features.enhanced_memory_manager",
        "bot.core.menu_manager",
        "bot.providers.voice.processor",
        "bot.payment",
        "bot.main"
    ]

    results = {}

    for module_name in critical_modules:
        fallback = FALLBACK_MODULES.get(module_name)
        module, success, error = safe_import(module_name, fallback)

        results[module_name] = {
            "success": success,
            "error": error,
            "used_fallback": fallback is not None and not success and module is not None
        }

    return results

def print_import_verification_results(results: Dict[str, Dict[str, Any]]) -> bool:
    """
    Print the results of import verification.

    Args:
        results: Dict mapping module names to import status information

    Returns:
        Boolean indicating if all critical imports were successful
    """
    logger.info("=== Import Verification Results ===")

    all_successful = True

    for module_name, result in results.items():
        if result["success"]:
            logger.info(f"✅ {module_name}: Successfully imported")
        elif result["used_fallback"]:
            logger.warning(f"⚠️ {module_name}: Using fallback module")
            all_successful = False
        else:
            logger.error(f"❌ {module_name}: Import failed - {result['error']}")
            all_successful = False

    if all_successful:
        logger.info("All critical imports successful!")
    else:
        logger.warning("Some imports failed or are using fallbacks. The application may not function correctly.")

    return all_successful

if __name__ == "__main__":
    # Test the import verification
    results = verify_critical_imports()
    success = print_import_verification_results(results)

    sys.exit(0 if success else 1)
