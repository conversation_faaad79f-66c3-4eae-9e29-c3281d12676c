"""
Google AI conversation module for VoicePal.

This module provides an implementation of the AI conversation interface using
Google AI Studio's Gemini model. It replaces the simple rule-based conversation
system with a more sophisticated AI model.
"""

import logging
import os
from typing import Optional
from datetime import datetime
from bot.ai_conversation import AIConversation  # This is correct as AIConversation is still in bot.ai_conversation

# Import Google AI SDK
try:
    from google import genai
    from google.genai import types
    GOOGLE_AI_AVAILABLE = True
except ImportError:
    GOOGLE_AI_AVAILABLE = False

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class GoogleAIConversation(AIConversation):
    """Google AI conversation handler for VoicePal using Gemini model."""

    def __init__(self, api_key: Optional[str] = None,
                 model_name: str = "gemini-2.0-flash",
                 personality: str = "friendly",
                 conversation_memory: int = 5,
                 temperature: float = 0.7,
                 top_p: float = 0.95,
                 top_k: int = 40):
        """
        Initialize the Google AI conversation handler.

        Args:
            api_key: Google AI API key (defaults to GOOGLE_AI_API_KEY env var)
            model_name: Google AI model name
            personality: Personality type (friendly, witty, calm, etc.)
            conversation_memory: Number of previous exchanges to remember
            temperature: Sampling temperature (0.0 to 1.0)
            top_p: Top-p sampling parameter
            top_k: Top-k sampling parameter
        """
        # Initialize parent class
        super().__init__(personality, conversation_memory)

        # Set up Google AI
        self.api_key = api_key or os.getenv("GOOGLE_AI_API_KEY")
        if not self.api_key:
            logger.warning("No Google AI API key provided. Using fallback conversation system.")
            self.use_google_ai = False
        else:
            try:
                # Store model name and generation parameters
                self.model_name = model_name
                self.temperature = temperature
                self.top_p = top_p
                self.top_k = top_k

                # Initialize Google AI client if available
                if GOOGLE_AI_AVAILABLE:
                    self.client = genai.Client(api_key=self.api_key)
                    self.use_google_ai = True
                    logger.info(f"Initialized Google AI conversation with {model_name} model")
                else:
                    logger.warning("Google AI SDK not found. Using fallback conversation system.")
                    self.use_google_ai = False
            except Exception as e:
                logger.error(f"Error initializing Google AI: {e}")
                self.use_google_ai = False

        # Create system prompts for each personality
        self.system_prompts = {
            "friendly": (
                "You are VoicePal, a friendly and supportive AI companion. "
                "Your tone is warm, empathetic, and encouraging. "
                "You're here to provide a positive and supportive conversation experience. "
                "Keep your responses concise (1-3 sentences) as this is a voice chat application."
            ),
            "witty": (
                "You are VoicePal, a witty and humorous AI companion. "
                "Your tone is light-hearted, clever, and playful with occasional jokes and wordplay. "
                "You're here to provide an entertaining conversation experience. "
                "Keep your responses concise (1-3 sentences) as this is a voice chat application."
            ),
            "calm": (
                "You are VoicePal, a calm and serene AI companion. "
                "Your tone is peaceful, measured, and reassuring. "
                "You're here to provide a tranquil conversation experience. "
                "Keep your responses concise (1-3 sentences) as this is a voice chat application."
            ),
            "motivational": (
                "You are VoicePal, a motivational and inspiring AI companion. "
                "Your tone is energetic, positive, and encouraging. "
                "You're here to provide an uplifting conversation experience. "
                "Keep your responses concise (1-3 sentences) as this is a voice chat application."
            ),
            "thoughtful": (
                "You are VoicePal, a thoughtful and reflective AI companion. "
                "Your tone is contemplative, philosophical, and insightful. "
                "You're here to provide a meaningful conversation experience. "
                "Keep your responses concise (1-3 sentences) as this is a voice chat application."
            )
        }

    def get_response(self, message: str) -> str:
        """
        Get a response to a user message using Google AI.

        Args:
            message: User message

        Returns:
            str: AI response
        """
        # Add message to conversation history
        self.conversation_history.append({"role": "user", "message": message, "timestamp": datetime.now().isoformat()})

        # Trim conversation history if needed
        if len(self.conversation_history) > self.conversation_memory * 2:
            self.conversation_history = self.conversation_history[-self.conversation_memory * 2:]

        # Use Google AI if available, otherwise fall back to parent implementation
        if self.use_google_ai:
            try:
                # Get system prompt based on personality
                system_prompt = self.system_prompts.get(self.personality, self.system_prompts["friendly"])

                # Prepare the prompt with system instruction and conversation history
                prompt = f"{system_prompt}\n\n"

                # Add conversation history
                for entry in self.conversation_history:
                    if entry["role"] == "user":
                        prompt += f"User: {entry['message']}\n"
                    else:
                        prompt += f"Assistant: {entry['message']}\n"

                # Add the current message
                prompt += f"User: {message}\nAssistant:"

                # Create generation config
                generation_config = types.GenerateContentConfig(
                    temperature=self.temperature,
                    top_p=self.top_p,
                    top_k=self.top_k,
                    max_output_tokens=1024
                )

                # Generate response
                response = self.client.models.generate_content(
                    model=self.model_name,
                    contents=prompt,
                    config=generation_config
                )

                # Extract response text
                ai_response = response.text

                # Add response to conversation history
                self.conversation_history.append({"role": "assistant", "message": ai_response, "timestamp": datetime.now().isoformat()})

                return ai_response

            except Exception as e:
                logger.error(f"Error getting response from Google AI: {e}")
                # Fall back to parent implementation
                return super().get_response(message)
        else:
            # Use parent implementation
            return super().get_response(message)
