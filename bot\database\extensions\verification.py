"""
Database extensions for user verification.

This module extends the Database class with methods for user verification.
"""

import logging
import time
from typing import Dict, Any, Optional, List
from datetime import datetime

import sqlite3

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

def extend_database_for_verification(database):
    """
    Extend the Database class with methods for user verification.
    
    Args:
        database: Database instance
    """
    try:
        conn = database.conn
        cursor = database.cursor
        
        # Check if users table has verification columns
        cursor.execute("PRAGMA table_info(users)")
        columns = cursor.fetchall()
        column_names = [column['name'] for column in columns]
        
        # Add verification columns if they don't exist
        if "is_verified" not in column_names:
            cursor.execute("ALTER TABLE users ADD COLUMN is_verified BOOLEAN DEFAULT 0")
            
        if "verification_date" not in column_names:
            cursor.execute("ALTER TABLE users ADD COLUMN verification_date TIMESTAMP")
            
        if "free_credits_date" not in column_names:
            cursor.execute("ALTER TABLE users ADD COLUMN free_credits_date TIMESTAMP")
        
        conn.commit()
        logger.info("Database schema extended for user verification")
    except Exception as e:
        logger.error(f"Error extending database schema for verification: {e}")
        if conn:
            conn.rollback()
        raise
    
    def update_user_verification(self, user_id: int, is_verified: bool = True) -> bool:
        """
        Update user's verification status.

        Args:
            user_id: Telegram user ID
            is_verified: Whether user is verified

        Returns:
            bool: True if successful, False if user doesn't exist
        """
        try:
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            self.cursor.execute(
                """UPDATE users SET 
                   is_verified = ?,
                   verification_date = ?
                   WHERE user_id = ?""",
                (is_verified, current_time if is_verified else None, user_id)
            )
            self.conn.commit()
            rows_affected = self.cursor.rowcount
            return rows_affected > 0
        except sqlite3.Error as e:
            logger.error(f"Database error updating user verification: {e}")
            self.conn.rollback()
            return False
            
    def is_user_verified(self, user_id: int) -> bool:
        """
        Check if user is verified.

        Args:
            user_id: Telegram user ID

        Returns:
            bool: True if user is verified, False otherwise
        """
        try:
            self.cursor.execute(
                "SELECT is_verified FROM users WHERE user_id = ?",
                (user_id,)
            )
            result = self.cursor.fetchone()
            if result:
                return bool(result['is_verified'])
            return False
        except sqlite3.Error as e:
            logger.error(f"Database error checking user verification: {e}")
            return False
            
    def update_user_free_credits(self, user_id: int, received: bool = True, credits_amount: int = 0) -> bool:
        """
        Update user's free credits received status.

        Args:
            user_id: Telegram user ID
            received: Whether user has received free credits
            credits_amount: Amount of free credits received

        Returns:
            bool: True if successful, False if user doesn't exist
        """
        try:
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            self.cursor.execute(
                """UPDATE users SET 
                   free_credits_received = ?,
                   free_credits_date = ?
                   WHERE user_id = ?""",
                (received, current_time if received else None, user_id)
            )
            self.conn.commit()
            
            # If credits were given, log the transaction
            if received and credits_amount > 0:
                self.cursor.execute(
                    """INSERT INTO transactions 
                       (user_id, amount, credits, transaction_id, status, source) 
                       VALUES (?, ?, ?, ?, ?, ?)""",
                    (user_id, 0, credits_amount, f"free_credits_{user_id}_{int(time.time())}", 
                     "completed", "free_trial")
                )
                self.conn.commit()
            
            rows_affected = self.cursor.rowcount
            return rows_affected > 0
        except sqlite3.Error as e:
            logger.error(f"Database error updating user free credits: {e}")
            self.conn.rollback()
            return False
            
    def get_users_with_same_device(self, device_id: str) -> List[Dict[str, Any]]:
        """
        Get all users with the same device ID.

        Args:
            device_id: Device ID

        Returns:
            List of user dictionaries
        """
        try:
            self.cursor.execute(
                """SELECT user_id, username, first_name, last_name, credits, 
                   created_at, last_active, free_credits_received, is_verified
                   FROM users WHERE device_id = ?""",
                (device_id,)
            )
            users = self.cursor.fetchall()
            return [dict(user) for user in users]
        except sqlite3.Error as e:
            logger.error(f"Database error getting users with same device: {e}")
            return []
            
    def get_users_with_same_ip(self, ip_address: str) -> List[Dict[str, Any]]:
        """
        Get all users with the same IP address.

        Args:
            ip_address: IP address

        Returns:
            List of user dictionaries
        """
        try:
            self.cursor.execute(
                """SELECT user_id, username, first_name, last_name, credits, 
                   created_at, last_active, free_credits_received, is_verified
                   FROM users WHERE registration_ip = ? OR last_ip = ?""",
                (ip_address, ip_address)
            )
            users = self.cursor.fetchall()
            return [dict(user) for user in users]
        except sqlite3.Error as e:
            logger.error(f"Database error getting users with same IP: {e}")
            return []
    
    # Add methods to Database class
    setattr(database.__class__, 'update_user_verification', update_user_verification)
    setattr(database.__class__, 'is_user_verified', is_user_verified)
    setattr(database.__class__, 'update_user_free_credits', update_user_free_credits)
    setattr(database.__class__, 'get_users_with_same_device', get_users_with_same_device)
    setattr(database.__class__, 'get_users_with_same_ip', get_users_with_same_ip)
    
    logger.info("Database extended with methods for user verification")
