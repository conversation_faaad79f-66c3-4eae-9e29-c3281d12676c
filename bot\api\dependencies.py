"""
FastAPI dependencies for VoicePal API.

This module provides dependency injection for the FastAPI application.
"""

import logging
from typing import Optional
from fastapi import Depends, HTTPException, status
from unittest.mock import Mock

logger = logging.getLogger(__name__)

# Global instances (to be injected by the main application)
_database_manager = None
_analytics_manager = None
_advanced_features_manager = None

def set_database_manager(db_manager):
    """Set the global database manager instance."""
    global _database_manager
    _database_manager = db_manager

def set_analytics_manager(analytics_manager):
    """Set the global analytics manager instance."""
    global _analytics_manager
    _analytics_manager = analytics_manager

def set_advanced_features_manager(advanced_manager):
    """Set the global advanced features manager instance."""
    global _advanced_features_manager
    _advanced_features_manager = advanced_manager

def get_database_manager():
    """
    Get database manager dependency.
    
    Returns:
        Database manager instance
        
    Raises:
        HTTPException: If database manager is not available
    """
    if _database_manager is None:
        # Return a mock for testing purposes
        logger.warning("Database manager not available, returning mock")
        mock_db = Mock()
        mock_db.get_user.return_value = {
            'user_id': 123,
            'username': 'test_user',
            'credits': 100,
            'created_at': '2024-01-01T00:00:00'
        }
        mock_db.get_all_users.return_value = []
        mock_db.update_user.return_value = True
        mock_db.delete_user.return_value = True
        return mock_db
    
    return _database_manager

def get_analytics_manager():
    """
    Get analytics manager dependency.
    
    Returns:
        Analytics manager instance
        
    Raises:
        HTTPException: If analytics manager is not available
    """
    if _analytics_manager is None:
        # Return a mock for testing purposes
        logger.warning("Analytics manager not available, returning mock")
        mock_analytics = Mock()
        mock_analytics.get_comprehensive_dashboard.return_value = {
            "timestamp": "2024-01-01T00:00:00",
            "user_metrics": {"total_users": 100},
            "conversation_metrics": {"total_conversations": 500},
            "business_metrics": {"total_revenue": 1000.0}
        }
        return mock_analytics
    
    return _analytics_manager

def get_advanced_features_manager():
    """
    Get advanced features manager dependency.
    
    Returns:
        Advanced features manager instance
        
    Raises:
        HTTPException: If advanced features manager is not available
    """
    if _advanced_features_manager is None:
        # Return a mock for testing purposes
        logger.warning("Advanced features manager not available, returning mock")
        mock_advanced = Mock()
        mock_advanced.create_ab_test.return_value = "test_123"
        mock_advanced.create_feature_flag.return_value = "flag_123"
        mock_advanced.send_notification.return_value = True
        return mock_advanced
    
    return _advanced_features_manager

def get_current_user(user_id: int = None):
    """
    Get current user dependency.
    
    Args:
        user_id: User ID from request
        
    Returns:
        User information
        
    Raises:
        HTTPException: If user is not found
    """
    if user_id is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User ID required"
        )
    
    db = get_database_manager()
    user = db.get_user(user_id)
    
    if user is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    return user

def verify_admin_user(user_id: int = None):
    """
    Verify admin user dependency.
    
    Args:
        user_id: User ID to verify
        
    Returns:
        User information if admin
        
    Raises:
        HTTPException: If user is not admin
    """
    # For testing purposes, allow any user to be admin
    # In production, this should check against a list of admin user IDs
    admin_user_ids = [616696346]  # Add your admin user IDs here
    
    if user_id not in admin_user_ids:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin access required"
        )
    
    return get_current_user(user_id)
