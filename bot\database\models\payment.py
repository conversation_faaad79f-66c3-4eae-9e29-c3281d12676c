"""
Payment domain models for VoicePal.

This module provides the payment-related models for the VoicePal database.
"""

import logging
from typing import Dict, List, Any, Optional, ClassVar, Type, Set, Tuple
from datetime import datetime

from bot.database.models.model import Model

# Set up logging
logger = logging.getLogger(__name__)

class Transaction(Model):
    """Transaction model."""
    
    _table_name = "transactions"
    _primary_key = "transaction_id"
    _required_columns = ["user_id", "amount", "type", "status"]
    _foreign_keys = {
        "user_id": ("users", "user_id")
    }
    
    # Transaction types
    TYPE_PURCHASE = "purchase"
    TYPE_REFUND = "refund"
    TYPE_CREDIT = "credit"
    TYPE_DEBIT = "debit"
    TYPE_SUBSCRIPTION = "subscription"
    
    VALID_TYPES = [TYPE_PURCHASE, TYPE_REFUND, TYPE_CREDIT, TYPE_DEBIT, TYPE_SUBSCRIPTION]
    
    # Transaction statuses
    STATUS_PENDING = "pending"
    STATUS_COMPLETED = "completed"
    STATUS_FAILED = "failed"
    STATUS_REFUNDED = "refunded"
    STATUS_CANCELLED = "cancelled"
    
    VALID_STATUSES = [STATUS_PENDING, STATUS_COMPLETED, STATUS_FAILED, STATUS_REFUNDED, STATUS_CANCELLED]
    
    def __init__(self,
                 transaction_id: Optional[str] = None,
                 user_id: str = None,
                 amount: int = None,
                 type: str = None,
                 status: str = None,
                 provider: Optional[str] = None,
                 provider_transaction_id: Optional[str] = None,
                 created_at: Optional[str] = None,
                 updated_at: Optional[str] = None,
                 **kwargs):
        """Initialize transaction model.
        
        Args:
            transaction_id: Transaction ID
            user_id: User ID
            amount: Transaction amount
            type: Transaction type
            status: Transaction status
            provider: Payment provider
            provider_transaction_id: Provider's transaction ID
            created_at: Creation timestamp
            updated_at: Last update timestamp
            **kwargs: Additional attributes
        """
        super().__init__(
            transaction_id=transaction_id,
            user_id=user_id,
            amount=amount,
            type=type,
            status=status,
            provider=provider,
            provider_transaction_id=provider_transaction_id,
            created_at=created_at,
            updated_at=updated_at,
            **kwargs
        )
    
    def validate(self) -> List[str]:
        """Validate transaction attributes.
        
        Returns:
            List of validation error messages (empty if valid)
        """
        errors = super().validate()
        
        # Validate type
        if hasattr(self, "type") and self.type not in self.VALID_TYPES:
            errors.append(f"Invalid type: {self.type}. Must be one of {self.VALID_TYPES}")
        
        # Validate status
        if hasattr(self, "status") and self.status not in self.VALID_STATUSES:
            errors.append(f"Invalid status: {self.status}. Must be one of {self.VALID_STATUSES}")
        
        # Validate amount
        if hasattr(self, "amount") and self.amount is not None:
            if not isinstance(self.amount, int):
                errors.append(f"Amount must be an integer: {self.amount}")
        
        return errors
    
    def complete(self, provider_transaction_id: Optional[str] = None) -> None:
        """Mark transaction as completed.
        
        Args:
            provider_transaction_id: Provider's transaction ID
        """
        self.status = self.STATUS_COMPLETED
        if provider_transaction_id:
            self.provider_transaction_id = provider_transaction_id
        self.updated_at = datetime.now().isoformat()
    
    def fail(self) -> None:
        """Mark transaction as failed."""
        self.status = self.STATUS_FAILED
        self.updated_at = datetime.now().isoformat()
    
    def refund(self) -> None:
        """Mark transaction as refunded."""
        self.status = self.STATUS_REFUNDED
        self.updated_at = datetime.now().isoformat()
    
    def cancel(self) -> None:
        """Mark transaction as cancelled."""
        self.status = self.STATUS_CANCELLED
        self.updated_at = datetime.now().isoformat()
    
    def is_completed(self) -> bool:
        """Check if transaction is completed.
        
        Returns:
            True if transaction is completed, False otherwise
        """
        return self.status == self.STATUS_COMPLETED
    
    def is_pending(self) -> bool:
        """Check if transaction is pending.
        
        Returns:
            True if transaction is pending, False otherwise
        """
        return self.status == self.STATUS_PENDING

class PaymentPackage(Model):
    """Payment package model."""
    
    _table_name = "payment_packages"
    _primary_key = "package_id"
    _required_columns = ["name", "credits", "price"]
    _defaults = {
        "currency": "USD",
        "is_active": True
    }
    
    def __init__(self,
                 package_id: Optional[str] = None,
                 name: str = None,
                 description: Optional[str] = None,
                 credits: int = None,
                 price: float = None,
                 currency: str = "USD",
                 is_active: bool = True,
                 created_at: Optional[str] = None,
                 updated_at: Optional[str] = None,
                 **kwargs):
        """Initialize payment package model.
        
        Args:
            package_id: Package ID
            name: Package name
            description: Package description
            credits: Number of credits
            price: Package price
            currency: Currency code
            is_active: Whether package is active
            created_at: Creation timestamp
            updated_at: Last update timestamp
            **kwargs: Additional attributes
        """
        super().__init__(
            package_id=package_id,
            name=name,
            description=description,
            credits=credits,
            price=price,
            currency=currency,
            is_active=is_active,
            created_at=created_at,
            updated_at=updated_at,
            **kwargs
        )
    
    def validate(self) -> List[str]:
        """Validate payment package attributes.
        
        Returns:
            List of validation error messages (empty if valid)
        """
        errors = super().validate()
        
        # Validate credits
        if hasattr(self, "credits") and self.credits is not None:
            if not isinstance(self.credits, int) or self.credits <= 0:
                errors.append(f"Credits must be a positive integer: {self.credits}")
        
        # Validate price
        if hasattr(self, "price") and self.price is not None:
            if not isinstance(self.price, (int, float)) or self.price < 0:
                errors.append(f"Price must be a non-negative number: {self.price}")
        
        return errors
    
    def activate(self) -> None:
        """Activate payment package."""
        self.is_active = True
        self.updated_at = datetime.now().isoformat()
    
    def deactivate(self) -> None:
        """Deactivate payment package."""
        self.is_active = False
        self.updated_at = datetime.now().isoformat()

class Subscription(Model):
    """Subscription model."""
    
    _table_name = "subscriptions"
    _primary_key = "subscription_id"
    _required_columns = ["user_id", "package_id", "status", "provider"]
    _foreign_keys = {
        "user_id": ("users", "user_id"),
        "package_id": ("payment_packages", "package_id")
    }
    
    # Subscription statuses
    STATUS_ACTIVE = "active"
    STATUS_INACTIVE = "inactive"
    STATUS_CANCELLED = "cancelled"
    STATUS_EXPIRED = "expired"
    STATUS_PENDING = "pending"
    
    VALID_STATUSES = [STATUS_ACTIVE, STATUS_INACTIVE, STATUS_CANCELLED, STATUS_EXPIRED, STATUS_PENDING]
    
    def __init__(self,
                 subscription_id: Optional[str] = None,
                 user_id: str = None,
                 package_id: str = None,
                 status: str = None,
                 provider: str = None,
                 provider_subscription_id: Optional[str] = None,
                 start_date: Optional[str] = None,
                 end_date: Optional[str] = None,
                 created_at: Optional[str] = None,
                 updated_at: Optional[str] = None,
                 **kwargs):
        """Initialize subscription model.
        
        Args:
            subscription_id: Subscription ID
            user_id: User ID
            package_id: Payment package ID
            status: Subscription status
            provider: Payment provider
            provider_subscription_id: Provider's subscription ID
            start_date: Subscription start date
            end_date: Subscription end date
            created_at: Creation timestamp
            updated_at: Last update timestamp
            **kwargs: Additional attributes
        """
        super().__init__(
            subscription_id=subscription_id,
            user_id=user_id,
            package_id=package_id,
            status=status,
            provider=provider,
            provider_subscription_id=provider_subscription_id,
            start_date=start_date,
            end_date=end_date,
            created_at=created_at,
            updated_at=updated_at,
            **kwargs
        )
    
    def validate(self) -> List[str]:
        """Validate subscription attributes.
        
        Returns:
            List of validation error messages (empty if valid)
        """
        errors = super().validate()
        
        # Validate status
        if hasattr(self, "status") and self.status not in self.VALID_STATUSES:
            errors.append(f"Invalid status: {self.status}. Must be one of {self.VALID_STATUSES}")
        
        return errors
    
    def activate(self, start_date: Optional[str] = None, end_date: Optional[str] = None) -> None:
        """Activate subscription.
        
        Args:
            start_date: Subscription start date
            end_date: Subscription end date
        """
        self.status = self.STATUS_ACTIVE
        if start_date:
            self.start_date = start_date
        if end_date:
            self.end_date = end_date
        self.updated_at = datetime.now().isoformat()
    
    def deactivate(self) -> None:
        """Deactivate subscription."""
        self.status = self.STATUS_INACTIVE
        self.updated_at = datetime.now().isoformat()
    
    def cancel(self) -> None:
        """Cancel subscription."""
        self.status = self.STATUS_CANCELLED
        self.updated_at = datetime.now().isoformat()
    
    def expire(self) -> None:
        """Mark subscription as expired."""
        self.status = self.STATUS_EXPIRED
        self.updated_at = datetime.now().isoformat()
    
    def is_active(self) -> bool:
        """Check if subscription is active.
        
        Returns:
            True if subscription is active, False otherwise
        """
        return self.status == self.STATUS_ACTIVE
