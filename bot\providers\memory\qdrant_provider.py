"""
Qdrant provider for VoicePal memory system.

This module provides a Qdrant client wrapper for vector search.
"""

import os
import logging
from typing import Any, Dict, List, Optional, Union, Tuple
from datetime import datetime

try:
    from qdrant_client import QdrantClient
    from qdrant_client.http import models
    from qdrant_client.http.models import (
        Distance, 
        VectorParams, 
        PointStruct, 
        Filter, 
        FieldCondition, 
        Range, 
        MatchValue
    )
    QDRANT_AVAILABLE = True
except ImportError:
    QDRANT_AVAILABLE = False

# Set up logging
logger = logging.getLogger(__name__)

class QdrantProvider:
    """Qdrant provider for VoicePal memory system."""

    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize the Qdrant provider.

        Args:
            config: Configuration dictionary
        """
        self.config = config or {}
        
        # Initialize Qdrant client
        self.client = self._initialize_client()
        
        # Collection configuration
        self.collection_name = self.config.get("collection_name", "voicepal_memories")
        self.vector_size = self.config.get("vector_size", 384)  # Default for sentence-transformers
        self.distance = self.config.get("distance", "cosine")
        
        # Initialize collection if needed
        if self.client and self.config.get("auto_init", True):
            self._ensure_collection_exists()
    
    def _initialize_client(self) -> Optional[Any]:
        """Initialize Qdrant client."""
        if not QDRANT_AVAILABLE:
            logger.warning("qdrant-client not installed, vector search functionality will be limited")
            return None
            
        try:
            # Try to initialize from environment variables
            url = os.environ.get("QDRANT_URL")
            api_key = os.environ.get("QDRANT_API_KEY")
            
            if url:
                if api_key:
                    client = QdrantClient(url=url, api_key=api_key)
                else:
                    client = QdrantClient(url=url)
                
                logger.info("Qdrant client initialized from environment variables")
                return client
        except Exception as e:
            logger.warning(f"Failed to initialize Qdrant from environment: {e}")
        
        try:
            # Try to initialize from config
            url = self.config.get("qdrant_url")
            api_key = self.config.get("qdrant_api_key")
            
            if url:
                if api_key:
                    client = QdrantClient(url=url, api_key=api_key)
                else:
                    client = QdrantClient(url=url)
                
                logger.info("Qdrant client initialized from config")
                return client
        except Exception as e:
            logger.warning(f"Failed to initialize Qdrant from config: {e}")
        
        try:
            # Try to initialize in-memory client
            if self.config.get("use_in_memory", True):
                client = QdrantClient(":memory:")
                logger.info("Qdrant client initialized in memory")
                return client
        except Exception as e:
            logger.error(f"Failed to initialize in-memory Qdrant: {e}")
        
        # Return None if initialization fails
        logger.warning("Qdrant client not initialized")
        return None
    
    def _ensure_collection_exists(self) -> bool:
        """
        Ensure that the collection exists.
        
        Returns:
            True if the collection exists or was created, False otherwise
        """
        if not self.client:
            return False
        
        try:
            # Check if collection exists
            collections = self.client.get_collections().collections
            collection_names = [collection.name for collection in collections]
            
            if self.collection_name in collection_names:
                logger.info(f"Collection '{self.collection_name}' already exists")
                return True
            
            # Create collection
            distance_map = {
                "cosine": Distance.COSINE,
                "euclid": Distance.EUCLID,
                "dot": Distance.DOT
            }
            
            distance = distance_map.get(self.distance.lower(), Distance.COSINE)
            
            self.client.create_collection(
                collection_name=self.collection_name,
                vectors_config=VectorParams(
                    size=self.vector_size,
                    distance=distance
                )
            )
            
            logger.info(f"Collection '{self.collection_name}' created")
            return True
        except Exception as e:
            logger.error(f"Error ensuring collection exists: {e}")
            return False
    
    def is_available(self) -> bool:
        """Check if Qdrant is available."""
        if not self.client:
            return False
        
        try:
            # Check if client is available
            self.client.get_collections()
            return True
        except Exception as e:
            logger.error(f"Qdrant is not available: {e}")
            return False
    
    def store_memory(
        self, 
        user_id: Union[int, str], 
        vector: List[float], 
        text: str, 
        metadata: Dict[str, Any] = None,
        memory_id: Optional[str] = None
    ) -> Optional[str]:
        """
        Store a memory in Qdrant.
        
        Args:
            user_id: User ID
            vector: Vector embedding
            text: Text content
            metadata: Additional metadata
            memory_id: Optional memory ID (if not provided, a UUID will be generated)
            
        Returns:
            Memory ID if successful, None otherwise
        """
        if not self.client:
            return None
        
        try:
            # Generate memory ID if not provided
            if memory_id is None:
                import uuid
                memory_id = str(uuid.uuid4())
            
            # Prepare payload
            payload = {
                "user_id": str(user_id),
                "text": text,
                "created_at": self._get_current_timestamp()
            }
            
            # Add metadata if provided
            if metadata:
                payload.update(metadata)
            
            # Store point
            self.client.upsert(
                collection_name=self.collection_name,
                points=[
                    PointStruct(
                        id=memory_id,
                        vector=vector,
                        payload=payload
                    )
                ]
            )
            
            logger.info(f"Memory stored with ID: {memory_id}")
            return memory_id
        except Exception as e:
            logger.error(f"Error storing memory: {e}")
            return None
    
    def search_memories(
        self, 
        user_id: Union[int, str], 
        vector: List[float], 
        limit: int = 5,
        score_threshold: Optional[float] = None,
        filter_params: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        Search for similar memories.
        
        Args:
            user_id: User ID
            vector: Query vector
            limit: Maximum number of results
            score_threshold: Minimum similarity score
            filter_params: Additional filter parameters
            
        Returns:
            List of memories
        """
        if not self.client:
            return []
        
        try:
            # Prepare filter
            must_conditions = [
                FieldCondition(
                    key="user_id",
                    match=MatchValue(value=str(user_id))
                )
            ]
            
            # Add additional filter conditions
            if filter_params:
                for key, value in filter_params.items():
                    if isinstance(value, (int, float)):
                        must_conditions.append(
                            FieldCondition(
                                key=key,
                                range=Range(
                                    gte=value
                                )
                            )
                        )
                    else:
                        must_conditions.append(
                            FieldCondition(
                                key=key,
                                match=MatchValue(value=value)
                            )
                        )
            
            query_filter = Filter(
                must=must_conditions
            )
            
            # Search for similar memories
            search_params = {}
            if score_threshold is not None:
                search_params["score_threshold"] = score_threshold
            
            results = self.client.search(
                collection_name=self.collection_name,
                query_vector=vector,
                query_filter=query_filter,
                limit=limit,
                **search_params
            )
            
            # Format results
            memories = []
            for result in results:
                memory = result.payload
                memory["id"] = result.id
                memory["score"] = result.score
                memories.append(memory)
            
            return memories
        except Exception as e:
            logger.error(f"Error searching memories: {e}")
            return []
    
    def delete_memory(self, memory_id: str) -> bool:
        """
        Delete a memory.
        
        Args:
            memory_id: Memory ID
            
        Returns:
            True if successful, False otherwise
        """
        if not self.client:
            return False
        
        try:
            self.client.delete(
                collection_name=self.collection_name,
                points_selector=[memory_id]
            )
            
            logger.info(f"Memory deleted: {memory_id}")
            return True
        except Exception as e:
            logger.error(f"Error deleting memory: {e}")
            return False
    
    def delete_user_memories(self, user_id: Union[int, str]) -> bool:
        """
        Delete all memories for a user.
        
        Args:
            user_id: User ID
            
        Returns:
            True if successful, False otherwise
        """
        if not self.client:
            return False
        
        try:
            self.client.delete(
                collection_name=self.collection_name,
                points_selector=Filter(
                    must=[
                        FieldCondition(
                            key="user_id",
                            match=MatchValue(value=str(user_id))
                        )
                    ]
                )
            )
            
            logger.info(f"All memories deleted for user: {user_id}")
            return True
        except Exception as e:
            logger.error(f"Error deleting user memories: {e}")
            return False
    
    def _get_current_timestamp(self) -> str:
        """Get current timestamp in ISO format."""
        return datetime.now().isoformat()
