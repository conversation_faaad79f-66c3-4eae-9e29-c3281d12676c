"""
Unit tests for the embedding utilities module.
"""

import pytest
import numpy as np
from unittest.mock import Magic<PERSON>ock, patch

from bot.utils.embedding_utils import EmbeddingProvider


@pytest.fixture
def mock_sentence_transformer():
    """Create a mock SentenceTransformer."""
    mock_transformer = MagicMock()
    # Mock encode method to return a fixed-size embedding
    mock_transformer.encode.return_value = np.random.rand(384)
    return mock_transformer


@pytest.fixture
def embedding_provider(mock_sentence_transformer):
    """Create an EmbeddingProvider with mocked dependencies."""
    with patch('bot.utils.embedding_utils.SentenceTransformer', return_value=mock_sentence_transformer):
        provider = EmbeddingProvider({
            "model_name": "all-MiniLM-L6-v2",
            "vector_size": 384,
            "batch_size": 32
        })
        provider._model = mock_sentence_transformer
        yield provider


@pytest.fixture
def embedding_provider_no_config():
    """Create an EmbeddingProvider with no configuration."""
    with patch('bot.utils.embedding_utils.SentenceTransformer', side_effect=Exception("Model loading error")):
        provider = EmbeddingProvider({})
        yield provider


class TestEmbeddingProvider:
    """Test cases for the EmbeddingProvider class."""

    def test_initialization_with_config(self, embedding_provider):
        """Test initialization with configuration."""
        assert embedding_provider.is_available() is True
        assert embedding_provider._model_name == "all-MiniLM-L6-v2"
        assert embedding_provider._vector_size == 384
        assert embedding_provider._batch_size == 32

    def test_initialization_without_config(self, embedding_provider_no_config):
        """Test initialization without configuration."""
        assert embedding_provider_no_config.is_available() is False
        assert embedding_provider_no_config._model_name == "all-MiniLM-L6-v2"  # Default value
        assert embedding_provider_no_config._vector_size == 384  # Default value
        assert embedding_provider_no_config._batch_size == 32  # Default value

    def test_is_available(self, embedding_provider, embedding_provider_no_config):
        """Test is_available method."""
        assert embedding_provider.is_available() is True
        assert embedding_provider_no_config.is_available() is False

    def test_generate_embedding(self, embedding_provider, mock_sentence_transformer):
        """Test generate_embedding method."""
        # Test with a single text
        embedding = embedding_provider.generate_embedding("This is a test text")
        assert isinstance(embedding, list)
        assert len(embedding) == 384  # Should match the vector size
        mock_sentence_transformer.encode.assert_called_once()

    def test_generate_embedding_with_error(self, embedding_provider, mock_sentence_transformer):
        """Test generate_embedding method with error."""
        mock_sentence_transformer.encode.side_effect = Exception("Embedding error")
        embedding = embedding_provider.generate_embedding("This is a test text")
        assert embedding is None

    def test_generate_embeddings_batch(self, embedding_provider, mock_sentence_transformer):
        """Test generate_embeddings_batch method."""
        # Test with a batch of texts
        texts = ["This is text 1", "This is text 2", "This is text 3"]
        
        # Mock the encode method to return a batch of embeddings
        mock_sentence_transformer.encode.return_value = np.random.rand(3, 384)
        
        embeddings = embedding_provider.generate_embeddings_batch(texts)
        assert isinstance(embeddings, list)
        assert len(embeddings) == 3
        assert all(len(emb) == 384 for emb in embeddings)
        mock_sentence_transformer.encode.assert_called_once()

    def test_generate_embeddings_batch_with_error(self, embedding_provider, mock_sentence_transformer):
        """Test generate_embeddings_batch method with error."""
        texts = ["This is text 1", "This is text 2", "This is text 3"]
        mock_sentence_transformer.encode.side_effect = Exception("Embedding error")
        embeddings = embedding_provider.generate_embeddings_batch(texts)
        assert embeddings == []

    def test_generate_embeddings_batch_empty(self, embedding_provider):
        """Test generate_embeddings_batch method with empty input."""
        embeddings = embedding_provider.generate_embeddings_batch([])
        assert embeddings == []

    def test_cosine_similarity(self, embedding_provider):
        """Test cosine_similarity method."""
        # Create two test vectors
        vec1 = np.random.rand(384).tolist()
        vec2 = np.random.rand(384).tolist()
        
        # Calculate similarity
        similarity = embedding_provider.cosine_similarity(vec1, vec2)
        assert isinstance(similarity, float)
        assert -1.0 <= similarity <= 1.0

    def test_cosine_similarity_with_error(self, embedding_provider):
        """Test cosine_similarity method with error."""
        # Test with invalid vectors
        vec1 = np.random.rand(384).tolist()
        vec2 = np.random.rand(100).tolist()  # Different size
        
        similarity = embedding_provider.cosine_similarity(vec1, vec2)
        assert similarity == 0.0

    def test_cosine_similarity_same_vector(self, embedding_provider):
        """Test cosine_similarity method with identical vectors."""
        # Same vector should have similarity 1.0
        vec = np.random.rand(384).tolist()
        similarity = embedding_provider.cosine_similarity(vec, vec)
        assert similarity == pytest.approx(1.0)

    def test_cosine_similarity_orthogonal_vectors(self, embedding_provider):
        """Test cosine_similarity method with orthogonal vectors."""
        # Orthogonal vectors should have similarity 0.0
        vec1 = [1.0] + [0.0] * 383
        vec2 = [0.0] + [1.0] + [0.0] * 382
        similarity = embedding_provider.cosine_similarity(vec1, vec2)
        assert similarity == pytest.approx(0.0)

    def test_cosine_similarity_opposite_vectors(self, embedding_provider):
        """Test cosine_similarity method with opposite vectors."""
        # Opposite vectors should have similarity -1.0
        vec1 = [1.0] * 384
        vec2 = [-1.0] * 384
        similarity = embedding_provider.cosine_similarity(vec1, vec2)
        assert similarity == pytest.approx(-1.0)

    def test_find_most_similar(self, embedding_provider):
        """Test find_most_similar method."""
        # Create a query vector and a list of candidate vectors
        query_vec = np.random.rand(384).tolist()
        candidate_vecs = [np.random.rand(384).tolist() for _ in range(5)]
        
        # Add a vector that's very similar to the query
        similar_vec = query_vec.copy()
        similar_vec[0] += 0.01  # Small difference
        candidate_vecs.append(similar_vec)
        
        # Find most similar
        most_similar, similarity = embedding_provider.find_most_similar(query_vec, candidate_vecs)
        assert most_similar == 5  # Should be the last one (the similar vector)
        assert similarity > 0.9  # Should be very similar

    def test_find_most_similar_with_error(self, embedding_provider):
        """Test find_most_similar method with error."""
        # Test with invalid vectors
        query_vec = np.random.rand(384).tolist()
        candidate_vecs = [np.random.rand(100).tolist() for _ in range(5)]  # Different size
        
        most_similar, similarity = embedding_provider.find_most_similar(query_vec, candidate_vecs)
        assert most_similar == -1
        assert similarity == 0.0

    def test_find_most_similar_empty_candidates(self, embedding_provider):
        """Test find_most_similar method with empty candidates."""
        query_vec = np.random.rand(384).tolist()
        most_similar, similarity = embedding_provider.find_most_similar(query_vec, [])
        assert most_similar == -1
        assert similarity == 0.0

    def test_fallback_when_model_unavailable(self, embedding_provider_no_config):
        """Test fallback behavior when the embedding model is unavailable."""
        # All operations should gracefully handle the model being unavailable
        assert embedding_provider_no_config.generate_embedding("This is a test text") is None
        assert embedding_provider_no_config.generate_embeddings_batch(["Text 1", "Text 2"]) == []
        
        # Cosine similarity should still work with valid vectors
        vec1 = np.random.rand(384).tolist()
        vec2 = np.random.rand(384).tolist()
        similarity = embedding_provider_no_config.cosine_similarity(vec1, vec2)
        assert isinstance(similarity, float)
        
        # find_most_similar should still work with valid vectors
        candidate_vecs = [np.random.rand(384).tolist() for _ in range(5)]
        most_similar, similarity = embedding_provider_no_config.find_most_similar(vec1, candidate_vecs)
        assert isinstance(most_similar, int)
        assert isinstance(similarity, float)
