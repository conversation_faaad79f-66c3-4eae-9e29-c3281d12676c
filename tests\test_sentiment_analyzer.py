"""
Test script for sentiment analyzer.

This script tests the sentiment analyzer functionality.
"""

import unittest
import asyncio
import logging
import os
import sys
from unittest.mock import MagicMock, patch
from datetime import datetime

# Add parent directory to path to import bot modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from bot.features.sentiment_analyzer import SentimentAnalyzer
from bot.database.extensions.sentiment import extend_database_for_sentiment

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class TestSentimentAnalyzer(unittest.TestCase):
    """Test case for sentiment analyzer."""

    def setUp(self):
        """Set up test fixtures."""
        # Create mock database
        self.mock_db = MagicMock()
        self.mock_db.add_mood_entry = MagicMock(return_value=1)

        # Create mock Deepgram provider
        self.mock_deepgram = MagicMock()
        self.mock_deepgram.supports_feature = MagicMock(return_value=True)
        self.mock_deepgram.transcribe_with_sentiment = MagicMock(return_value={
            "transcript": "I'm feeling great today!",
            "language": "en",
            "confidence": 0.95,
            "sentiment": {
                "sentiment": "positive",
                "confidence": 0.8
            }
        })

        # Create mock AI provider
        self.mock_ai = MagicMock()
        self.mock_ai.supports_feature = MagicMock(return_value=True)
        self.mock_ai.analyze_sentiment = MagicMock(return_value={
            "sentiment": "positive",
            "confidence": 0.8
        })

        # Create sentiment analyzer
        self.sentiment_analyzer = SentimentAnalyzer(
            deepgram_provider=self.mock_deepgram,
            ai_provider=self.mock_ai,
            database=self.mock_db
        )

    async def test_analyze_voice_sentiment(self):
        """Test analyze_voice_sentiment method."""
        # Test with valid audio file
        sentiment_data = await self.sentiment_analyzer.analyze_voice_sentiment(
            "test_audio.ogg", 123
        )

        # Check that sentiment data is as expected
        self.assertEqual(sentiment_data["sentiment"], "positive")
        self.assertEqual(sentiment_data["confidence"], 0.8)
        self.assertEqual(sentiment_data["source"], "voice")
        self.assertEqual(sentiment_data["transcript"], "I'm feeling great today!")

        # Check that Deepgram provider was called
        self.mock_deepgram.transcribe_with_sentiment.assert_called_once_with("test_audio.ogg")

        # Check that mood entry was added
        self.mock_db.add_mood_entry.assert_called_once()

    async def test_analyze_text_sentiment(self):
        """Test analyze_text_sentiment method."""
        # Test with valid text
        sentiment_data = await self.sentiment_analyzer.analyze_text_sentiment(
            "I'm feeling great today!", 123
        )

        # Check that sentiment data is as expected
        self.assertEqual(sentiment_data["sentiment"], "positive")
        self.assertEqual(sentiment_data["confidence"], 0.8)
        self.assertEqual(sentiment_data["source"], "text")
        self.assertEqual(sentiment_data["transcript"], "I'm feeling great today!")

        # Check that AI provider was called
        self.mock_ai.analyze_sentiment.assert_called_once_with("I'm feeling great today!")

        # Check that mood entry was added
        self.mock_db.add_mood_entry.assert_called_once()

    def test_standardize_sentiment(self):
        """Test _standardize_sentiment method."""
        # Test with already standardized sentiment
        self.assertEqual(self.sentiment_analyzer._standardize_sentiment("positive"), "positive")
        self.assertEqual(self.sentiment_analyzer._standardize_sentiment("negative"), "negative")
        self.assertEqual(self.sentiment_analyzer._standardize_sentiment("neutral"), "neutral")

        # Test with non-standardized sentiment
        self.assertEqual(self.sentiment_analyzer._standardize_sentiment("happy"), "positive")
        self.assertEqual(self.sentiment_analyzer._standardize_sentiment("sad"), "negative")
        self.assertEqual(self.sentiment_analyzer._standardize_sentiment("calm"), "neutral")

        # Test with unknown sentiment
        self.assertEqual(self.sentiment_analyzer._standardize_sentiment("unknown"), "neutral")

    def test_generate_emotions(self):
        """Test _generate_emotions method."""
        # Test with positive sentiment
        emotions = self.sentiment_analyzer._generate_emotions("positive", 0.8)
        self.assertIn("happy", emotions)
        self.assertIn("excited", emotions)
        self.assertIn("content", emotions)

        # Test with negative sentiment
        emotions = self.sentiment_analyzer._generate_emotions("negative", 0.8)
        self.assertIn("sad", emotions)
        self.assertIn("angry", emotions)
        self.assertIn("frustrated", emotions)

        # Test with neutral sentiment
        emotions = self.sentiment_analyzer._generate_emotions("neutral", 0.8)
        self.assertIn("calm", emotions)
        self.assertIn("indifferent", emotions)

    def test_get_response_adjustment(self):
        """Test get_response_adjustment method."""
        # Test with positive sentiment
        adjustment = self.sentiment_analyzer.get_response_adjustment({
            "sentiment": "positive",
            "confidence": 0.8,
            "emotions": {"happy": 0.8, "excited": 0.6}
        })
        self.assertEqual(adjustment["tone"], "enthusiastic")
        self.assertEqual(adjustment["empathy_level"], "medium")

        # Test with negative sentiment
        adjustment = self.sentiment_analyzer.get_response_adjustment({
            "sentiment": "negative",
            "confidence": 0.8,
            "emotions": {"sad": 0.8, "disappointed": 0.6}
        })
        self.assertEqual(adjustment["tone"], "supportive")
        self.assertEqual(adjustment["empathy_level"], "high")

        # Test with neutral sentiment
        adjustment = self.sentiment_analyzer.get_response_adjustment({
            "sentiment": "neutral",
            "confidence": 0.8,
            "emotions": {"calm": 0.8, "indifferent": 0.6}
        })
        self.assertEqual(adjustment["tone"], "neutral")
        self.assertEqual(adjustment["empathy_level"], "medium")

class TestDatabaseExtensionSentiment(unittest.TestCase):
    """Test case for database extension for sentiment."""

    def setUp(self):
        """Set up test fixtures."""
        # Create mock database
        self.mock_db = MagicMock()
        self.mock_db.conn = MagicMock()
        self.mock_db.cursor = MagicMock()

        # Mock cursor.execute
        self.mock_db.cursor.execute = MagicMock()

        # Mock cursor.fetchone
        self.mock_db.cursor.fetchone = MagicMock(return_value=None)

        # Mock cursor.fetchall
        self.mock_db.cursor.fetchall = MagicMock(return_value=[])

        # Mock cursor.lastrowid
        self.mock_db.cursor.lastrowid = 1

        # Extend database
        extend_database_for_sentiment(self.mock_db)

    def test_add_mood_entry(self):
        """Test add_mood_entry method."""
        # Test with valid mood data
        mood_entry_id = self.mock_db.add_mood_entry(123, {
            "sentiment": "positive",
            "confidence": 0.8,
            "source": "voice",
            "message_text": "I'm feeling great today!",
            "emotions": {"happy": 0.8, "excited": 0.6}
        })

        # Check that mood entry was added
        self.assertEqual(mood_entry_id, 1)
        self.mock_db.cursor.execute.assert_called_once()
        self.mock_db.conn.commit.assert_called_once()

    def test_get_mood_history(self):
        """Test get_mood_history method."""
        # Mock cursor.fetchall to return mood entries
        self.mock_db.cursor.fetchall.return_value = [
            {
                "id": 1,
                "user_id": 123,
                "sentiment": "positive",
                "confidence": 0.8,
                "source": "voice",
                "message_text": "I'm feeling great today!",
                "emotions": '{"happy": 0.8, "excited": 0.6}',
                "created_at": "2023-01-01 12:00:00"
            }
        ]

        # Test with valid user ID
        mood_history = self.mock_db.get_mood_history(123)

        # Check that mood history was retrieved
        self.assertEqual(len(mood_history), 1)
        self.assertEqual(mood_history[0]["sentiment"], "positive")
        self.assertEqual(mood_history[0]["emotions"]["happy"], 0.8)

    def test_get_sentiment_distribution(self):
        """Test get_sentiment_distribution method."""
        # Mock cursor.fetchall to return sentiment counts
        self.mock_db.cursor.fetchall.return_value = [
            ("positive", 5),
            ("negative", 2),
            ("neutral", 3)
        ]

        # Test with valid user ID
        sentiment_distribution = self.mock_db.get_sentiment_distribution(123)

        # Check that sentiment distribution was retrieved
        self.assertEqual(sentiment_distribution["positive"], 5)
        self.assertEqual(sentiment_distribution["negative"], 2)
        self.assertEqual(sentiment_distribution["neutral"], 3)

    def test_add_sentiment_response_template(self):
        """Test add_sentiment_response_template method."""
        # Test with valid template
        template_id = self.mock_db.add_sentiment_response_template(
            sentiment="positive",
            response_template="I'm glad to hear you're feeling positive! {response}",
            tone="enthusiastic",
            empathy_level="medium"
        )

        # Check that template was added
        self.assertEqual(template_id, 1)
        self.mock_db.cursor.execute.assert_called_once()
        self.mock_db.conn.commit.assert_called_once()

def run_tests():
    """Run the tests."""
    unittest.main()

if __name__ == "__main__":
    run_tests()
