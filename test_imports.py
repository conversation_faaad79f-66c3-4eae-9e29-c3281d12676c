#!/usr/bin/env python3
"""
Test imports for VoicePal.
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """Test all critical imports."""
    results = {}
    
    # Test database imports
    try:
        from bot.database.database_manager import DatabaseManager
        results['DatabaseManager'] = True
    except Exception as e:
        results['DatabaseManager'] = f"Failed: {e}"
    
    # Test security imports
    try:
        from bot.security.encryption import EncryptionManager
        results['EncryptionManager'] = True
    except Exception as e:
        results['EncryptionManager'] = f"Failed: {e}"
    
    # Test API imports
    try:
        from bot.api.main import create_app
        results['API main'] = True
    except Exception as e:
        results['API main'] = f"Failed: {e}"
    
    # Test analytics imports
    try:
        from bot.analytics.business_analytics import BusinessAnalytics
        results['BusinessAnalytics'] = True
    except Exception as e:
        results['BusinessAnalytics'] = f"Failed: {e}"
    
    # Test performance imports
    try:
        from bot.performance.cache_manager import CacheManager
        results['CacheManager'] = True
    except Exception as e:
        results['CacheManager'] = f"Failed: {e}"
    
    return results

if __name__ == "__main__":
    print("🚀 Testing VoicePal Imports")
    print("=" * 40)
    
    results = test_imports()
    
    for component, result in results.items():
        if result is True:
            print(f"✅ {component}")
        else:
            print(f"❌ {component}: {result}")
    
    passed = sum(1 for r in results.values() if r is True)
    total = len(results)
    
    print("=" * 40)
    print(f"Results: {passed}/{total} imports successful")
    
    if passed == total:
        print("🎉 All imports successful!")
    else:
        print("💥 Some imports failed!")
