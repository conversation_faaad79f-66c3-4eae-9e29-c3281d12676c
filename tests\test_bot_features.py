"""
Test script for VoicePal bot features.

This script tests the key features of the VoicePal bot:
1. Sentiment analysis
2. Payment system
3. Enhanced memory
"""

import os
import sys
import logging
import asyncio
from pathlib import Path

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

async def test_sentiment_analyzer():
    """Test sentiment analyzer."""
    try:
        from bot.features.sentiment_analyzer import SentimentAnalyzer
        from bot.database import Database

        # Create temporary database
        db_file = "test_sentiment.db"
        if os.path.exists(db_file):
            os.remove(db_file)

        # Initialize database
        database = Database(db_file)

        # Initialize sentiment analyzer
        sentiment_analyzer = SentimentAnalyzer(database=database)

        # Test text sentiment analysis
        text = "I'm feeling really happy today!"
        sentiment_data = await sentiment_analyzer.analyze_text_sentiment(text)

        logger.info(f"Text sentiment analysis result: {sentiment_data}")

        # Check if sentiment is positive
        if sentiment_data.get("sentiment") == "positive":
            logger.info("✅ Sentiment analyzer correctly identified positive sentiment")
        else:
            logger.warning(f"❌ Sentiment analyzer failed to identify positive sentiment: {sentiment_data.get('sentiment')}")

        # Clean up
        if os.path.exists(db_file):
            os.remove(db_file)

        return True
    except Exception as e:
        logger.error(f"Error testing sentiment analyzer: {e}")
        return False

async def test_payment_system():
    """Test payment system."""
    try:
        from bot.payment.telegram_stars_payment import TelegramStarsPayment
        from bot.database import Database

        # Create temporary database
        db_file = "test_payment.db"
        if os.path.exists(db_file):
            os.remove(db_file)

        # Initialize database
        database = Database(db_file)

        # Initialize payment system
        payment_system = TelegramStarsPayment(database)

        # Test credit packages
        credit_packages = payment_system.get_credit_packages()

        logger.info(f"Credit packages: {credit_packages}")

        # Check if credit packages are available
        if credit_packages and len(credit_packages) > 0:
            logger.info(f"✅ Payment system has {len(credit_packages)} credit packages")
        else:
            logger.warning("❌ Payment system has no credit packages")

        # Test payment keyboard
        keyboard = payment_system.get_payment_keyboard()

        logger.info(f"Payment keyboard: {keyboard}")

        # Check if keyboard has buttons
        if keyboard and hasattr(keyboard, "inline_keyboard") and len(keyboard.inline_keyboard) > 0:
            logger.info(f"✅ Payment system has a keyboard with {len(keyboard.inline_keyboard)} buttons")
        else:
            logger.warning("❌ Payment system has no keyboard buttons")

        # Clean up
        if os.path.exists(db_file):
            os.remove(db_file)

        return True
    except Exception as e:
        logger.error(f"Error testing payment system: {e}")
        return False

async def test_enhanced_memory():
    """Test enhanced memory system."""
    try:
        # Import memory-related modules
        from bot.features.enhanced_memory_manager import EnhancedMemoryManager as MemoryManager
        from bot.database import Database

        # Create temporary database
        db_file = "test_memory.db"
        if os.path.exists(db_file):
            os.remove(db_file)

        # Initialize database
        database = Database(db_file)

        # Initialize memory manager
        memory_manager = MemoryManager(database=database)

        # Test adding conversation
        user_id = 123456789
        message = "Hello, how are you?"
        response = "I'm doing well, thank you for asking!"

        memory_manager.add_conversation(user_id, message, response)

        # Test getting conversation context
        context = memory_manager.get_conversation_context(user_id)

        logger.info(f"Conversation context: {context}")

        # Check if context contains the conversation
        if context and "conversations" in context and len(context["conversations"]) > 0:
            logger.info("✅ Memory manager correctly stored and retrieved conversation")
        else:
            logger.warning("❌ Memory manager failed to store or retrieve conversation")

        # Clean up
        if os.path.exists(db_file):
            os.remove(db_file)

        return True
    except Exception as e:
        logger.error(f"Error testing enhanced memory: {e}")
        return False

async def main():
    """Main function."""
    logger.info("Starting VoicePal bot feature tests")

    # Test sentiment analyzer
    sentiment_result = await test_sentiment_analyzer()
    logger.info(f"Sentiment analyzer test: {'Success' if sentiment_result else 'Failed'}")

    # Test payment system
    payment_result = await test_payment_system()
    logger.info(f"Payment system test: {'Success' if payment_result else 'Failed'}")

    # Test enhanced memory
    memory_result = await test_enhanced_memory()
    logger.info(f"Enhanced memory test: {'Success' if memory_result else 'Failed'}")

    # Summary
    logger.info("\n--- Test Summary ---")
    logger.info(f"Sentiment analyzer: {'✅ OK' if sentiment_result else '❌ Failed'}")
    logger.info(f"Payment system: {'✅ OK' if payment_result else '❌ Failed'}")
    logger.info(f"Enhanced memory: {'✅ OK' if memory_result else '❌ Failed'}")

    if sentiment_result and payment_result and memory_result:
        logger.info("✅ All tests passed")
        return 0
    else:
        logger.error("❌ Some tests failed. See above for details.")
        return 1

if __name__ == "__main__":
    asyncio.run(main())
