"""
Database module for VoicePal.

This module handles all database operations including:
- User management
- Credit tracking
- Conversation history
- Transaction records

The database uses SQLite for simplicity and ease of deployment.
"""

import sqlite3
from datetime import datetime, timedelta
import logging
import os
from typing import Dict, List, Optional, Any

from bot.database.schema import initialize_database, SCHEMA_DEFINITIONS
from bot.core.exceptions import (
    DatabaseError, DatabaseConnectionError, DatabaseQueryError,
    DatabaseIntegrityError, InsufficientCreditsError
)

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class Database:
    """Database handler for VoicePal."""

    def __init__(self, db_file: str = "voicepal.db"):
        """
        Initialize the database connection.

        Args:
            db_file: Path to the SQLite database file
        """
        try:
            # Create database directory if it doesn't exist
            db_dir = os.path.dirname(db_file)
            if db_dir and not os.path.exists(db_dir):
                os.makedirs(db_dir)

            self.db_file = db_file

            # Initialize database with schema
            self.conn = initialize_database(db_file)
            self.cursor = self.conn.cursor()

            logger.info(f"Database initialized: {db_file}")
        except Exception as e:
            logger.error(f"Error initializing database: {e}")
            raise DatabaseConnectionError(f"Error connecting to database: {e}") from e

    def add_user(self, user_id: int, username: Optional[str] = None,
                first_name: Optional[str] = None, last_name: Optional[str] = None,
                ip_address: Optional[str] = None, device_id: Optional[str] = None) -> bool:
        """
        Add a new user.

        Args:
            user_id: Telegram user ID
            username: Telegram username
            first_name: User's first name
            last_name: User's last name
            ip_address: User's IP address
            device_id: User's device ID

        Returns:
            bool: True if a new user was added, False if user already exists

        Raises:
            DatabaseError: If there's a database error
        """
        try:
            # Check if user exists
            self.cursor.execute("SELECT user_id FROM users WHERE user_id = ?", (user_id,))
            existing_user = self.cursor.fetchone()

            if existing_user:
                # User already exists
                logger.info("User already exists: %s", user_id)
                return False

            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            # Insert new user
            self.cursor.execute(
                """INSERT INTO users
                   (user_id, username, first_name, last_name, last_active, registration_ip, device_id, free_credits_received)
                   VALUES (?, ?, ?, ?, ?, ?, ?, 0)""",
                (user_id, username, first_name, last_name, current_time, ip_address, device_id)
            )
            self.conn.commit()
            logger.info("Added new user: %s", user_id)
            return True
        except sqlite3.IntegrityError as e:
            logger.error("Database integrity error adding user %s: %s", user_id, e)
            self.conn.rollback()
            raise DatabaseIntegrityError(f"Database integrity error: {e}") from e
        except sqlite3.OperationalError as e:
            logger.error("Database operational error adding user %s: %s", user_id, e)
            self.conn.rollback()
            raise DatabaseQueryError(f"Database query error: {e}") from e
        except Exception as e:
            logger.error("Unexpected error adding user %s: %s", user_id, e)
            self.conn.rollback()
            raise DatabaseError(f"Unexpected database error: {e}") from e

    def add_or_update_user(self, user_id: int, username: Optional[str] = None,
                          first_name: Optional[str] = None, last_name: Optional[str] = None,
                          ip_address: Optional[str] = None, device_id: Optional[str] = None) -> bool:
        """
        Add a new user or update an existing user.

        Args:
            user_id: Telegram user ID
            username: Telegram username
            first_name: User's first name
            last_name: User's last name
            ip_address: User's IP address
            device_id: User's device ID

        Returns:
            bool: True if a new user was added, False if an existing user was updated

        Raises:
            DatabaseError: If there's a database error
        """
        try:
            # Check if user exists
            self.cursor.execute("SELECT user_id FROM users WHERE user_id = ?", (user_id,))
            existing_user = self.cursor.fetchone()

            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            if existing_user:
                # Update existing user
                self.cursor.execute(
                    """UPDATE users SET
                       username = COALESCE(?, username),
                       first_name = COALESCE(?, first_name),
                       last_name = COALESCE(?, last_name),
                       last_active = ?,
                       registration_ip = COALESCE(?, registration_ip),
                       device_id = COALESCE(?, device_id)
                       WHERE user_id = ?""",
                    (username, first_name, last_name, current_time, ip_address, device_id, user_id)
                )
                self.conn.commit()
                logger.info("Updated user: %s", user_id)
                return False
            else:
                # Insert new user
                self.cursor.execute(
                    """INSERT INTO users
                       (user_id, username, first_name, last_name, last_active, registration_ip, device_id, free_credits_received)
                       VALUES (?, ?, ?, ?, ?, ?, ?, 0)""",
                    (user_id, username, first_name, last_name, current_time, ip_address, device_id)
                )
                self.conn.commit()
                logger.info("Added new user: %s", user_id)
                return True
        except sqlite3.IntegrityError as e:
            logger.error("Database integrity error adding/updating user %s: %s", user_id, e)
            self.conn.rollback()
            raise DatabaseIntegrityError(f"Database integrity error: {e}") from e
        except sqlite3.OperationalError as e:
            logger.error("Database operational error adding/updating user %s: %s", user_id, e)
            self.conn.rollback()
            raise DatabaseQueryError(f"Database query error: {e}") from e
        except Exception as e:
            logger.error("Unexpected error adding/updating user %s: %s", user_id, e)
            self.conn.rollback()
            raise DatabaseError(f"Unexpected database error: {e}") from e

    def get_user(self, user_id: int) -> Optional[Dict[str, Any]]:
        """
        Get user information.

        Args:
            user_id: Telegram user ID

        Returns:
            Dict containing user information or None if user doesn't exist

        Raises:
            DatabaseError: If there's a database error
        """
        try:
            # Always query without login_count to avoid errors
            self.cursor.execute(
                """SELECT user_id, username, first_name, last_name,
                   credits, personality, created_at, last_active,
                   device_id, registration_ip, last_ip
                   FROM users WHERE user_id = ?""",
                (user_id,)
            )
            user = self.cursor.fetchone()

            if user:
                return dict(user)
            return None
        except sqlite3.OperationalError as e:
            logger.error("Database operational error getting user %s: %s", user_id, e)
            raise DatabaseQueryError(f"Database query error: {e}") from e
        except Exception as e:
            logger.error("Unexpected error getting user %s: %s", user_id, e)
            raise DatabaseError(f"Unexpected database error: {e}") from e

    def update_user_activity(self, user_id: int, ip_info: Optional[Dict[str, Any]] = None) -> None:
        """
        Update user's last active time and IP information.

        Args:
            user_id: Telegram user ID
            ip_info: IP information dictionary

        Raises:
            DatabaseError: If there's a database error
        """
        try:
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            # Extract IP address from ip_info if provided
            ip_address = None
            if ip_info and 'ip' in ip_info:
                ip_address = ip_info['ip']

            # Always update without login_count to avoid errors
            if ip_address:
                self.cursor.execute(
                    """UPDATE users SET
                       last_active = ?,
                       last_ip = ?
                       WHERE user_id = ?""",
                    (current_time, ip_address, user_id)
                )
            else:
                self.cursor.execute(
                    """UPDATE users SET
                       last_active = ?
                       WHERE user_id = ?""",
                    (current_time, user_id)
                )

            self.conn.commit()
        except sqlite3.IntegrityError as e:
            logger.error("Database integrity error updating activity for user %s: %s", user_id, e)
            self.conn.rollback()
            raise DatabaseIntegrityError(f"Database integrity error: {e}") from e
        except sqlite3.OperationalError as e:
            logger.error("Database operational error updating activity for user %s: %s", user_id, e)
            self.conn.rollback()
            raise DatabaseQueryError(f"Database query error: {e}") from e
        except Exception as e:
            logger.error("Unexpected error updating activity for user %s: %s", user_id, e)
            self.conn.rollback()
            raise DatabaseError(f"Unexpected database error: {e}") from e

    def set_user_personality(self, user_id: int, personality: str) -> bool:
        """
        Set user's preferred AI personality.

        Args:
            user_id: Telegram user ID
            personality: Personality name

        Returns:
            bool: True if successful

        Raises:
            DatabaseError: If there's a database error
        """
        try:
            self.cursor.execute(
                "UPDATE users SET personality = ? WHERE user_id = ?",
                (personality, user_id)
            )
            self.conn.commit()
            logger.info("Updated personality for user %s to %s", user_id, personality)
            return True
        except sqlite3.IntegrityError as e:
            logger.error("Database integrity error setting personality for user %s: %s", user_id, e)
            self.conn.rollback()
            raise DatabaseIntegrityError(f"Database integrity error: {e}") from e
        except sqlite3.OperationalError as e:
            logger.error("Database operational error setting personality for user %s: %s", user_id, e)
            self.conn.rollback()
            raise DatabaseQueryError(f"Database query error: {e}") from e
        except Exception as e:
            logger.error("Unexpected error setting personality for user %s: %s", user_id, e)
            self.conn.rollback()
            raise DatabaseError(f"Unexpected database error: {e}") from e

    def get_user_personality(self, user_id: int) -> str:
        """
        Get user's preferred AI personality.

        Args:
            user_id: Telegram user ID

        Returns:
            str: Personality name, defaults to 'friendly' if not set
        """
        try:
            self.cursor.execute(
                "SELECT personality FROM users WHERE user_id = ?",
                (user_id,)
            )
            result = self.cursor.fetchone()
            return result['personality'] if result else 'friendly'
        except sqlite3.OperationalError as e:
            logger.error("Database operational error getting personality for user %s: %s", user_id, e)
            # Return default value in case of error
            return 'friendly'
        except Exception as e:
            logger.error("Unexpected error getting personality for user %s: %s", user_id, e)
            # Return default value in case of error
            return 'friendly'

    def check_for_multiple_accounts(self, device_id: str, ip_address: str = None) -> bool:
        """
        Check if there are multiple accounts with the same device ID or IP address.

        Args:
            device_id: Device identifier
            ip_address: IP address (optional)

        Returns:
            bool: True if multiple accounts are found, False otherwise

        Raises:
            DatabaseError: If there's a database error
        """
        try:
            # Check for multiple accounts with the same device ID
            if device_id:
                self.cursor.execute(
                    """SELECT COUNT(DISTINCT user_id) as account_count
                       FROM users
                       WHERE device_id = ? AND free_credits_received = 1""",
                    (device_id,)
                )
                result = self.cursor.fetchone()
                if result and result['account_count'] > 0:
                    logger.warning("Found %s accounts with device ID %s",
                                  result['account_count'], device_id)
                    return True

            # Check for multiple accounts with the same IP address
            if ip_address:
                self.cursor.execute(
                    """SELECT COUNT(DISTINCT user_id) as account_count
                       FROM users
                       WHERE registration_ip = ? AND free_credits_received = 1""",
                    (ip_address,)
                )
                result = self.cursor.fetchone()
                if result and result['account_count'] > 0:
                    logger.warning("Found %s accounts with IP address %s",
                                  result['account_count'], ip_address)
                    return True

            return False
        except sqlite3.OperationalError as e:
            logger.error("Database operational error checking for multiple accounts: %s", e)
            return False
        except Exception as e:
            logger.error("Unexpected error checking for multiple accounts: %s", e)
            return False

    def get_user_credits(self, user_id: int) -> int:
        """
        Get user's current credit balance.

        Args:
            user_id: Telegram user ID

        Returns:
            int: Current credit balance, 0 if user doesn't exist
        """
        try:
            self.cursor.execute(
                "SELECT credits FROM users WHERE user_id = ?",
                (user_id,)
            )
            result = self.cursor.fetchone()
            return result['credits'] if result else 0
        except sqlite3.OperationalError as e:
            logger.error("Database operational error getting credits for user %s: %s", user_id, e)
            # Return default value in case of error
            return 0
        except Exception as e:
            logger.error("Unexpected error getting credits for user %s: %s", user_id, e)
            # Return default value in case of error
            return 0

    def add_credits(self, user_id: int, amount: int, source: str = "purchase") -> int:
        """
        Add credits to a user's account.

        Args:
            user_id: Telegram user ID
            amount: Amount of credits to add
            source: Source of the credits (purchase, gift, etc.)

        Returns:
            int: New credit balance

        Raises:
            DatabaseError: If there's a database error
        """
        try:
            # Start a transaction
            self.conn.execute("BEGIN TRANSACTION")

            # Get current credits
            self.cursor.execute("SELECT credits FROM users WHERE user_id = ?", (user_id,))
            result = self.cursor.fetchone()

            if not result:
                # User doesn't exist, create them
                self.cursor.execute(
                    "INSERT INTO users (user_id, credits) VALUES (?, ?)",
                    (user_id, amount)
                )
                new_balance = amount
            else:
                # Update existing user
                current_credits = result['credits']
                new_balance = current_credits + amount

                self.cursor.execute(
                    "UPDATE users SET credits = ? WHERE user_id = ?",
                    (new_balance, user_id)
                )

            # Record the transaction
            self.cursor.execute(
                """INSERT INTO transactions
                   (user_id, amount, credits, status, source)
                   VALUES (?, ?, ?, ?, ?)""",
                (user_id, amount, amount, "completed", source)
            )

            # Commit the transaction
            self.conn.commit()

            logger.info("Added %s credits to user %s (source: %s)", amount, user_id, source)
            return new_balance
        except sqlite3.IntegrityError as e:
            logger.error("Database integrity error adding credits for user %s: %s", user_id, e)
            self.conn.rollback()
            raise DatabaseIntegrityError(f"Database integrity error: {e}") from e
        except sqlite3.OperationalError as e:
            logger.error("Database operational error adding credits for user %s: %s", user_id, e)
            self.conn.rollback()
            raise DatabaseQueryError(f"Database query error: {e}") from e
        except Exception as e:
            logger.error("Unexpected error adding credits for user %s: %s", user_id, e)
            self.conn.rollback()
            raise DatabaseError(f"Unexpected database error: {e}") from e

    def use_credits(self, user_id: int, credit_amount: int) -> bool:
        """
        Use credits from user's balance.

        Args:
            user_id: Telegram user ID
            credit_amount: Number of credits to use

        Returns:
            bool: True if successful

        Raises:
            InsufficientCreditsError: If user has insufficient credits
            DatabaseError: If there's a database error
        """
        try:
            current_credits = self.get_user_credits(user_id)

            if current_credits < credit_amount:
                logger.warning(
                    "Insufficient credits for user %s. Has: %s, Needs: %s",
                    user_id, current_credits, credit_amount
                )
                error_msg = (
                    f"User {user_id} has insufficient credits. "
                    f"Has: {current_credits}, Needs: {credit_amount}"
                )
                raise InsufficientCreditsError(error_msg)

            new_credits = current_credits - credit_amount
            self.cursor.execute(
                "UPDATE users SET credits = ? WHERE user_id = ?",
                (new_credits, user_id)
            )
            self.conn.commit()
            logger.info("Used %s credits from user %s. New balance: %s",
                       credit_amount, user_id, new_credits)
            return True
        except sqlite3.IntegrityError as e:
            logger.error("Database integrity error using credits for user %s: %s", user_id, e)
            self.conn.rollback()
            raise DatabaseIntegrityError(f"Database integrity error: {e}") from e
        except sqlite3.OperationalError as e:
            logger.error("Database operational error using credits for user %s: %s", user_id, e)
            self.conn.rollback()
            raise DatabaseQueryError(f"Database query error: {e}") from e
        except InsufficientCreditsError:
            # Re-raise the InsufficientCreditsError
            raise
        except Exception as e:
            logger.error("Unexpected error using credits for user %s: %s", user_id, e)
            self.conn.rollback()
            raise DatabaseError(f"Unexpected database error: {e}") from e

    def add_transaction(self, user_id: int, amount: float, credit_amount: int,
                       transaction_id: str, status: str) -> int:
        """
        Record a credit transaction.

        Args:
            user_id: Telegram user ID
            amount: Money amount (in currency)
            credit_amount: Number of credits
            transaction_id: External transaction ID
            status: Transaction status

        Returns:
            int: Transaction ID

        Raises:
            DatabaseError: If there's a database error
        """
        try:
            self.cursor.execute(
                """INSERT INTO transactions
                   (user_id, amount, credits, transaction_id, status)
                   VALUES (?, ?, ?, ?, ?)""",
                (user_id, amount, credit_amount, transaction_id, status)
            )
            self.conn.commit()
            transaction_id = self.cursor.lastrowid
            logger.info("Added transaction %s for user %s: %s credits",
                       transaction_id, user_id, credit_amount)
            return transaction_id
        except sqlite3.IntegrityError as e:
            logger.error("Database integrity error adding transaction for user %s: %s", user_id, e)
            self.conn.rollback()
            raise DatabaseIntegrityError(f"Database integrity error: {e}") from e
        except sqlite3.OperationalError as e:
            logger.error("Database operational error adding transaction for user %s: %s", user_id, e)
            self.conn.rollback()
            raise DatabaseQueryError(f"Database query error: {e}") from e
        except Exception as e:
            logger.error("Unexpected error adding transaction for user %s: %s", user_id, e)
            self.conn.rollback()
            raise DatabaseError(f"Unexpected database error: {e}") from e

    def get_user_transactions(self, user_id: int, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Get user's transaction history.

        Args:
            user_id: Telegram user ID
            limit: Maximum number of transactions to return

        Returns:
            List of transaction dictionaries
        """
        try:
            self.cursor.execute(
                """SELECT id, user_id, amount, credits, transaction_id, status, created_at
                   FROM transactions
                   WHERE user_id = ?
                   ORDER BY created_at DESC
                   LIMIT ?""",
                (user_id, limit)
            )
            transactions = self.cursor.fetchall()
            return [dict(t) for t in transactions]
        except sqlite3.OperationalError as e:
            logger.error("Database operational error getting transactions for user %s: %s", user_id, e)
            return []
        except Exception as e:
            logger.error("Unexpected error getting transactions for user %s: %s", user_id, e)
            return []

    def add_conversation(self, user_id: int, message: str, response: str,
                        is_voice: bool, credits_used: int) -> int:
        """
        Record a conversation exchange.

        Args:
            user_id: Telegram user ID
            message: User's message
            response: Bot's response
            is_voice: Whether the conversation was voice-based
            credits_used: Number of credits used

        Returns:
            int: Conversation ID

        Raises:
            DatabaseError: If there's a database error
        """
        try:
            self.cursor.execute(
                """INSERT INTO conversations
                   (user_id, message, response, is_voice, credits_used)
                   VALUES (?, ?, ?, ?, ?)""",
                (user_id, message, response, is_voice, credits_used)
            )
            self.conn.commit()
            conversation_id = self.cursor.lastrowid
            logger.info("Added conversation %s for user %s", conversation_id, user_id)
            return conversation_id
        except sqlite3.IntegrityError as e:
            logger.error("Database integrity error adding conversation for user %s: %s", user_id, e)
            self.conn.rollback()
            raise DatabaseIntegrityError(f"Database integrity error: {e}") from e
        except sqlite3.OperationalError as e:
            logger.error("Database operational error adding conversation for user %s: %s", user_id, e)
            self.conn.rollback()
            raise DatabaseQueryError(f"Database query error: {e}") from e
        except Exception as e:
            logger.error("Unexpected error adding conversation for user %s: %s", user_id, e)
            self.conn.rollback()
            raise DatabaseError(f"Unexpected database error: {e}") from e

    def get_conversations(self, user_id: int, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Get user's conversation history.

        Args:
            user_id: Telegram user ID
            limit: Maximum number of conversations to return

        Returns:
            List of conversation dictionaries
        """
        try:
            self.cursor.execute(
                """SELECT id, user_id, message, response, is_voice, credits_used, created_at
                   FROM conversations
                   WHERE user_id = ?
                   ORDER BY created_at DESC
                   LIMIT ?""",
                (user_id, limit)
            )
            conversations = self.cursor.fetchall()
            return [dict(c) for c in conversations]
        except sqlite3.OperationalError as e:
            logger.error("Database operational error getting conversations for user %s: %s", user_id, e)
            return []
        except Exception as e:
            logger.error("Unexpected error getting conversations for user %s: %s", user_id, e)
            return []

    def get_active_users(self, days: int = 7) -> List[Dict[str, Any]]:
        """
        Get users who have been active in the last X days.

        Args:
            days: Number of days to look back

        Returns:
            List of user dictionaries
        """
        try:
            # Calculate the date X days ago
            cutoff_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d %H:%M:%S')

            self.cursor.execute(
                """SELECT user_id, username, first_name, last_name,
                          credits, personality, created_at, last_active
                   FROM users
                   WHERE last_active > ?
                   ORDER BY last_active DESC""",
                (cutoff_date,)
            )
            users = self.cursor.fetchall()

            return [dict(user) for user in users]
        except sqlite3.OperationalError as e:
            logger.error("Database operational error getting active users: %s", e)
            return []
        except Exception as e:
            logger.error("Unexpected error getting active users: %s", e)
            return []

    def get_stats(self) -> Dict[str, Any]:
        """
        Get system statistics.

        Returns:
            Dict containing system statistics
        """
        try:
            stats = {}

            # Get total users
            self.cursor.execute("SELECT COUNT(*) as count FROM users")
            result = self.cursor.fetchone()
            stats["total_users"] = result["count"] if result else 0

            # Get active users (last 7 days)
            cutoff_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d %H:%M:%S')
            self.cursor.execute(
                "SELECT COUNT(*) as count FROM users WHERE last_active > ?",
                (cutoff_date,)
            )
            result = self.cursor.fetchone()
            stats["active_users"] = result["count"] if result else 0

            # Get total conversations
            self.cursor.execute("SELECT COUNT(*) as count FROM conversations")
            result = self.cursor.fetchone()
            stats["total_conversations"] = result["count"] if result else 0

            # Get voice conversations
            self.cursor.execute("SELECT COUNT(*) as count FROM conversations WHERE is_voice = 1")
            result = self.cursor.fetchone()
            stats["voice_conversations"] = result["count"] if result else 0

            # Get total credits used
            self.cursor.execute("SELECT SUM(credits_used) as sum FROM conversations")
            result = self.cursor.fetchone()
            stats["total_credits_used"] = result["sum"] if result and result["sum"] else 0

            # Get total revenue
            self.cursor.execute(
                "SELECT SUM(amount) as sum FROM transactions WHERE status = 'completed'"
            )
            result = self.cursor.fetchone()
            stats["total_revenue"] = result["sum"] if result and result["sum"] else 0

            return stats
        except sqlite3.OperationalError as e:
            logger.error("Database operational error getting system statistics: %s", e)
            return {
                "total_users": 0,
                "active_users": 0,
                "total_conversations": 0,
                "voice_conversations": 0,
                "total_credits_used": 0,
                "total_revenue": 0
            }
        except Exception as e:
            logger.error("Unexpected error getting system statistics: %s", e)
            return {
                "total_users": 0,
                "active_users": 0,
                "total_conversations": 0,
                "voice_conversations": 0,
                "total_credits_used": 0,
                "total_revenue": 0
            }

    def add_user_credits(self, user_id: int, amount: int) -> bool:
        """
        Add credits to user account (alias for add_credits).

        Args:
            user_id: Telegram user ID
            amount: Amount of credits to add

        Returns:
            bool: True if successful
        """
        try:
            new_balance = self.add_credits(user_id, amount, source="system")
            return new_balance > 0
        except Exception as e:
            logger.error(f"Error adding user credits: {e}")
            return False

    def create_or_update_user(self, user_id: int, username: str, first_name: str) -> bool:
        """
        Create or update user (alias for add_or_update_user).

        Args:
            user_id: Telegram user ID
            username: Telegram username
            first_name: User's first name

        Returns:
            bool: True if successful
        """
        try:
            self.add_or_update_user(user_id, username, first_name)
            return True
        except Exception as e:
            logger.error(f"Error creating/updating user: {e}")
            return False

    def store_conversation(self, user_id: int, message: str, response: str) -> bool:
        """
        Store conversation entry (alias for add_conversation).

        Args:
            user_id: Telegram user ID
            message: User's message
            response: Bot's response

        Returns:
            bool: True if successful
        """
        try:
            conversation_id = self.add_conversation(
                user_id=user_id,
                message=message,
                response=response,
                is_voice=False,
                credits_used=0
            )
            return conversation_id > 0
        except Exception as e:
            logger.error(f"Error storing conversation: {e}")
            return False

    def close(self) -> None:
        """Close the database connection."""
        if self.conn:
            self.conn.close()
            logger.info("Database connection closed")

    def __del__(self) -> None:
        """Destructor to ensure database connection is closed."""
        self.close()
