"""
User verification utilities for VoicePal.

This module provides verification functionality to ensure users can only
receive free trial credits once, with proper tracking and validation.
"""

import logging
import time
import random
import string
import hashlib
from typing import Dict, Any, Optional, Tuple, List
from datetime import datetime, timedelta

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class VerificationManager:
    """
    Verification manager for VoicePal.
    
    This class provides verification functionality to ensure users can only
    receive free trial credits once, with proper tracking and validation.
    """
    
    def __init__(self, db_connection):
        """
        Initialize the verification manager.
        
        Args:
            db_connection: SQLite database connection
        """
        self.db = db_connection
        
        # Set up database tables
        self._setup_database()
        
    def _setup_database(self) -> None:
        """Create verification tables if they don't exist."""
        try:
            cursor = self.db.cursor()
            
            # Create verification_codes table
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS verification_codes (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                code TEXT,
                verification_type TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                expires_at TIMESTAMP,
                used BOOLEAN DEFAULT 0,
                UNIQUE(user_id, verification_type)
            )
            ''')
            
            # Create verification_attempts table
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS verification_attempts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                verification_type TEXT,
                attempt_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                success BOOLEAN,
                ip_address TEXT,
                device_id TEXT
            )
            ''')
            
            # Create free_credits_tracking table
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS free_credits_tracking (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER UNIQUE,
                credits_received INTEGER DEFAULT 0,
                first_received_at TIMESTAMP,
                last_received_at TIMESTAMP,
                total_received INTEGER DEFAULT 0,
                ip_address TEXT,
                device_id TEXT
            )
            ''')
            
            # Create device_blacklist table
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS device_blacklist (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                device_id TEXT UNIQUE,
                ip_address TEXT,
                reason TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''')
            
            self.db.commit()
            logger.info("Verification tables created")
        except Exception as e:
            logger.error(f"Error setting up verification tables: {e}")
            raise
            
    def generate_verification_code(self, user_id: int, verification_type: str = "free_credits",
                                 expires_in_minutes: int = 30) -> str:
        """
        Generate a verification code for a user.
        
        Args:
            user_id: User ID
            verification_type: Type of verification
            expires_in_minutes: Minutes until code expires
            
        Returns:
            str: Generated verification code
        """
        try:
            # Generate a random code
            code_length = 6
            code = ''.join(random.choices(string.digits, k=code_length))
            
            # Calculate expiration time
            expires_at = datetime.now() + timedelta(minutes=expires_in_minutes)
            
            # Store in database
            cursor = self.db.cursor()
            
            # Check if user already has a code of this type
            cursor.execute(
                """SELECT id FROM verification_codes 
                   WHERE user_id = ? AND verification_type = ?""",
                (user_id, verification_type)
            )
            existing = cursor.fetchone()
            
            if existing:
                # Update existing code
                cursor.execute(
                    """UPDATE verification_codes SET 
                       code = ?, 
                       created_at = CURRENT_TIMESTAMP, 
                       expires_at = ?,
                       used = 0
                       WHERE user_id = ? AND verification_type = ?""",
                    (code, expires_at.isoformat(), user_id, verification_type)
                )
            else:
                # Insert new code
                cursor.execute(
                    """INSERT INTO verification_codes 
                       (user_id, code, verification_type, expires_at) 
                       VALUES (?, ?, ?, ?)""",
                    (user_id, code, verification_type, expires_at.isoformat())
                )
                
            self.db.commit()
            logger.info(f"Generated verification code for user {user_id}: {verification_type}")
            
            return code
        except Exception as e:
            logger.error(f"Error generating verification code: {e}")
            # Return a fallback code in case of error
            return ''.join(random.choices(string.digits, k=6))
            
    def verify_code(self, user_id: int, code: str, verification_type: str = "free_credits",
                   ip_address: Optional[str] = None, device_id: Optional[str] = None) -> bool:
        """
        Verify a code for a user.
        
        Args:
            user_id: User ID
            code: Verification code
            verification_type: Type of verification
            ip_address: User's IP address
            device_id: User's device ID
            
        Returns:
            bool: True if verification successful, False otherwise
        """
        try:
            # Check if device is blacklisted
            if device_id and self.is_device_blacklisted(device_id):
                logger.warning(f"Verification attempt from blacklisted device: {device_id}")
                self._log_verification_attempt(user_id, verification_type, False, ip_address, device_id)
                return False
            
            # Get code from database
            cursor = self.db.cursor()
            cursor.execute(
                """SELECT code, expires_at, used 
                   FROM verification_codes 
                   WHERE user_id = ? AND verification_type = ?""",
                (user_id, verification_type)
            )
            result = cursor.fetchone()
            
            success = False
            
            if result:
                db_code, expires_at, used = result
                
                # Check if code is already used
                if used:
                    logger.warning(f"Verification code already used: {user_id}, {verification_type}")
                else:
                    # Check if code is expired
                    if datetime.fromisoformat(expires_at) < datetime.now():
                        logger.warning(f"Verification code expired: {user_id}, {verification_type}")
                    else:
                        # Check if code matches
                        if code == db_code:
                            # Mark code as used
                            cursor.execute(
                                """UPDATE verification_codes SET 
                                   used = 1 
                                   WHERE user_id = ? AND verification_type = ?""",
                                (user_id, verification_type)
                            )
                            self.db.commit()
                            
                            success = True
                            logger.info(f"Verification successful: {user_id}, {verification_type}")
                        else:
                            logger.warning(f"Verification code mismatch: {user_id}, {verification_type}")
            else:
                logger.warning(f"No verification code found: {user_id}, {verification_type}")
                
            # Log verification attempt
            self._log_verification_attempt(user_id, verification_type, success, ip_address, device_id)
            
            return success
        except Exception as e:
            logger.error(f"Error verifying code: {e}")
            # Log verification attempt failure
            self._log_verification_attempt(user_id, verification_type, False, ip_address, device_id)
            return False
            
    def _log_verification_attempt(self, user_id: int, verification_type: str, success: bool,
                                 ip_address: Optional[str] = None, device_id: Optional[str] = None) -> None:
        """
        Log a verification attempt.
        
        Args:
            user_id: User ID
            verification_type: Type of verification
            success: Whether verification was successful
            ip_address: User's IP address
            device_id: User's device ID
        """
        try:
            cursor = self.db.cursor()
            cursor.execute(
                """INSERT INTO verification_attempts 
                   (user_id, verification_type, success, ip_address, device_id) 
                   VALUES (?, ?, ?, ?, ?)""",
                (user_id, verification_type, success, ip_address, device_id)
            )
            self.db.commit()
        except Exception as e:
            logger.error(f"Error logging verification attempt: {e}")
            
    def track_free_credits(self, user_id: int, credits: int, 
                          ip_address: Optional[str] = None, device_id: Optional[str] = None) -> bool:
        """
        Track free credits given to a user.
        
        Args:
            user_id: User ID
            credits: Number of credits given
            ip_address: User's IP address
            device_id: User's device ID
            
        Returns:
            bool: True if tracking successful, False otherwise
        """
        try:
            cursor = self.db.cursor()
            
            # Check if user already received free credits
            cursor.execute(
                "SELECT id, credits_received, total_received FROM free_credits_tracking WHERE user_id = ?",
                (user_id,)
            )
            result = cursor.fetchone()
            
            current_time = datetime.now().isoformat()
            
            if result:
                # Update existing record
                _, credits_received, total_received = result
                new_total = total_received + credits
                
                cursor.execute(
                    """UPDATE free_credits_tracking SET 
                       credits_received = credits_received + ?,
                       last_received_at = ?,
                       total_received = ?,
                       ip_address = COALESCE(?, ip_address),
                       device_id = COALESCE(?, device_id)
                       WHERE user_id = ?""",
                    (credits, current_time, new_total, ip_address, device_id, user_id)
                )
            else:
                # Insert new record
                cursor.execute(
                    """INSERT INTO free_credits_tracking 
                       (user_id, credits_received, first_received_at, last_received_at, 
                        total_received, ip_address, device_id) 
                       VALUES (?, ?, ?, ?, ?, ?, ?)""",
                    (user_id, credits, current_time, current_time, credits, ip_address, device_id)
                )
                
            self.db.commit()
            logger.info(f"Tracked {credits} free credits for user {user_id}")
            
            return True
        except Exception as e:
            logger.error(f"Error tracking free credits: {e}")
            return False
            
    def has_received_free_credits(self, user_id: int) -> bool:
        """
        Check if a user has already received free credits.
        
        Args:
            user_id: User ID
            
        Returns:
            bool: True if user has received free credits, False otherwise
        """
        try:
            cursor = self.db.cursor()
            cursor.execute(
                "SELECT credits_received FROM free_credits_tracking WHERE user_id = ?",
                (user_id,)
            )
            result = cursor.fetchone()
            
            if result and result[0] > 0:
                return True
            return False
        except Exception as e:
            logger.error(f"Error checking if user received free credits: {e}")
            # In case of error, assume user has received credits to be safe
            return True
            
    def check_device_for_free_credits(self, device_id: str) -> bool:
        """
        Check if a device has already been used to receive free credits.
        
        Args:
            device_id: Device ID
            
        Returns:
            bool: True if device has been used for free credits, False otherwise
        """
        try:
            cursor = self.db.cursor()
            cursor.execute(
                "SELECT COUNT(*) FROM free_credits_tracking WHERE device_id = ?",
                (device_id,)
            )
            result = cursor.fetchone()
            
            if result and result[0] > 0:
                return True
            return False
        except Exception as e:
            logger.error(f"Error checking device for free credits: {e}")
            # In case of error, assume device has been used to be safe
            return True
            
    def blacklist_device(self, device_id: str, ip_address: Optional[str] = None, 
                        reason: str = "multiple_accounts") -> bool:
        """
        Blacklist a device.
        
        Args:
            device_id: Device ID
            ip_address: IP address
            reason: Reason for blacklisting
            
        Returns:
            bool: True if blacklisting successful, False otherwise
        """
        try:
            cursor = self.db.cursor()
            
            # Check if device is already blacklisted
            cursor.execute(
                "SELECT id FROM device_blacklist WHERE device_id = ?",
                (device_id,)
            )
            existing = cursor.fetchone()
            
            if not existing:
                # Add to blacklist
                cursor.execute(
                    "INSERT INTO device_blacklist (device_id, ip_address, reason) VALUES (?, ?, ?)",
                    (device_id, ip_address, reason)
                )
                self.db.commit()
                logger.info(f"Blacklisted device {device_id}: {reason}")
                
            return True
        except Exception as e:
            logger.error(f"Error blacklisting device: {e}")
            return False
            
    def is_device_blacklisted(self, device_id: str) -> bool:
        """
        Check if a device is blacklisted.
        
        Args:
            device_id: Device ID
            
        Returns:
            bool: True if device is blacklisted, False otherwise
        """
        try:
            cursor = self.db.cursor()
            cursor.execute(
                "SELECT id FROM device_blacklist WHERE device_id = ?",
                (device_id,)
            )
            result = cursor.fetchone()
            
            return bool(result)
        except Exception as e:
            logger.error(f"Error checking if device is blacklisted: {e}")
            # In case of error, assume device is not blacklisted
            return False
