"""
Test script for the VoicePal bot.

This script tests the basic functionality of the bot by running it in polling mode.
"""

import asyncio
import logging
import os
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

async def main():
    """Run the bot in test mode."""
    # Load environment variables
    load_dotenv()
    
    # Check if required environment variables are set
    required_vars = ["TELEGRAM_TOKEN", "DEEPGRAM_API_KEY"]
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        logger.error(f"Missing required environment variables: {', '.join(missing_vars)}")
        logger.error("Please set these variables in the .env file")
        return
    
    logger.info("Starting VoicePal bot in test mode")
    
    # Import the bot module
    from bot.main import main as bot_main
    
    # Run the bot
    try:
        await bot_main()
    except KeyboardInterrupt:
        logger.info("Bo<PERSON> stopped by user")
    except Exception as e:
        logger.error(f"Error running bot: {e}")

if __name__ == "__main__":
    asyncio.run(main())
