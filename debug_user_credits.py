#!/usr/bin/env python3
"""
Debug script to check user credits and database state.
"""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath('.'))

from bot.database import Database
from bot.config_manager import ConfigManager

def main():
    """Check user credits and database state."""
    print("🔍 VoicePal User Credits Debug")
    print("=" * 40)
    
    try:
        # Initialize database
        config_manager = ConfigManager()
        db_config = config_manager.get_database_config()
        db_file = db_config.get("file", "voicepal.db")
        
        print(f"📁 Database file: {db_file}")
        
        if not os.path.exists(db_file):
            print("❌ Database file does not exist!")
            return
        
        # Connect to database
        db = Database(db_file)
        print("✅ Connected to database")
        
        # Your user ID (from memories)
        user_id = 616696346
        print(f"👤 Checking user ID: {user_id}")
        
        # Check if user exists
        user = db.get_user(user_id)
        if user:
            print("✅ User exists in database")
            print(f"   Username: {user.get('username', 'N/A')}")
            print(f"   First name: {user.get('first_name', 'N/A')}")
            print(f"   Last name: {user.get('last_name', 'N/A')}")
            print(f"   Credits: {user.get('credits', 0)}")
            print(f"   Free credits received: {user.get('free_credits_received', 0)}")
            print(f"   Registration date: {user.get('registration_date', 'N/A')}")
            print(f"   Last active: {user.get('last_active', 'N/A')}")
        else:
            print("❌ User does not exist in database")
            
        # Check current credits
        credits = db.get_user_credits(user_id)
        print(f"💰 Current credits: {credits}")
        
        # Check transactions
        try:
            transactions = db.get_user_transactions(user_id)
            print(f"📊 Total transactions: {len(transactions)}")
            
            if transactions:
                print("\n📋 Recent transactions:")
                for i, tx in enumerate(transactions[-5:]):  # Show last 5
                    print(f"   {i+1}. {tx.get('source', 'unknown')} - {tx.get('credits', 0)} credits - {tx.get('timestamp', 'N/A')}")
        except Exception as e:
            print(f"⚠️ Could not get transactions: {e}")
        
        # Check credit system configuration
        credit_config = config_manager.get_credit_system_config()
        print(f"\n⚙️ Credit system configuration:")
        print(f"   Enabled: {credit_config.get('enabled', True)}")
        print(f"   Free trial credits: {credit_config.get('free_trial_credits', 100)}")
        print(f"   Text cost: {credit_config.get('text_credit_cost', 1)}")
        print(f"   Voice cost: {credit_config.get('voice_credit_cost', 3)}")
        
        # Test adding credits manually (for debugging)
        print(f"\n🧪 Testing credit addition...")
        try:
            # Try to add 1 test credit
            new_balance = db.add_credits(user_id, 1, source="debug_test")
            print(f"✅ Successfully added 1 test credit. New balance: {new_balance}")
            
            # Remove the test credit
            db.use_credits(user_id, 1)
            final_balance = db.get_user_credits(user_id)
            print(f"✅ Removed test credit. Final balance: {final_balance}")
        except Exception as e:
            print(f"❌ Error testing credit addition: {e}")
        
        # Close database
        db.close()
        print("\n✅ Debug complete!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
