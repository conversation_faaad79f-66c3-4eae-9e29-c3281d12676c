"""
Script to install required dependencies for VoicePal.
"""

import subprocess
import sys

def install_dependencies():
    """Install required dependencies."""
    # Core dependencies
    core_dependencies = [
        "python-telegram-bot==20.6",
        "python-dotenv==1.0.0",
        "requests==2.31.0",
        "httpx==0.25.0",  # Compatible with python-telegram-bot
        "pydantic>=1.10.0,<2.0.0"  # For compatibility with FastAPI and SQLModel
    ]

    # Voice processing dependencies
    voice_dependencies = [
        "deepgram-sdk==2.12.0",
        "gTTS==2.3.2",
        "elevenlabs==1.58.1",
        "soundfile==0.12.1"
    ]

    # AI provider dependencies
    ai_dependencies = [
        "google-genai==0.3.0",  # Using older version for compatibility
        "huggingface-hub==0.19.4"
    ]

    # Optional dependencies for advanced features
    optional_dependencies = [
        "fastapi==0.96.1",
        "uvicorn==0.24.0",
        "numpy==2.0.1",
        "transformers==4.38.2",
        "torch==2.2.1"
    ]

    # Combine all dependencies
    dependencies = core_dependencies + voice_dependencies + ai_dependencies + optional_dependencies

    print("Installing dependencies...")
    for dep in dependencies:
        print(f"Installing {dep}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", dep])

    print("All dependencies installed successfully!")

if __name__ == "__main__":
    install_dependencies()
