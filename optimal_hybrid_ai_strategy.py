#!/usr/bin/env python3
"""
Optimal Hybrid AI Strategy for MoneyMule Bot

Based on Groq free tier analysis and best practices, this implements:
1. Groq + Google AI hybrid system for optimal performance
2. Emotional intelligence and conversation memory
3. Model selection based on conversation type
4. Cost optimization with free tiers
"""

import os
import json
import asyncio
import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum
import httpx

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ConversationType(Enum):
    """Types of conversations for optimal model selection."""
    QUICK_RESPONSE = "quick"           # Simple questions, greetings
    EMOTIONAL_SUPPORT = "emotional"    # Mood tracking, therapy-like
    DEEP_CONVERSATION = "deep"         # Complex, memory-heavy chats
    CREATIVE = "creative"              # Storytelling, roleplay
    TECHNICAL = "technical"            # Problem solving, analysis

@dataclass
class ModelConfig:
    """Configuration for each AI model."""
    name: str
    provider: str
    max_tokens: int
    requests_per_day: int
    cost_per_request: float
    strengths: List[str]
    best_for: List[ConversationType]

class OptimalHybridAI:
    """Optimal hybrid AI system using Groq + Google AI."""
    
    def __init__(self):
        self.groq_api_key = "********************************************************"
        self.google_api_key = os.getenv('GOOGLE_AI_API_KEY')
        
        # Model configurations based on your Groq limits
        self.models = {
            # GROQ MODELS (FREE TIER)
            "llama-4-scout": ModelConfig(
                name="meta-llama/llama-4-scout-17b-16e-instruct",
                provider="groq",
                max_tokens=8000,
                requests_per_day=6000,  # Based on your screenshot
                cost_per_request=0.0,
                strengths=["fast_response", "emotional_intelligence", "conversation"],
                best_for=[ConversationType.EMOTIONAL_SUPPORT, ConversationType.QUICK_RESPONSE]
            ),
            "mixtral-8x7b": ModelConfig(
                name="mixtral-8x7b-32768",
                provider="groq",
                max_tokens=32768,
                requests_per_day=5000,
                cost_per_request=0.0,
                strengths=["large_context", "reasoning", "multilingual"],
                best_for=[ConversationType.DEEP_CONVERSATION, ConversationType.TECHNICAL]
            ),
            "llama-3.1-70b": ModelConfig(
                name="llama-3.1-70b-versatile",
                provider="groq",
                max_tokens=8000,
                requests_per_day=6000,
                cost_per_request=0.0,
                strengths=["versatile", "reasoning", "creative"],
                best_for=[ConversationType.CREATIVE, ConversationType.DEEP_CONVERSATION]
            ),
            "gemma2-9b": ModelConfig(
                name="gemma2-9b-it",
                provider="groq",
                max_tokens=8000,
                requests_per_day=15000,
                cost_per_request=0.0,
                strengths=["fast", "efficient", "instruction_following"],
                best_for=[ConversationType.QUICK_RESPONSE, ConversationType.TECHNICAL]
            ),
            
            # GOOGLE AI MODELS
            "gemini-1.5-flash": ModelConfig(
                name="gemini-1.5-flash",
                provider="google",
                max_tokens=8192,
                requests_per_day=1500,
                cost_per_request=0.0,
                strengths=["memory", "context", "multimodal"],
                best_for=[ConversationType.DEEP_CONVERSATION, ConversationType.EMOTIONAL_SUPPORT]
            )
        }
        
        # Usage tracking
        self.daily_usage = {model: 0 for model in self.models.keys()}
        
        # Conversation context for model selection
        self.conversation_contexts = {}
    
    def classify_conversation_type(self, message: str, user_context: Dict[str, Any] = None) -> ConversationType:
        """Classify conversation type for optimal model selection."""
        message_lower = message.lower()
        
        # Quick response indicators
        quick_indicators = ["hi", "hello", "thanks", "bye", "yes", "no", "ok", "sure"]
        if any(word in message_lower for word in quick_indicators) and len(message.split()) <= 5:
            return ConversationType.QUICK_RESPONSE
        
        # Emotional support indicators
        emotional_indicators = [
            "feel", "sad", "lonely", "depressed", "anxious", "worried", "scared",
            "happy", "excited", "angry", "frustrated", "mood", "emotion"
        ]
        if any(word in message_lower for word in emotional_indicators):
            return ConversationType.EMOTIONAL_SUPPORT
        
        # Creative indicators
        creative_indicators = ["story", "imagine", "pretend", "roleplay", "creative", "write"]
        if any(word in message_lower for word in creative_indicators):
            return ConversationType.CREATIVE
        
        # Technical indicators
        technical_indicators = ["how to", "explain", "solve", "problem", "help me", "tutorial"]
        if any(phrase in message_lower for phrase in technical_indicators):
            return ConversationType.TECHNICAL
        
        # Default to deep conversation for longer messages
        if len(message.split()) > 20 or (user_context and user_context.get('conversation_length', 0) > 5):
            return ConversationType.DEEP_CONVERSATION
        
        return ConversationType.QUICK_RESPONSE
    
    def select_optimal_model(self, conversation_type: ConversationType, user_id: int = None) -> str:
        """Select the optimal model based on conversation type and usage limits."""
        
        # Get models suitable for this conversation type
        suitable_models = [
            model_name for model_name, config in self.models.items()
            if conversation_type in config.best_for and self.daily_usage[model_name] < config.requests_per_day
        ]
        
        if not suitable_models:
            # Fallback to any available model
            suitable_models = [
                model_name for model_name, config in self.models.items()
                if self.daily_usage[model_name] < config.requests_per_day
            ]
        
        if not suitable_models:
            logger.warning("All models at daily limit, using gemma2-9b (highest limit)")
            return "gemma2-9b"
        
        # Priority selection based on conversation type
        if conversation_type == ConversationType.QUICK_RESPONSE:
            # Prefer fast models with high limits
            for model in ["gemma2-9b", "llama-4-scout"]:
                if model in suitable_models:
                    return model
        
        elif conversation_type == ConversationType.EMOTIONAL_SUPPORT:
            # Prefer emotionally intelligent models
            for model in ["llama-4-scout", "gemini-1.5-flash", "llama-3.1-70b"]:
                if model in suitable_models:
                    return model
        
        elif conversation_type == ConversationType.DEEP_CONVERSATION:
            # Prefer models with large context and memory
            for model in ["mixtral-8x7b", "gemini-1.5-flash", "llama-3.1-70b"]:
                if model in suitable_models:
                    return model
        
        elif conversation_type == ConversationType.CREATIVE:
            # Prefer creative models
            for model in ["llama-3.1-70b", "mixtral-8x7b"]:
                if model in suitable_models:
                    return model
        
        elif conversation_type == ConversationType.TECHNICAL:
            # Prefer reasoning models
            for model in ["mixtral-8x7b", "gemma2-9b"]:
                if model in suitable_models:
                    return model
        
        # Default to first suitable model
        return suitable_models[0]
    
    async def generate_response_groq(self, model_name: str, messages: List[Dict], system_prompt: str = None) -> str:
        """Generate response using Groq API."""
        try:
            # Prepare messages
            formatted_messages = []
            
            if system_prompt:
                formatted_messages.append({"role": "system", "content": system_prompt})
            
            formatted_messages.extend(messages)
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    "https://api.groq.com/openai/v1/chat/completions",
                    headers={
                        "Authorization": f"Bearer {self.groq_api_key}",
                        "Content-Type": "application/json"
                    },
                    json={
                        "model": self.models[model_name].name,
                        "messages": formatted_messages,
                        "max_tokens": min(self.models[model_name].max_tokens, 1000),
                        "temperature": 0.7
                    },
                    timeout=30.0
                )
                
                if response.status_code == 200:
                    result = response.json()
                    self.daily_usage[model_name] += 1
                    return result['choices'][0]['message']['content']
                else:
                    logger.error(f"Groq API error: {response.status_code} - {response.text}")
                    return None
                    
        except Exception as e:
            logger.error(f"Error calling Groq API: {e}")
            return None
    
    async def generate_response_google(self, model_name: str, messages: List[Dict], system_prompt: str = None) -> str:
        """Generate response using Google AI API."""
        try:
            # Convert messages to Google AI format
            contents = []
            
            for message in messages:
                if message['role'] == 'user':
                    contents.append({"parts": [{"text": message['content']}]})
            
            # Combine system prompt with user message if provided
            if system_prompt and contents:
                combined_text = f"{system_prompt}\n\nUser: {contents[-1]['parts'][0]['text']}"
                contents[-1]['parts'][0]['text'] = combined_text
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"https://generativelanguage.googleapis.com/v1beta/models/{self.models[model_name].name}:generateContent?key={self.google_api_key}",
                    json={
                        "contents": contents,
                        "generationConfig": {
                            "maxOutputTokens": min(self.models[model_name].max_tokens, 1000),
                            "temperature": 0.7
                        }
                    },
                    timeout=30.0
                )
                
                if response.status_code == 200:
                    result = response.json()
                    if 'candidates' in result and result['candidates']:
                        self.daily_usage[model_name] += 1
                        return result['candidates'][0]['content']['parts'][0]['text']
                    else:
                        logger.error("No candidates in Google AI response")
                        return None
                else:
                    logger.error(f"Google AI API error: {response.status_code} - {response.text}")
                    return None
                    
        except Exception as e:
            logger.error(f"Error calling Google AI API: {e}")
            return None
    
    async def generate_response(self, user_id: int, message: str, conversation_history: List[Dict] = None, user_context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Generate optimal response using hybrid AI strategy."""
        
        # Classify conversation type
        conversation_type = self.classify_conversation_type(message, user_context)
        
        # Select optimal model
        selected_model = self.select_optimal_model(conversation_type, user_id)
        model_config = self.models[selected_model]
        
        # Prepare conversation history
        messages = conversation_history or []
        messages.append({"role": "user", "content": message})
        
        # Create context-aware system prompt
        system_prompt = self._create_system_prompt(conversation_type, user_context)
        
        # Generate response
        if model_config.provider == "groq":
            response = await self.generate_response_groq(selected_model, messages, system_prompt)
        else:
            response = await self.generate_response_google(selected_model, messages, system_prompt)
        
        # Fallback to alternative model if primary fails
        if not response:
            logger.warning(f"Primary model {selected_model} failed, trying fallback")
            fallback_model = self._get_fallback_model(selected_model)
            if fallback_model:
                fallback_config = self.models[fallback_model]
                if fallback_config.provider == "groq":
                    response = await self.generate_response_groq(fallback_model, messages, system_prompt)
                else:
                    response = await self.generate_response_google(fallback_model, messages, system_prompt)
        
        return {
            "response": response or "I'm having trouble responding right now. Please try again.",
            "model_used": selected_model,
            "conversation_type": conversation_type.value,
            "usage_remaining": model_config.requests_per_day - self.daily_usage[selected_model]
        }
    
    def _create_system_prompt(self, conversation_type: ConversationType, user_context: Dict[str, Any] = None) -> str:
        """Create context-aware system prompt."""
        base_prompt = "You are VoicePal, a compassionate AI companion designed to help lonely people through meaningful conversations."
        
        # Add conversation type specific instructions
        if conversation_type == ConversationType.EMOTIONAL_SUPPORT:
            base_prompt += " Focus on providing emotional support, empathy, and understanding. Remember personal details shared by the user."
        elif conversation_type == ConversationType.DEEP_CONVERSATION:
            base_prompt += " Engage in thoughtful, deep conversations. Remember previous topics and build upon them meaningfully."
        elif conversation_type == ConversationType.QUICK_RESPONSE:
            base_prompt += " Provide quick, friendly, and helpful responses. Keep it concise but warm."
        elif conversation_type == ConversationType.CREATIVE:
            base_prompt += " Be creative, imaginative, and engaging. Help with storytelling and creative activities."
        elif conversation_type == ConversationType.TECHNICAL:
            base_prompt += " Provide clear, helpful explanations and solutions. Be informative and practical."
        
        # Add user context if available
        if user_context:
            if user_context.get('user_name'):
                base_prompt += f" The user's name is {user_context['user_name']}."
            if user_context.get('current_mood'):
                base_prompt += f" The user's current mood is {user_context['current_mood']}."
            if user_context.get('conversation_topic'):
                base_prompt += f" You've been discussing {user_context['conversation_topic']}."
        
        base_prompt += " Always be warm, understanding, and human-like in your responses."
        
        return base_prompt
    
    def _get_fallback_model(self, primary_model: str) -> Optional[str]:
        """Get fallback model if primary fails."""
        fallback_map = {
            "llama-4-scout": "gemma2-9b",
            "mixtral-8x7b": "llama-3.1-70b", 
            "llama-3.1-70b": "mixtral-8x7b",
            "gemma2-9b": "llama-4-scout",
            "gemini-1.5-flash": "llama-4-scout"
        }
        
        fallback = fallback_map.get(primary_model)
        if fallback and self.daily_usage[fallback] < self.models[fallback].requests_per_day:
            return fallback
        
        # Find any available model
        for model_name, config in self.models.items():
            if self.daily_usage[model_name] < config.requests_per_day:
                return model_name
        
        return None
    
    def get_usage_stats(self) -> Dict[str, Any]:
        """Get current usage statistics."""
        stats = {}
        for model_name, config in self.models.items():
            stats[model_name] = {
                "used": self.daily_usage[model_name],
                "limit": config.requests_per_day,
                "remaining": config.requests_per_day - self.daily_usage[model_name],
                "percentage_used": (self.daily_usage[model_name] / config.requests_per_day) * 100
            }
        return stats

async def test_hybrid_system():
    """Test the hybrid AI system."""
    ai = OptimalHybridAI()
    
    # Test different conversation types
    test_cases = [
        {
            "message": "Hi there!",
            "context": {"user_name": "Sarah"},
            "expected_type": ConversationType.QUICK_RESPONSE
        },
        {
            "message": "I'm feeling really lonely and sad today. I don't know what to do.",
            "context": {"user_name": "Sarah", "current_mood": "sad"},
            "expected_type": ConversationType.EMOTIONAL_SUPPORT
        },
        {
            "message": "Can you tell me a story about a brave knight who saves a kingdom?",
            "context": {"user_name": "Sarah"},
            "expected_type": ConversationType.CREATIVE
        },
        {
            "message": "I've been thinking about the meaning of life and what makes relationships meaningful. What are your thoughts on building deep connections with people?",
            "context": {"user_name": "Sarah", "conversation_length": 10},
            "expected_type": ConversationType.DEEP_CONVERSATION
        }
    ]
    
    print("🧪 Testing Optimal Hybrid AI System")
    print("=" * 50)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\nTest {i}: {test_case['message'][:50]}...")
        
        result = await ai.generate_response(
            user_id=12345,
            message=test_case['message'],
            user_context=test_case['context']
        )
        
        print(f"Conversation Type: {result['conversation_type']}")
        print(f"Model Used: {result['model_used']}")
        print(f"Response: {result['response'][:100]}...")
        print(f"Usage Remaining: {result['usage_remaining']}")
    
    # Print usage statistics
    print(f"\n📊 USAGE STATISTICS:")
    stats = ai.get_usage_stats()
    for model, stat in stats.items():
        print(f"{model}: {stat['used']}/{stat['limit']} ({stat['percentage_used']:.1f}%)")

if __name__ == "__main__":
    asyncio.run(test_hybrid_system())
