"""
Notification system for VoicePal.

This module provides comprehensive notification functionality including scheduled notifications,
push notifications, and multi-channel delivery.
"""

import logging
import json
import asyncio
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
import threading
import time

logger = logging.getLogger(__name__)

class NotificationType(Enum):
    """Notification types."""
    REMINDER = "reminder"
    PROMOTIONAL = "promotional"
    SYSTEM = "system"
    ALERT = "alert"
    WELCOME = "welcome"
    RETENTION = "retention"
    ENGAGEMENT = "engagement"

class NotificationChannel(Enum):
    """Notification delivery channels."""
    TELEGRAM = "telegram"
    EMAIL = "email"
    PUSH = "push"
    SMS = "sms"
    IN_APP = "in_app"

class NotificationStatus(Enum):
    """Notification status."""
    PENDING = "pending"
    SENT = "sent"
    DELIVERED = "delivered"
    FAILED = "failed"
    CANCELLED = "cancelled"

@dataclass
class NotificationTemplate:
    """Notification template."""
    template_id: str
    name: str
    notification_type: NotificationType
    channel: NotificationChannel
    subject: Optional[str]
    content: str
    variables: List[str]
    is_active: bool

@dataclass
class Notification:
    """Notification instance."""
    notification_id: str
    user_id: int
    template_id: str
    channel: NotificationChannel
    notification_type: NotificationType
    subject: Optional[str]
    content: str
    scheduled_at: datetime
    sent_at: Optional[datetime]
    status: NotificationStatus
    metadata: Dict[str, Any]
    retry_count: int = 0
    max_retries: int = 3

@dataclass
class NotificationRule:
    """Notification rule for automated sending."""
    rule_id: str
    name: str
    trigger_event: str
    conditions: Dict[str, Any]
    template_id: str
    delay_minutes: int
    is_active: bool

class NotificationManager:
    """Notification management system."""
    
    def __init__(
        self,
        database,
        telegram_bot=None,
        email_service=None,
        redis_client=None
    ):
        """
        Initialize notification manager.
        
        Args:
            database: Database instance
            telegram_bot: Telegram bot instance
            email_service: Email service instance
            redis_client: Redis client for scheduling
        """
        self.database = database
        self.telegram_bot = telegram_bot
        self.email_service = email_service
        self.redis_client = redis_client
        
        # Notification handlers
        self.channel_handlers: Dict[NotificationChannel, Callable] = {
            NotificationChannel.TELEGRAM: self._send_telegram_notification,
            NotificationChannel.EMAIL: self._send_email_notification,
            NotificationChannel.IN_APP: self._send_in_app_notification
        }
        
        # Scheduler
        self.scheduler_thread: Optional[threading.Thread] = None
        self.is_running = False
        
        # Initialize tables
        self._initialize_tables()
        
        logger.info("Notification manager initialized")
    
    def _initialize_tables(self):
        """Initialize notification tables."""
        try:
            # Notification templates
            self.database.execute("""
                CREATE TABLE IF NOT EXISTS notification_templates (
                    template_id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    notification_type TEXT NOT NULL,
                    channel TEXT NOT NULL,
                    subject TEXT,
                    content TEXT NOT NULL,
                    variables TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Notifications
            self.database.execute("""
                CREATE TABLE IF NOT EXISTS notifications (
                    notification_id TEXT PRIMARY KEY,
                    user_id INTEGER NOT NULL,
                    template_id TEXT,
                    channel TEXT NOT NULL,
                    notification_type TEXT NOT NULL,
                    subject TEXT,
                    content TEXT NOT NULL,
                    scheduled_at TEXT NOT NULL,
                    sent_at TEXT,
                    status TEXT NOT NULL,
                    metadata TEXT,
                    retry_count INTEGER DEFAULT 0,
                    max_retries INTEGER DEFAULT 3,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Notification rules
            self.database.execute("""
                CREATE TABLE IF NOT EXISTS notification_rules (
                    rule_id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    trigger_event TEXT NOT NULL,
                    conditions TEXT,
                    template_id TEXT NOT NULL,
                    delay_minutes INTEGER DEFAULT 0,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # User notification preferences
            self.database.execute("""
                CREATE TABLE IF NOT EXISTS user_notification_preferences (
                    user_id INTEGER PRIMARY KEY,
                    telegram_enabled BOOLEAN DEFAULT 1,
                    email_enabled BOOLEAN DEFAULT 1,
                    push_enabled BOOLEAN DEFAULT 1,
                    promotional_enabled BOOLEAN DEFAULT 1,
                    reminder_enabled BOOLEAN DEFAULT 1,
                    quiet_hours_start TEXT,
                    quiet_hours_end TEXT,
                    timezone TEXT DEFAULT 'UTC',
                    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Create indexes
            self.database.execute("CREATE INDEX IF NOT EXISTS idx_notifications_user_status ON notifications(user_id, status)")
            self.database.execute("CREATE INDEX IF NOT EXISTS idx_notifications_scheduled ON notifications(scheduled_at, status)")
            
            self.database.commit()
            
        except Exception as e:
            logger.error(f"Failed to initialize notification tables: {e}")
    
    def start_scheduler(self):
        """Start notification scheduler."""
        if self.is_running:
            return
        
        self.is_running = True
        self.scheduler_thread = threading.Thread(
            target=self._scheduler_loop,
            daemon=True,
            name="NotificationScheduler"
        )
        self.scheduler_thread.start()
        
        logger.info("Notification scheduler started")
    
    def stop_scheduler(self):
        """Stop notification scheduler."""
        self.is_running = False
        if self.scheduler_thread:
            self.scheduler_thread.join(timeout=5)
        
        logger.info("Notification scheduler stopped")
    
    def _scheduler_loop(self):
        """Main scheduler loop."""
        while self.is_running:
            try:
                # Process pending notifications
                self._process_pending_notifications()
                
                # Sleep for 30 seconds
                time.sleep(30)
                
            except Exception as e:
                logger.error(f"Error in notification scheduler: {e}")
                time.sleep(30)
    
    def _process_pending_notifications(self):
        """Process pending notifications."""
        try:
            # Get notifications ready to send
            current_time = datetime.utcnow()
            
            notifications = self.database.execute("""
                SELECT * FROM notifications
                WHERE status = ? AND scheduled_at <= ?
                ORDER BY scheduled_at
                LIMIT 100
            """, (NotificationStatus.PENDING.value, current_time.isoformat())).fetchall()
            
            for notif_data in notifications:
                try:
                    # Create notification object
                    notification = Notification(
                        notification_id=notif_data['notification_id'],
                        user_id=notif_data['user_id'],
                        template_id=notif_data['template_id'],
                        channel=NotificationChannel(notif_data['channel']),
                        notification_type=NotificationType(notif_data['notification_type']),
                        subject=notif_data['subject'],
                        content=notif_data['content'],
                        scheduled_at=datetime.fromisoformat(notif_data['scheduled_at']),
                        sent_at=datetime.fromisoformat(notif_data['sent_at']) if notif_data['sent_at'] else None,
                        status=NotificationStatus(notif_data['status']),
                        metadata=json.loads(notif_data['metadata']) if notif_data['metadata'] else {},
                        retry_count=notif_data['retry_count'],
                        max_retries=notif_data['max_retries']
                    )
                    
                    # Check user preferences
                    if not self._should_send_notification(notification):
                        self._update_notification_status(
                            notification.notification_id,
                            NotificationStatus.CANCELLED
                        )
                        continue
                    
                    # Send notification
                    success = self._send_notification(notification)
                    
                    if success:
                        self._update_notification_status(
                            notification.notification_id,
                            NotificationStatus.SENT,
                            sent_at=datetime.utcnow()
                        )
                    else:
                        # Handle retry
                        if notification.retry_count < notification.max_retries:
                            self._schedule_retry(notification)
                        else:
                            self._update_notification_status(
                                notification.notification_id,
                                NotificationStatus.FAILED
                            )
                    
                except Exception as e:
                    logger.error(f"Failed to process notification {notif_data['notification_id']}: {e}")
            
        except Exception as e:
            logger.error(f"Failed to process pending notifications: {e}")
    
    def _should_send_notification(self, notification: Notification) -> bool:
        """Check if notification should be sent based on user preferences."""
        try:
            # Get user preferences
            prefs = self.database.execute("""
                SELECT * FROM user_notification_preferences
                WHERE user_id = ?
            """, (notification.user_id,)).fetchone()
            
            if not prefs:
                return True  # Default to sending if no preferences set
            
            # Check channel preferences
            if notification.channel == NotificationChannel.TELEGRAM and not prefs['telegram_enabled']:
                return False
            elif notification.channel == NotificationChannel.EMAIL and not prefs['email_enabled']:
                return False
            elif notification.channel == NotificationChannel.PUSH and not prefs['push_enabled']:
                return False
            
            # Check notification type preferences
            if notification.notification_type == NotificationType.PROMOTIONAL and not prefs['promotional_enabled']:
                return False
            elif notification.notification_type == NotificationType.REMINDER and not prefs['reminder_enabled']:
                return False
            
            # Check quiet hours
            if prefs['quiet_hours_start'] and prefs['quiet_hours_end']:
                current_hour = datetime.utcnow().hour
                quiet_start = int(prefs['quiet_hours_start'].split(':')[0])
                quiet_end = int(prefs['quiet_hours_end'].split(':')[0])
                
                if quiet_start <= current_hour < quiet_end:
                    # Reschedule for after quiet hours
                    new_time = datetime.utcnow().replace(hour=quiet_end, minute=0, second=0, microsecond=0)
                    if new_time <= datetime.utcnow():
                        new_time += timedelta(days=1)
                    
                    self._reschedule_notification(notification.notification_id, new_time)
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to check notification preferences: {e}")
            return True
    
    def _send_notification(self, notification: Notification) -> bool:
        """Send notification through appropriate channel."""
        try:
            handler = self.channel_handlers.get(notification.channel)
            if not handler:
                logger.error(f"No handler for channel: {notification.channel}")
                return False
            
            return handler(notification)
            
        except Exception as e:
            logger.error(f"Failed to send notification: {e}")
            return False
    
    def _send_telegram_notification(self, notification: Notification) -> bool:
        """Send Telegram notification."""
        try:
            if not self.telegram_bot:
                logger.error("Telegram bot not configured")
                return False
            
            # Send message
            asyncio.create_task(
                self.telegram_bot.send_message(
                    chat_id=notification.user_id,
                    text=notification.content,
                    parse_mode="Markdown"
                )
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to send Telegram notification: {e}")
            return False
    
    def _send_email_notification(self, notification: Notification) -> bool:
        """Send email notification."""
        try:
            if not self.email_service:
                logger.error("Email service not configured")
                return False
            
            # Get user email
            user = self.database.execute("""
                SELECT email FROM users WHERE user_id = ?
            """, (notification.user_id,)).fetchone()
            
            if not user or not user['email']:
                logger.warning(f"No email for user {notification.user_id}")
                return False
            
            # Send email (placeholder - implement based on your email service)
            # self.email_service.send_email(
            #     to=user['email'],
            #     subject=notification.subject,
            #     content=notification.content
            # )
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to send email notification: {e}")
            return False
    
    def _send_in_app_notification(self, notification: Notification) -> bool:
        """Send in-app notification."""
        try:
            # Store in Redis for real-time delivery
            if self.redis_client:
                key = f"notifications:user:{notification.user_id}"
                self.redis_client.lpush(key, json.dumps({
                    "id": notification.notification_id,
                    "type": notification.notification_type.value,
                    "content": notification.content,
                    "timestamp": datetime.utcnow().isoformat()
                }))
                self.redis_client.expire(key, 86400)  # 24 hours
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to send in-app notification: {e}")
            return False
    
    def schedule_notification(
        self,
        user_id: int,
        template_id: str,
        channel: NotificationChannel,
        scheduled_at: datetime,
        variables: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Schedule a notification.
        
        Args:
            user_id: User ID
            template_id: Template ID
            channel: Notification channel
            scheduled_at: When to send
            variables: Template variables
            
        Returns:
            Notification ID
        """
        try:
            # Get template
            template = self.get_template(template_id)
            if not template:
                raise ValueError(f"Template not found: {template_id}")
            
            # Render content
            content = self._render_template(template.content, variables or {})
            subject = self._render_template(template.subject, variables or {}) if template.subject else None
            
            # Generate notification ID
            notification_id = f"notif_{int(datetime.utcnow().timestamp() * 1000)}"
            
            # Store notification
            self.database.execute("""
                INSERT INTO notifications (
                    notification_id, user_id, template_id, channel, notification_type,
                    subject, content, scheduled_at, status, metadata
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                notification_id,
                user_id,
                template_id,
                channel.value,
                template.notification_type.value,
                subject,
                content,
                scheduled_at.isoformat(),
                NotificationStatus.PENDING.value,
                json.dumps(variables or {})
            ))
            
            self.database.commit()
            
            logger.info(f"Scheduled notification {notification_id} for user {user_id}")
            return notification_id
            
        except Exception as e:
            logger.error(f"Failed to schedule notification: {e}")
            raise
    
    def _render_template(self, template: str, variables: Dict[str, Any]) -> str:
        """Render template with variables."""
        try:
            rendered = template
            for key, value in variables.items():
                rendered = rendered.replace(f"{{{key}}}", str(value))
            return rendered
        except Exception as e:
            logger.error(f"Failed to render template: {e}")
            return template
    
    def create_template(
        self,
        name: str,
        notification_type: NotificationType,
        channel: NotificationChannel,
        content: str,
        subject: Optional[str] = None,
        variables: Optional[List[str]] = None
    ) -> str:
        """
        Create notification template.
        
        Args:
            name: Template name
            notification_type: Type of notification
            channel: Delivery channel
            content: Template content
            subject: Template subject (for email)
            variables: Template variables
            
        Returns:
            Template ID
        """
        try:
            template_id = f"template_{int(datetime.utcnow().timestamp())}"
            
            self.database.execute("""
                INSERT INTO notification_templates (
                    template_id, name, notification_type, channel, subject, content, variables
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                template_id,
                name,
                notification_type.value,
                channel.value,
                subject,
                content,
                json.dumps(variables or [])
            ))
            
            self.database.commit()
            
            logger.info(f"Created notification template: {template_id}")
            return template_id
            
        except Exception as e:
            logger.error(f"Failed to create template: {e}")
            raise
    
    def get_template(self, template_id: str) -> Optional[NotificationTemplate]:
        """Get notification template."""
        try:
            template_data = self.database.execute("""
                SELECT * FROM notification_templates WHERE template_id = ?
            """, (template_id,)).fetchone()
            
            if not template_data:
                return None
            
            return NotificationTemplate(
                template_id=template_data['template_id'],
                name=template_data['name'],
                notification_type=NotificationType(template_data['notification_type']),
                channel=NotificationChannel(template_data['channel']),
                subject=template_data['subject'],
                content=template_data['content'],
                variables=json.loads(template_data['variables']) if template_data['variables'] else [],
                is_active=bool(template_data['is_active'])
            )
            
        except Exception as e:
            logger.error(f"Failed to get template: {e}")
            return None
    
    def _update_notification_status(
        self,
        notification_id: str,
        status: NotificationStatus,
        sent_at: Optional[datetime] = None
    ):
        """Update notification status."""
        try:
            if sent_at:
                self.database.execute("""
                    UPDATE notifications 
                    SET status = ?, sent_at = ?
                    WHERE notification_id = ?
                """, (status.value, sent_at.isoformat(), notification_id))
            else:
                self.database.execute("""
                    UPDATE notifications 
                    SET status = ?
                    WHERE notification_id = ?
                """, (status.value, notification_id))
            
            self.database.commit()
            
        except Exception as e:
            logger.error(f"Failed to update notification status: {e}")
    
    def _schedule_retry(self, notification: Notification):
        """Schedule notification retry."""
        try:
            # Exponential backoff: 5, 15, 45 minutes
            delay_minutes = 5 * (3 ** notification.retry_count)
            retry_time = datetime.utcnow() + timedelta(minutes=delay_minutes)
            
            self.database.execute("""
                UPDATE notifications 
                SET scheduled_at = ?, retry_count = retry_count + 1
                WHERE notification_id = ?
            """, (retry_time.isoformat(), notification.notification_id))
            
            self.database.commit()
            
        except Exception as e:
            logger.error(f"Failed to schedule retry: {e}")
    
    def _reschedule_notification(self, notification_id: str, new_time: datetime):
        """Reschedule notification."""
        try:
            self.database.execute("""
                UPDATE notifications 
                SET scheduled_at = ?
                WHERE notification_id = ?
            """, (new_time.isoformat(), notification_id))
            
            self.database.commit()
            
        except Exception as e:
            logger.error(f"Failed to reschedule notification: {e}")
    
    def get_user_notifications(self, user_id: int, limit: int = 50) -> List[Dict[str, Any]]:
        """Get user's recent notifications."""
        try:
            notifications = self.database.execute("""
                SELECT * FROM notifications
                WHERE user_id = ?
                ORDER BY created_at DESC
                LIMIT ?
            """, (user_id, limit)).fetchall()
            
            return [dict(notif) for notif in notifications]
            
        except Exception as e:
            logger.error(f"Failed to get user notifications: {e}")
            return []
    
    def update_user_preferences(
        self,
        user_id: int,
        preferences: Dict[str, Any]
    ):
        """Update user notification preferences."""
        try:
            # Build update query dynamically
            fields = []
            values = []
            
            for key, value in preferences.items():
                if key in ['telegram_enabled', 'email_enabled', 'push_enabled', 
                          'promotional_enabled', 'reminder_enabled', 'quiet_hours_start',
                          'quiet_hours_end', 'timezone']:
                    fields.append(f"{key} = ?")
                    values.append(value)
            
            if fields:
                fields.append("updated_at = ?")
                values.append(datetime.utcnow().isoformat())
                values.append(user_id)
                
                self.database.execute(f"""
                    INSERT OR REPLACE INTO user_notification_preferences 
                    (user_id, {', '.join(key for key in preferences.keys() if key in ['telegram_enabled', 'email_enabled', 'push_enabled', 'promotional_enabled', 'reminder_enabled', 'quiet_hours_start', 'quiet_hours_end', 'timezone'])}, updated_at)
                    VALUES ({', '.join(['?'] * (len(fields)))})
                """, [user_id] + list(preferences.values()) + [datetime.utcnow().isoformat()])
                
                self.database.commit()
            
        except Exception as e:
            logger.error(f"Failed to update user preferences: {e}")
    
    def send_immediate_notification(
        self,
        user_id: int,
        channel: NotificationChannel,
        notification_type: NotificationType,
        content: str,
        subject: Optional[str] = None
    ) -> bool:
        """Send immediate notification."""
        try:
            notification = Notification(
                notification_id=f"immediate_{int(datetime.utcnow().timestamp() * 1000)}",
                user_id=user_id,
                template_id="",
                channel=channel,
                notification_type=notification_type,
                subject=subject,
                content=content,
                scheduled_at=datetime.utcnow(),
                sent_at=None,
                status=NotificationStatus.PENDING,
                metadata={}
            )
            
            return self._send_notification(notification)
            
        except Exception as e:
            logger.error(f"Failed to send immediate notification: {e}")
            return False
