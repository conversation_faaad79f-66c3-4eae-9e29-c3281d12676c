"""
Analytics endpoints for VoicePal API.
"""

import logging
from fastapi import APIRouter, HTTPException, Depends, Query
from bot.api.dependencies import get_analytics_manager

logger = logging.getLogger(__name__)
router = APIRouter()

@router.get("/analytics/dashboard")
async def get_analytics_dashboard(
    days: int = Query(30, ge=1, le=365),
    analytics=Depends(get_analytics_manager)
):
    """Get comprehensive analytics dashboard."""
    try:
        dashboard = analytics.get_comprehensive_dashboard(days=days)
        return dashboard
    except Exception as e:
        logger.error(f"Error getting analytics dashboard: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving analytics")

@router.get("/analytics/users")
async def get_user_analytics(
    days: int = Query(30, ge=1, le=365),
    analytics=Depends(get_analytics_manager)
):
    """Get user analytics."""
    try:
        metrics = analytics.user_analytics.get_user_metrics(days=days)
        return metrics.__dict__ if hasattr(metrics, '__dict__') else metrics
    except Exception as e:
        logger.error(f"Error getting user analytics: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving user analytics")

@router.get("/analytics/conversations")
async def get_conversation_analytics(
    days: int = Query(30, ge=1, le=365),
    user_id: int = Query(None),
    analytics=Depends(get_analytics_manager)
):
    """Get conversation analytics."""
    try:
        metrics = analytics.conversation_analytics.analyze_conversation_patterns(
            user_id=user_id, days=days
        )
        return metrics.__dict__ if hasattr(metrics, '__dict__') else metrics
    except Exception as e:
        logger.error(f"Error getting conversation analytics: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving conversation analytics")

@router.get("/analytics/revenue")
async def get_revenue_analytics(
    days: int = Query(30, ge=1, le=365),
    analytics=Depends(get_analytics_manager)
):
    """Get revenue analytics."""
    try:
        metrics = analytics.business_analytics.get_revenue_metrics(days=days)
        return metrics.__dict__ if hasattr(metrics, '__dict__') else metrics
    except Exception as e:
        logger.error(f"Error getting revenue analytics: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving revenue analytics")
