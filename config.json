{"providers": {"stt": {"type": "deepgram", "api_key": "****************************************"}, "ai": {"type": "google_ai", "api_key": "AIzaSyAz--q4nScobQoG9NzQ7pCP9kYW9XUFTJw", "model_name": "gemini-1.5-flash"}, "tts": {"type": "deepgram", "api_key": "****************************************", "voice_id": "aura-2-thalia-en", "model_id": "aura-2"}}, "features": {"memory": {"enabled": true, "conversation_memory": 10, "summary_update_frequency": 24}, "mood_tracking": {"enabled": true, "analysis_frequency": 7}, "personalization": {"enabled": true, "default_personality": "friendly", "default_language": "en", "default_voice": "default"}}, "credit_system": {"enabled": true, "text_credit_cost": 1, "voice_credit_cost": 3, "free_trial_credits": 100}, "telegram": {"token": "**********:AAFRk-X_jICC4fHJUXkNEgN2S41EXlrye_E", "payment_provider": "stripe", "payment_provider_token": "123456789:TEST:pk_test_51RRH1XFyuxtpsLV0lD7PwpRJRTAU3kI2dWuHLekvcECaETPNaQoAFINiWXspYokj8pQDVNvRcJOO0VdpxGSs1yBr00ybJ6snJL", "admin_user_ids": [616696346]}, "database": {"file": "voicepal.db"}}