"""
<PERSON><PERSON><PERSON> to test the payment system.

This script runs the payment system tests and generates a report.
"""

import os
import sys
import logging
import unittest
import argparse
from datetime import datetime

# Add parent directory to path to import bot modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import test modules
from tests.test_payment import TestPaymentSystem, TestPaymentProviders

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

def run_tests(verbose=False, output_file=None):
    """
    Run the payment system tests.
    
    Args:
        verbose: Whether to print verbose output
        output_file: Path to output file for test results
    
    Returns:
        True if all tests passed, False otherwise
    """
    # Create test suite
    suite = unittest.TestSuite()
    
    # Add test cases
    suite.addTest(unittest.makeSuite(TestPaymentSystem))
    suite.addTest(unittest.makeSuite(TestPaymentProviders))
    
    # Create test runner
    runner = unittest.TextTestRunner(verbosity=2 if verbose else 1)
    
    # Run tests
    logger.info("Running payment system tests...")
    result = runner.run(suite)
    
    # Print summary
    logger.info("Tests completed:")
    logger.info(f"  Ran {result.testsRun} tests")
    logger.info(f"  Failures: {len(result.failures)}")
    logger.info(f"  Errors: {len(result.errors)}")
    logger.info(f"  Skipped: {len(result.skipped)}")
    
    # Write results to file if specified
    if output_file:
        with open(output_file, 'w') as f:
            f.write(f"Payment System Test Results - {datetime.now()}\n")
            f.write(f"Ran {result.testsRun} tests\n")
            f.write(f"Failures: {len(result.failures)}\n")
            f.write(f"Errors: {len(result.errors)}\n")
            f.write(f"Skipped: {len(result.skipped)}\n\n")
            
            if result.failures:
                f.write("Failures:\n")
                for test, traceback in result.failures:
                    f.write(f"{test}\n")
                    f.write(f"{traceback}\n\n")
            
            if result.errors:
                f.write("Errors:\n")
                for test, traceback in result.errors:
                    f.write(f"{test}\n")
                    f.write(f"{traceback}\n\n")
        
        logger.info(f"Test results written to {output_file}")
    
    return len(result.failures) == 0 and len(result.errors) == 0

def main():
    """Main function to run the payment system tests."""
    parser = argparse.ArgumentParser(description="Run payment system tests")
    parser.add_argument("-v", "--verbose", action="store_true", help="Print verbose output")
    parser.add_argument("-o", "--output", help="Output file for test results")
    args = parser.parse_args()
    
    success = run_tests(verbose=args.verbose, output_file=args.output)
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
