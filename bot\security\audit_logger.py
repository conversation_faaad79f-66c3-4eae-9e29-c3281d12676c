"""
Audit logging module for VoicePal.

This module provides comprehensive audit logging for security and compliance.
"""

import json
import logging
from datetime import datetime
from typing import Optional, Dict, Any, List
from enum import Enum
from dataclasses import dataclass, asdict

logger = logging.getLogger(__name__)

class AuditEventType(Enum):
    """Types of audit events."""
    USER_LOGIN = "user_login"
    USER_LOGOUT = "user_logout"
    USER_REGISTRATION = "user_registration"
    USER_DELETION = "user_deletion"
    MESSAGE_SENT = "message_sent"
    MESSAGE_RECEIVED = "message_received"
    PAYMENT_INITIATED = "payment_initiated"
    PAYMENT_COMPLETED = "payment_completed"
    PAYMENT_FAILED = "payment_failed"
    CREDITS_ADDED = "credits_added"
    CREDITS_USED = "credits_used"
    VOICE_PROCESSED = "voice_processed"
    AI_REQUEST = "ai_request"
    DATA_EXPORT = "data_export"
    DATA_DELETION = "data_deletion"
    ADMIN_ACTION = "admin_action"
    SECURITY_VIOLATION = "security_violation"
    RATE_LIMIT_EXCEEDED = "rate_limit_exceeded"
    API_ACCESS = "api_access"
    CONFIG_CHANGE = "config_change"
    FEATURE_TOGGLE = "feature_toggle"

class AuditSeverity(Enum):
    """Severity levels for audit events."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

@dataclass
class AuditEvent:
    """Audit event data structure."""
    event_type: AuditEventType
    user_id: Optional[int]
    timestamp: datetime
    severity: AuditSeverity
    description: str
    details: Dict[str, Any]
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    session_id: Optional[str] = None
    request_id: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert audit event to dictionary."""
        data = asdict(self)
        data['event_type'] = self.event_type.value
        data['severity'] = self.severity.value
        data['timestamp'] = self.timestamp.isoformat()
        return data
    
    def to_json(self) -> str:
        """Convert audit event to JSON string."""
        return json.dumps(self.to_dict(), default=str)

class AuditLogger:
    """Audit logger for VoicePal."""
    
    def __init__(self, database=None, log_to_file: bool = True, log_file: str = "audit.log"):
        """
        Initialize audit logger.
        
        Args:
            database: Database instance for storing audit logs
            log_to_file: Whether to log to file
            log_file: Path to audit log file
        """
        self.database = database
        self.log_to_file = log_to_file
        self.log_file = log_file
        
        # Set up file logger if enabled
        if self.log_to_file:
            self.file_logger = logging.getLogger("voicepal.audit")
            self.file_logger.setLevel(logging.INFO)
            
            # Create file handler
            file_handler = logging.FileHandler(log_file)
            file_handler.setLevel(logging.INFO)
            
            # Create formatter
            formatter = logging.Formatter(
                '%(asctime)s - %(levelname)s - %(message)s'
            )
            file_handler.setFormatter(formatter)
            
            # Add handler to logger
            if not self.file_logger.handlers:
                self.file_logger.addHandler(file_handler)
    
    def log_event(self, event: AuditEvent) -> bool:
        """
        Log an audit event.
        
        Args:
            event: Audit event to log
            
        Returns:
            True if logged successfully, False otherwise
        """
        try:
            # Log to database
            if self.database:
                self._log_to_database(event)
            
            # Log to file
            if self.log_to_file:
                self._log_to_file(event)
            
            # Log to console for critical events
            if event.severity == AuditSeverity.CRITICAL:
                logger.critical(f"AUDIT: {event.description}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to log audit event: {e}")
            return False
    
    def _log_to_database(self, event: AuditEvent) -> None:
        """Log event to database."""
        try:
            # Create audit_logs table if it doesn't exist
            self.database.execute("""
                CREATE TABLE IF NOT EXISTS audit_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    event_type TEXT NOT NULL,
                    user_id INTEGER,
                    timestamp TEXT NOT NULL,
                    severity TEXT NOT NULL,
                    description TEXT NOT NULL,
                    details TEXT,
                    ip_address TEXT,
                    user_agent TEXT,
                    session_id TEXT,
                    request_id TEXT,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Insert audit event
            self.database.execute("""
                INSERT INTO audit_logs (
                    event_type, user_id, timestamp, severity, description,
                    details, ip_address, user_agent, session_id, request_id
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                event.event_type.value,
                event.user_id,
                event.timestamp.isoformat(),
                event.severity.value,
                event.description,
                json.dumps(event.details),
                event.ip_address,
                event.user_agent,
                event.session_id,
                event.request_id
            ))
            
        except Exception as e:
            logger.error(f"Failed to log to database: {e}")
    
    def _log_to_file(self, event: AuditEvent) -> None:
        """Log event to file."""
        try:
            log_message = f"[{event.event_type.value}] User:{event.user_id} - {event.description}"
            if event.details:
                log_message += f" - Details: {json.dumps(event.details)}"
            
            self.file_logger.info(log_message)
            
        except Exception as e:
            logger.error(f"Failed to log to file: {e}")
    
    def log_user_action(
        self,
        event_type: AuditEventType,
        user_id: int,
        description: str,
        details: Optional[Dict[str, Any]] = None,
        severity: AuditSeverity = AuditSeverity.LOW,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> bool:
        """
        Log a user action.
        
        Args:
            event_type: Type of event
            user_id: User ID
            description: Event description
            details: Additional event details
            severity: Event severity
            ip_address: User's IP address
            user_agent: User's user agent
            
        Returns:
            True if logged successfully, False otherwise
        """
        event = AuditEvent(
            event_type=event_type,
            user_id=user_id,
            timestamp=datetime.utcnow(),
            severity=severity,
            description=description,
            details=details or {},
            ip_address=ip_address,
            user_agent=user_agent
        )
        
        return self.log_event(event)
    
    def log_security_event(
        self,
        event_type: AuditEventType,
        description: str,
        details: Optional[Dict[str, Any]] = None,
        user_id: Optional[int] = None,
        severity: AuditSeverity = AuditSeverity.HIGH,
        ip_address: Optional[str] = None
    ) -> bool:
        """
        Log a security event.
        
        Args:
            event_type: Type of event
            description: Event description
            details: Additional event details
            user_id: User ID (if applicable)
            severity: Event severity
            ip_address: IP address
            
        Returns:
            True if logged successfully, False otherwise
        """
        event = AuditEvent(
            event_type=event_type,
            user_id=user_id,
            timestamp=datetime.utcnow(),
            severity=severity,
            description=description,
            details=details or {},
            ip_address=ip_address
        )
        
        return self.log_event(event)
    
    def get_audit_logs(
        self,
        user_id: Optional[int] = None,
        event_type: Optional[AuditEventType] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """
        Retrieve audit logs.
        
        Args:
            user_id: Filter by user ID
            event_type: Filter by event type
            start_date: Filter by start date
            end_date: Filter by end date
            limit: Maximum number of logs to return
            
        Returns:
            List of audit log dictionaries
        """
        if not self.database:
            return []
        
        try:
            query = "SELECT * FROM audit_logs WHERE 1=1"
            params = []
            
            if user_id:
                query += " AND user_id = ?"
                params.append(user_id)
            
            if event_type:
                query += " AND event_type = ?"
                params.append(event_type.value)
            
            if start_date:
                query += " AND timestamp >= ?"
                params.append(start_date.isoformat())
            
            if end_date:
                query += " AND timestamp <= ?"
                params.append(end_date.isoformat())
            
            query += " ORDER BY timestamp DESC LIMIT ?"
            params.append(limit)
            
            rows = self.database.execute(query, params).fetchall()
            
            return [
                {
                    'id': row[0],
                    'event_type': row[1],
                    'user_id': row[2],
                    'timestamp': row[3],
                    'severity': row[4],
                    'description': row[5],
                    'details': json.loads(row[6]) if row[6] else {},
                    'ip_address': row[7],
                    'user_agent': row[8],
                    'session_id': row[9],
                    'request_id': row[10],
                    'created_at': row[11]
                }
                for row in rows
            ]
            
        except Exception as e:
            logger.error(f"Failed to retrieve audit logs: {e}")
            return []
