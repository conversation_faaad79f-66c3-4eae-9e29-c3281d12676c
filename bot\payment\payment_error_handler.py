"""
Payment error handler for VoicePal.

This module provides error handling for payment operations.
"""

import logging
import traceback
from typing import Dict, Any, Optional, Tuple, Union
from enum import Enum

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class PaymentErrorType(Enum):
    """Payment error types."""
    PROVIDER_TOKEN_MISSING = "provider_token_missing"
    INVALID_PROVIDER_TOKEN = "invalid_provider_token"
    USER_NOT_FOUND = "user_not_found"
    PACKAGE_NOT_FOUND = "package_not_found"
    DATABASE_ERROR = "database_error"
    TELEGRAM_API_ERROR = "telegram_api_error"
    PAYMENT_PROCESSING_ERROR = "payment_processing_error"
    INVOICE_CREATION_ERROR = "invoice_creation_error"
    UNKNOWN_ERROR = "unknown_error"

class PaymentError(Exception):
    """Payment error exception."""
    
    def __init__(self, error_type: PaymentErrorType, message: str, details: Optional[Dict[str, Any]] = None):
        """
        Initialize payment error.
        
        Args:
            error_type: Type of payment error
            message: Error message
            details: Additional error details
        """
        self.error_type = error_type
        self.message = message
        self.details = details or {}
        super().__init__(message)

def handle_payment_error(error: Exception, user_id: Optional[int] = None) -> Tuple[str, Dict[str, Any]]:
    """
    Handle payment error.
    
    Args:
        error: Exception that occurred
        user_id: User ID (if available)
        
    Returns:
        Tuple containing user-friendly error message and error details
    """
    error_details = {
        "error_type": "unknown_error",
        "traceback": traceback.format_exc(),
        "user_id": user_id
    }
    
    user_message = "Sorry, there was an error processing your payment. Please try again later."
    
    if isinstance(error, PaymentError):
        error_details["error_type"] = error.error_type.value
        error_details.update(error.details)
        
        # Provide specific user messages based on error type
        if error.error_type == PaymentErrorType.PROVIDER_TOKEN_MISSING:
            user_message = "Payment system is not properly configured. Please contact support."
        elif error.error_type == PaymentErrorType.INVALID_PROVIDER_TOKEN:
            user_message = "Payment provider is currently unavailable. Please try again later."
        elif error.error_type == PaymentErrorType.USER_NOT_FOUND:
            user_message = "Your user account could not be found. Please restart the bot and try again."
        elif error.error_type == PaymentErrorType.PACKAGE_NOT_FOUND:
            user_message = "The selected payment package is no longer available. Please choose another package."
        elif error.error_type == PaymentErrorType.DATABASE_ERROR:
            user_message = "There was a database error processing your payment. Please try again later."
    
    # Log the error
    logger.error(f"Payment error: {error}")
    if user_id:
        logger.error(f"User ID: {user_id}")
    logger.error(traceback.format_exc())
    
    return user_message, error_details

def log_payment_attempt(database, user_id: int, package_id: str, status: str, error: Optional[Exception] = None) -> None:
    """
    Log payment attempt.
    
    Args:
        database: Database instance
        user_id: User ID
        package_id: Package ID
        status: Payment status
        error: Exception that occurred (if any)
    """
    try:
        error_details = None
        if error:
            if isinstance(error, PaymentError):
                error_details = {
                    "error_type": error.error_type.value,
                    "message": error.message,
                    "details": error.details
                }
            else:
                error_details = {
                    "error_type": "unknown_error",
                    "message": str(error),
                    "traceback": traceback.format_exc()
                }
        
        # Log to database if payment_logs table exists
        if hasattr(database, 'add_payment_log'):
            database.add_payment_log(
                user_id=user_id,
                package_id=package_id,
                status=status,
                error_details=error_details
            )
        
        # Always log to application logs
        if error:
            logger.error(f"Payment attempt failed for user {user_id}, package {package_id}: {error}")
            if error_details:
                logger.error(f"Error details: {error_details}")
        else:
            logger.info(f"Payment attempt {status} for user {user_id}, package {package_id}")
    except Exception as e:
        logger.error(f"Error logging payment attempt: {e}")
