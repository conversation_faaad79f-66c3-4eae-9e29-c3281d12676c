#!/usr/bin/env python3
"""
Production Test Runner for MoneyMule Bot

This script runs all production readiness tests and existing test suites
to ensure the bot is ready for deployment and monetization.
"""

import os
import sys
import asyncio
import subprocess
import logging
from pathlib import Path
from typing import List, Dict, Any
import json

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ProductionTestRunner:
    """Comprehensive test runner for production readiness."""
    
    def __init__(self):
        self.test_results = {}
        self.failed_tests = []
        self.warnings = []
        
    def run_command(self, command: List[str], description: str) -> Dict[str, Any]:
        """Run a command and return results."""
        logger.info(f"Running: {description}")
        try:
            result = subprocess.run(
                command,
                capture_output=True,
                text=True,
                timeout=300  # 5 minutes timeout
            )
            
            success = result.returncode == 0
            if not success:
                self.failed_tests.append(f"{description}: {result.stderr}")
            
            return {
                'success': success,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'returncode': result.returncode
            }
        except subprocess.TimeoutExpired:
            error_msg = f"{description}: Test timed out after 5 minutes"
            self.failed_tests.append(error_msg)
            return {
                'success': False,
                'stdout': '',
                'stderr': 'Timeout',
                'returncode': -1
            }
        except Exception as e:
            error_msg = f"{description}: {str(e)}"
            self.failed_tests.append(error_msg)
            return {
                'success': False,
                'stdout': '',
                'stderr': str(e),
                'returncode': -1
            }

    async def run_production_readiness_tests(self):
        """Run the main production readiness test suite."""
        logger.info("🚀 Running Production Readiness Tests...")
        
        try:
            # Import and run the production readiness test suite
            from production_readiness_test import ProductionReadinessTestSuite
            
            suite = ProductionReadinessTestSuite()
            results = await suite.run_all_tests()
            
            self.test_results['production_readiness'] = results
            
            if results.get('critical_failures'):
                self.failed_tests.extend(results['critical_failures'])
            
            if results.get('warnings'):
                self.warnings.extend(results['warnings'])
                
            logger.info(f"✅ Production readiness tests completed: {results['overall_status']}")
            return results
            
        except Exception as e:
            error_msg = f"Production readiness tests failed: {e}"
            logger.error(error_msg)
            self.failed_tests.append(error_msg)
            return {'success': False, 'error': str(e)}

    def run_existing_tests(self):
        """Run existing test suites."""
        logger.info("🧪 Running Existing Test Suites...")
        
        # Test categories to run
        test_commands = [
            # Unit tests
            {
                'command': [sys.executable, '-m', 'pytest', 'tests/', '-v', '--tb=short'],
                'description': 'Unit Tests (pytest)',
                'optional': True
            },
            {
                'command': [sys.executable, '-m', 'unittest', 'discover', 'tests', '-v'],
                'description': 'Unit Tests (unittest)',
                'optional': True
            },
            
            # Specific bot tests
            {
                'command': [sys.executable, 'tests/run_comprehensive_tests.py'],
                'description': 'Comprehensive Bot Tests',
                'optional': True
            },
            {
                'command': [sys.executable, 'run_memory_tests.py'],
                'description': 'Memory System Tests',
                'optional': True
            },
            {
                'command': [sys.executable, 'validate_tests.py'],
                'description': 'Test Validation',
                'optional': True
            },
            
            # Bot-specific tests
            {
                'command': [sys.executable, 'bot/tests/run_all_tests.py'],
                'description': 'Bot Integration Tests',
                'optional': True
            }
        ]
        
        for test_config in test_commands:
            command = test_config['command']
            description = test_config['description']
            optional = test_config.get('optional', False)
            
            # Check if test file exists
            if len(command) > 1 and command[1].endswith('.py'):
                if not os.path.exists(command[1]):
                    if optional:
                        logger.info(f"⏭️ Skipping {description} - file not found")
                        continue
                    else:
                        self.warnings.append(f"{description} - test file not found")
                        continue
            
            result = self.run_command(command, description)
            self.test_results[description.lower().replace(' ', '_')] = result
            
            if result['success']:
                logger.info(f"✅ {description} passed")
            else:
                if optional:
                    logger.warning(f"⚠️ {description} failed (optional)")
                    self.warnings.append(f"{description}: {result['stderr']}")
                else:
                    logger.error(f"❌ {description} failed")

    def run_security_tests(self):
        """Run security-focused tests."""
        logger.info("🔒 Running Security Tests...")
        
        security_commands = [
            {
                'command': [sys.executable, '-m', 'bandit', '-r', 'bot/', '-f', 'json'],
                'description': 'Security Scan (Bandit)',
                'optional': True
            },
            {
                'command': [sys.executable, '-m', 'safety', 'check', '--json'],
                'description': 'Dependency Security Check (Safety)',
                'optional': True
            }
        ]
        
        for test_config in security_commands:
            result = self.run_command(test_config['command'], test_config['description'])
            self.test_results[test_config['description'].lower().replace(' ', '_').replace('(', '').replace(')', '')] = result
            
            if result['success']:
                logger.info(f"✅ {test_config['description']} passed")
            else:
                logger.warning(f"⚠️ {test_config['description']} failed (optional)")
                self.warnings.append(f"{test_config['description']}: {result['stderr']}")

    def run_code_quality_tests(self):
        """Run code quality tests."""
        logger.info("📊 Running Code Quality Tests...")
        
        quality_commands = [
            {
                'command': [sys.executable, '-m', 'flake8', 'bot/', '--max-line-length=120'],
                'description': 'Code Style Check (Flake8)',
                'optional': True
            },
            {
                'command': [sys.executable, '-m', 'pylint', 'bot/', '--output-format=json'],
                'description': 'Code Quality Check (Pylint)',
                'optional': True
            },
            {
                'command': [sys.executable, '-m', 'mypy', 'bot/', '--ignore-missing-imports'],
                'description': 'Type Checking (MyPy)',
                'optional': True
            }
        ]
        
        for test_config in quality_commands:
            result = self.run_command(test_config['command'], test_config['description'])
            self.test_results[test_config['description'].lower().replace(' ', '_').replace('(', '').replace(')', '')] = result
            
            if result['success']:
                logger.info(f"✅ {test_config['description']} passed")
            else:
                logger.warning(f"⚠️ {test_config['description']} failed (optional)")
                self.warnings.append(f"{test_config['description']}: {result['stderr']}")

    def run_performance_tests(self):
        """Run performance tests."""
        logger.info("⚡ Running Performance Tests...")
        
        # Simple performance test - check import times
        performance_result = self.run_command(
            [sys.executable, '-c', '''
import time
start = time.time()
try:
    import bot.main
    print(f"Bot import time: {time.time() - start:.3f}s")
except Exception as e:
    print(f"Import failed: {e}")
            '''],
            'Bot Import Performance Test'
        )
        
        self.test_results['performance_import'] = performance_result
        
        if performance_result['success']:
            logger.info("✅ Performance tests passed")
        else:
            logger.warning("⚠️ Performance tests failed")
            self.warnings.append(f"Performance test: {performance_result['stderr']}")

    def check_deployment_readiness(self):
        """Check deployment readiness."""
        logger.info("🚀 Checking Deployment Readiness...")
        
        # Check required files
        required_files = [
            'requirements.txt',
            'Procfile',
            '.env.example',
            'bot/main.py',
            'run_bot.py'
        ]
        
        missing_files = []
        for file_path in required_files:
            if not os.path.exists(file_path):
                missing_files.append(file_path)
        
        if missing_files:
            self.failed_tests.append(f"Missing deployment files: {missing_files}")
            logger.error(f"❌ Missing deployment files: {missing_files}")
        else:
            logger.info("✅ All deployment files present")
        
        # Check environment variables
        required_env_vars = [
            'BOT_TOKEN',
            'DEEPGRAM_API_KEY',
            'GOOGLE_AI_API_KEY'
        ]
        
        missing_env_vars = []
        for env_var in required_env_vars:
            if not os.getenv(env_var):
                missing_env_vars.append(env_var)
        
        if missing_env_vars:
            self.warnings.append(f"Missing environment variables: {missing_env_vars}")
            logger.warning(f"⚠️ Missing environment variables: {missing_env_vars}")
        else:
            logger.info("✅ All required environment variables present")

    async def run_all_tests(self):
        """Run all test categories."""
        logger.info("🎯 Starting Comprehensive Production Test Suite")
        
        # Run production readiness tests first
        await self.run_production_readiness_tests()
        
        # Run other test categories
        self.run_existing_tests()
        self.run_security_tests()
        self.run_code_quality_tests()
        self.run_performance_tests()
        self.check_deployment_readiness()
        
        return self.generate_final_report()

    def generate_final_report(self):
        """Generate final comprehensive report."""
        total_tests = len(self.test_results)
        successful_tests = sum(1 for result in self.test_results.values() 
                             if isinstance(result, dict) and result.get('success', False))
        
        # Determine overall status
        if self.failed_tests:
            overall_status = "❌ NOT READY FOR PRODUCTION"
            recommendation = "Critical issues must be resolved before deployment"
        elif len(self.warnings) > 10:
            overall_status = "⚠️ NEEDS ATTENTION"
            recommendation = "Address warnings before production deployment"
        else:
            overall_status = "✅ PRODUCTION READY"
            recommendation = "Bot appears ready for production deployment"
        
        report = {
            'overall_status': overall_status,
            'recommendation': recommendation,
            'summary': {
                'total_test_categories': total_tests,
                'successful_categories': successful_tests,
                'failed_tests': len(self.failed_tests),
                'warnings': len(self.warnings)
            },
            'failed_tests': self.failed_tests,
            'warnings': self.warnings,
            'detailed_results': self.test_results
        }
        
        return report

    def print_summary(self, report):
        """Print test summary."""
        print("\n" + "="*80)
        print("COMPREHENSIVE PRODUCTION TEST REPORT")
        print("="*80)
        print(f"Overall Status: {report['overall_status']}")
        print(f"Recommendation: {report['recommendation']}")
        print(f"Test Categories: {report['summary']['successful_categories']}/{report['summary']['total_test_categories']} passed")
        
        if report['failed_tests']:
            print(f"\n❌ CRITICAL FAILURES ({len(report['failed_tests'])}):")
            for failure in report['failed_tests'][:5]:  # Show first 5
                print(f"  • {failure}")
            if len(report['failed_tests']) > 5:
                print(f"  ... and {len(report['failed_tests']) - 5} more failures")
        
        if report['warnings']:
            print(f"\n⚠️ WARNINGS ({len(report['warnings'])}):")
            for warning in report['warnings'][:5]:  # Show first 5
                print(f"  • {warning}")
            if len(report['warnings']) > 5:
                print(f"  ... and {len(report['warnings']) - 5} more warnings")
        
        print(f"\n📄 Detailed report saved to: comprehensive_test_report.json")

async def main():
    """Main function."""
    runner = ProductionTestRunner()
    report = await runner.run_all_tests()
    
    # Save detailed report
    with open('comprehensive_test_report.json', 'w') as f:
        json.dump(report, f, indent=2)
    
    # Print summary
    runner.print_summary(report)
    
    # Exit with appropriate code
    if report['failed_tests']:
        sys.exit(1)
    else:
        sys.exit(0)

if __name__ == "__main__":
    asyncio.run(main())
