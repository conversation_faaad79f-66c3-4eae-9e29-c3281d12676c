"""
Provider registry for VoicePal.

This module provides a registry for managing all available providers.
"""

import os
import logging
import importlib
from typing import Dict, Any, Optional, List, Type, TypeVar, Generic, Union, get_type_hints
from pathlib import Path

from bot.providers.core.provider import Provider
from bot.providers.core.factory import ProviderFactory
from bot.providers.core.exceptions import (
    ProviderError,
    ProviderRegistryError,
    ProviderNotRegisteredError,
    ProviderAlreadyRegisteredError,
    ProviderTypeNotRegisteredError,
    ProviderTypeAlreadyRegisteredError
)

# Set up logging
logger = logging.getLogger(__name__)

# Type variables
T = TypeVar('T')
P = TypeVar('P', bound=Provider)

class ProviderRegistry:
    """Registry for managing all available providers.
    
    This class provides methods for registering and managing provider factories.
    """
    
    def __init__(self):
        """Initialize provider registry."""
        self.factories: Dict[str, ProviderFactory] = {}
        self.default_providers: Dict[str, str] = {}
    
    def register_provider_type(self, provider_type: str, default_provider: Optional[str] = None) -> ProviderFactory:
        """Register provider type.
        
        Args:
            provider_type: Provider type
            default_provider: Default provider name
            
        Returns:
            Provider factory
            
        Raises:
            ProviderTypeAlreadyRegisteredError: If provider type is already registered
        """
        if provider_type in self.factories:
            raise ProviderTypeAlreadyRegisteredError(f"Provider type '{provider_type}' is already registered")
        
        factory = ProviderFactory(provider_type, default_provider)
        self.factories[provider_type] = factory
        
        if default_provider:
            self.default_providers[provider_type] = default_provider
        
        logger.debug(f"Registered provider type '{provider_type}' with default provider '{default_provider}'")
        
        return factory
    
    def unregister_provider_type(self, provider_type: str) -> None:
        """Unregister provider type.
        
        Args:
            provider_type: Provider type
            
        Raises:
            ProviderTypeNotRegisteredError: If provider type is not registered
        """
        if provider_type not in self.factories:
            raise ProviderTypeNotRegisteredError(f"Provider type '{provider_type}' is not registered")
        
        # Shutdown all provider instances
        self.factories[provider_type].shutdown_all()
        
        # Remove factory
        del self.factories[provider_type]
        
        # Remove default provider
        if provider_type in self.default_providers:
            del self.default_providers[provider_type]
        
        logger.debug(f"Unregistered provider type '{provider_type}'")
    
    def get_factory(self, provider_type: str) -> ProviderFactory:
        """Get provider factory.
        
        Args:
            provider_type: Provider type
            
        Returns:
            Provider factory
            
        Raises:
            ProviderTypeNotRegisteredError: If provider type is not registered
        """
        if provider_type not in self.factories:
            raise ProviderTypeNotRegisteredError(f"Provider type '{provider_type}' is not registered")
        
        return self.factories[provider_type]
    
    def register_provider(self, provider_type: str, name: str, provider_class: Type[Provider]) -> None:
        """Register provider.
        
        Args:
            provider_type: Provider type
            name: Provider name
            provider_class: Provider class
            
        Raises:
            ProviderTypeNotRegisteredError: If provider type is not registered
            ProviderAlreadyRegisteredError: If provider is already registered
        """
        factory = self.get_factory(provider_type)
        factory.register_provider(name, provider_class)
        logger.debug(f"Registered provider '{name}' of type '{provider_type}'")
    
    def register_provider_from_module(self, provider_type: str, name: str, module_path: str, class_name: str) -> None:
        """Register provider from module.
        
        Args:
            provider_type: Provider type
            name: Provider name
            module_path: Module path
            class_name: Provider class name
            
        Raises:
            ProviderTypeNotRegisteredError: If provider type is not registered
            ProviderAlreadyRegisteredError: If provider is already registered
            ProviderRegistryError: If module or class not found
        """
        factory = self.get_factory(provider_type)
        factory.register_provider_from_module(name, module_path, class_name)
        logger.debug(f"Registered provider '{name}' of type '{provider_type}' from module '{module_path}.{class_name}'")
    
    def unregister_provider(self, provider_type: str, name: str) -> None:
        """Unregister provider.
        
        Args:
            provider_type: Provider type
            name: Provider name
            
        Raises:
            ProviderTypeNotRegisteredError: If provider type is not registered
            ProviderNotRegisteredError: If provider is not registered
        """
        factory = self.get_factory(provider_type)
        factory.unregister_provider(name)
        
        # Update default provider if needed
        if provider_type in self.default_providers and self.default_providers[provider_type] == name:
            del self.default_providers[provider_type]
        
        logger.debug(f"Unregistered provider '{name}' of type '{provider_type}'")
    
    def set_default_provider(self, provider_type: str, name: str) -> None:
        """Set default provider.
        
        Args:
            provider_type: Provider type
            name: Provider name
            
        Raises:
            ProviderTypeNotRegisteredError: If provider type is not registered
            ProviderNotRegisteredError: If provider is not registered
        """
        factory = self.get_factory(provider_type)
        
        # Check if provider is registered
        factory.get_provider_class(name)
        
        # Set default provider
        factory.default_provider = name
        self.default_providers[provider_type] = name
        
        logger.debug(f"Set default provider '{name}' for type '{provider_type}'")
    
    def get_default_provider(self, provider_type: str) -> Optional[str]:
        """Get default provider.
        
        Args:
            provider_type: Provider type
            
        Returns:
            Default provider name or None if not set
            
        Raises:
            ProviderTypeNotRegisteredError: If provider type is not registered
        """
        if provider_type not in self.factories:
            raise ProviderTypeNotRegisteredError(f"Provider type '{provider_type}' is not registered")
        
        return self.factories[provider_type].default_provider
    
    def create_provider(self, provider_type: str, config: Any, name: Optional[str] = None, initialize: bool = True) -> Provider:
        """Create provider instance.
        
        Args:
            provider_type: Provider type
            config: Provider configuration
            name: Provider name (default: default provider)
            initialize: Whether to initialize provider
            
        Returns:
            Provider instance
            
        Raises:
            ProviderTypeNotRegisteredError: If provider type is not registered
            ProviderNotRegisteredError: If provider is not registered
            ProviderError: If provider creation fails
        """
        factory = self.get_factory(provider_type)
        return factory.create_provider(config, name, initialize)
    
    def get_or_create_provider(self, provider_type: str, config: Any, name: Optional[str] = None, initialize: bool = True) -> Provider:
        """Get existing provider instance or create a new one.
        
        Args:
            provider_type: Provider type
            config: Provider configuration
            name: Provider name (default: default provider)
            initialize: Whether to initialize provider
            
        Returns:
            Provider instance
            
        Raises:
            ProviderTypeNotRegisteredError: If provider type is not registered
            ProviderNotRegisteredError: If provider is not registered
            ProviderError: If provider creation fails
        """
        factory = self.get_factory(provider_type)
        return factory.get_or_create_provider(config, name, initialize)
    
    def get_provider(self, provider_type: str, name: Optional[str] = None) -> Provider:
        """Get existing provider instance.
        
        Args:
            provider_type: Provider type
            name: Provider name (default: default provider)
            
        Returns:
            Provider instance
            
        Raises:
            ProviderTypeNotRegisteredError: If provider type is not registered
            ProviderNotRegisteredError: If provider is not registered
            ProviderNotFoundError: If provider instance does not exist
        """
        factory = self.get_factory(provider_type)
        return factory.get_provider(name)
    
    def shutdown_provider(self, provider_type: str, name: Optional[str] = None) -> None:
        """Shutdown provider instance.
        
        Args:
            provider_type: Provider type
            name: Provider name (default: default provider)
            
        Raises:
            ProviderTypeNotRegisteredError: If provider type is not registered
            ProviderNotRegisteredError: If provider is not registered
            ProviderNotFoundError: If provider instance does not exist
        """
        factory = self.get_factory(provider_type)
        factory.shutdown_provider(name)
    
    def shutdown_all(self) -> None:
        """Shutdown all provider instances."""
        for factory in self.factories.values():
            factory.shutdown_all()
    
    def get_registered_provider_types(self) -> List[str]:
        """Get list of registered provider types.
        
        Returns:
            List of registered provider types
        """
        return list(self.factories.keys())
    
    def get_registered_providers(self, provider_type: str) -> List[str]:
        """Get list of registered provider names for a provider type.
        
        Args:
            provider_type: Provider type
            
        Returns:
            List of registered provider names
            
        Raises:
            ProviderTypeNotRegisteredError: If provider type is not registered
        """
        factory = self.get_factory(provider_type)
        return factory.get_registered_providers()
    
    def get_active_providers(self, provider_type: str) -> List[str]:
        """Get list of active provider instance names for a provider type.
        
        Args:
            provider_type: Provider type
            
        Returns:
            List of active provider instance names
            
        Raises:
            ProviderTypeNotRegisteredError: If provider type is not registered
        """
        factory = self.get_factory(provider_type)
        return factory.get_active_providers()
    
    def discover_providers(self, package_path: str) -> Dict[str, List[str]]:
        """Discover and register providers from a package.
        
        Args:
            package_path: Package path
            
        Returns:
            Dictionary of provider types and names
            
        Raises:
            ProviderRegistryError: If provider discovery fails
        """
        try:
            # Import package
            package = importlib.import_module(package_path)
            
            # Get package directory
            package_dir = Path(package.__file__).parent
            
            # Discover provider types
            discovered_providers = {}
            
            for provider_type_dir in package_dir.iterdir():
                if provider_type_dir.is_dir() and not provider_type_dir.name.startswith("__"):
                    provider_type = provider_type_dir.name
                    
                    # Register provider type if not already registered
                    if provider_type not in self.factories:
                        self.register_provider_type(provider_type)
                    
                    # Discover providers
                    discovered_providers[provider_type] = []
                    
                    for provider_file in provider_type_dir.glob("*_provider.py"):
                        # Get provider name
                        provider_name = provider_file.stem.replace("_provider", "")
                        
                        # Get module path
                        module_path = f"{package_path}.{provider_type}.{provider_file.stem}"
                        
                        # Get class name
                        class_name = "".join(word.capitalize() for word in provider_name.split("_")) + "Provider"
                        
                        try:
                            # Register provider
                            self.register_provider_from_module(provider_type, provider_name, module_path, class_name)
                            discovered_providers[provider_type].append(provider_name)
                        except Exception as e:
                            logger.warning(f"Failed to register provider '{provider_name}' of type '{provider_type}': {e}")
            
            return discovered_providers
        except Exception as e:
            logger.error(f"Failed to discover providers from package '{package_path}': {e}")
            raise ProviderRegistryError(f"Failed to discover providers from package '{package_path}': {e}") from e
    
    def __del__(self):
        """Destructor."""
        self.shutdown_all()
