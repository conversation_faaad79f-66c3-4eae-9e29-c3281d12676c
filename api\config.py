"""
API configuration for VoicePal.

This module provides configuration for the VoicePal API.
"""

import os
from typing import List, Optional
from dataclasses import dataclass, field

@dataclass
class APIConfig:
    """Configuration for the VoicePal API."""
    
    # Server configuration
    host: str = field(default_factory=lambda: os.getenv("API_HOST", "0.0.0.0"))
    port: int = field(default_factory=lambda: int(os.getenv("API_PORT", "8000")))
    reload: bool = field(default_factory=lambda: os.getenv("API_RELOAD", "false").lower() == "true")
    log_level: str = field(default_factory=lambda: os.getenv("API_LOG_LEVEL", "info"))
    
    # Security configuration
    api_key: Optional[str] = field(default_factory=lambda: os.getenv("API_KEY"))
    admin_api_key: Optional[str] = field(default_factory=lambda: os.getenv("ADMIN_API_KEY"))
    webhook_secret: Optional[str] = field(default_factory=lambda: os.getenv("WEBHOOK_SECRET"))
    
    # CORS configuration
    cors_origins: List[str] = field(default_factory=lambda: [
        origin.strip() for origin in os.getenv("CORS_ORIGINS", "*").split(",")
    ])
    
    # Rate limiting configuration
    rate_limit_enabled: bool = field(default_factory=lambda: os.getenv("RATE_LIMIT_ENABLED", "true").lower() == "true")
    rate_limit_requests: int = field(default_factory=lambda: int(os.getenv("RATE_LIMIT_REQUESTS", "100")))
    rate_limit_window: int = field(default_factory=lambda: int(os.getenv("RATE_LIMIT_WINDOW", "60")))
    
    # Documentation configuration
    enable_docs: bool = field(default_factory=lambda: os.getenv("ENABLE_DOCS", "true").lower() == "true")
    
    # Metrics configuration
    metrics_enabled: bool = field(default_factory=lambda: os.getenv("METRICS_ENABLED", "true").lower() == "true")
    metrics_path: str = field(default_factory=lambda: os.getenv("METRICS_PATH", "/metrics"))
    
    # Webhook configuration
    webhook_timeout: int = field(default_factory=lambda: int(os.getenv("WEBHOOK_TIMEOUT", "30")))
    webhook_retry_attempts: int = field(default_factory=lambda: int(os.getenv("WEBHOOK_RETRY_ATTEMPTS", "3")))
    
    # Admin configuration
    admin_enabled: bool = field(default_factory=lambda: os.getenv("ADMIN_ENABLED", "true").lower() == "true")
    admin_user_ids: List[int] = field(default_factory=lambda: [
        int(user_id.strip()) for user_id in os.getenv("ADMIN_USER_IDS", "").split(",")
        if user_id.strip().isdigit()
    ])
    
    def validate(self) -> List[str]:
        """
        Validate the configuration.
        
        Returns:
            List of validation error messages
        """
        errors = []
        
        # Validate port
        if not (1 <= self.port <= 65535):
            errors.append(f"Invalid port: {self.port}. Must be between 1 and 65535.")
        
        # Validate rate limiting
        if self.rate_limit_enabled:
            if self.rate_limit_requests <= 0:
                errors.append(f"Invalid rate limit requests: {self.rate_limit_requests}. Must be positive.")
            if self.rate_limit_window <= 0:
                errors.append(f"Invalid rate limit window: {self.rate_limit_window}. Must be positive.")
        
        # Validate webhook configuration
        if self.webhook_timeout <= 0:
            errors.append(f"Invalid webhook timeout: {self.webhook_timeout}. Must be positive.")
        if self.webhook_retry_attempts < 0:
            errors.append(f"Invalid webhook retry attempts: {self.webhook_retry_attempts}. Must be non-negative.")
        
        return errors
    
    def is_admin_user(self, user_id: int) -> bool:
        """
        Check if a user is an admin.
        
        Args:
            user_id: User ID to check
            
        Returns:
            True if user is admin, False otherwise
        """
        return user_id in self.admin_user_ids
    
    def get_cors_origins(self) -> List[str]:
        """
        Get CORS origins.
        
        Returns:
            List of allowed CORS origins
        """
        if "*" in self.cors_origins:
            return ["*"]
        return self.cors_origins
    
    @classmethod
    def from_env(cls) -> "APIConfig":
        """
        Create configuration from environment variables.
        
        Returns:
            APIConfig instance
        """
        return cls()
    
    def to_dict(self) -> dict:
        """
        Convert configuration to dictionary.
        
        Returns:
            Configuration as dictionary
        """
        return {
            "host": self.host,
            "port": self.port,
            "reload": self.reload,
            "log_level": self.log_level,
            "rate_limit_enabled": self.rate_limit_enabled,
            "rate_limit_requests": self.rate_limit_requests,
            "rate_limit_window": self.rate_limit_window,
            "enable_docs": self.enable_docs,
            "metrics_enabled": self.metrics_enabled,
            "metrics_path": self.metrics_path,
            "webhook_timeout": self.webhook_timeout,
            "webhook_retry_attempts": self.webhook_retry_attempts,
            "admin_enabled": self.admin_enabled,
            "cors_origins": self.cors_origins
        }
