"""
User repositories for VoicePal.

This module provides the user-related repositories for the VoicePal database.
"""

import logging
from typing import Dict, List, Any, Optional, Union
from datetime import datetime

from bot.database.core.connection import DatabaseConnection
from bot.database.core.exceptions import (
    DatabaseError,
    DatabaseNotFoundError,
    DatabaseDuplicateError
)
from bot.database.repositories.repository import Repository
from bot.database.models.user import User, UserPreference, UserStat

# Set up logging
logger = logging.getLogger(__name__)

class UserRepository(Repository[User]):
    """Repository for User model."""
    
    _model_class = User
    
    def find_by_username(self, username: str) -> Optional[User]:
        """Find user by username.
        
        Args:
            username: Username to search for
            
        Returns:
            User instance or None if not found
            
        Raises:
            DatabaseError: If query fails
        """
        return self.find_one({"username": username})
    
    def find_active_users(self) -> List[User]:
        """Find all active users.
        
        Returns:
            List of active users
            
        Raises:
            DatabaseError: If query fails
        """
        return self.find_all({"is_active": True})
    
    def find_users_with_credits(self, min_credits: int = 0) -> List[User]:
        """Find users with at least the specified number of credits.
        
        Args:
            min_credits: Minimum number of credits
            
        Returns:
            List of users with sufficient credits
            
        Raises:
            DatabaseError: If query fails
        """
        try:
            query = f"""
                SELECT * FROM {self._model_class.get_table_name()}
                WHERE credits >= ?
                ORDER BY credits DESC
            """
            
            cursor = self.connection.execute(query, (min_credits,))
            rows = cursor.fetchall()
            
            # Convert rows to model instances
            users = []
            for row in cursor:
                data = dict(row)
                user = self._model_class.from_dict(data)
                users.append(user)
            
            return users
        except Exception as e:
            logger.error(f"Failed to find users with credits: {e}")
            raise DatabaseError(f"Failed to find users with credits: {e}") from e
    
    def update_last_interaction(self, user_id: str) -> Optional[User]:
        """Update user's last interaction timestamp.
        
        Args:
            user_id: User ID
            
        Returns:
            Updated user or None if not found
            
        Raises:
            DatabaseError: If update fails
        """
        try:
            user = self.find_by_id(user_id)
            if not user:
                return None
            
            user.update_last_interaction()
            return self.update(user)
        except Exception as e:
            logger.error(f"Failed to update last interaction: {e}")
            raise DatabaseError(f"Failed to update last interaction: {e}") from e
    
    def add_credits(self, user_id: str, amount: int) -> Optional[User]:
        """Add credits to user's balance.
        
        Args:
            user_id: User ID
            amount: Amount to add
            
        Returns:
            Updated user or None if not found
            
        Raises:
            DatabaseError: If update fails
            ValueError: If amount is negative
        """
        if amount < 0:
            raise ValueError("Amount must be positive")
        
        try:
            user = self.find_by_id(user_id)
            if not user:
                return None
            
            user.add_credits(amount)
            return self.update(user)
        except Exception as e:
            logger.error(f"Failed to add credits: {e}")
            raise DatabaseError(f"Failed to add credits: {e}") from e
    
    def use_credits(self, user_id: str, amount: int) -> Optional[User]:
        """Use credits from user's balance.
        
        Args:
            user_id: User ID
            amount: Amount to use
            
        Returns:
            Updated user or None if not found
            
        Raises:
            DatabaseError: If update fails
            ValueError: If amount is negative or user has insufficient credits
        """
        if amount < 0:
            raise ValueError("Amount must be positive")
        
        try:
            user = self.find_by_id(user_id)
            if not user:
                return None
            
            user.use_credits(amount)
            return self.update(user)
        except ValueError as e:
            logger.error(f"Failed to use credits: {e}")
            raise
        except Exception as e:
            logger.error(f"Failed to use credits: {e}")
            raise DatabaseError(f"Failed to use credits: {e}") from e

class UserPreferenceRepository(Repository[UserPreference]):
    """Repository for UserPreference model."""
    
    _model_class = UserPreference
    
    def find_by_user_and_key(self, user_id: str, key: str) -> Optional[UserPreference]:
        """Find user preference by user ID and key.
        
        Args:
            user_id: User ID
            key: Preference key
            
        Returns:
            UserPreference instance or None if not found
            
        Raises:
            DatabaseError: If query fails
        """
        return self.find_one({"user_id": user_id, "key": key})
    
    def find_by_user(self, user_id: str) -> List[UserPreference]:
        """Find all preferences for a user.
        
        Args:
            user_id: User ID
            
        Returns:
            List of user preferences
            
        Raises:
            DatabaseError: If query fails
        """
        return self.find_all({"user_id": user_id})
    
    def set_preference(self, user_id: str, key: str, value: str) -> UserPreference:
        """Set user preference.
        
        Args:
            user_id: User ID
            key: Preference key
            value: Preference value
            
        Returns:
            UserPreference instance
            
        Raises:
            DatabaseError: If update fails
        """
        try:
            preference = self.find_by_user_and_key(user_id, key)
            
            if preference:
                preference.value = value
                preference.updated_at = datetime.now().isoformat()
                return self.update(preference)
            else:
                preference = UserPreference(
                    user_id=user_id,
                    key=key,
                    value=value
                )
                return self.create(preference)
        except Exception as e:
            logger.error(f"Failed to set preference: {e}")
            raise DatabaseError(f"Failed to set preference: {e}") from e
    
    def get_preference(self, user_id: str, key: str, default: Optional[str] = None) -> Optional[str]:
        """Get user preference value.
        
        Args:
            user_id: User ID
            key: Preference key
            default: Default value if preference not found
            
        Returns:
            Preference value or default
            
        Raises:
            DatabaseError: If query fails
        """
        try:
            preference = self.find_by_user_and_key(user_id, key)
            return preference.value if preference else default
        except Exception as e:
            logger.error(f"Failed to get preference: {e}")
            raise DatabaseError(f"Failed to get preference: {e}") from e
    
    def delete_preference(self, user_id: str, key: str) -> bool:
        """Delete user preference.
        
        Args:
            user_id: User ID
            key: Preference key
            
        Returns:
            True if preference was deleted, False otherwise
            
        Raises:
            DatabaseError: If deletion fails
        """
        try:
            preference = self.find_by_user_and_key(user_id, key)
            if not preference:
                return False
            
            return self.delete(preference)
        except Exception as e:
            logger.error(f"Failed to delete preference: {e}")
            raise DatabaseError(f"Failed to delete preference: {e}") from e

class UserStatRepository(Repository[UserStat]):
    """Repository for UserStat model."""
    
    _model_class = UserStat
    
    def find_by_user_and_key(self, user_id: str, key: str) -> Optional[UserStat]:
        """Find user stat by user ID and key.
        
        Args:
            user_id: User ID
            key: Stat key
            
        Returns:
            UserStat instance or None if not found
            
        Raises:
            DatabaseError: If query fails
        """
        return self.find_one({"user_id": user_id, "key": key})
    
    def find_by_user(self, user_id: str) -> List[UserStat]:
        """Find all stats for a user.
        
        Args:
            user_id: User ID
            
        Returns:
            List of user stats
            
        Raises:
            DatabaseError: If query fails
        """
        return self.find_all({"user_id": user_id})
    
    def increment_stat(self, user_id: str, key: str, amount: int = 1) -> UserStat:
        """Increment user stat.
        
        Args:
            user_id: User ID
            key: Stat key
            amount: Amount to increment
            
        Returns:
            UserStat instance
            
        Raises:
            DatabaseError: If update fails
        """
        try:
            stat = self.find_by_user_and_key(user_id, key)
            
            if stat:
                stat.increment(amount)
                return self.update(stat)
            else:
                stat = UserStat(
                    user_id=user_id,
                    key=key,
                    value=amount
                )
                return self.create(stat)
        except Exception as e:
            logger.error(f"Failed to increment stat: {e}")
            raise DatabaseError(f"Failed to increment stat: {e}") from e
    
    def get_stat(self, user_id: str, key: str, default: int = 0) -> int:
        """Get user stat value.
        
        Args:
            user_id: User ID
            key: Stat key
            default: Default value if stat not found
            
        Returns:
            Stat value or default
            
        Raises:
            DatabaseError: If query fails
        """
        try:
            stat = self.find_by_user_and_key(user_id, key)
            return stat.value if stat else default
        except Exception as e:
            logger.error(f"Failed to get stat: {e}")
            raise DatabaseError(f"Failed to get stat: {e}") from e
    
    def reset_stat(self, user_id: str, key: str) -> Optional[UserStat]:
        """Reset user stat to zero.
        
        Args:
            user_id: User ID
            key: Stat key
            
        Returns:
            UserStat instance or None if not found
            
        Raises:
            DatabaseError: If update fails
        """
        try:
            stat = self.find_by_user_and_key(user_id, key)
            if not stat:
                return None
            
            stat.reset()
            return self.update(stat)
        except Exception as e:
            logger.error(f"Failed to reset stat: {e}")
            raise DatabaseError(f"Failed to reset stat: {e}") from e
