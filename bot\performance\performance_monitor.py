"""
Performance monitoring for VoicePal.

This module provides comprehensive performance monitoring and metrics collection.
"""

import time
import logging
import psutil
import threading
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from collections import deque, defaultdict
import statistics

logger = logging.getLogger(__name__)

@dataclass
class PerformanceMetric:
    """Performance metric data point."""
    timestamp: float
    value: float
    tags: Dict[str, str] = field(default_factory=dict)

@dataclass
class SystemMetrics:
    """System performance metrics."""
    cpu_percent: float
    memory_percent: float
    disk_percent: float
    network_io: Dict[str, int]
    process_count: int
    load_average: Optional[List[float]]
    timestamp: float

@dataclass
class ApplicationMetrics:
    """Application performance metrics."""
    response_time_avg: float
    response_time_p95: float
    response_time_p99: float
    requests_per_second: float
    error_rate: float
    active_users: int
    database_connections: int
    cache_hit_rate: float
    memory_usage_mb: float
    timestamp: float

class PerformanceMonitor:
    """Performance monitoring system."""
    
    def __init__(
        self,
        collection_interval: int = 60,
        retention_period: int = 86400,  # 24 hours
        alert_thresholds: Optional[Dict[str, float]] = None
    ):
        """
        Initialize performance monitor.
        
        Args:
            collection_interval: Metrics collection interval in seconds
            retention_period: Metrics retention period in seconds
            alert_thresholds: Alert thresholds for various metrics
        """
        self.collection_interval = collection_interval
        self.retention_period = retention_period
        
        # Default alert thresholds
        self.alert_thresholds = alert_thresholds or {
            "cpu_percent": 80.0,
            "memory_percent": 85.0,
            "disk_percent": 90.0,
            "response_time_p95": 5.0,  # 5 seconds
            "error_rate": 0.05,  # 5%
            "cache_hit_rate": 0.7  # 70%
        }
        
        # Metrics storage
        self.system_metrics: deque = deque(maxlen=int(retention_period / collection_interval))
        self.application_metrics: deque = deque(maxlen=int(retention_period / collection_interval))
        self.custom_metrics: Dict[str, deque] = defaultdict(
            lambda: deque(maxlen=int(retention_period / collection_interval))
        )
        
        # Response time tracking
        self.response_times: deque = deque(maxlen=1000)
        self.request_count = 0
        self.error_count = 0
        self.last_request_time = time.time()
        
        # Alert callbacks
        self.alert_callbacks: List[Callable] = []
        
        # Monitoring thread
        self.monitoring_thread: Optional[threading.Thread] = None
        self.is_monitoring = False
        
        # Lock for thread safety
        self.lock = threading.RLock()
        
        logger.info(f"Performance monitor initialized: {collection_interval}s interval")
    
    def start_monitoring(self):
        """Start performance monitoring."""
        if self.is_monitoring:
            return
        
        self.is_monitoring = True
        self.monitoring_thread = threading.Thread(
            target=self._monitoring_loop,
            daemon=True,
            name="PerformanceMonitor"
        )
        self.monitoring_thread.start()
        
        logger.info("Performance monitoring started")
    
    def stop_monitoring(self):
        """Stop performance monitoring."""
        self.is_monitoring = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5)
        
        logger.info("Performance monitoring stopped")
    
    def _monitoring_loop(self):
        """Main monitoring loop."""
        while self.is_monitoring:
            try:
                # Collect system metrics
                system_metrics = self._collect_system_metrics()
                with self.lock:
                    self.system_metrics.append(system_metrics)
                
                # Collect application metrics
                app_metrics = self._collect_application_metrics()
                with self.lock:
                    self.application_metrics.append(app_metrics)
                
                # Check alerts
                self._check_alerts(system_metrics, app_metrics)
                
                # Clean up old metrics
                self._cleanup_old_metrics()
                
                # Sleep until next collection
                time.sleep(self.collection_interval)
                
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                time.sleep(self.collection_interval)
    
    def _collect_system_metrics(self) -> SystemMetrics:
        """Collect system performance metrics."""
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # Memory usage
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            
            # Disk usage
            disk = psutil.disk_usage('/')
            disk_percent = disk.percent
            
            # Network I/O
            network = psutil.net_io_counters()
            network_io = {
                "bytes_sent": network.bytes_sent,
                "bytes_recv": network.bytes_recv,
                "packets_sent": network.packets_sent,
                "packets_recv": network.packets_recv
            }
            
            # Process count
            process_count = len(psutil.pids())
            
            # Load average (Unix only)
            load_average = None
            if hasattr(psutil, 'getloadavg'):
                load_average = list(psutil.getloadavg())
            
            return SystemMetrics(
                cpu_percent=cpu_percent,
                memory_percent=memory_percent,
                disk_percent=disk_percent,
                network_io=network_io,
                process_count=process_count,
                load_average=load_average,
                timestamp=time.time()
            )
            
        except Exception as e:
            logger.error(f"Failed to collect system metrics: {e}")
            return SystemMetrics(
                cpu_percent=0.0,
                memory_percent=0.0,
                disk_percent=0.0,
                network_io={},
                process_count=0,
                load_average=None,
                timestamp=time.time()
            )
    
    def _collect_application_metrics(self) -> ApplicationMetrics:
        """Collect application performance metrics."""
        try:
            current_time = time.time()
            
            # Calculate response time metrics
            with self.lock:
                if self.response_times:
                    response_times_list = list(self.response_times)
                    response_time_avg = statistics.mean(response_times_list)
                    response_time_p95 = statistics.quantiles(response_times_list, n=20)[18]  # 95th percentile
                    response_time_p99 = statistics.quantiles(response_times_list, n=100)[98]  # 99th percentile
                else:
                    response_time_avg = response_time_p95 = response_time_p99 = 0.0
                
                # Calculate requests per second
                time_window = current_time - self.last_request_time
                if time_window > 0:
                    requests_per_second = len(self.response_times) / min(time_window, 60)  # Last minute
                else:
                    requests_per_second = 0.0
                
                # Calculate error rate
                total_requests = self.request_count
                if total_requests > 0:
                    error_rate = self.error_count / total_requests
                else:
                    error_rate = 0.0
            
            # Get process memory usage
            process = psutil.Process()
            memory_usage_mb = process.memory_info().rss / 1024 / 1024
            
            return ApplicationMetrics(
                response_time_avg=response_time_avg,
                response_time_p95=response_time_p95,
                response_time_p99=response_time_p99,
                requests_per_second=requests_per_second,
                error_rate=error_rate,
                active_users=0,  # To be updated by application
                database_connections=0,  # To be updated by application
                cache_hit_rate=0.0,  # To be updated by application
                memory_usage_mb=memory_usage_mb,
                timestamp=current_time
            )
            
        except Exception as e:
            logger.error(f"Failed to collect application metrics: {e}")
            return ApplicationMetrics(
                response_time_avg=0.0,
                response_time_p95=0.0,
                response_time_p99=0.0,
                requests_per_second=0.0,
                error_rate=0.0,
                active_users=0,
                database_connections=0,
                cache_hit_rate=0.0,
                memory_usage_mb=0.0,
                timestamp=time.time()
            )
    
    def _check_alerts(self, system_metrics: SystemMetrics, app_metrics: ApplicationMetrics):
        """Check for alert conditions."""
        alerts = []
        
        # System alerts
        if system_metrics.cpu_percent > self.alert_thresholds["cpu_percent"]:
            alerts.append({
                "type": "cpu_high",
                "message": f"High CPU usage: {system_metrics.cpu_percent:.1f}%",
                "value": system_metrics.cpu_percent,
                "threshold": self.alert_thresholds["cpu_percent"]
            })
        
        if system_metrics.memory_percent > self.alert_thresholds["memory_percent"]:
            alerts.append({
                "type": "memory_high",
                "message": f"High memory usage: {system_metrics.memory_percent:.1f}%",
                "value": system_metrics.memory_percent,
                "threshold": self.alert_thresholds["memory_percent"]
            })
        
        if system_metrics.disk_percent > self.alert_thresholds["disk_percent"]:
            alerts.append({
                "type": "disk_high",
                "message": f"High disk usage: {system_metrics.disk_percent:.1f}%",
                "value": system_metrics.disk_percent,
                "threshold": self.alert_thresholds["disk_percent"]
            })
        
        # Application alerts
        if app_metrics.response_time_p95 > self.alert_thresholds["response_time_p95"]:
            alerts.append({
                "type": "response_time_high",
                "message": f"High response time (P95): {app_metrics.response_time_p95:.2f}s",
                "value": app_metrics.response_time_p95,
                "threshold": self.alert_thresholds["response_time_p95"]
            })
        
        if app_metrics.error_rate > self.alert_thresholds["error_rate"]:
            alerts.append({
                "type": "error_rate_high",
                "message": f"High error rate: {app_metrics.error_rate:.2%}",
                "value": app_metrics.error_rate,
                "threshold": self.alert_thresholds["error_rate"]
            })
        
        if app_metrics.cache_hit_rate < self.alert_thresholds["cache_hit_rate"]:
            alerts.append({
                "type": "cache_hit_rate_low",
                "message": f"Low cache hit rate: {app_metrics.cache_hit_rate:.2%}",
                "value": app_metrics.cache_hit_rate,
                "threshold": self.alert_thresholds["cache_hit_rate"]
            })
        
        # Trigger alert callbacks
        for alert in alerts:
            self._trigger_alert(alert)
    
    def _trigger_alert(self, alert: Dict[str, Any]):
        """Trigger alert callbacks."""
        for callback in self.alert_callbacks:
            try:
                callback(alert)
            except Exception as e:
                logger.error(f"Alert callback failed: {e}")
    
    def _cleanup_old_metrics(self):
        """Clean up old metrics beyond retention period."""
        cutoff_time = time.time() - self.retention_period
        
        with self.lock:
            # Clean up response times
            while self.response_times and self.response_times[0] < cutoff_time:
                self.response_times.popleft()
    
    def record_request(self, response_time: float, success: bool = True):
        """
        Record a request for performance tracking.
        
        Args:
            response_time: Request response time in seconds
            success: Whether the request was successful
        """
        with self.lock:
            self.response_times.append(response_time)
            self.request_count += 1
            if not success:
                self.error_count += 1
            self.last_request_time = time.time()
    
    def record_custom_metric(self, name: str, value: float, tags: Optional[Dict[str, str]] = None):
        """
        Record a custom metric.
        
        Args:
            name: Metric name
            value: Metric value
            tags: Optional tags for the metric
        """
        metric = PerformanceMetric(
            timestamp=time.time(),
            value=value,
            tags=tags or {}
        )
        
        with self.lock:
            self.custom_metrics[name].append(metric)
    
    def update_application_metric(self, metric_name: str, value: float):
        """
        Update application metric.
        
        Args:
            metric_name: Name of the metric
            value: Metric value
        """
        with self.lock:
            if self.application_metrics:
                latest_metrics = self.application_metrics[-1]
                setattr(latest_metrics, metric_name, value)
    
    def get_current_metrics(self) -> Dict[str, Any]:
        """
        Get current performance metrics.
        
        Returns:
            Dictionary of current metrics
        """
        with self.lock:
            current_system = self.system_metrics[-1] if self.system_metrics else None
            current_app = self.application_metrics[-1] if self.application_metrics else None
            
            return {
                "system": current_system.__dict__ if current_system else {},
                "application": current_app.__dict__ if current_app else {},
                "custom": {
                    name: metrics[-1].__dict__ if metrics else {}
                    for name, metrics in self.custom_metrics.items()
                }
            }
    
    def get_metrics_history(self, hours: int = 1) -> Dict[str, List[Dict[str, Any]]]:
        """
        Get metrics history for the specified time period.
        
        Args:
            hours: Number of hours of history to return
            
        Returns:
            Dictionary of metrics history
        """
        cutoff_time = time.time() - (hours * 3600)
        
        with self.lock:
            system_history = [
                m.__dict__ for m in self.system_metrics
                if m.timestamp >= cutoff_time
            ]
            
            app_history = [
                m.__dict__ for m in self.application_metrics
                if m.timestamp >= cutoff_time
            ]
            
            custom_history = {}
            for name, metrics in self.custom_metrics.items():
                custom_history[name] = [
                    m.__dict__ for m in metrics
                    if m.timestamp >= cutoff_time
                ]
            
            return {
                "system": system_history,
                "application": app_history,
                "custom": custom_history
            }
    
    def add_alert_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """
        Add alert callback function.
        
        Args:
            callback: Function to call when alert is triggered
        """
        self.alert_callbacks.append(callback)
    
    def remove_alert_callback(self, callback: Callable):
        """
        Remove alert callback function.
        
        Args:
            callback: Function to remove
        """
        if callback in self.alert_callbacks:
            self.alert_callbacks.remove(callback)
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """
        Get performance summary.
        
        Returns:
            Performance summary dictionary
        """
        with self.lock:
            if not self.system_metrics or not self.application_metrics:
                return {}
            
            # Calculate averages over last hour
            hour_ago = time.time() - 3600
            recent_system = [m for m in self.system_metrics if m.timestamp >= hour_ago]
            recent_app = [m for m in self.application_metrics if m.timestamp >= hour_ago]
            
            if not recent_system or not recent_app:
                return {}
            
            return {
                "avg_cpu_percent": statistics.mean(m.cpu_percent for m in recent_system),
                "avg_memory_percent": statistics.mean(m.memory_percent for m in recent_system),
                "avg_response_time": statistics.mean(m.response_time_avg for m in recent_app),
                "avg_requests_per_second": statistics.mean(m.requests_per_second for m in recent_app),
                "avg_error_rate": statistics.mean(m.error_rate for m in recent_app),
                "total_requests": self.request_count,
                "total_errors": self.error_count,
                "uptime_hours": (time.time() - recent_system[0].timestamp) / 3600
            }
