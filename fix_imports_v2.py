"""
<PERSON><PERSON>t to fix imports in the VoicePal bot codebase.
"""

import os
import re
import sys

def fix_imports(file_path):
    """
    Fix imports in a file.
    
    Args:
        file_path: Path to the file to fix
    """
    print(f"Fixing imports in {file_path}")
    
    # Read the file
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Fix imports
    content = content.replace(
        'from bot.features.feature_registry import FeatureRegistry',
        'from bot.core.feature_registry import FeatureRegistry'
    )
    content = content.replace(
        'from bot.core.dialog_engine import DialogEngine',
        'from bot.core.enhanced_dialog_engine import EnhancedDialogEngine as DialogEngine'
    )
    
    # Write the file
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"Fixed imports in {file_path}")

def main():
    """Main function."""
    # Fix imports in initialization_manager.py
    fix_imports('bot/core/initialization_manager.py')
    
    # Fix imports in main.py
    fix_imports('bot/main.py')
    
    print("Done!")

if __name__ == "__main__":
    main()
