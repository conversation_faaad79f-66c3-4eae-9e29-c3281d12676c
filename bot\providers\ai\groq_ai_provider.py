"""
Groq AI provider for VoicePal.

This module provides a provider for Groq AI services.
"""

import os
import logging
import asyncio
import json
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass, field

import httpx

from bot.providers.core.provider import AIProvider
from bot.providers.core.config import AIProviderConfig
from bot.providers.core.exceptions import (
    ProviderError,
    ProviderConfigError,
    ProviderAuthError,
    ProviderRateLimitError,
    ProviderTimeoutError,
    ProviderNotFoundError,
    ProviderValidationError,
    ProviderNotInitializedError
)

# Set up logging
logger = logging.getLogger(__name__)

@dataclass
class GroqAIConfig(AIProviderConfig):
    """Configuration for Groq AI provider."""
    
    api_key: str = ""
    model: str = "llama3-8b-8192"
    max_tokens: int = 1000
    temperature: float = 0.7
    top_p: float = 1.0
    stop_sequences: List[str] = field(default_factory=list)
    
    def __post_init__(self):
        """Post-initialization."""
        self.provider_type = "ai"
        self.provider_name = "groq_ai"
    
    def validate(self) -> List[str]:
        """Validate configuration.
        
        Returns:
            List of validation error messages (empty if valid)
        """
        errors = super().validate()
        
        return errors

class GroqAIProvider(AIProvider[GroqAIConfig]):
    """Provider for Groq AI services."""
    
    provider_type = "ai"
    provider_name = "groq_ai"
    provider_version = "1.0.0"
    provider_description = "Provider for Groq AI services"
    config_class = GroqAIConfig
    
    def __init__(self, config: GroqAIConfig):
        """Initialize provider.
        
        Args:
            config: Provider configuration
        """
        super().__init__(config)
        self.client = None
        self.api_base = "https://api.groq.com/openai/v1"
        self.chat_histories = {}
        self.max_history_turns = 10
    
    def validate_config(self) -> None:
        """Validate provider configuration.
        
        Raises:
            ProviderConfigError: If configuration is invalid
        """
        errors = self.config.validate()
        if errors:
            error_message = "; ".join(errors)
            raise ProviderConfigError(f"Invalid configuration: {error_message}")
    
    def initialize(self) -> None:
        """Initialize provider.
        
        Raises:
            ProviderInitializationError: If initialization fails
        """
        try:
            # Initialize HTTP client
            self.client = httpx.AsyncClient(
                timeout=self.config.timeout,
                headers={
                    "Authorization": f"Bearer {self.config.api_key}",
                    "Content-Type": "application/json"
                }
            )
            
            self.initialized = True
            logger.info(f"Initialized {self.provider_name} provider")
        except Exception as e:
            logger.error(f"Failed to initialize {self.provider_name} provider: {e}")
            raise ProviderInitializationError(f"Failed to initialize {self.provider_name} provider: {e}") from e
    
    def shutdown(self) -> None:
        """Shutdown provider.
        
        Raises:
            ProviderShutdownError: If shutdown fails
        """
        if self.client:
            asyncio.create_task(self.client.aclose())
        
        self.client = None
        self.chat_histories = {}
        self.initialized = False
        logger.info(f"Shutdown {self.provider_name} provider")
    
    async def generate_text(self, prompt: str, **kwargs) -> str:
        """Generate text from prompt.
        
        Args:
            prompt: Text prompt
            **kwargs: Additional arguments
            
        Returns:
            Generated text
            
        Raises:
            ProviderError: If text generation fails
        """
        if not self.initialized:
            raise ProviderNotInitializedError(f"{self.provider_name} provider is not initialized")
        
        try:
            # Convert to chat format for Groq
            messages = [{"role": "user", "content": prompt}]
            
            # Generate response
            response_data = await self._chat_completion(messages, **kwargs)
            
            # Extract text from response
            if response_data.get("choices") and len(response_data["choices"]) > 0:
                return response_data["choices"][0]["message"]["content"]
            else:
                return ""
        except Exception as e:
            logger.error(f"Failed to generate text: {e}")
            
            # Re-raise provider exceptions
            if isinstance(e, ProviderError):
                raise
            
            # Map exceptions to provider exceptions
            if "authentication" in str(e).lower() or "api key" in str(e).lower():
                raise ProviderAuthError(f"Invalid API key: {e}") from e
            elif "rate limit" in str(e).lower():
                raise ProviderRateLimitError(f"Rate limit exceeded: {e}") from e
            elif "timeout" in str(e).lower():
                raise ProviderTimeoutError(f"Request timed out: {e}") from e
            elif "not found" in str(e).lower():
                raise ProviderNotFoundError(f"Resource not found: {e}") from e
            elif "invalid" in str(e).lower():
                raise ProviderValidationError(f"Invalid request: {e}") from e
            else:
                raise ProviderError(f"Failed to generate text: {e}") from e
    
    async def generate_chat_response(self, messages: List[Dict[str, str]], **kwargs) -> Dict[str, Any]:
        """Generate chat response from messages.
        
        Args:
            messages: List of messages in the format {"role": "user|assistant|system", "content": "message"}
            **kwargs: Additional arguments
            
        Returns:
            Generated response
            
        Raises:
            ProviderError: If chat response generation fails
        """
        if not self.initialized:
            raise ProviderNotInitializedError(f"{self.provider_name} provider is not initialized")
        
        try:
            # Get user ID from kwargs
            user_id = kwargs.get("user_id", "default_user")
            
            # Initialize chat history for this user if it doesn't exist
            if user_id not in self.chat_histories:
                self.chat_histories[user_id] = []
            
            # Generate response
            response_data = await self._chat_completion(messages, **kwargs)
            
            # Extract response
            if response_data.get("choices") and len(response_data["choices"]) > 0:
                response_text = response_data["choices"][0]["message"]["content"]
                
                # Update chat history
                self.chat_histories[user_id].append({
                    "user": messages[-1]["content"] if messages[-1]["role"] == "user" else "",
                    "assistant": response_text
                })
                
                # Trim history if needed
                if len(self.chat_histories[user_id]) > self.max_history_turns:
                    self.chat_histories[user_id] = self.chat_histories[user_id][-self.max_history_turns:]
                
                return {
                    "role": "assistant",
                    "content": response_text
                }
            else:
                return {
                    "role": "assistant",
                    "content": ""
                }
        except Exception as e:
            logger.error(f"Failed to generate chat response: {e}")
            
            # Re-raise provider exceptions
            if isinstance(e, ProviderError):
                raise
            
            # Map exceptions to provider exceptions
            if "authentication" in str(e).lower() or "api key" in str(e).lower():
                raise ProviderAuthError(f"Invalid API key: {e}") from e
            elif "rate limit" in str(e).lower():
                raise ProviderRateLimitError(f"Rate limit exceeded: {e}") from e
            elif "timeout" in str(e).lower():
                raise ProviderTimeoutError(f"Request timed out: {e}") from e
            elif "not found" in str(e).lower():
                raise ProviderNotFoundError(f"Resource not found: {e}") from e
            elif "invalid" in str(e).lower():
                raise ProviderValidationError(f"Invalid request: {e}") from e
            else:
                raise ProviderError(f"Failed to generate chat response: {e}") from e
    
    async def embed_text(self, text: str, **kwargs) -> List[float]:
        """Generate embeddings for text.
        
        Args:
            text: Text to embed
            **kwargs: Additional arguments
            
        Returns:
            Text embeddings
            
        Raises:
            ProviderError: If embedding generation fails
        """
        if not self.initialized:
            raise ProviderNotInitializedError(f"{self.provider_name} provider is not initialized")
        
        try:
            # Groq doesn't support embeddings directly, so we'll use a workaround
            # by asking the model to generate a JSON array of embeddings
            prompt = f"""
            Generate a compact numerical embedding vector for the following text.
            The embedding should be a JSON array of 384 floating point numbers between -1 and 1.
            Only return the JSON array, nothing else.
            
            Text to embed: {text}
            """
            
            # Generate embeddings
            embedding_text = await self.generate_text(prompt)
            
            # Extract JSON array from response
            try:
                # Find the JSON array in the response
                start_idx = embedding_text.find('[')
                end_idx = embedding_text.rfind(']') + 1
                
                if start_idx >= 0 and end_idx > start_idx:
                    json_str = embedding_text[start_idx:end_idx]
                    embeddings = json.loads(json_str)
                    
                    # Validate embeddings
                    if isinstance(embeddings, list) and len(embeddings) > 0:
                        return embeddings
            except json.JSONDecodeError:
                pass
            
            # If we couldn't extract embeddings, return a default embedding
            logger.warning("Failed to extract embeddings from response, returning zeros")
            return [0.0] * 384
        except Exception as e:
            logger.error(f"Failed to generate embeddings: {e}")
            
            # Re-raise provider exceptions
            if isinstance(e, ProviderError):
                raise
            
            # Map exceptions to provider exceptions
            if "authentication" in str(e).lower() or "api key" in str(e).lower():
                raise ProviderAuthError(f"Invalid API key: {e}") from e
            elif "rate limit" in str(e).lower():
                raise ProviderRateLimitError(f"Rate limit exceeded: {e}") from e
            elif "timeout" in str(e).lower():
                raise ProviderTimeoutError(f"Request timed out: {e}") from e
            elif "not found" in str(e).lower():
                raise ProviderNotFoundError(f"Resource not found: {e}") from e
            elif "invalid" in str(e).lower():
                raise ProviderValidationError(f"Invalid request: {e}") from e
            else:
                raise ProviderError(f"Failed to generate embeddings: {e}") from e
    
    async def _chat_completion(self, messages: List[Dict[str, str]], **kwargs) -> Dict[str, Any]:
        """Send chat completion request to Groq API.
        
        Args:
            messages: List of messages
            **kwargs: Additional arguments
            
        Returns:
            API response
            
        Raises:
            ProviderError: If API request fails
        """
        if not self.client:
            raise ProviderNotInitializedError(f"{self.provider_name} provider is not initialized")
        
        # Prepare request payload
        payload = {
            "model": kwargs.get("model", self.config.model),
            "messages": messages,
            "max_tokens": kwargs.get("max_tokens", self.config.max_tokens),
            "temperature": kwargs.get("temperature", self.config.temperature),
            "top_p": kwargs.get("top_p", self.config.top_p)
        }
        
        # Add stop sequences if provided
        stop_sequences = kwargs.get("stop_sequences", self.config.stop_sequences)
        if stop_sequences:
            payload["stop"] = stop_sequences
        
        try:
            # Send request
            response = await self.client.post(
                f"{self.api_base}/chat/completions",
                json=payload
            )
            
            # Check for errors
            if response.status_code != 200:
                error_message = f"API request failed with status {response.status_code}: {response.text}"
                logger.error(error_message)
                
                # Map error codes to exceptions
                if response.status_code == 401:
                    raise ProviderAuthError(f"Authentication failed: {response.text}")
                elif response.status_code == 404:
                    raise ProviderNotFoundError(f"Resource not found: {response.text}")
                elif response.status_code == 429:
                    raise ProviderRateLimitError(f"Rate limit exceeded: {response.text}")
                else:
                    raise ProviderError(error_message)
            
            # Parse response
            return response.json()
        except httpx.TimeoutException as e:
            raise ProviderTimeoutError(f"Request timed out: {e}") from e
        except httpx.HTTPError as e:
            raise ProviderError(f"HTTP error: {e}") from e
        except json.JSONDecodeError as e:
            raise ProviderError(f"Failed to parse response: {e}") from e
        except Exception as e:
            # Re-raise provider exceptions
            if isinstance(e, ProviderError):
                raise
            
            raise ProviderError(f"Unexpected error: {e}") from e
