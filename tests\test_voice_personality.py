"""
Test script for VoicePal voice personality matching.

This script tests the voice personality matching functionality of the VoicePal bot.
It generates voice responses for each personality type and saves them to files for comparison.

Usage:
    python test_voice_personality.py
"""

import os
import logging
import tempfile
import asyncio
from pathlib import Path

from bot.providers.voice.processor import VoiceProcessor
from bot.ai_conversation import AIConversation
from bot.config import Config

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# Test text for each personality
TEST_TEXTS = {
    "friendly": "Hello! It's so nice to meet you. How are you feeling today?",
    "witty": "Hey! Ready for some conversation with a side of wit? How's your day going?",
    "calm": "Hello. I'm here to provide a calm space for conversation. How are you today?",
    "motivational": "Hello! I'm excited to chat with you today. What goals are you working towards?",
    "thoughtful": "Hello. I'm looking forward to our conversation. What's been on your mind lately?"
}

async def test_voice_personality_matching():
    """Test the voice personality matching functionality."""
    logger.info("Testing voice personality matching functionality...")

    # Initialize components
    voice_processor = VoiceProcessor(
        deepgram_api_key=Config.DEEPGRAM_API_KEY,
        default_language=Config.DEFAULT_LANGUAGE
    )

    ai_conversation = AIConversation()

    try:
        # Create output directory
        output_dir = Path("test_output")
        output_dir.mkdir(exist_ok=True)

        # Test each personality
        for personality in TEST_TEXTS.keys():
            logger.info(f"Testing {personality} personality...")

            # Set personality
            voice_processor.set_personality(personality)

            # Get test text
            text = TEST_TEXTS[personality]

            # Generate voice response
            voice_response_path = voice_processor.generate_voice_response(
                text=text,
                personality=personality
            )

            if voice_response_path:
                # Copy to a permanent location
                permanent_path = output_dir / f"{personality}_voice.mp3"

                # Copy file
                with open(voice_response_path, "rb") as src, open(permanent_path, "wb") as dst:
                    dst.write(src.read())

                # Clean up temporary file
                voice_processor.cleanup_temp_file(voice_response_path)

                logger.info(f"{personality.capitalize()} voice saved to: {permanent_path}")
            else:
                logger.error(f"Failed to generate voice for {personality} personality")

        logger.info("Voice personality matching test completed!")

    except Exception as e:
        logger.error(f"Error testing voice personality matching: {e}")
    finally:
        # Close voice processor
        await voice_processor.close()

async def main():
    """Run all tests."""
    logger.info("Testing VoicePal voice personality matching...")

    # Create output directory
    output_dir = Path("test_output")
    output_dir.mkdir(exist_ok=True)

    # Test voice personality matching
    await test_voice_personality_matching()

    logger.info("All tests completed!")

if __name__ == "__main__":
    asyncio.run(main())
