"""
Verification script for the VoicePal bot changes.

This script verifies the changes made to the VoicePal bot,
focusing on the credit system and TTS provider improvements.
"""

import os
import sys
import logging
import argparse
from datetime import datetime

# Add the parent directory to the path so we can import the bot modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

def verify_database_changes():
    """Verify the database changes."""
    logger.info("Verifying database changes...")
    
    try:
        from bot.database.core import Database
        
        # Create a test database
        test_db_file = "verify_db.db"
        if os.path.exists(test_db_file):
            os.remove(test_db_file)
        
        db = Database(test_db_file)
        
        # Check if the database has the new fields
        db.cursor.execute("PRAGMA table_info(users)")
        user_columns = [column[1] for column in db.cursor.fetchall()]
        
        required_columns = ["free_credits_received", "registration_ip", "device_id"]
        missing_columns = [col for col in required_columns if col not in user_columns]
        
        if missing_columns:
            logger.error(f"Missing user columns: {missing_columns}")
            return False
        else:
            logger.info("✅ User table has all required columns")
        
        # Check transactions table
        db.cursor.execute("PRAGMA table_info(transactions)")
        transaction_columns = [column[1] for column in db.cursor.fetchall()]
        
        if "source" not in transaction_columns:
            logger.error("Missing 'source' column in transactions table")
            return False
        else:
            logger.info("✅ Transactions table has 'source' column")
        
        # Test free trial credits
        user_id = 123456789
        db.add_user(user_id)
        
        # Add free trial credits
        db.add_credits(user_id, 100, source="free_trial")
        
        # Check that free_credits_received is set
        db.cursor.execute("SELECT free_credits_received FROM users WHERE user_id = ?", (user_id,))
        free_credits_received = db.cursor.fetchone()[0]
        
        if not free_credits_received:
            logger.error("free_credits_received not set after adding free trial credits")
            return False
        else:
            logger.info("✅ free_credits_received flag set correctly")
        
        # Try to add free trial credits again
        initial_credits = db.get_user_credits(user_id)
        db.add_credits(user_id, 100, source="free_trial")
        final_credits = db.get_user_credits(user_id)
        
        if final_credits > initial_credits:
            logger.error("Free trial credits were added multiple times")
            return False
        else:
            logger.info("✅ Free trial credits can only be received once")
        
        # Clean up
        db.close()
        if os.path.exists(test_db_file):
            os.remove(test_db_file)
        
        return True
    except Exception as e:
        logger.error(f"Error verifying database changes: {e}")
        return False

def verify_deepgram_changes():
    """Verify the Deepgram TTS provider changes."""
    logger.info("Verifying Deepgram TTS provider changes...")
    
    try:
        from bot.providers.tts.deepgram_provider import DeepgramTTSProvider
        
        # Create provider with test settings
        provider = DeepgramTTSProvider(
            api_key="test_key",
            cache_ttl=60,
            use_cache=True
        )
        
        # Check if cache directory exists
        if not os.path.exists(provider.cache_dir):
            logger.error("Cache directory not created")
            return False
        else:
            logger.info(f"✅ Cache directory created: {provider.cache_dir}")
        
        # Check if cache dictionaries exist
        if not hasattr(provider, "cache") or not hasattr(provider, "cache_timestamps"):
            logger.error("Cache dictionaries not initialized")
            return False
        else:
            logger.info("✅ Cache dictionaries initialized")
        
        # Check if clean_expired_cache method exists
        if not hasattr(provider, "_clean_expired_cache"):
            logger.error("_clean_expired_cache method not found")
            return False
        else:
            logger.info("✅ _clean_expired_cache method exists")
        
        return True
    except Exception as e:
        logger.error(f"Error verifying Deepgram changes: {e}")
        return False

def main():
    """Run verification tests."""
    parser = argparse.ArgumentParser(description="Verify VoicePal bot changes")
    parser.add_argument("--test", choices=["database", "deepgram", "all"], 
                        default="all", help="Test to run")
    args = parser.parse_args()
    
    logger.info("Starting verification tests")
    
    if args.test == "database" or args.test == "all":
        db_success = verify_database_changes()
        logger.info(f"Database changes verification: {'Success' if db_success else 'Failed'}")
    
    if args.test == "deepgram" or args.test == "all":
        deepgram_success = verify_deepgram_changes()
        logger.info(f"Deepgram changes verification: {'Success' if deepgram_success else 'Failed'}")
    
    logger.info("Verification tests completed")

if __name__ == "__main__":
    main()
