"""
Unit tests for the Redis provider module.
"""

import pytest
import time
from unittest.mock import MagicMock, patch

from bot.providers.memory.redis_provider import RedisProvider


@pytest.fixture
def mock_upstash_redis():
    """Create a mock Upstash Redis client."""
    mock_redis = MagicMock()
    mock_redis.set.return_value = True
    mock_redis.get.return_value = "test_value"
    mock_redis.delete.return_value = True
    mock_redis.incr.return_value = 1
    mock_redis.expire.return_value = True
    return mock_redis


@pytest.fixture
def mock_upstash_ratelimit():
    """Create a mock Upstash RateLimit client."""
    mock_ratelimit = MagicMock()
    mock_ratelimit.limit.return_value = MagicMock(
        success=True,
        limit=10,
        remaining=9,
        reset=int(time.time()) + 60
    )
    return mock_ratelimit


@pytest.fixture
def redis_provider(mock_upstash_redis, mock_upstash_ratelimit):
    """Create a RedisProvider with mocked dependencies."""
    with patch('bot.providers.memory.redis_provider.Redis', return_value=mock_upstash_redis), \
         patch('bot.providers.memory.redis_provider.Ratelimit', return_value=mock_upstash_ratelimit):
        provider = RedisProvider({
            "url": "redis://fake:<EMAIL>:12345",
            "cache_ttl_minutes": 30,
            "rate_limit_max_requests": 10,
            "rate_limit_window_seconds": 10
        })
        provider._redis = mock_upstash_redis
        provider._ratelimit = mock_upstash_ratelimit
        yield provider


@pytest.fixture
def redis_provider_no_config():
    """Create a RedisProvider with no configuration."""
    with patch('bot.providers.memory.redis_provider.Redis', side_effect=Exception("Connection error")), \
         patch('bot.providers.memory.redis_provider.Ratelimit', side_effect=Exception("Connection error")):
        provider = RedisProvider({})
        yield provider


class TestRedisProvider:
    """Test cases for the RedisProvider class."""

    def test_initialization_with_config(self, redis_provider):
        """Test initialization with configuration."""
        assert redis_provider.is_available() is True
        assert redis_provider._cache_ttl_minutes == 30
        assert redis_provider._rate_limit_max_requests == 10
        assert redis_provider._rate_limit_window_seconds == 10

    def test_initialization_without_config(self, redis_provider_no_config):
        """Test initialization without configuration."""
        assert redis_provider_no_config.is_available() is False
        assert redis_provider_no_config._cache_ttl_minutes == 30  # Default value
        assert redis_provider_no_config._rate_limit_max_requests == 10  # Default value
        assert redis_provider_no_config._rate_limit_window_seconds == 10  # Default value

    def test_is_available(self, redis_provider, redis_provider_no_config):
        """Test is_available method."""
        assert redis_provider.is_available() is True
        assert redis_provider_no_config.is_available() is False

    def test_set(self, redis_provider, mock_upstash_redis):
        """Test set method."""
        # Test with TTL
        result = redis_provider.set("test_key", "test_value", 60)
        assert result is True
        mock_upstash_redis.set.assert_called_with("test_key", "test_value")
        mock_upstash_redis.expire.assert_called_with("test_key", 60)

        # Test without TTL
        result = redis_provider.set("test_key", "test_value")
        assert result is True
        mock_upstash_redis.set.assert_called_with("test_key", "test_value")
        mock_upstash_redis.expire.assert_called_with("test_key", 30 * 60)  # Default TTL

    def test_set_with_error(self, redis_provider, mock_upstash_redis):
        """Test set method with error."""
        mock_upstash_redis.set.side_effect = Exception("Redis error")
        result = redis_provider.set("test_key", "test_value")
        assert result is False

    def test_get(self, redis_provider, mock_upstash_redis):
        """Test get method."""
        result = redis_provider.get("test_key")
        assert result == "test_value"
        mock_upstash_redis.get.assert_called_with("test_key")

    def test_get_with_error(self, redis_provider, mock_upstash_redis):
        """Test get method with error."""
        mock_upstash_redis.get.side_effect = Exception("Redis error")
        result = redis_provider.get("test_key")
        assert result is None

    def test_delete(self, redis_provider, mock_upstash_redis):
        """Test delete method."""
        result = redis_provider.delete("test_key")
        assert result is True
        mock_upstash_redis.delete.assert_called_with("test_key")

    def test_delete_with_error(self, redis_provider, mock_upstash_redis):
        """Test delete method with error."""
        mock_upstash_redis.delete.side_effect = Exception("Redis error")
        result = redis_provider.delete("test_key")
        assert result is False

    def test_check_rate_limit(self, redis_provider, mock_upstash_ratelimit):
        """Test check_rate_limit method."""
        # Test successful rate limit check
        result = redis_provider.check_rate_limit("test_key")
        assert result is True
        mock_upstash_ratelimit.limit.assert_called_once()

        # Test with tokens
        result = redis_provider.check_rate_limit("test_key", tokens=3)
        assert result is True
        assert mock_upstash_ratelimit.limit.call_count == 2

    def test_check_rate_limit_exceeded(self, redis_provider, mock_upstash_ratelimit):
        """Test check_rate_limit method when limit is exceeded."""
        # Mock rate limit exceeded
        mock_upstash_ratelimit.limit.return_value = MagicMock(
            success=False,
            limit=10,
            remaining=0,
            reset=int(time.time()) + 60
        )
        result = redis_provider.check_rate_limit("test_key")
        assert result is False

    def test_check_rate_limit_with_error(self, redis_provider, mock_upstash_ratelimit):
        """Test check_rate_limit method with error."""
        mock_upstash_ratelimit.limit.side_effect = Exception("Rate limit error")
        result = redis_provider.check_rate_limit("test_key")
        assert result is True  # Should default to allowing the request on error

    def test_get_rate_limit_info(self, redis_provider, mock_upstash_ratelimit):
        """Test get_rate_limit_info method."""
        info = redis_provider.get_rate_limit_info("test_key")
        assert info["limit"] == 10
        assert info["remaining"] == 9
        assert "reset" in info
        mock_upstash_ratelimit.limit.assert_called_once()

    def test_get_rate_limit_info_with_error(self, redis_provider, mock_upstash_ratelimit):
        """Test get_rate_limit_info method with error."""
        mock_upstash_ratelimit.limit.side_effect = Exception("Rate limit error")
        info = redis_provider.get_rate_limit_info("test_key")
        assert info["limit"] == 10  # Default value
        assert info["remaining"] == 10  # Default value
        assert "reset" in info

    def test_fallback_when_redis_unavailable(self, redis_provider_no_config):
        """Test fallback behavior when Redis is unavailable."""
        # All operations should gracefully handle Redis being unavailable
        assert redis_provider_no_config.set("test_key", "test_value") is False
        assert redis_provider_no_config.get("test_key") is None
        assert redis_provider_no_config.delete("test_key") is False
        assert redis_provider_no_config.check_rate_limit("test_key") is True  # Allow by default
        
        info = redis_provider_no_config.get_rate_limit_info("test_key")
        assert info["limit"] == 10
        assert info["remaining"] == 10
        assert "reset" in info
