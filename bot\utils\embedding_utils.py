"""
Embedding utilities for VoicePal.

This module provides utilities for generating and managing embeddings.
"""

import os
import logging
import asyncio
from typing import List, Dict, Any, Optional, Union, Tuple

try:
    import numpy as np
    NUMPY_AVAILABLE = True
except ImportError:
    NUMPY_AVAILABLE = False

try:
    from sentence_transformers import SentenceTransformer
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    SENTENCE_TRANSFORMERS_AVAILABLE = False

# Set up logging
logger = logging.getLogger(__name__)

class EmbeddingProvider:
    """Embedding provider for VoicePal."""

    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize the embedding provider.

        Args:
            config: Configuration dictionary
        """
        self.config = config or {}
        
        # Initialize embedding model
        self.model = self._initialize_model()
        self.model_name = self.config.get("model_name", "all-MiniLM-L6-v2")
        self.vector_size = self.config.get("vector_size", 384)
        
        # Batch processing configuration
        self.batch_size = self.config.get("batch_size", 32)
    
    def _initialize_model(self):
        """Initialize embedding model."""
        if not SENTENCE_TRANSFORMERS_AVAILABLE:
            logger.warning("sentence-transformers not installed, using fallback embedding")
            return None
            
        try:
            # Get model name from config or environment
            model_name = self.config.get(
                "model_name", 
                os.environ.get("EMBEDDING_MODEL", "all-MiniLM-L6-v2")
            )
            
            # Initialize model
            model = SentenceTransformer(model_name)
            logger.info(f"Embedding model initialized: {model_name}")
            return model
        except Exception as e:
            logger.error(f"Error initializing embedding model: {e}")
            return None
    
    def is_available(self) -> bool:
        """Check if embedding model is available."""
        return self.model is not None
    
    def generate_embedding(self, text: str) -> List[float]:
        """
        Generate embedding for a text.
        
        Args:
            text: Input text
            
        Returns:
            Embedding vector
        """
        if not self.model:
            # Return random embedding as fallback
            return self._generate_fallback_embedding()
        
        try:
            # Generate embedding
            embedding = self.model.encode(text)
            return embedding.tolist()
        except Exception as e:
            logger.error(f"Error generating embedding: {e}")
            return self._generate_fallback_embedding()
    
    def generate_embeddings(self, texts: List[str]) -> List[List[float]]:
        """
        Generate embeddings for multiple texts.
        
        Args:
            texts: List of input texts
            
        Returns:
            List of embedding vectors
        """
        if not self.model:
            # Return random embeddings as fallback
            return [self._generate_fallback_embedding() for _ in texts]
        
        try:
            # Generate embeddings in batches
            all_embeddings = []
            
            for i in range(0, len(texts), self.batch_size):
                batch = texts[i:i + self.batch_size]
                embeddings = self.model.encode(batch)
                all_embeddings.extend(embeddings.tolist())
            
            return all_embeddings
        except Exception as e:
            logger.error(f"Error generating embeddings: {e}")
            return [self._generate_fallback_embedding() for _ in texts]
    
    async def generate_embedding_async(self, text: str) -> List[float]:
        """
        Generate embedding for a text asynchronously.
        
        Args:
            text: Input text
            
        Returns:
            Embedding vector
        """
        # Note: sentence-transformers doesn't have native async support
        # This is a wrapper around the synchronous method
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.generate_embedding, text)
    
    async def generate_embeddings_async(self, texts: List[str]) -> List[List[float]]:
        """
        Generate embeddings for multiple texts asynchronously.
        
        Args:
            texts: List of input texts
            
        Returns:
            List of embedding vectors
        """
        # Note: sentence-transformers doesn't have native async support
        # This is a wrapper around the synchronous method
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.generate_embeddings, texts)
    
    def _generate_fallback_embedding(self) -> List[float]:
        """
        Generate a fallback embedding.
        
        Returns:
            Random embedding vector
        """
        # Generate random embedding with the correct dimension
        if NUMPY_AVAILABLE:
            return np.random.rand(self.vector_size).tolist()
        else:
            import random
            return [random.random() for _ in range(self.vector_size)]
    
    def calculate_similarity(self, embedding1: List[float], embedding2: List[float]) -> float:
        """
        Calculate cosine similarity between two embeddings.
        
        Args:
            embedding1: First embedding
            embedding2: Second embedding
            
        Returns:
            Cosine similarity (0-1)
        """
        try:
            if not NUMPY_AVAILABLE:
                # Fallback implementation without numpy
                dot_product = sum(a * b for a, b in zip(embedding1, embedding2))
                norm1 = sum(a * a for a in embedding1) ** 0.5
                norm2 = sum(b * b for b in embedding2) ** 0.5
                
                if norm1 == 0 or norm2 == 0:
                    return 0.0
                
                return dot_product / (norm1 * norm2)
            
            # Convert to numpy arrays
            vec1 = np.array(embedding1)
            vec2 = np.array(embedding2)
            
            # Calculate cosine similarity
            dot_product = np.dot(vec1, vec2)
            norm1 = np.linalg.norm(vec1)
            norm2 = np.linalg.norm(vec2)
            
            if norm1 == 0 or norm2 == 0:
                return 0.0
            
            return dot_product / (norm1 * norm2)
        except Exception as e:
            logger.error(f"Error calculating similarity: {e}")
            return 0.0
    
    def find_most_similar(
        self, 
        query_embedding: List[float], 
        embeddings: List[List[float]], 
        top_k: int = 5
    ) -> List[Tuple[int, float]]:
        """
        Find the most similar embeddings.
        
        Args:
            query_embedding: Query embedding
            embeddings: List of embeddings to compare against
            top_k: Number of top results to return
            
        Returns:
            List of (index, similarity) tuples
        """
        try:
            if not NUMPY_AVAILABLE:
                # Fallback implementation without numpy
                similarities = [
                    (i, self.calculate_similarity(query_embedding, embedding))
                    for i, embedding in enumerate(embeddings)
                ]
                
                # Sort by similarity (descending)
                similarities.sort(key=lambda x: x[1], reverse=True)
                
                # Return top-k results
                return similarities[:top_k]
            
            # Convert to numpy arrays
            query_vec = np.array(query_embedding)
            embedding_matrix = np.array(embeddings)
            
            # Calculate cosine similarities
            dot_products = np.dot(embedding_matrix, query_vec)
            norms = np.linalg.norm(embedding_matrix, axis=1) * np.linalg.norm(query_vec)
            
            # Handle zero norms
            norms = np.where(norms == 0, 1e-10, norms)
            
            similarities = dot_products / norms
            
            # Get top-k indices
            top_indices = np.argsort(similarities)[-top_k:][::-1]
            
            # Return (index, similarity) tuples
            return [(int(idx), float(similarities[idx])) for idx in top_indices]
        except Exception as e:
            logger.error(f"Error finding most similar embeddings: {e}")
            return []
    
    def get_embedding_dimension(self) -> int:
        """
        Get the dimension of the embeddings.
        
        Returns:
            Embedding dimension
        """
        return self.vector_size
    
    def get_model_name(self) -> str:
        """
        Get the name of the embedding model.
        
        Returns:
            Model name
        """
        return self.model_name
