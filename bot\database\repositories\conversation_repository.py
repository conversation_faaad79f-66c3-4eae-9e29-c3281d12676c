"""
Conversation repositories for VoicePal.

This module provides the conversation-related repositories for the VoicePal database.
"""

import logging
from typing import Dict, List, Any, Optional, Union, Tuple
from datetime import datetime

from bot.database.core.connection import DatabaseConnection
from bot.database.core.exceptions import (
    DatabaseError,
    DatabaseNotFoundError,
    DatabaseDuplicateError
)
from bot.database.repositories.repository import Repository
from bot.database.models.conversation import Conversation, Message, MessageMetadata

# Set up logging
logger = logging.getLogger(__name__)

class ConversationRepository(Repository[Conversation]):
    """Repository for Conversation model."""
    
    _model_class = Conversation
    
    def find_by_user(self, user_id: str, active_only: bool = False) -> List[Conversation]:
        """Find conversations by user ID.
        
        Args:
            user_id: User ID
            active_only: Whether to return only active conversations
            
        Returns:
            List of conversations
            
        Raises:
            DatabaseError: If query fails
        """
        where = {"user_id": user_id}
        if active_only:
            where["is_active"] = True
        
        return self.find_all(where=where, order_by="updated_at DESC")
    
    def find_active_by_user(self, user_id: str) -> List[Conversation]:
        """Find active conversations by user ID.
        
        Args:
            user_id: User ID
            
        Returns:
            List of active conversations
            
        Raises:
            DatabaseError: If query fails
        """
        return self.find_by_user(user_id, active_only=True)
    
    def get_or_create_active_conversation(self, user_id: str) -> Tuple[Conversation, bool]:
        """Get or create an active conversation for user.
        
        Args:
            user_id: User ID
            
        Returns:
            Tuple of (conversation, created) where created is True if a new conversation was created
            
        Raises:
            DatabaseError: If query fails
        """
        try:
            # Try to find an active conversation
            active_conversations = self.find_active_by_user(user_id)
            
            if active_conversations:
                # Return the most recently updated conversation
                return active_conversations[0], False
            
            # Create a new conversation
            conversation = Conversation(
                user_id=user_id,
                title=f"Conversation {datetime.now().strftime('%Y-%m-%d %H:%M')}"
            )
            
            self.create(conversation)
            return conversation, True
        except Exception as e:
            logger.error(f"Failed to get or create active conversation: {e}")
            raise DatabaseError(f"Failed to get or create active conversation: {e}") from e
    
    def archive_conversation(self, conversation_id: str) -> Optional[Conversation]:
        """Archive a conversation.
        
        Args:
            conversation_id: Conversation ID
            
        Returns:
            Updated conversation or None if not found
            
        Raises:
            DatabaseError: If update fails
        """
        try:
            conversation = self.find_by_id(conversation_id)
            if not conversation:
                return None
            
            conversation.archive()
            return self.update(conversation)
        except Exception as e:
            logger.error(f"Failed to archive conversation: {e}")
            raise DatabaseError(f"Failed to archive conversation: {e}") from e
    
    def activate_conversation(self, conversation_id: str) -> Optional[Conversation]:
        """Activate a conversation.
        
        Args:
            conversation_id: Conversation ID
            
        Returns:
            Updated conversation or None if not found
            
        Raises:
            DatabaseError: If update fails
        """
        try:
            conversation = self.find_by_id(conversation_id)
            if not conversation:
                return None
            
            conversation.activate()
            return self.update(conversation)
        except Exception as e:
            logger.error(f"Failed to activate conversation: {e}")
            raise DatabaseError(f"Failed to activate conversation: {e}") from e
    
    def update_title(self, conversation_id: str, title: str) -> Optional[Conversation]:
        """Update conversation title.
        
        Args:
            conversation_id: Conversation ID
            title: New title
            
        Returns:
            Updated conversation or None if not found
            
        Raises:
            DatabaseError: If update fails
        """
        try:
            conversation = self.find_by_id(conversation_id)
            if not conversation:
                return None
            
            conversation.update_title(title)
            return self.update(conversation)
        except Exception as e:
            logger.error(f"Failed to update conversation title: {e}")
            raise DatabaseError(f"Failed to update conversation title: {e}") from e

class MessageRepository(Repository[Message]):
    """Repository for Message model."""
    
    _model_class = Message
    
    def find_by_conversation(self, conversation_id: str, limit: Optional[int] = None, offset: Optional[int] = None) -> List[Message]:
        """Find messages by conversation ID.
        
        Args:
            conversation_id: Conversation ID
            limit: Maximum number of messages to return
            offset: Offset for pagination
            
        Returns:
            List of messages
            
        Raises:
            DatabaseError: If query fails
        """
        return self.find_all(
            where={"conversation_id": conversation_id},
            order_by="created_at ASC",
            limit=limit,
            offset=offset
        )
    
    def find_by_user(self, user_id: str, limit: Optional[int] = None, offset: Optional[int] = None) -> List[Message]:
        """Find messages by user ID.
        
        Args:
            user_id: User ID
            limit: Maximum number of messages to return
            offset: Offset for pagination
            
        Returns:
            List of messages
            
        Raises:
            DatabaseError: If query fails
        """
        return self.find_all(
            where={"user_id": user_id},
            order_by="created_at DESC",
            limit=limit,
            offset=offset
        )
    
    def find_by_role(self, conversation_id: str, role: str) -> List[Message]:
        """Find messages by role.
        
        Args:
            conversation_id: Conversation ID
            role: Message role
            
        Returns:
            List of messages
            
        Raises:
            DatabaseError: If query fails
        """
        try:
            query = f"""
                SELECT * FROM {self._model_class.get_table_name()}
                WHERE conversation_id = ? AND role = ?
                ORDER BY created_at ASC
            """
            
            cursor = self.connection.execute(query, (conversation_id, role))
            rows = cursor.fetchall()
            
            # Convert rows to model instances
            messages = []
            for row in cursor:
                data = dict(row)
                message = self._model_class.from_dict(data)
                messages.append(message)
            
            return messages
        except Exception as e:
            logger.error(f"Failed to find messages by role: {e}")
            raise DatabaseError(f"Failed to find messages by role: {e}") from e
    
    def get_conversation_history(self, conversation_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Get conversation history in a format suitable for AI context.
        
        Args:
            conversation_id: Conversation ID
            limit: Maximum number of messages to return
            
        Returns:
            List of message dictionaries with role and content
            
        Raises:
            DatabaseError: If query fails
        """
        try:
            messages = self.find_by_conversation(conversation_id, limit=limit)
            
            # Format messages for AI context
            history = []
            for message in messages:
                history.append({
                    "role": message.role,
                    "content": message.content
                })
            
            return history
        except Exception as e:
            logger.error(f"Failed to get conversation history: {e}")
            raise DatabaseError(f"Failed to get conversation history: {e}") from e
    
    def add_message(self, conversation_id: str, user_id: str, content: str, role: str) -> Message:
        """Add a message to a conversation.
        
        Args:
            conversation_id: Conversation ID
            user_id: User ID
            content: Message content
            role: Message role
            
        Returns:
            Created message
            
        Raises:
            DatabaseError: If creation fails
            ValueError: If role is invalid
        """
        if role not in Message.VALID_ROLES:
            raise ValueError(f"Invalid role: {role}. Must be one of {Message.VALID_ROLES}")
        
        try:
            message = Message(
                conversation_id=conversation_id,
                user_id=user_id,
                content=content,
                role=role
            )
            
            return self.create(message)
        except Exception as e:
            logger.error(f"Failed to add message: {e}")
            raise DatabaseError(f"Failed to add message: {e}") from e

class MessageMetadataRepository(Repository[MessageMetadata]):
    """Repository for MessageMetadata model."""
    
    _model_class = MessageMetadata
    
    def find_by_message(self, message_id: str) -> List[MessageMetadata]:
        """Find metadata by message ID.
        
        Args:
            message_id: Message ID
            
        Returns:
            List of metadata
            
        Raises:
            DatabaseError: If query fails
        """
        return self.find_all({"message_id": message_id})
    
    def find_by_message_and_key(self, message_id: str, key: str) -> Optional[MessageMetadata]:
        """Find metadata by message ID and key.
        
        Args:
            message_id: Message ID
            key: Metadata key
            
        Returns:
            Metadata or None if not found
            
        Raises:
            DatabaseError: If query fails
        """
        return self.find_one({"message_id": message_id, "key": key})
    
    def set_metadata(self, message_id: str, key: str, value: str) -> MessageMetadata:
        """Set message metadata.
        
        Args:
            message_id: Message ID
            key: Metadata key
            value: Metadata value
            
        Returns:
            Created or updated metadata
            
        Raises:
            DatabaseError: If creation or update fails
        """
        try:
            metadata = self.find_by_message_and_key(message_id, key)
            
            if metadata:
                metadata.value = value
                return self.update(metadata)
            else:
                metadata = MessageMetadata(
                    message_id=message_id,
                    key=key,
                    value=value
                )
                return self.create(metadata)
        except Exception as e:
            logger.error(f"Failed to set metadata: {e}")
            raise DatabaseError(f"Failed to set metadata: {e}") from e
    
    def get_metadata(self, message_id: str, key: str, default: Optional[str] = None) -> Optional[str]:
        """Get message metadata value.
        
        Args:
            message_id: Message ID
            key: Metadata key
            default: Default value if metadata not found
            
        Returns:
            Metadata value or default
            
        Raises:
            DatabaseError: If query fails
        """
        try:
            metadata = self.find_by_message_and_key(message_id, key)
            return metadata.value if metadata else default
        except Exception as e:
            logger.error(f"Failed to get metadata: {e}")
            raise DatabaseError(f"Failed to get metadata: {e}") from e
    
    def delete_metadata(self, message_id: str, key: str) -> bool:
        """Delete message metadata.
        
        Args:
            message_id: Message ID
            key: Metadata key
            
        Returns:
            True if metadata was deleted, False otherwise
            
        Raises:
            DatabaseError: If deletion fails
        """
        try:
            metadata = self.find_by_message_and_key(message_id, key)
            if not metadata:
                return False
            
            return self.delete(metadata)
        except Exception as e:
            logger.error(f"Failed to delete metadata: {e}")
            raise DatabaseError(f"Failed to delete metadata: {e}") from e
