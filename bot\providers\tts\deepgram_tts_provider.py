"""
Deepgram TTS provider for VoicePal.

This module provides a provider for Deepgram TTS services.
"""

import os
import logging
import asyncio
import json
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass, field

import httpx
from deepgram import (
    DeepgramClient,
    DeepgramClientOptions,
    PrerecordedOptions,
    FileSource
)

from bot.providers.core.provider import TTSProvider
from bot.providers.core.config import TTSProviderConfig
from bot.providers.core.exceptions import (
    ProviderError,
    ProviderConfigError,
    ProviderAuthError,
    ProviderRateLimitError,
    ProviderTimeoutError,
    ProviderNotFoundError,
    ProviderValidationError,
    ProviderNotInitializedError
)

# Set up logging
logger = logging.getLogger(__name__)

@dataclass
class DeepgramTTSConfig(TTSProviderConfig):
    """Configuration for Deepgram TTS provider."""
    
    api_key: str = ""
    voice_id: str = "aura"
    sample_rate: int = 24000
    audio_format: str = "mp3"
    timeout: int = 30
    
    def __post_init__(self):
        """Post-initialization."""
        self.provider_type = "tts"
        self.provider_name = "deepgram_tts"
    
    def validate(self) -> List[str]:
        """Validate configuration.
        
        Returns:
            List of validation error messages (empty if valid)
        """
        errors = super().validate()
        
        # Validate voice ID
        valid_voices = ["aura", "stella", "nova", "athena", "luna", "zeus", "hera", "orion", "apollo"]
        if self.voice_id not in valid_voices:
            errors.append(f"Invalid voice ID: {self.voice_id}. Must be one of {valid_voices}")
        
        # Validate audio format
        valid_formats = ["mp3", "wav", "ogg", "flac"]
        if self.audio_format not in valid_formats:
            errors.append(f"Invalid audio format: {self.audio_format}. Must be one of {valid_formats}")
        
        return errors

class DeepgramTTSProvider(TTSProvider[DeepgramTTSConfig]):
    """Provider for Deepgram TTS services."""
    
    provider_type = "tts"
    provider_name = "deepgram_tts"
    provider_version = "1.0.0"
    provider_description = "Provider for Deepgram TTS services"
    config_class = DeepgramTTSConfig
    
    def __init__(self, config: DeepgramTTSConfig):
        """Initialize provider.
        
        Args:
            config: Provider configuration
        """
        super().__init__(config)
        self.client = None
        self.voices_cache = None
    
    def validate_config(self) -> None:
        """Validate provider configuration.
        
        Raises:
            ProviderConfigError: If configuration is invalid
        """
        errors = self.config.validate()
        if errors:
            error_message = "; ".join(errors)
            raise ProviderConfigError(f"Invalid configuration: {error_message}")
    
    def initialize(self) -> None:
        """Initialize provider.
        
        Raises:
            ProviderInitializationError: If initialization fails
        """
        try:
            # Initialize Deepgram client
            options = DeepgramClientOptions(
                api_key=self.config.api_key,
                timeout=self.config.timeout
            )
            self.client = DeepgramClient(options)
            
            self.initialized = True
            logger.info(f"Initialized {self.provider_name} provider")
        except Exception as e:
            logger.error(f"Failed to initialize {self.provider_name} provider: {e}")
            raise ProviderInitializationError(f"Failed to initialize {self.provider_name} provider: {e}") from e
    
    def shutdown(self) -> None:
        """Shutdown provider.
        
        Raises:
            ProviderShutdownError: If shutdown fails
        """
        self.client = None
        self.voices_cache = None
        self.initialized = False
        logger.info(f"Shutdown {self.provider_name} provider")
    
    async def text_to_speech(self, text: str, voice_id: Optional[str] = None, **kwargs) -> bytes:
        """Convert text to speech.
        
        Args:
            text: Text to convert to speech
            voice_id: Voice ID (default: config voice_id)
            **kwargs: Additional arguments
            
        Returns:
            Audio data
            
        Raises:
            ProviderError: If text-to-speech conversion fails
        """
        if not self.initialized:
            raise ProviderNotInitializedError(f"{self.provider_name} provider is not initialized")
        
        try:
            # Use provided voice ID or default from config
            voice = voice_id or self.config.voice_id
            
            # Get audio format
            audio_format = kwargs.get("audio_format", self.config.audio_format)
            
            # Get sample rate
            sample_rate = kwargs.get("sample_rate", self.config.sample_rate)
            
            # Prepare TTS options
            options = {
                "model": "aura-2",  # Deepgram's TTS model
                "encoding": audio_format,
                "container": audio_format,
                "sample_rate": sample_rate,
                "voice": voice
            }
            
            # Add optional parameters
            if "pitch" in kwargs:
                options["pitch"] = kwargs["pitch"]
            
            if "rate" in kwargs:
                options["speed"] = kwargs["rate"]
            
            # Generate speech
            response = await self.client.speak.sync(
                text=text,
                **options
            )
            
            # Return audio data
            return response.audio_data
        except Exception as e:
            logger.error(f"Failed to convert text to speech: {e}")
            
            # Map exceptions to provider exceptions
            if "authentication" in str(e).lower() or "api key" in str(e).lower():
                raise ProviderAuthError(f"Invalid API key: {e}") from e
            elif "rate limit" in str(e).lower():
                raise ProviderRateLimitError(f"Rate limit exceeded: {e}") from e
            elif "timeout" in str(e).lower():
                raise ProviderTimeoutError(f"Request timed out: {e}") from e
            elif "not found" in str(e).lower() or "no such voice" in str(e).lower():
                raise ProviderNotFoundError(f"Resource not found: {e}") from e
            elif "invalid" in str(e).lower():
                raise ProviderValidationError(f"Invalid request: {e}") from e
            else:
                raise ProviderError(f"Failed to convert text to speech: {e}") from e
    
    def get_available_voices(self) -> List[Dict[str, Any]]:
        """Get available voices.
        
        Returns:
            List of available voices
            
        Raises:
            ProviderError: If getting available voices fails
        """
        if not self.initialized:
            raise ProviderNotInitializedError(f"{self.provider_name} provider is not initialized")
        
        # Return cached voices if available
        if self.voices_cache:
            return self.voices_cache
        
        # Deepgram voices (as of May 2024)
        voices = [
            {
                "id": "aura",
                "name": "Aura",
                "gender": "female",
                "language": "en",
                "description": "A friendly and professional female voice"
            },
            {
                "id": "stella",
                "name": "Stella",
                "gender": "female",
                "language": "en",
                "description": "A warm and engaging female voice"
            },
            {
                "id": "nova",
                "name": "Nova",
                "gender": "female",
                "language": "en",
                "description": "A clear and articulate female voice"
            },
            {
                "id": "athena",
                "name": "Athena",
                "gender": "female",
                "language": "en",
                "description": "A confident and authoritative female voice"
            },
            {
                "id": "luna",
                "name": "Luna",
                "gender": "female",
                "language": "en",
                "description": "A soft and gentle female voice"
            },
            {
                "id": "zeus",
                "name": "Zeus",
                "gender": "male",
                "language": "en",
                "description": "A deep and powerful male voice"
            },
            {
                "id": "hera",
                "name": "Hera",
                "gender": "female",
                "language": "en",
                "description": "A mature and sophisticated female voice"
            },
            {
                "id": "orion",
                "name": "Orion",
                "gender": "male",
                "language": "en",
                "description": "A clear and professional male voice"
            },
            {
                "id": "apollo",
                "name": "Apollo",
                "gender": "male",
                "language": "en",
                "description": "A warm and friendly male voice"
            }
        ]
        
        # Cache voices
        self.voices_cache = voices
        
        return voices
