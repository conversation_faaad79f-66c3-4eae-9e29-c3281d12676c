"""
Centralized logging configuration for VoicePal.

This module provides a unified logging configuration for the entire application,
with environment-specific settings and filtering of third-party logs.
"""

import os
import sys
import logging
from logging.handlers import RotatingFileHandler
from typing import Dict, Any, Optional, List, Union

# Default log levels for different environments
DEFAULT_LOG_LEVELS = {
    "development": logging.DEBUG,
    "testing": logging.INFO,
    "production": logging.WARNING,
    "default": logging.INFO
}

# Third-party libraries to filter in production
FILTERED_LIBRARIES = [
    "telegram",
    "httpx",
    "asyncio",
    "urllib3",
    "pip",
    "deepgram",
    "google",
    "elevenlabs",
    "websockets",
    "aiohttp"
]

# Global logger instance
logger = logging.getLogger(__name__)


def configure_logging(
    environment: str = None,
    log_level: str = None,
    log_file: str = None,
    log_to_console: bool = True,
    log_format: str = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
) -> None:
    """
    Configure logging for the application.

    Args:
        environment: Environment name ('development', 'testing', 'production')
        log_level: Override log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: Path to log file
        log_to_console: Whether to log to console
        log_format: Log format string
    """
    # Determine environment
    if environment is None:
        environment = os.getenv("ENVIRONMENT", "default").lower()

    # Determine log level
    numeric_level = None
    if log_level:
        numeric_level = getattr(logging, log_level.upper(), None)
    else:
        env_log_level = os.getenv("LOGGING_LEVEL", os.getenv("LOG_LEVEL"))
        if env_log_level:
            numeric_level = getattr(logging, env_log_level.upper(), None)

    if not isinstance(numeric_level, int):
        numeric_level = DEFAULT_LOG_LEVELS.get(environment, DEFAULT_LOG_LEVELS["default"])

    # Reset root logger
    root_logger = logging.getLogger()
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    root_logger.setLevel(numeric_level)

    # Create formatters
    formatter = logging.Formatter(log_format)

    # Add console handler if requested
    if log_to_console:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        console_handler.setLevel(numeric_level)
        root_logger.addHandler(console_handler)

    # Add file handler if log file is specified
    if log_file:
        # Create log directory if it doesn't exist
        log_dir = os.path.dirname(log_file)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)

        # Create rotating file handler (10 MB max size, 5 backup files)
        file_handler = RotatingFileHandler(
            log_file,
            maxBytes=10 * 1024 * 1024,  # 10 MB
            backupCount=5,
            encoding='utf-8'
        )
        file_handler.setFormatter(formatter)
        file_handler.setLevel(numeric_level)
        root_logger.addHandler(file_handler)

    # Filter third-party libraries in production
    if environment == "production":
        _filter_third_party_logs()

    # Log configuration details
    logger.info(f"Logging configured: environment={environment}, level={logging.getLevelName(numeric_level)}")
    if log_file:
        logger.info(f"Log file: {log_file}")


def _filter_third_party_logs() -> None:
    """
    Filter out DEBUG and INFO logs from third-party libraries in production.
    """
    for lib_name in FILTERED_LIBRARIES:
        lib_logger = logging.getLogger(lib_name)
        lib_logger.setLevel(logging.WARNING)
        logger.debug(f"Filtered library logs: {lib_name} (WARNING+)")


def get_logger(name: str) -> logging.Logger:
    """
    Get a logger with the specified name.

    Args:
        name: Logger name

    Returns:
        Logger instance
    """
    return logging.getLogger(name)
