"""
Tests for provider utilities.

This module tests the provider utility functions.
"""

import os
import pytest
import tempfile
from pathlib import Path

from bot.providers.core.utils import (
    get_api_key,
    create_temp_file,
    clean_text_for_tts,
    generate_cache_key,
    ensure_directory_exists,
    get_file_extension_for_content_type,
    truncate_text,
    parse_language_code
)

def test_get_api_key(monkeypatch):
    """Test getting API key."""
    # Test with specific environment variable
    monkeypatch.setenv("TEST_API_KEY", "test_key")
    key = get_api_key("test", env_var="TEST_API_KEY")
    assert key == "test_key"
    
    # Test with provider-specific environment variable
    monkeypatch.setenv("TEST_API_KEY", "test_key")
    key = get_api_key("test")
    assert key == "test_key"
    
    # Test with generic environment variable
    monkeypatch.delenv("TEST_API_KEY")
    monkeypatch.setenv("API_KEY", "generic_key")
    key = get_api_key("test")
    assert key == "generic_key"
    
    # Test with no environment variables
    monkeypatch.delenv("API_KEY")
    key = get_api_key("test")
    assert key is None

def test_create_temp_file():
    """Test creating temporary file."""
    # Test with default suffix
    temp_path = create_temp_file()
    assert os.path.exists(temp_path)
    assert temp_path.endswith(".tmp")
    
    # Clean up
    os.unlink(temp_path)
    
    # Test with custom suffix
    temp_path = create_temp_file(suffix=".txt")
    assert os.path.exists(temp_path)
    assert temp_path.endswith(".txt")
    
    # Clean up
    os.unlink(temp_path)

def test_clean_text_for_tts():
    """Test cleaning text for TTS."""
    # Test removing emoji descriptions
    text = "Hello (smile) world (laughing)"
    cleaned = clean_text_for_tts(text)
    assert cleaned == "Hello  world"
    
    # Test removing emoji codes
    text = "Hello :smile: world :laughing:"
    cleaned = clean_text_for_tts(text)
    assert cleaned == "Hello  world"
    
    # Test removing special characters
    text = "Hello 😊 world 😂"
    cleaned = clean_text_for_tts(text)
    assert cleaned == "Hello  world"
    
    # Test preserving punctuation
    text = "Hello, world! How are you? I'm fine; thanks."
    cleaned = clean_text_for_tts(text)
    assert cleaned == text
    
    # Test preserving parentheses
    text = "Hello (world)"
    cleaned = clean_text_for_tts(text)
    assert cleaned == text

def test_generate_cache_key():
    """Test generating cache key."""
    # Test with string
    key1 = generate_cache_key("test", "data")
    assert key1.startswith("test_")
    assert len(key1) > 5
    
    # Test with dictionary
    key2 = generate_cache_key("test", {"key": "value"})
    assert key2.startswith("test_")
    assert len(key2) > 5
    
    # Test with other type
    key3 = generate_cache_key("test", 123)
    assert key3.startswith("test_")
    assert len(key3) > 5
    
    # Test deterministic
    key4 = generate_cache_key("test", "data")
    assert key4 == key1
    
    key5 = generate_cache_key("test", {"key": "value"})
    assert key5 == key2

def test_ensure_directory_exists():
    """Test ensuring directory exists."""
    # Test with string path
    temp_dir = tempfile.mkdtemp()
    test_dir = os.path.join(temp_dir, "test_dir")
    
    path = ensure_directory_exists(test_dir)
    assert os.path.exists(test_dir)
    assert isinstance(path, Path)
    assert path == Path(test_dir)
    
    # Test with Path object
    test_dir2 = os.path.join(temp_dir, "test_dir2")
    path = ensure_directory_exists(Path(test_dir2))
    assert os.path.exists(test_dir2)
    assert isinstance(path, Path)
    assert path == Path(test_dir2)
    
    # Test with existing directory
    path = ensure_directory_exists(test_dir)
    assert os.path.exists(test_dir)
    assert path == Path(test_dir)
    
    # Clean up
    import shutil
    shutil.rmtree(temp_dir)

def test_get_file_extension_for_content_type():
    """Test getting file extension for content type."""
    # Test audio types
    assert get_file_extension_for_content_type("audio/mpeg") == ".mp3"
    assert get_file_extension_for_content_type("audio/mp3") == ".mp3"
    assert get_file_extension_for_content_type("audio/wav") == ".wav"
    assert get_file_extension_for_content_type("audio/wave") == ".wav"
    assert get_file_extension_for_content_type("audio/x-wav") == ".wav"
    assert get_file_extension_for_content_type("audio/webm") == ".webm"
    assert get_file_extension_for_content_type("audio/ogg") == ".ogg"
    assert get_file_extension_for_content_type("audio/aac") == ".aac"
    assert get_file_extension_for_content_type("audio/flac") == ".flac"
    assert get_file_extension_for_content_type("audio/x-flac") == ".flac"
    
    # Test image types
    assert get_file_extension_for_content_type("image/jpeg") == ".jpg"
    assert get_file_extension_for_content_type("image/png") == ".png"
    assert get_file_extension_for_content_type("image/gif") == ".gif"
    assert get_file_extension_for_content_type("image/webp") == ".webp"
    
    # Test video types
    assert get_file_extension_for_content_type("video/mp4") == ".mp4"
    assert get_file_extension_for_content_type("video/webm") == ".webm"
    
    # Test other types
    assert get_file_extension_for_content_type("application/json") == ".json"
    assert get_file_extension_for_content_type("text/plain") == ".txt"
    assert get_file_extension_for_content_type("text/html") == ".html"
    
    # Test unknown type
    assert get_file_extension_for_content_type("application/unknown") == ".bin"

def test_truncate_text():
    """Test truncating text."""
    # Test short text
    text = "Hello, world!"
    truncated = truncate_text(text)
    assert truncated == text
    
    # Test long text
    text = "x" * 200
    truncated = truncate_text(text)
    assert len(truncated) == 100
    assert truncated.endswith("...")
    
    # Test with custom max length
    text = "x" * 50
    truncated = truncate_text(text, max_length=20)
    assert len(truncated) == 20
    assert truncated.endswith("...")

def test_parse_language_code():
    """Test parsing language code."""
    # Test simple codes
    assert parse_language_code("en") == "en"
    assert parse_language_code("ru") == "ru"
    assert parse_language_code("es") == "es"
    
    # Test with region
    assert parse_language_code("en-US") == "en"
    assert parse_language_code("ru-RU") == "ru"
    assert parse_language_code("es-ES") == "es"
    
    # Test with mapping
    assert parse_language_code("eng") == "en"
    assert parse_language_code("english") == "en"
    assert parse_language_code("rus") == "ru"
    assert parse_language_code("russian") == "ru"
    assert parse_language_code("spa") == "es"
    assert parse_language_code("spanish") == "es"
    
    # Test with empty or None
    assert parse_language_code("") == "en"
    assert parse_language_code(None) == "en"
    
    # Test unknown code
    assert parse_language_code("xyz") == "xyz"
