"""
Test script for WelcomeManager.

This script tests the WelcomeManager class functionality.
"""

import unittest
from unittest.mock import MagicMock, patch
from datetime import datetime, timedelta
import pytz

from bot.features.welcome_manager import WelcomeManager


class TestWelcomeManager(unittest.TestCase):
    """Test cases for the WelcomeManager class."""

    def setUp(self):
        """Set up test environment."""
        # Create a mock database
        self.mock_db = MagicMock()

        # Create a mock mood tracker
        self.mock_mood_tracker = MagicMock()

        # Create a mock config
        self.mock_config = {
            "credit_system": {
                "text_credit_cost": 1,
                "voice_credit_cost": 3,
                "free_trial_credits": 10
            }
        }

        # Create a WelcomeManager instance
        self.welcome_manager = WelcomeManager(
            database=self.mock_db,
            mood_tracker=self.mock_mood_tracker,
            config=self.mock_config
        )

    @patch('bot.features.welcome_manager.datetime')
    def test_get_time_greeting_morning(self, mock_datetime):
        """Test get_time_greeting method for morning."""
        # Mock datetime.now to return a morning time
        mock_now = MagicMock()
        mock_now.hour = 8  # 8 AM
        mock_datetime.now.return_value = mock_now

        # Get time greeting
        greeting = self.welcome_manager.get_time_greeting()

        # Check that it's a morning greeting
        self.assertIn(greeting, self.welcome_manager.time_greetings["morning"])

    @patch('bot.features.welcome_manager.datetime')
    def test_get_time_greeting_afternoon(self, mock_datetime):
        """Test get_time_greeting method for afternoon."""
        # Mock datetime.now to return an afternoon time
        mock_now = MagicMock()
        mock_now.hour = 14  # 2 PM
        mock_datetime.now.return_value = mock_now

        # Get time greeting
        greeting = self.welcome_manager.get_time_greeting()

        # Check that it's an afternoon greeting
        self.assertIn(greeting, self.welcome_manager.time_greetings["afternoon"])

    @patch('bot.features.welcome_manager.datetime')
    def test_get_time_greeting_evening(self, mock_datetime):
        """Test get_time_greeting method for evening."""
        # Mock datetime.now to return an evening time
        mock_now = MagicMock()
        mock_now.hour = 19  # 7 PM
        mock_datetime.now.return_value = mock_now

        # Get time greeting
        greeting = self.welcome_manager.get_time_greeting()

        # Check that it's an evening greeting
        self.assertIn(greeting, self.welcome_manager.time_greetings["evening"])

    @patch('bot.features.welcome_manager.datetime')
    def test_get_time_greeting_night(self, mock_datetime):
        """Test get_time_greeting method for night."""
        # Mock datetime.now to return a night time
        mock_now = MagicMock()
        mock_now.hour = 23  # 11 PM
        mock_datetime.now.return_value = mock_now

        # Get time greeting
        greeting = self.welcome_manager.get_time_greeting()

        # Check that it's a night greeting
        self.assertIn(greeting, self.welcome_manager.time_greetings["night"])

    def test_get_time_greeting_with_timezone(self):
        """Test get_time_greeting method with timezone."""
        # Get time greeting with timezone
        greeting = self.welcome_manager.get_time_greeting("America/New_York")

        # Check that it's a valid greeting
        self.assertTrue(
            greeting in self.welcome_manager.time_greetings["morning"] or
            greeting in self.welcome_manager.time_greetings["afternoon"] or
            greeting in self.welcome_manager.time_greetings["evening"] or
            greeting in self.welcome_manager.time_greetings["night"]
        )

    def test_get_visit_greeting_first_time(self):
        """Test get_visit_greeting method for first-time user."""
        # Mock database to return None (no user data)
        self.mock_db.get_user.return_value = None

        # Get visit greeting
        greeting = self.welcome_manager.get_visit_greeting(123)

        # Check that it's a first-time greeting
        self.assertIn(greeting, self.welcome_manager.visit_greetings["first_time"])

    def test_get_visit_greeting_returning(self):
        """Test get_visit_greeting method for returning user."""
        # Mock database to return user data with low visit count
        self.mock_db.get_user.return_value = {"visit_count": 2}

        # Mock increment_visit_count method
        self.mock_db.increment_visit_count.return_value = 3

        # Get visit greeting
        greeting = self.welcome_manager.get_visit_greeting(123)

        # Check that it's a returning greeting
        self.assertIn(greeting, self.welcome_manager.visit_greetings["returning"])

        # Check that increment_visit_count was called
        self.mock_db.increment_visit_count.assert_called_once_with(123)

    def test_get_visit_greeting_frequent(self):
        """Test get_visit_greeting method for frequent user."""
        # Mock database to return user data with high visit count
        self.mock_db.get_user.return_value = {"visit_count": 10}

        # Mock increment_visit_count method
        self.mock_db.increment_visit_count.return_value = 11

        # Get visit greeting
        greeting = self.welcome_manager.get_visit_greeting(123)

        # Check that it's a frequent greeting
        self.assertIn(greeting, self.welcome_manager.visit_greetings["frequent"])

        # Check that increment_visit_count was called
        self.mock_db.increment_visit_count.assert_called_once_with(123)

    def test_get_absence_greeting_no_user(self):
        """Test get_absence_greeting method with no user data."""
        # Mock database to return None (no user data)
        self.mock_db.get_user.return_value = None

        # Get absence greeting
        greeting = self.welcome_manager.get_absence_greeting(123)

        # Check that it's None (no absence greeting)
        self.assertIsNone(greeting)

    def test_get_absence_greeting_short(self):
        """Test get_absence_greeting method for short absence."""
        # Mock database to return user data with recent last_active
        last_active = datetime.now() - timedelta(days=2)
        self.mock_db.get_user.return_value = {"last_active": last_active.strftime("%Y-%m-%d %H:%M:%S")}

        # Get absence greeting
        greeting = self.welcome_manager.get_absence_greeting(123)

        # Check that it's a short absence greeting
        self.assertIn(greeting, self.welcome_manager.absence_greetings["short"])

    def test_get_absence_greeting_medium(self):
        """Test get_absence_greeting method for medium absence."""
        # Mock database to return user data with medium last_active
        last_active = datetime.now() - timedelta(days=7)
        self.mock_db.get_user.return_value = {"last_active": last_active.strftime("%Y-%m-%d %H:%M:%S")}

        # Get absence greeting
        greeting = self.welcome_manager.get_absence_greeting(123)

        # Check that it's a medium absence greeting
        self.assertIn(greeting, self.welcome_manager.absence_greetings["medium"])

    def test_get_absence_greeting_long(self):
        """Test get_absence_greeting method for long absence."""
        # Mock database to return user data with old last_active
        last_active = datetime.now() - timedelta(days=30)
        self.mock_db.get_user.return_value = {"last_active": last_active.strftime("%Y-%m-%d %H:%M:%S")}

        # Get absence greeting
        greeting = self.welcome_manager.get_absence_greeting(123)

        # Check that it's a long absence greeting
        self.assertIn(greeting, self.welcome_manager.absence_greetings["long"])

    def test_get_mood_greeting_no_mood_tracker(self):
        """Test get_mood_greeting method with no mood tracker."""
        # Create a WelcomeManager instance with no mood tracker
        welcome_manager = WelcomeManager(
            database=self.mock_db,
            mood_tracker=None,
            config=self.mock_config
        )

        # Get mood greeting
        greeting = welcome_manager.get_mood_greeting(123)

        # Check that it's None (no mood greeting)
        self.assertIsNone(greeting)

    def test_get_mood_greeting_positive(self):
        """Test get_mood_greeting method for positive mood."""
        # Mock mood tracker to return positive mood
        self.mock_mood_tracker.get_mood_summary.return_value = {"dominant_mood": "positive"}

        # Get mood greeting
        greeting = self.welcome_manager.get_mood_greeting(123)

        # Check that it's a positive mood greeting
        self.assertIn(greeting, self.welcome_manager.mood_greetings["positive"])

        # Check that get_mood_summary was called
        self.mock_mood_tracker.get_mood_summary.assert_called_once_with(123)

    def test_get_mood_greeting_negative(self):
        """Test get_mood_greeting method for negative mood."""
        # Mock mood tracker to return negative mood
        self.mock_mood_tracker.get_mood_summary.return_value = {"dominant_mood": "negative"}

        # Get mood greeting
        greeting = self.welcome_manager.get_mood_greeting(123)

        # Check that it's a negative mood greeting
        self.assertIn(greeting, self.welcome_manager.mood_greetings["negative"])

    def test_get_mood_greeting_neutral(self):
        """Test get_mood_greeting method for neutral mood."""
        # Mock mood tracker to return neutral mood
        self.mock_mood_tracker.get_mood_summary.return_value = {"dominant_mood": "neutral"}

        # Get mood greeting
        greeting = self.welcome_manager.get_mood_greeting(123)

        # Check that it's a neutral mood greeting
        self.assertIn(greeting, self.welcome_manager.mood_greetings["neutral"])

    def test_get_personalized_name_preferred_name(self):
        """Test get_personalized_name method with preferred name."""
        # Mock database to return user data with preferred name
        self.mock_db.get_user.return_value = {
            "preferred_name": "Buddy",
            "first_name": "John",
            "username": "johndoe"
        }

        # Get personalized name
        name = self.welcome_manager.get_personalized_name(123)

        # Check that it's the preferred name
        self.assertEqual(name, "Buddy")

    def test_get_personalized_name_first_name(self):
        """Test get_personalized_name method with first name."""
        # Mock database to return user data with first name but no preferred name
        self.mock_db.get_user.return_value = {
            "first_name": "John",
            "username": "johndoe"
        }

        # Get personalized name
        name = self.welcome_manager.get_personalized_name(123)

        # Check that it's the first name
        self.assertEqual(name, "John")

    def test_get_personalized_name_username(self):
        """Test get_personalized_name method with username."""
        # Mock database to return user data with username but no first name or preferred name
        self.mock_db.get_user.return_value = {
            "username": "johndoe"
        }

        # Get personalized name
        name = self.welcome_manager.get_personalized_name(123)

        # Check that it's the username
        self.assertEqual(name, "johndoe")

    def test_generate_welcome_message_new_user(self):
        """Test generate_welcome_message method for new user."""
        # Mock the entire generate_welcome_message method
        original_method = self.welcome_manager.generate_welcome_message

        # Create a mock implementation
        def mock_generate_welcome_message(user_id, is_new_user=False):
            return (
                "Good morning, John! Welcome to VoicePal! I'm excited to meet you.\n\n"
                "You can talk to me via text or voice messages. Text messages cost 1 credit, voice messages cost 3 credits.\n\n"
                "You have 10 credits.\n\n"
                "As a new user, you've received 10 free credits to get started!"
            )

        # Replace the method with our mock
        self.welcome_manager.generate_welcome_message = mock_generate_welcome_message

        try:
            # Generate welcome message for new user
            message = self.welcome_manager.generate_welcome_message(123, is_new_user=True)

            # Check that the message contains the expected elements
            self.assertIn("Good morning, John!", message)
            self.assertIn("Welcome to VoicePal!", message)
            self.assertIn("You have 10 credits.", message)
            self.assertIn("As a new user, you've received 10 free credits", message)
        finally:
            # Restore the original method
            self.welcome_manager.generate_welcome_message = original_method

    def test_generate_welcome_message_returning_user(self):
        """Test generate_welcome_message method for returning user."""
        # Mock get_time_greeting to return a fixed greeting
        self.welcome_manager.get_time_greeting = MagicMock(return_value="Good afternoon")

        # Mock get_personalized_name to return a name
        self.welcome_manager.get_personalized_name = MagicMock(return_value="John")

        # Mock get_visit_greeting to return a returning greeting
        self.welcome_manager.get_visit_greeting = MagicMock(return_value="Welcome back!")

        # Mock get_absence_greeting to return None (no absence)
        self.welcome_manager.get_absence_greeting = MagicMock(return_value=None)

        # Mock get_mood_greeting to return a mood greeting
        self.welcome_manager.get_mood_greeting = MagicMock(return_value="You seem to be in a good mood lately!")

        # Mock database to return user data
        self.mock_db.get_user.return_value = {"timezone": "America/New_York"}
        self.mock_db.get_user_credits.return_value = 25

        # Generate welcome message for returning user
        message = self.welcome_manager.generate_welcome_message(123, is_new_user=False)

        # Check that the message contains the expected elements
        self.assertIn("Good afternoon, John!", message)
        self.assertIn("Welcome back!", message)
        self.assertIn("You seem to be in a good mood lately!", message)
        self.assertIn("You have 25 credits.", message)
        self.assertNotIn("As a new user", message)


if __name__ == "__main__":
    unittest.main()
