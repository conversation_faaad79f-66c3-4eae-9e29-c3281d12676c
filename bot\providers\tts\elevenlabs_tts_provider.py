"""
ElevenLabs TTS provider for VoicePal.

This module provides a provider for ElevenLabs TTS services.
"""

import os
import logging
import asyncio
import json
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass, field

import httpx
from elevenlabs import Voice, VoiceSettings, generate, set_api_key, get_api_key, voices

from bot.providers.core.provider import TTSProvider
from bot.providers.core.config import TTSProviderConfig
from bot.providers.core.exceptions import (
    ProviderError,
    ProviderConfigError,
    ProviderAuthError,
    ProviderRateLimitError,
    ProviderTimeoutError,
    ProviderNotFoundError,
    ProviderValidationError,
    ProviderNotInitializedError
)

# Set up logging
logger = logging.getLogger(__name__)

@dataclass
class ElevenLabsConfig(TTSProviderConfig):
    """Configuration for ElevenLabs TTS provider."""
    
    api_key: str = ""
    voice_id: str = "21m00Tcm4TlvDq8ikWAM"  # Rachel voice ID
    model_id: str = "eleven_monolingual_v1"
    stability: float = 0.5
    similarity_boost: float = 0.75
    style: float = 0.0
    use_speaker_boost: bool = True
    
    def __post_init__(self):
        """Post-initialization."""
        self.provider_type = "tts"
        self.provider_name = "elevenlabs_tts"
    
    def validate(self) -> List[str]:
        """Validate configuration.
        
        Returns:
            List of validation error messages (empty if valid)
        """
        errors = super().validate()
        
        # Validate stability
        if self.stability < 0 or self.stability > 1:
            errors.append(f"Invalid stability: {self.stability}. Must be between 0 and 1")
        
        # Validate similarity boost
        if self.similarity_boost < 0 or self.similarity_boost > 1:
            errors.append(f"Invalid similarity boost: {self.similarity_boost}. Must be between 0 and 1")
        
        # Validate style
        if self.style < 0 or self.style > 1:
            errors.append(f"Invalid style: {self.style}. Must be between 0 and 1")
        
        return errors

class ElevenLabsTTSProvider(TTSProvider[ElevenLabsConfig]):
    """Provider for ElevenLabs TTS services."""
    
    provider_type = "tts"
    provider_name = "elevenlabs_tts"
    provider_version = "1.0.0"
    provider_description = "Provider for ElevenLabs TTS services"
    config_class = ElevenLabsConfig
    
    def __init__(self, config: ElevenLabsConfig):
        """Initialize provider.
        
        Args:
            config: Provider configuration
        """
        super().__init__(config)
        self.voices_cache = None
    
    def validate_config(self) -> None:
        """Validate provider configuration.
        
        Raises:
            ProviderConfigError: If configuration is invalid
        """
        errors = self.config.validate()
        if errors:
            error_message = "; ".join(errors)
            raise ProviderConfigError(f"Invalid configuration: {error_message}")
    
    def initialize(self) -> None:
        """Initialize provider.
        
        Raises:
            ProviderInitializationError: If initialization fails
        """
        try:
            # Set API key
            set_api_key(self.config.api_key)
            
            # Verify API key
            if not get_api_key():
                raise ProviderAuthError("Failed to set ElevenLabs API key")
            
            self.initialized = True
            logger.info(f"Initialized {self.provider_name} provider")
        except Exception as e:
            logger.error(f"Failed to initialize {self.provider_name} provider: {e}")
            raise ProviderInitializationError(f"Failed to initialize {self.provider_name} provider: {e}") from e
    
    def shutdown(self) -> None:
        """Shutdown provider.
        
        Raises:
            ProviderShutdownError: If shutdown fails
        """
        self.voices_cache = None
        self.initialized = False
        logger.info(f"Shutdown {self.provider_name} provider")
    
    async def text_to_speech(self, text: str, voice_id: Optional[str] = None, **kwargs) -> bytes:
        """Convert text to speech.
        
        Args:
            text: Text to convert to speech
            voice_id: Voice ID (default: config voice_id)
            **kwargs: Additional arguments
            
        Returns:
            Audio data
            
        Raises:
            ProviderError: If text-to-speech conversion fails
        """
        if not self.initialized:
            raise ProviderNotInitializedError(f"{self.provider_name} provider is not initialized")
        
        try:
            # Use provided voice ID or default from config
            voice = voice_id or self.config.voice_id
            
            # Get model ID
            model_id = kwargs.get("model_id", self.config.model_id)
            
            # Create voice settings
            voice_settings = VoiceSettings(
                stability=kwargs.get("stability", self.config.stability),
                similarity_boost=kwargs.get("similarity_boost", self.config.similarity_boost),
                style=kwargs.get("style", self.config.style),
                use_speaker_boost=kwargs.get("use_speaker_boost", self.config.use_speaker_boost)
            )
            
            # Generate speech
            audio_data = await asyncio.to_thread(
                generate,
                text=text,
                voice=voice,
                model=model_id,
                voice_settings=voice_settings
            )
            
            # Return audio data
            return audio_data
        except Exception as e:
            logger.error(f"Failed to convert text to speech: {e}")
            
            # Map exceptions to provider exceptions
            if "api key" in str(e).lower() or "unauthorized" in str(e).lower():
                raise ProviderAuthError(f"Invalid API key: {e}") from e
            elif "rate limit" in str(e).lower() or "quota" in str(e).lower():
                raise ProviderRateLimitError(f"Rate limit exceeded: {e}") from e
            elif "timeout" in str(e).lower():
                raise ProviderTimeoutError(f"Request timed out: {e}") from e
            elif "not found" in str(e).lower() or "no such voice" in str(e).lower():
                raise ProviderNotFoundError(f"Resource not found: {e}") from e
            elif "invalid" in str(e).lower():
                raise ProviderValidationError(f"Invalid request: {e}") from e
            else:
                raise ProviderError(f"Failed to convert text to speech: {e}") from e
    
    def get_available_voices(self) -> List[Dict[str, Any]]:
        """Get available voices.
        
        Returns:
            List of available voices
            
        Raises:
            ProviderError: If getting available voices fails
        """
        if not self.initialized:
            raise ProviderNotInitializedError(f"{self.provider_name} provider is not initialized")
        
        # Return cached voices if available
        if self.voices_cache:
            return self.voices_cache
        
        try:
            # Get voices from ElevenLabs
            elevenlabs_voices = voices()
            
            # Convert to standard format
            result = []
            for voice in elevenlabs_voices:
                result.append({
                    "id": voice.voice_id,
                    "name": voice.name,
                    "gender": "unknown",  # ElevenLabs doesn't provide gender info
                    "language": ", ".join(voice.labels.get("language", [])) if hasattr(voice, "labels") else "en",
                    "description": voice.description,
                    "preview_url": voice.preview_url if hasattr(voice, "preview_url") else None,
                    "category": voice.category if hasattr(voice, "category") else "premade"
                })
            
            # Cache voices
            self.voices_cache = result
            
            return result
        except Exception as e:
            logger.error(f"Failed to get available voices: {e}")
            
            # Map exceptions to provider exceptions
            if "api key" in str(e).lower() or "unauthorized" in str(e).lower():
                raise ProviderAuthError(f"Invalid API key: {e}") from e
            elif "rate limit" in str(e).lower() or "quota" in str(e).lower():
                raise ProviderRateLimitError(f"Rate limit exceeded: {e}") from e
            elif "timeout" in str(e).lower():
                raise ProviderTimeoutError(f"Request timed out: {e}") from e
            else:
                raise ProviderError(f"Failed to get available voices: {e}") from e
