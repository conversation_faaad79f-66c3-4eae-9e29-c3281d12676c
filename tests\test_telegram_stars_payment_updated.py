"""
Test script for the updated Telegram Stars payment system.

This script tests the functionality of the Telegram Stars payment system,
focusing on the credit source tracking and transaction recording.
"""

import os
import unittest
import sys
import logging
from unittest.mock import MagicMock, AsyncMock, patch
from datetime import datetime

# Add the parent directory to the path so we can import the bot modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from bot.payment.telegram_stars_payment import TelegramStarsPayment

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class MockDatabase:
    """Mock database for testing."""
    
    def __init__(self):
        self.users = {}
        self.transactions = []
        self.invoices = []
    
    def get_user(self, user_id):
        """Get user data."""
        return self.users.get(user_id, None)
    
    def add_user(self, user_id, username=None, first_name=None, last_name=None, **kwargs):
        """Add a new user."""
        if user_id not in self.users:
            self.users[user_id] = {
                'user_id': user_id,
                'username': username,
                'first_name': first_name,
                'last_name': last_name,
                'credits': 0
            }
            return True
        return False
    
    def get_user_credits(self, user_id):
        """Get user credits."""
        return self.users.get(user_id, {}).get('credits', 0)
    
    def add_credits(self, user_id, credit_amount, source="manual"):
        """Add credits to user."""
        if user_id not in self.users:
            self.users[user_id] = {'user_id': user_id, 'credits': 0}
        self.users[user_id]['credits'] += credit_amount
        
        # Record transaction
        transaction = {
            'user_id': user_id,
            'amount': 0.0,
            'credits': credit_amount,
            'transaction_id': f"test_transaction_{len(self.transactions)}",
            'status': "completed",
            'source': source
        }
        self.transactions.append(transaction)
        
        return self.users[user_id]['credits']
    
    def add_transaction(self, user_id, amount, credits, transaction_id, status):
        """Add transaction."""
        transaction = {
            'user_id': user_id,
            'amount': amount,
            'credits': credits,
            'transaction_id': transaction_id,
            'status': status
        }
        self.transactions.append(transaction)
        return len(self.transactions)
    
    def add_invoice(self, user_id, package_id, payload, amount, currency, status):
        """Add invoice."""
        invoice = {
            'user_id': user_id,
            'package_id': package_id,
            'payload': payload,
            'amount': amount,
            'currency': currency,
            'status': status
        }
        self.invoices.append(invoice)
        return len(self.invoices)
    
    def update_invoice_status(self, payload, status):
        """Update invoice status."""
        for invoice in self.invoices:
            if invoice['payload'] == payload:
                invoice['status'] = status
                return True
        return False
    
    def get_user_transactions(self, user_id, limit=10):
        """Get user transactions."""
        return [t for t in self.transactions if t['user_id'] == user_id][:limit]

class TestTelegramStarsPaymentUpdated(unittest.TestCase):
    """Test case for the updated Telegram Stars payment system."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create mock database
        self.database = MockDatabase()
        
        # Create payment system
        self.payment_system = TelegramStarsPayment(database=self.database)
    
    def test_get_credit_packages(self):
        """Test getting credit packages."""
        packages = self.payment_system.get_credit_packages()
        
        # Check that packages exist
        self.assertIsNotNone(packages)
        self.assertIsInstance(packages, dict)
        self.assertGreater(len(packages), 0)
        
        # Check package structure
        for package_id, package in packages.items():
            self.assertIn('id', package)
            self.assertIn('title', package)
            self.assertIn('description', package)
            self.assertIn('credits', package)
            self.assertIn('price', package)
            self.assertIn('currency', package)
    
    def test_process_payment_with_source(self):
        """Test processing payment with source tracking."""
        # Add a user
        self.database.add_user(user_id=123456789)
        
        # Create a mock payment
        class MockPayment:
            def __init__(self):
                self.invoice_payload = "credits-small-12345"
                self.total_amount = 100
                self.telegram_payment_charge_id = "test_charge_id"
        
        payment = MockPayment()
        
        # Process payment
        credits_added = self.payment_system.process_payment(123456789, payment)
        
        # Check that credits were added
        self.assertEqual(credits_added, 100)
        self.assertEqual(self.database.get_user_credits(123456789), 100)
        
        # Check that transaction was recorded
        transactions = self.database.get_user_transactions(123456789)
        self.assertEqual(len(transactions), 1)
        
        # Check transaction details
        transaction = transactions[0]
        self.assertEqual(transaction['user_id'], 123456789)
        self.assertEqual(transaction['credits'], 100)
        self.assertEqual(transaction['status'], "completed")
    
    @patch('bot.payment.telegram_stars_payment.TelegramStarsPayment.process_payment')
    async def test_successful_payment_callback(self, mock_process_payment):
        """Test successful payment callback with source tracking."""
        # Set up mock
        mock_process_payment.return_value = 100
        
        # Create mock update and context
        update = MagicMock()
        update.message.successful_payment.invoice_payload = "credits-small-12345"
        update.message.successful_payment.total_amount = 100
        update.message.successful_payment.telegram_payment_charge_id = "test_charge_id"
        update.effective_user.id = 123456789
        update.message.reply_text = AsyncMock()
        
        context = MagicMock()
        
        # Call the method
        await self.payment_system.successful_payment_callback(update, context)
        
        # Check that process_payment was called
        mock_process_payment.assert_called_once_with(
            123456789, update.message.successful_payment
        )
        
        # Check that reply_text was called
        update.message.reply_text.assert_called_once()

if __name__ == "__main__":
    unittest.main()
