"""
Event tracking for VoicePal analytics.

This module provides comprehensive event tracking for user actions and system events.
"""

import logging
import json
import time
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum

logger = logging.getLogger(__name__)

class EventType(Enum):
    """Event types for tracking."""
    USER_REGISTRATION = "user_registration"
    USER_LOGIN = "user_login"
    CONVERSATION_STARTED = "conversation_started"
    CONVERSATION_ENDED = "conversation_ended"
    MESSAGE_SENT = "message_sent"
    VOICE_MESSAGE_SENT = "voice_message_sent"
    AI_RESPONSE_GENERATED = "ai_response_generated"
    PAYMENT_INITIATED = "payment_initiated"
    PAYMENT_COMPLETED = "payment_completed"
    PAYMENT_FAILED = "payment_failed"
    CREDITS_PURCHASED = "credits_purchased"
    CREDITS_USED = "credits_used"
    FEATURE_USED = "feature_used"
    ERROR_OCCURRED = "error_occurred"
    PERFORMANCE_METRIC = "performance_metric"
    USER_FEEDBACK = "user_feedback"
    SUBSCRIPTION_CREATED = "subscription_created"
    SUBSCRIPTION_CANCELLED = "subscription_cancelled"
    REFERRAL_MADE = "referral_made"
    SETTINGS_CHANGED = "settings_changed"

@dataclass
class Event:
    """Event data structure."""
    event_id: str
    event_type: EventType
    user_id: Optional[int]
    session_id: Optional[str]
    timestamp: datetime
    properties: Dict[str, Any]
    context: Dict[str, Any]

    def to_dict(self) -> Dict[str, Any]:
        """Convert event to dictionary."""
        return {
            "event_id": self.event_id,
            "event_type": self.event_type.value,
            "user_id": self.user_id,
            "session_id": self.session_id,
            "timestamp": self.timestamp.isoformat(),
            "properties": self.properties,
            "context": self.context
        }

@dataclass
class EventSummary:
    """Event summary statistics."""
    event_type: str
    count: int
    unique_users: int
    first_occurrence: datetime
    last_occurrence: datetime
    avg_per_user: float

class EventTracker:
    """Event tracking system for analytics."""

    def __init__(self, database, redis_client=None, batch_size: int = 100):
        """
        Initialize event tracker.

        Args:
            database: Database instance
            redis_client: Redis client for real-time tracking
            batch_size: Batch size for bulk operations
        """
        self.database = database
        self.redis_client = redis_client
        self.batch_size = batch_size

        # Event buffer for batch processing
        self.event_buffer: List[Event] = []

        # Initialize events table
        self._initialize_events_table()

        logger.info("Event tracker initialized")

    def _initialize_events_table(self):
        """Initialize events table if it doesn't exist."""
        try:
            self.database.execute("""
                CREATE TABLE IF NOT EXISTS events (
                    event_id TEXT PRIMARY KEY,
                    event_type TEXT NOT NULL,
                    user_id INTEGER,
                    session_id TEXT,
                    timestamp TEXT NOT NULL,
                    properties TEXT,
                    context TEXT,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # Create indexes for performance
            self.database.execute("CREATE INDEX IF NOT EXISTS idx_events_type ON events(event_type)")
            self.database.execute("CREATE INDEX IF NOT EXISTS idx_events_user_id ON events(user_id)")
            self.database.execute("CREATE INDEX IF NOT EXISTS idx_events_timestamp ON events(timestamp)")

            self.database.commit()

        except Exception as e:
            logger.error(f"Failed to initialize events table: {e}")

    def track_event(
        self,
        event_type: EventType,
        user_id: Optional[int] = None,
        session_id: Optional[str] = None,
        properties: Optional[Dict[str, Any]] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Track an event.

        Args:
            event_type: Type of event
            user_id: User ID (if applicable)
            session_id: Session ID (if applicable)
            properties: Event properties
            context: Event context

        Returns:
            Event ID
        """
        try:
            # Generate event ID
            event_id = f"{event_type.value}_{int(time.time() * 1000000)}"

            # Create event
            event = Event(
                event_id=event_id,
                event_type=event_type,
                user_id=user_id,
                session_id=session_id,
                timestamp=datetime.utcnow(),
                properties=properties or {},
                context=context or {}
            )

            # Add to buffer
            self.event_buffer.append(event)

            # Store in Redis for real-time analytics
            if self.redis_client:
                try:
                    self.redis_client.lpush("events:realtime", json.dumps(event.to_dict()))
                    self.redis_client.expire("events:realtime", 3600)  # 1 hour TTL
                except Exception as e:
                    logger.warning(f"Failed to store event in Redis: {e}")

            # Flush buffer if it's full
            if len(self.event_buffer) >= self.batch_size:
                self.flush_events()

            return event_id

        except Exception as e:
            logger.error(f"Failed to track event: {e}")
            return ""

    def flush_events(self):
        """Flush event buffer to database."""
        if not self.event_buffer:
            return

        try:
            # Prepare batch insert
            events_data = []
            for event in self.event_buffer:
                events_data.append((
                    event.event_id,
                    event.event_type.value,
                    event.user_id,
                    event.session_id,
                    event.timestamp.isoformat(),
                    json.dumps(event.properties),
                    json.dumps(event.context)
                ))

            # Batch insert
            self.database.executemany("""
                INSERT OR REPLACE INTO events
                (event_id, event_type, user_id, session_id, timestamp, properties, context)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, events_data)

            self.database.commit()

            logger.debug(f"Flushed {len(self.event_buffer)} events to database")
            self.event_buffer.clear()

        except Exception as e:
            logger.error(f"Failed to flush events: {e}")

    def get_event_summary(self, days: int = 30) -> List[EventSummary]:
        """
        Get event summary statistics.

        Args:
            days: Number of days to analyze

        Returns:
            List of event summaries
        """
        try:
            # Ensure events are flushed
            self.flush_events()

            cutoff_date = datetime.utcnow() - timedelta(days=days)

            summaries = []

            # Get all event types
            event_types = self.database.execute("""
                SELECT DISTINCT event_type FROM events
                WHERE timestamp >= ?
            """, (cutoff_date.isoformat(),)).fetchall()

            for event_type_row in event_types:
                event_type = event_type_row['event_type']

                # Get statistics for this event type
                stats = self.database.execute("""
                    SELECT
                        COUNT(*) as count,
                        COUNT(DISTINCT user_id) as unique_users,
                        MIN(timestamp) as first_occurrence,
                        MAX(timestamp) as last_occurrence
                    FROM events
                    WHERE event_type = ? AND timestamp >= ?
                """, (event_type, cutoff_date.isoformat())).fetchone()

                if stats:
                    avg_per_user = stats['count'] / stats['unique_users'] if stats['unique_users'] > 0 else 0

                    summaries.append(EventSummary(
                        event_type=event_type,
                        count=stats['count'],
                        unique_users=stats['unique_users'],
                        first_occurrence=datetime.fromisoformat(stats['first_occurrence']),
                        last_occurrence=datetime.fromisoformat(stats['last_occurrence']),
                        avg_per_user=avg_per_user
                    ))

            return summaries

        except Exception as e:
            logger.error(f"Failed to get event summary: {e}")
            return []

    def get_user_events(self, user_id: int, days: int = 30) -> List[Dict[str, Any]]:
        """
        Get events for a specific user.

        Args:
            user_id: User ID
            days: Number of days to look back

        Returns:
            List of user events
        """
        try:
            self.flush_events()

            cutoff_date = datetime.utcnow() - timedelta(days=days)

            events = self.database.execute("""
                SELECT * FROM events
                WHERE user_id = ? AND timestamp >= ?
                ORDER BY timestamp DESC
            """, (user_id, cutoff_date.isoformat())).fetchall()

            result = []
            for event in events:
                event_dict = dict(event)
                # Parse JSON fields
                try:
                    event_dict['properties'] = json.loads(event['properties']) if event['properties'] else {}
                    event_dict['context'] = json.loads(event['context']) if event['context'] else {}
                except json.JSONDecodeError:
                    event_dict['properties'] = {}
                    event_dict['context'] = {}

                result.append(event_dict)

            return result

        except Exception as e:
            logger.error(f"Failed to get user events: {e}")
            return []

    def get_event_trends(self, event_type: EventType, days: int = 30) -> List[Tuple[str, int]]:
        """
        Get daily trends for a specific event type.

        Args:
            event_type: Event type to analyze
            days: Number of days to analyze

        Returns:
            List of (date, count) tuples
        """
        try:
            self.flush_events()

            trends = []
            end_date = datetime.utcnow()

            for i in range(days):
                day_start = end_date - timedelta(days=i+1)
                day_end = end_date - timedelta(days=i)

                count = self.database.execute("""
                    SELECT COUNT(*) FROM events
                    WHERE event_type = ?
                    AND timestamp >= ? AND timestamp < ?
                """, (event_type.value, day_start.isoformat(), day_end.isoformat())).fetchone()[0]

                trends.append((day_start.strftime("%Y-%m-%d"), count))

            trends.reverse()  # Chronological order
            return trends

        except Exception as e:
            logger.error(f"Failed to get event trends: {e}")
            return []

    def get_funnel_analysis(self, funnel_events: List[EventType], days: int = 30) -> Dict[str, Any]:
        """
        Analyze conversion funnel for a sequence of events.

        Args:
            funnel_events: List of events in funnel order
            days: Number of days to analyze

        Returns:
            Funnel analysis data
        """
        try:
            self.flush_events()

            cutoff_date = datetime.utcnow() - timedelta(days=days)

            funnel_data = {
                "steps": [],
                "conversion_rates": [],
                "drop_off_rates": []
            }

            previous_users = None

            for i, event_type in enumerate(funnel_events):
                # Get users who completed this step
                users = self.database.execute("""
                    SELECT DISTINCT user_id FROM events
                    WHERE event_type = ? AND timestamp >= ?
                    AND user_id IS NOT NULL
                """, (event_type.value, cutoff_date.isoformat())).fetchall()

                user_count = len(users)
                user_ids = {user['user_id'] for user in users}

                step_data = {
                    "step": i + 1,
                    "event_type": event_type.value,
                    "users": user_count
                }

                if previous_users is not None:
                    # Calculate conversion rate from previous step
                    if len(previous_users) > 0:
                        # Users who completed both this step and previous step
                        converted_users = user_ids.intersection(previous_users)
                        conversion_rate = len(converted_users) / len(previous_users)
                        drop_off_rate = 1 - conversion_rate
                    else:
                        conversion_rate = 0.0
                        drop_off_rate = 1.0

                    step_data["conversion_rate"] = conversion_rate
                    step_data["drop_off_rate"] = drop_off_rate

                    funnel_data["conversion_rates"].append(conversion_rate)
                    funnel_data["drop_off_rates"].append(drop_off_rate)

                funnel_data["steps"].append(step_data)
                previous_users = user_ids

            return funnel_data

        except Exception as e:
            logger.error(f"Failed to analyze funnel: {e}")
            return {"steps": [], "conversion_rates": [], "drop_off_rates": []}

    def get_cohort_events(self, cohort_date: datetime, event_type: EventType) -> Dict[str, Any]:
        """
        Get events for a specific user cohort.

        Args:
            cohort_date: Date when cohort users registered
            event_type: Event type to analyze

        Returns:
            Cohort event data
        """
        try:
            self.flush_events()

            # Get users who registered on cohort date
            cohort_start = cohort_date
            cohort_end = cohort_date + timedelta(days=1)

            cohort_users = self.database.execute("""
                SELECT user_id FROM users
                WHERE created_at >= ? AND created_at < ?
            """, (cohort_start.isoformat(), cohort_end.isoformat())).fetchall()

            if not cohort_users:
                return {"cohort_size": 0, "events": []}

            user_ids = [user['user_id'] for user in cohort_users]
            placeholders = ','.join(['?'] * len(user_ids))

            # Get events for cohort users
            events = self.database.execute(f"""
                SELECT timestamp, COUNT(*) as event_count
                FROM events
                WHERE user_id IN ({placeholders})
                AND event_type = ?
                GROUP BY DATE(timestamp)
                ORDER BY timestamp
            """, user_ids + [event_type.value]).fetchall()

            return {
                "cohort_size": len(cohort_users),
                "cohort_date": cohort_date.strftime("%Y-%m-%d"),
                "event_type": event_type.value,
                "events": [{"date": event['timestamp'][:10], "count": event['event_count']} for event in events]
            }

        except Exception as e:
            logger.error(f"Failed to get cohort events: {e}")
            return {"cohort_size": 0, "events": []}

    def cleanup_old_events(self, days: int = 90):
        """
        Clean up events older than specified days.

        Args:
            days: Number of days to retain events
        """
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days)

            result = self.database.execute("""
                DELETE FROM events
                WHERE timestamp < ?
            """, (cutoff_date.isoformat(),))

            self.database.commit()

            logger.info(f"Cleaned up {result.rowcount} old events")

        except Exception as e:
            logger.error(f"Failed to cleanup old events: {e}")

    # Convenience methods for common events

    def track_user_registration(self, user_id: int, registration_method: str = "telegram"):
        """Track user registration event."""
        return self.track_event(
            EventType.USER_REGISTRATION,
            user_id=user_id,
            properties={"registration_method": registration_method}
        )

    def track_conversation_started(self, user_id: int, conversation_id: str, session_id: str):
        """Track conversation started event."""
        return self.track_event(
            EventType.CONVERSATION_STARTED,
            user_id=user_id,
            session_id=session_id,
            properties={"conversation_id": conversation_id}
        )

    def track_message_sent(self, user_id: int, message_type: str, session_id: str):
        """Track message sent event."""
        return self.track_event(
            EventType.MESSAGE_SENT,
            user_id=user_id,
            session_id=session_id,
            properties={"message_type": message_type}
        )

    def track_payment_completed(self, user_id: int, amount: float, currency: str, payment_method: str):
        """Track payment completed event."""
        return self.track_event(
            EventType.PAYMENT_COMPLETED,
            user_id=user_id,
            properties={
                "amount": amount,
                "currency": currency,
                "payment_method": payment_method
            }
        )

    def track_feature_used(self, user_id: int, feature_name: str, session_id: str):
        """Track feature usage event."""
        return self.track_event(
            EventType.FEATURE_USED,
            user_id=user_id,
            session_id=session_id,
            properties={"feature_name": feature_name}
        )
