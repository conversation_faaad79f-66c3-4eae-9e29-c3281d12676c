"""
Database migration script for VoicePal.

This script performs a safe migration of the database schema to ensure
consistency and fix foreign key constraint issues.
"""

import os
import sys
import logging
import sqlite3
import json
from typing import Dict, List, Any, Tuple
from datetime import datetime
import traceback

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# Define the target schema
TARGET_SCHEMA = {
    "users": """
        CREATE TABLE users (
            user_id INTEGER PRIMARY KEY,
            username TEXT,
            first_name TEXT,
            last_name TEXT,
            credits INTEGER DEFAULT 0,
            personality TEXT DEFAULT 'friendly',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_active TIMESTAMP,
            free_credits_received BOOLEAN DEFAULT 0,
            free_credits_date TIMESTAMP,
            is_verified BOOLEAN DEFAULT 0,
            verification_date TIMESTAMP,
            device_id TEXT,
            registration_ip TEXT,
            last_ip TEXT,
            login_count INTEGER DEFAULT 1,
            visit_count INTEGER DEFAULT 0,
            timezone TEXT,
            preferred_name TEXT
        )
    """,
    "transactions": """
        CREATE TABLE transactions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            amount REAL,
            credits INTEGER,
            transaction_id TEXT,
            status TEXT,
            source TEXT DEFAULT 'purchase',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (user_id) ON DELETE CASCADE
        )
    """,
    "conversations": """
        CREATE TABLE conversations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            message TEXT,
            response TEXT,
            is_voice BOOLEAN,
            credits_used INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (user_id) ON DELETE CASCADE
        )
    """,
    "user_preferences": """
        CREATE TABLE user_preferences (
            user_id INTEGER,
            preference_key TEXT,
            preference_value TEXT,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (user_id, preference_key),
            FOREIGN KEY (user_id) REFERENCES users (user_id) ON DELETE CASCADE
        )
    """,
    "mood_entries": """
        CREATE TABLE mood_entries (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            sentiment TEXT,
            confidence REAL,
            source TEXT,
            message_text TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (user_id) ON DELETE CASCADE
        )
    """,
    "user_summaries": """
        CREATE TABLE user_summaries (
            user_id INTEGER PRIMARY KEY,
            summary TEXT,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (user_id) ON DELETE CASCADE
        )
    """,
    "user_interests": """
        CREATE TABLE user_interests (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            interest TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (user_id) ON DELETE CASCADE
        )
    """,
    "invoices": """
        CREATE TABLE invoices (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            package_id TEXT,
            payload TEXT UNIQUE,
            amount INTEGER,
            currency TEXT,
            status TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (user_id) ON DELETE CASCADE
        )
    """,
    "sentiment_responses": """
        CREATE TABLE sentiment_responses (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            sentiment TEXT,
            response_template TEXT,
            tone TEXT,
            empathy_level TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    """
}

def backup_database(db_file: str) -> str:
    """
    Create a backup of the database.
    
    Args:
        db_file: Path to the database file
        
    Returns:
        Path to the backup file
    """
    backup_file = f"{db_file}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    try:
        # Copy the database file
        with open(db_file, 'rb') as src, open(backup_file, 'wb') as dst:
            dst.write(src.read())
        logger.info(f"Created database backup: {backup_file}")
        return backup_file
    except Exception as e:
        logger.error(f"Error creating database backup: {e}")
        raise

def export_data(conn: sqlite3.Connection) -> Dict[str, List[Dict[str, Any]]]:
    """
    Export all data from the database.
    
    Args:
        conn: SQLite connection object
        
    Returns:
        Dict mapping table names to lists of records
    """
    data = {}
    cursor = conn.cursor()
    
    # Get all tables
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = [row[0] for row in cursor.fetchall()]
    
    # Export data from each table
    for table in tables:
        try:
            cursor.execute(f"SELECT * FROM {table}")
            columns = [column[0] for column in cursor.description]
            records = []
            
            for row in cursor.fetchall():
                record = {}
                for i, column in enumerate(columns):
                    record[column] = row[i]
                records.append(record)
            
            data[table] = records
            logger.info(f"Exported {len(records)} records from {table}")
        except sqlite3.Error as e:
            logger.error(f"Error exporting data from {table}: {e}")
    
    return data

def migrate_database(db_file: str) -> bool:
    """
    Migrate the database to the target schema.
    
    Args:
        db_file: Path to the database file
        
    Returns:
        True if migration was successful, False otherwise
    """
    try:
        # Create backup
        backup_file = backup_database(db_file)
        
        # Connect to the database
        conn = sqlite3.connect(db_file)
        conn.row_factory = sqlite3.Row
        
        # Export data
        data = export_data(conn)
        
        # Close connection
        conn.close()
        
        # Create new database with target schema
        if os.path.exists(db_file):
            os.remove(db_file)
        
        # Connect to the new database
        conn = sqlite3.connect(db_file)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # Create tables with target schema
        for table, schema in TARGET_SCHEMA.items():
            cursor.execute(schema)
        
        # Import data
        for table, records in data.items():
            if table in TARGET_SCHEMA and records:
                # Get columns for this table
                cursor.execute(f"PRAGMA table_info({table})")
                columns = [column['name'] for column in cursor.fetchall()]
                
                # Filter records to only include columns that exist in the new schema
                for record in records:
                    # Filter record to only include columns that exist in the new schema
                    filtered_record = {k: v for k, v in record.items() if k in columns}
                    
                    # Skip if no valid columns
                    if not filtered_record:
                        continue
                    
                    # Generate placeholders and values
                    placeholders = ', '.join(['?'] * len(filtered_record))
                    columns_str = ', '.join(filtered_record.keys())
                    values = list(filtered_record.values())
                    
                    # Insert record
                    try:
                        cursor.execute(
                            f"INSERT INTO {table} ({columns_str}) VALUES ({placeholders})",
                            values
                        )
                    except sqlite3.Error as e:
                        logger.error(f"Error inserting record into {table}: {e}")
                        logger.error(f"Record: {filtered_record}")
        
        # Commit changes
        conn.commit()
        
        # Create indexes
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_transactions_user_id ON transactions(user_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_conversations_user_id ON conversations(user_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_mood_entries_user_id ON mood_entries(user_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_user_interests_user_id ON user_interests(user_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_invoices_user_id ON invoices(user_id)")
        
        # Commit changes
        conn.commit()
        
        # Close connection
        conn.close()
        
        logger.info("Database migration completed successfully")
        return True
    except Exception as e:
        logger.error(f"Error migrating database: {e}")
        logger.error(traceback.format_exc())
        return False

def main():
    """Main function to migrate the database."""
    # Get database file path from command line or use default
    if len(sys.argv) > 1:
        db_file = sys.argv[1]
    else:
        db_file = "voicepal.db"
    
    logger.info(f"Migrating database: {db_file}")
    
    if not os.path.exists(db_file):
        logger.error(f"Database file not found: {db_file}")
        return 1
    
    success = migrate_database(db_file)
    
    if success:
        logger.info("Database migration completed successfully")
        return 0
    else:
        logger.error("Database migration failed")
        return 1

if __name__ == "__main__":
    sys.exit(main())
