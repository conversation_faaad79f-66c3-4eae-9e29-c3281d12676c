"""
Security monitoring system for VoicePal.

This module provides a comprehensive security monitoring system for the VoicePal bot,
including anomaly detection, security event tracking, and admin alerts.
"""

import logging
import json
import time
import asyncio
import hashlib
from typing import Dict, Any, List, Optional, Tuple, Set, Union
from datetime import datetime, timed<PERSON>ta
from collections import defaultdict, Counter
import sqlite3

from telegram import Bot, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.error import TelegramError

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# Security event severity levels
SEVERITY_INFO = "INFO"
SEVERITY_LOW = "LOW"
SEVERITY_MEDIUM = "MEDIUM"
SEVERITY_HIGH = "HIGH"
SEVERITY_CRITICAL = "CRITICAL"

# Security event categories
CATEGORY_AUTH = "AUTHENTICATION"
CATEGORY_ACCESS = "ACCESS_CONTROL"
CATEGORY_PAYMENT = "PAYMENT"
CATEGORY_ABUSE = "ABUSE"
CATEGORY_RATE_LIMIT = "RATE_LIMIT"
CATEGORY_API = "API"
CATEGORY_SYSTEM = "SYSTEM"

class SecurityMonitor:
    """
    Security monitoring system for VoicePal.

    This class provides comprehensive security monitoring capabilities,
    including anomaly detection, security event tracking, and admin alerts.
    """

    def __init__(self, database, config_manager, bot: Optional[Bot] = None):
        """
        Initialize the security monitor.

        Args:
            database: Database instance
            config_manager: Configuration manager instance
            bot: Telegram bot instance for sending alerts
        """
        self.database = database
        self.config_manager = config_manager
        self.bot = bot

        # Get admin user IDs from config
        self.admin_ids = self.config_manager.get_admin_user_ids()

        # Get security monitoring configuration
        self.config = self.config_manager.get_feature_config("security_monitoring") or {}

        # Default configuration values
        self.alert_thresholds = self.config.get("alert_thresholds", {
            "failed_login_attempts": 5,
            "payment_failures": 3,
            "api_key_access": 10,
            "credit_usage_rate": 50,  # credits per minute
            "multiple_accounts": 2,
            "verification_failures": 3
        })

        # Alert cooldowns to prevent spam (in seconds)
        self.alert_cooldowns = self.config.get("alert_cooldowns", {
            SEVERITY_INFO: 3600,      # 1 hour
            SEVERITY_LOW: 1800,       # 30 minutes
            SEVERITY_MEDIUM: 900,     # 15 minutes
            SEVERITY_HIGH: 300,       # 5 minutes
            SEVERITY_CRITICAL: 60     # 1 minute
        })

        # Initialize database tables
        self._setup_database()

        # Track last alert times to implement cooldowns
        self.last_alert_times = {}

        # Cache for recent security events to detect patterns
        self.recent_events_cache = []
        self.recent_events_cache_size = 100

        logger.info("Security monitor initialized")

    def _setup_database(self) -> None:
        """Create security monitoring tables if they don't exist."""
        try:
            cursor = self.database.conn.cursor()

            # Create security_events table
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS security_events (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                event_type TEXT NOT NULL,
                category TEXT NOT NULL,
                severity TEXT NOT NULL,
                user_id INTEGER,
                ip_address TEXT,
                device_id TEXT,
                description TEXT,
                details TEXT,
                resolved BOOLEAN DEFAULT 0,
                resolution_notes TEXT,
                resolution_time TIMESTAMP
            )
            ''')

            # Create security_alerts table
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS security_alerts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                alert_type TEXT NOT NULL,
                severity TEXT NOT NULL,
                description TEXT,
                details TEXT,
                notified_admins TEXT,
                resolved BOOLEAN DEFAULT 0,
                resolution_time TIMESTAMP
            )
            ''')

            # Create security_metrics table
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS security_metrics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                metric_name TEXT NOT NULL,
                metric_value REAL,
                details TEXT
            )
            ''')

            # Create anomaly_detections table
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS anomaly_detections (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                anomaly_type TEXT NOT NULL,
                severity TEXT NOT NULL,
                user_id INTEGER,
                ip_address TEXT,
                device_id TEXT,
                description TEXT,
                details TEXT,
                resolved BOOLEAN DEFAULT 0,
                resolution_time TIMESTAMP
            )
            ''')

            self.database.conn.commit()
            logger.info("Security monitoring tables created")
        except sqlite3.Error as e:
            logger.error(f"Error setting up security monitoring tables: {e}")
            raise

    def log_security_event(self, event_type: str, category: str, severity: str,
                          user_id: Optional[int] = None, ip_address: Optional[str] = None,
                          device_id: Optional[str] = None, description: Optional[str] = None,
                          details: Optional[Dict[str, Any]] = None) -> int:
        """
        Log a security event.

        Args:
            event_type: Type of security event
            category: Category of security event
            severity: Severity level of event
            user_id: User ID associated with event
            ip_address: IP address associated with event
            device_id: Device ID associated with event
            description: Human-readable description of event
            details: Additional details as a dictionary

        Returns:
            int: ID of the logged event
        """
        try:
            cursor = self.database.conn.cursor()

            # Convert details dictionary to JSON string
            details_json = json.dumps(details) if details else None

            # Insert event into database
            cursor.execute(
                """INSERT INTO security_events
                   (timestamp, event_type, category, severity, user_id, ip_address,
                    device_id, description, details)
                   VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)""",
                (datetime.now().isoformat(), event_type, category, severity, user_id,
                 ip_address, device_id, description, details_json)
            )
            self.database.conn.commit()
            event_id = cursor.lastrowid

            # Add to recent events cache
            self.recent_events_cache.append({
                'id': event_id,
                'timestamp': datetime.now(),
                'event_type': event_type,
                'category': category,
                'severity': severity,
                'user_id': user_id
            })

            # Trim cache if needed
            if len(self.recent_events_cache) > self.recent_events_cache_size:
                self.recent_events_cache = self.recent_events_cache[-self.recent_events_cache_size:]

            # Check if we should generate an alert based on this event
            self._check_for_alert_conditions(event_type, category, severity, user_id)

            # Log to console for high severity events
            if severity in [SEVERITY_HIGH, SEVERITY_CRITICAL]:
                logger.warning(f"Security event: {event_type} - {description}")
            else:
                logger.info(f"Security event: {event_type} - {description}")

            return event_id
        except Exception as e:
            logger.error(f"Error logging security event: {e}")
            return -1

    def _check_for_alert_conditions(self, event_type: str, category: str,
                                   severity: str, user_id: Optional[int] = None) -> None:
        """
        Check if current event should trigger an alert based on patterns.

        Args:
            event_type: Type of security event
            category: Category of security event
            severity: Severity level of event
            user_id: User ID associated with event
        """
        # Skip low severity events
        if severity == SEVERITY_INFO:
            return

        # Always alert for critical events
        if severity == SEVERITY_CRITICAL:
            self.create_alert(
                alert_type=f"critical_{event_type}",
                severity=SEVERITY_CRITICAL,
                description=f"Critical security event: {event_type}",
                details={"category": category, "user_id": user_id}
            )
            return

        # Check for patterns in recent events
        now = datetime.now()
        one_hour_ago = now - timedelta(hours=1)

        # Filter recent events by time
        recent_events = [e for e in self.recent_events_cache
                        if e['timestamp'] > one_hour_ago]

        # Count events by type
        event_counts = Counter([e['event_type'] for e in recent_events])

        # Count events by user
        if user_id:
            user_events = [e for e in recent_events if e['user_id'] == user_id]
            user_event_counts = Counter([e['event_type'] for e in user_events])

            # Check for multiple failed attempts by the same user
            if event_type.startswith("failed_") and user_event_counts[event_type] >= self.alert_thresholds.get("failed_login_attempts", 5):
                self.create_alert(
                    alert_type="multiple_failures",
                    severity=SEVERITY_HIGH,
                    description=f"Multiple {event_type} events detected for user {user_id}",
                    details={"count": user_event_counts[event_type], "user_id": user_id}
                )

        # Check for high frequency of the same event type
        if event_counts[event_type] >= 10:
            self.create_alert(
                alert_type="high_frequency_event",
                severity=SEVERITY_MEDIUM,
                description=f"High frequency of {event_type} events detected",
                details={"count": event_counts[event_type]}
            )

    def create_alert(self, alert_type: str, severity: str, description: str,
                    details: Optional[Dict[str, Any]] = None) -> int:
        """
        Create a security alert.

        Args:
            alert_type: Type of alert
            severity: Severity level of alert
            description: Human-readable description of alert
            details: Additional details as a dictionary

        Returns:
            int: ID of the created alert
        """
        try:
            # Check cooldown for this alert type
            current_time = time.time()
            cooldown = self.alert_cooldowns.get(severity, 300)  # Default 5 minutes

            if alert_type in self.last_alert_times:
                time_since_last = current_time - self.last_alert_times[alert_type]
                if time_since_last < cooldown:
                    logger.info(f"Alert {alert_type} suppressed due to cooldown")
                    return -1

            # Update last alert time
            self.last_alert_times[alert_type] = current_time

            cursor = self.database.conn.cursor()

            # Convert details dictionary to JSON string
            details_json = json.dumps(details) if details else None

            # Insert alert into database
            cursor.execute(
                """INSERT INTO security_alerts
                   (timestamp, alert_type, severity, description, details)
                   VALUES (?, ?, ?, ?, ?)""",
                (datetime.now().isoformat(), alert_type, severity, description, details_json)
            )
            self.database.conn.commit()
            alert_id = cursor.lastrowid

            # Notify admins for high severity alerts
            if severity in [SEVERITY_HIGH, SEVERITY_CRITICAL]:
                asyncio.create_task(self.notify_admins(alert_id, alert_type, severity, description, details))

            logger.warning(f"Security alert created: {alert_type} - {description}")

            return alert_id
        except Exception as e:
            logger.error(f"Error creating security alert: {e}")
            return -1

    async def notify_admins(self, alert_id: int, alert_type: str, severity: str,
                           description: str, details: Optional[Dict[str, Any]] = None) -> None:
        """
        Notify admin users about a security alert.

        Args:
            alert_id: ID of the alert
            alert_type: Type of alert
            severity: Severity level of alert
            description: Human-readable description of alert
            details: Additional details as a dictionary
        """
        if not self.bot or not self.admin_ids:
            logger.warning("Cannot notify admins: bot instance or admin IDs not available")
            return

        # Format alert message
        severity_emoji = {
            SEVERITY_INFO: "ℹ️",
            SEVERITY_LOW: "🔵",
            SEVERITY_MEDIUM: "🟡",
            SEVERITY_HIGH: "🔴",
            SEVERITY_CRITICAL: "⚠️"
        }

        emoji = severity_emoji.get(severity, "🔔")

        message = (
            f"{emoji} *SECURITY ALERT* {emoji}\n\n"
            f"*Type:* {alert_type}\n"
            f"*Severity:* {severity}\n"
            f"*Time:* {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
            f"*Description:* {description}\n"
        )

        if details:
            message += "\n*Details:*\n"
            for key, value in details.items():
                if value is not None:
                    message += f"- {key}: {value}\n"

        # Add action buttons
        keyboard = [
            [
                InlineKeyboardButton("View Details", callback_data=f"security_alert_{alert_id}"),
                InlineKeyboardButton("Mark Resolved", callback_data=f"resolve_alert_{alert_id}")
            ],
            [
                InlineKeyboardButton("Security Dashboard", callback_data="security_dashboard")
            ]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)

        # Send to all admins
        notified_admins = []
        for admin_id in self.admin_ids:
            try:
                await self.bot.send_message(
                    chat_id=admin_id,
                    text=message,
                    parse_mode="Markdown",
                    reply_markup=reply_markup
                )
                notified_admins.append(str(admin_id))
                logger.info(f"Security alert sent to admin {admin_id}")
            except TelegramError as e:
                logger.error(f"Error sending security alert to admin {admin_id}: {e}")

        # Update alert with notified admins
        if notified_admins:
            try:
                cursor = self.database.conn.cursor()
                cursor.execute(
                    "UPDATE security_alerts SET notified_admins = ? WHERE id = ?",
                    (",".join(notified_admins), alert_id)
                )
                self.database.conn.commit()
            except Exception as e:
                logger.error(f"Error updating notified admins for alert {alert_id}: {e}")

    def get_active_alerts(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Get active (unresolved) security alerts.

        Args:
            limit: Maximum number of alerts to return

        Returns:
            List of alert dictionaries
        """
        try:
            cursor = self.database.conn.cursor()
            cursor.execute(
                """SELECT id, timestamp, alert_type, severity, description, details, notified_admins
                   FROM security_alerts
                   WHERE resolved = 0
                   ORDER BY
                       CASE severity
                           WHEN 'CRITICAL' THEN 1
                           WHEN 'HIGH' THEN 2
                           WHEN 'MEDIUM' THEN 3
                           WHEN 'LOW' THEN 4
                           WHEN 'INFO' THEN 5
                       END,
                       timestamp DESC
                   LIMIT ?""",
                (limit,)
            )

            alerts = cursor.fetchall()
            result = []

            for alert in alerts:
                alert_dict = dict(alert)

                # Parse details JSON
                if alert_dict.get("details"):
                    try:
                        alert_dict["details"] = json.loads(alert_dict["details"])
                    except json.JSONDecodeError:
                        pass

                result.append(alert_dict)

            return result
        except Exception as e:
            logger.error(f"Error getting active alerts: {e}")
            return []

    def resolve_alert(self, alert_id: int, resolution_notes: Optional[str] = None) -> bool:
        """
        Mark a security alert as resolved.

        Args:
            alert_id: ID of the alert to resolve
            resolution_notes: Notes about the resolution

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            cursor = self.database.conn.cursor()
            cursor.execute(
                """UPDATE security_alerts SET
                   resolved = 1,
                   resolution_time = ?
                   WHERE id = ?""",
                (datetime.now().isoformat(), alert_id)
            )
            self.database.conn.commit()

            logger.info(f"Security alert {alert_id} marked as resolved")
            return True
        except Exception as e:
            logger.error(f"Error resolving security alert {alert_id}: {e}")
            return False

    def resolve_security_event(self, event_id: int, resolution_notes: Optional[str] = None) -> bool:
        """
        Mark a security event as resolved.

        Args:
            event_id: ID of the event to resolve
            resolution_notes: Notes about the resolution

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            cursor = self.database.conn.cursor()
            cursor.execute(
                """UPDATE security_events SET
                   resolved = 1,
                   resolution_notes = ?,
                   resolution_time = ?
                   WHERE id = ?""",
                (resolution_notes, datetime.now().isoformat(), event_id)
            )
            self.database.conn.commit()

            logger.info(f"Security event {event_id} marked as resolved")
            return True
        except Exception as e:
            logger.error(f"Error resolving security event {event_id}: {e}")
            return False

    def log_metric(self, metric_name: str, metric_value: float,
                  details: Optional[Dict[str, Any]] = None) -> int:
        """
        Log a security metric.

        Args:
            metric_name: Name of the metric
            metric_value: Value of the metric
            details: Additional details as a dictionary

        Returns:
            int: ID of the logged metric
        """
        try:
            cursor = self.database.conn.cursor()

            # Convert details dictionary to JSON string
            details_json = json.dumps(details) if details else None

            # Insert metric into database
            cursor.execute(
                """INSERT INTO security_metrics
                   (timestamp, metric_name, metric_value, details)
                   VALUES (?, ?, ?, ?)""",
                (datetime.now().isoformat(), metric_name, metric_value, details_json)
            )
            self.database.conn.commit()
            metric_id = cursor.lastrowid

            logger.debug(f"Security metric logged: {metric_name} = {metric_value}")

            return metric_id
        except Exception as e:
            logger.error(f"Error logging security metric: {e}")
            return -1
