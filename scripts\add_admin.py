"""
<PERSON><PERSON><PERSON> to add an admin user to the VoicePal bot.

This script adds a user ID to the admin list in the .env file and
gives the user admin privileges in the database.

Usage:
    python add_admin.py <user_id>
"""

import os
import sys
import sqlite3
from dotenv import load_dotenv, set_key

# Load environment variables
load_dotenv()

def add_admin_to_env(user_id):
    """Add user ID to ADMIN_USER_IDS in .env file."""
    # Get current admin IDs
    admin_ids = os.getenv("ADMIN_USER_IDS", "")
    
    # Add new admin ID if not already present
    admin_id_list = [id.strip() for id in admin_ids.split(",") if id.strip()]
    if str(user_id) not in admin_id_list:
        admin_id_list.append(str(user_id))
    
    # Update .env file
    new_admin_ids = ",".join(admin_id_list)
    set_key(".env", "ADMIN_USER_IDS", new_admin_ids)
    
    print(f"Added user ID {user_id} to ADMIN_USER_IDS in .env file")

def add_credits_to_user(user_id, credits=1000):
    """Add credits to user in database."""
    # Connect to database
    db_file = os.getenv("DATABASE_FILE", "voicepal.db")
    conn = sqlite3.connect(db_file)
    cursor = conn.cursor()
    
    # Check if user exists
    cursor.execute("SELECT * FROM users WHERE user_id = ?", (user_id,))
    user = cursor.fetchone()
    
    if user:
        # Get current credits
        cursor.execute("SELECT credits FROM users WHERE user_id = ?", (user_id,))
        current_credits = cursor.fetchone()[0]
        
        # Update credits
        new_credits = current_credits + credits
        cursor.execute("UPDATE users SET credits = ? WHERE user_id = ?", (new_credits, user_id))
        
        # Add transaction record
        cursor.execute(
            "INSERT INTO transactions (user_id, amount, credits, transaction_id, status) VALUES (?, ?, ?, ?, ?)",
            (user_id, 0, credits, "admin_adjustment", "completed")
        )
        
        conn.commit()
        print(f"Added {credits} credits to user {user_id}. New balance: {new_credits}")
    else:
        print(f"User {user_id} not found in database. Please start the bot first to create the user.")
    
    conn.close()

def main():
    """Main function."""
    if len(sys.argv) < 2:
        print("Usage: python add_admin.py <user_id>")
        return
    
    try:
        user_id = int(sys.argv[1])
        
        # Add user to admin list in .env file
        add_admin_to_env(user_id)
        
        # Add credits to user in database
        add_credits_to_user(user_id)
        
        print(f"\nUser {user_id} is now an admin with 1000 additional credits.")
        print("You can now use the /addcredits command in the bot.")
        print("Example: /addcredits <user_id> <credits>")
        
    except ValueError:
        print("Error: User ID must be a number")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
