#!/usr/bin/env python
"""
Test script for Telegram Stars payment system.

This script tests the Telegram Stars payment system to verify that:
1. The payment system is properly initialized
2. The payment process can be started
3. The pre-checkout process works
4. The successful payment process works

Usage:
    python test_telegram_stars_payment_fixed.py
"""

import os
import sys
import logging
import asyncio
import uuid
from pathlib import Path
from unittest.mock import MagicMock, AsyncMock, patch

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath("."))

# Import the necessary components
from bot.database.database import Database
from bot.database.extensions.payment import extend_database_for_payment
from bot.payment.telegram_stars_payment import TelegramStarsPayment
from bot.config_manager import ConfigManager

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class MockUpdate:
    """Mock Telegram Update object."""

    def __init__(self, user_id=12345, username="test_user", first_name="Test", last_name="User"):
        # Create a mock user
        self.mock_user = MagicMock()
        self.mock_user.id = user_id
        self.mock_user.username = username
        self.mock_user.first_name = first_name
        self.mock_user.last_name = last_name

        # Set effective_user
        self.effective_user = self.mock_user

        # Set callback_query
        self.callback_query = MagicMock()
        self.callback_query.from_user = self.mock_user
        self.callback_query.message = MagicMock()
        self.callback_query.message.reply_text = AsyncMock()

        # Set message
        self.message = MagicMock()
        self.message.reply_text = AsyncMock()

        # Set pre_checkout_query
        self.pre_checkout_query = MagicMock()
        self.pre_checkout_query.id = "test_pre_checkout_id"
        self.pre_checkout_query.from_user = self.mock_user
        self.pre_checkout_query.invoice_payload = f"credits-small-{uuid.uuid4()}"
        self.pre_checkout_query.answer = AsyncMock()

class MockContext:
    """Mock Telegram Context object."""

    def __init__(self):
        self.bot = MagicMock()
        self.bot.send_invoice = AsyncMock()

class PaymentTester:
    """Class to test the Telegram Stars payment system."""

    def __init__(self):
        """Initialize the payment tester."""
        # Create a temporary database
        self.db_path = Path("test_payment.db")
        if self.db_path.exists():
            self.db_path.unlink()

        # Initialize database
        self.database = Database(self.db_path)

        # Extend database with payment methods
        extend_database_for_payment(self.database)

        # Create a mock config manager
        self.config_manager = MagicMock()
        self.config_manager.get_telegram_config.return_value = {
            "payment_provider_token": "test_provider_token"
        }

        # Initialize payment system
        self.payment_system = TelegramStarsPayment(
            database=self.database,
            config_manager=self.config_manager,
            provider_token="test_provider_token"
        )

        # Create mock update and context
        self.update = MockUpdate()
        self.context = MockContext()

        logger.info("Payment tester initialized")

    async def cleanup(self):
        """Clean up resources."""
        try:
            # Close database connection
            self.database.conn.close()

            # Delete database file
            if self.db_path.exists():
                self.db_path.unlink()

            logger.info("Cleaned up resources")
        except Exception as e:
            logger.error(f"Error cleaning up: {e}")

    async def test_initialization(self):
        """Test that the payment system is properly initialized."""
        logger.info("=== Testing Payment System Initialization ===")

        # Check that the payment system has the correct provider token
        assert self.payment_system.provider_token == "test_provider_token", \
            f"Expected provider token 'test_provider_token', got '{self.payment_system.provider_token}'"

        # Check that the credit packages are defined
        assert len(self.payment_system.CREDIT_PACKAGES) > 0, \
            "Expected credit packages to be defined"

        # Check that the small package is defined
        assert "small" in self.payment_system.CREDIT_PACKAGES, \
            "Expected 'small' package to be defined"

        logger.info("Payment system initialization test passed!")
        return True

    async def test_start_payment(self):
        """Test starting the payment process."""
        logger.info("\n=== Testing Start Payment Process ===")

        # Start payment for small package
        await self.payment_system.start_payment(self.update, self.context, "small")

        # Check that send_invoice was called
        self.context.bot.send_invoice.assert_called_once()

        # Check that the invoice was added to the database
        self.database.cursor.execute(
            "SELECT * FROM invoices WHERE user_id = ? AND package_id = ?",
            (self.update.callback_query.from_user.id, "small")
        )
        invoices = self.database.cursor.fetchall()

        assert len(invoices) > 0, "Expected invoice to be added to database"
        assert invoices[0]["status"] == "pending", \
            f"Expected invoice status to be 'pending', got '{invoices[0]['status']}'"

        logger.info("Start payment test passed!")
        return True

    async def test_pre_checkout(self):
        """Test the pre-checkout process."""
        logger.info("\n=== Testing Pre-Checkout Process ===")

        # First start a payment to create an invoice
        await self.payment_system.start_payment(self.update, self.context, "small")

        # Get the invoice payload
        self.database.cursor.execute(
            "SELECT * FROM invoices WHERE user_id = ? AND package_id = ?",
            (self.update.callback_query.from_user.id, "small")
        )
        invoices = self.database.cursor.fetchall()

        assert len(invoices) > 0, "Expected invoice to be added to database"

        # Set the pre-checkout query payload to match the invoice
        self.update.pre_checkout_query.invoice_payload = invoices[0]["payload"]

        # Process pre-checkout
        await self.payment_system.precheckout_callback(self.update, self.context)

        # Check that pre_checkout_query.answer was called with ok=True
        self.update.pre_checkout_query.answer.assert_called_once_with(ok=True)

        # Check that the invoice status was updated
        self.database.cursor.execute(
            "SELECT * FROM invoices WHERE payload = ?",
            (invoices[0]["payload"],)
        )
        updated_invoices = self.database.cursor.fetchall()

        assert len(updated_invoices) > 0, "Expected invoice to still exist in database"
        assert updated_invoices[0]["status"] == "pre_checkout", \
            f"Expected invoice status to be 'pre_checkout', got '{updated_invoices[0]['status']}'"

        logger.info("Pre-checkout test passed!")
        return True

    async def test_successful_payment(self):
        """Test the successful payment process."""
        logger.info("\n=== Testing Successful Payment Process ===")

        # First start a payment to create an invoice
        await self.payment_system.start_payment(self.update, self.context, "small")

        # Get the invoice payload
        self.database.cursor.execute(
            "SELECT * FROM invoices WHERE user_id = ? AND package_id = ?",
            (self.update.callback_query.from_user.id, "small")
        )
        invoices = self.database.cursor.fetchall()

        assert len(invoices) > 0, "Expected invoice to be added to database"

        # Create a mock successful payment
        mock_payment = MagicMock()
        mock_payment.invoice_payload = invoices[0]["payload"]
        mock_payment.total_amount = self.payment_system.CREDIT_PACKAGES["small"]["price"]
        mock_payment.telegram_payment_charge_id = f"test_charge_{uuid.uuid4()}"

        # Add the mock payment to the update
        self.update.message.successful_payment = mock_payment

        # Process successful payment
        await self.payment_system.successful_payment_callback(self.update, self.context)

        # Check that the invoice status was updated
        self.database.cursor.execute(
            "SELECT * FROM invoices WHERE payload = ?",
            (invoices[0]["payload"],)
        )
        updated_invoices = self.database.cursor.fetchall()

        assert len(updated_invoices) > 0, "Expected invoice to still exist in database"
        assert updated_invoices[0]["status"] == "completed", \
            f"Expected invoice status to be 'completed', got '{updated_invoices[0]['status']}'"

        # Check that the transaction was added
        self.database.cursor.execute(
            "SELECT * FROM transactions WHERE user_id = ? AND transaction_id = ?",
            (self.update.callback_query.from_user.id, mock_payment.telegram_payment_charge_id)
        )
        transactions = self.database.cursor.fetchall()

        assert len(transactions) > 0, "Expected transaction to be added to database"
        assert transactions[0]["status"] == "completed", \
            f"Expected transaction status to be 'completed', got '{transactions[0]['status']}'"

        # Check that the credits were added to the user
        user = self.database.get_user(self.update.callback_query.from_user.id)
        assert user is not None, "Expected user to exist in database"
        assert user["credits"] == self.payment_system.CREDIT_PACKAGES["small"]["credits"], \
            f"Expected user to have {self.payment_system.CREDIT_PACKAGES['small']['credits']} credits, " \
            f"got {user['credits']}"

        logger.info("Successful payment test passed!")
        return True

    async def run_all_tests(self):
        """Run all payment tests."""
        try:
            await self.test_initialization()
            await self.test_start_payment()
            await self.test_pre_checkout()
            await self.test_successful_payment()

            logger.info("\n=== All Payment Tests Passed! ===")
            return True
        except AssertionError as e:
            logger.error(f"Test failed: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False
        finally:
            await self.cleanup()

async def main():
    """Run the payment tests."""
    tester = PaymentTester()
    await tester.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
