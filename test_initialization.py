"""
Test script to verify bot initialization.
"""

import os
import sys
import logging
import asyncio
import traceback
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.DEBUG
)
logger = logging.getLogger(__name__)

async def test_initialization():
    """Test initializing the bot."""
    logger.info("Testing bot initialization...")

    try:
        # Import the bot class
        logger.info("Importing VoicePalBot class...")
        from bot.main import VoicePalBot
        logger.info("VoicePalBot class imported successfully")

        # Create the bot instance
        logger.info("Creating bot instance...")
        bot = VoicePalBot()
        logger.info("Bot instance created successfully")

        # Register initialization steps
        logger.info("Checking initialization steps...")
        init_status = bot.init_manager.get_initialization_status()
        logger.info(f"Initialization steps: {len(init_status['initialization_steps'])}")
        for step in init_status['initialization_steps']:
            logger.info(f"Step: {step['name']}, executed: {step['executed']}, success: {step['success']}")

        # Initialize the components
        logger.info("Initializing bot components...")
        try:
            success = await bot._initialize_components()

            if success:
                logger.info("Bot components initialized successfully")
            else:
                logger.error("Failed to initialize bot components")
                return False
        except Exception as init_error:
            logger.error(f"Error during component initialization: {init_error}")
            logger.error(traceback.format_exc())
            return False

        logger.info("Bot initialization test completed successfully")
        return True
    except Exception as e:
        logger.error(f"Error testing bot initialization: {e}")
        logger.error(traceback.format_exc())
        return False

async def main():
    """Main function."""
    # Load environment variables
    load_dotenv()

    success = await test_initialization()
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
