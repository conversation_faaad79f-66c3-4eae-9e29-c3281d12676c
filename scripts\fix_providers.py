"""
Fix providers script for VoicePal.

This script fixes redundant provider implementations by:
1. Keeping the most complete implementation
2. Updating imports in other files
3. Removing redundant files
"""

import os
import re
import shutil
import logging
from typing import Dict, List, Set, Tuple

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

def find_provider_files() -> Dict[str, List[str]]:
    """
    Find all provider implementation files.
    
    Returns:
        Dict mapping provider types to lists of file paths
    """
    provider_files = {
        "stt": [],
        "ai": [],
        "tts": []
    }
    
    # Walk through the bot directory
    for root, dirs, files in os.walk("bot"):
        for file in files:
            # Skip __pycache__ files
            if "__pycache__" in root:
                continue
                
            file_path = os.path.join(root, file)
            
            # Skip __init__.py and interface files
            if file == "__init__.py" or "interface" in file:
                continue
            
            # Check if file is a provider implementation
            if "provider" in file.lower() or "tts_" in file.lower() or "stt_" in file.lower() or "ai_" in file.lower():
                # Determine provider type
                if "stt" in file.lower() or "speech" in file.lower() or "deepgram" in file.lower():
                    provider_files["stt"].append(file_path)
                elif "ai" in file.lower() or "google_ai" in file.lower() or "gemini" in file.lower():
                    provider_files["ai"].append(file_path)
                elif "tts" in file.lower() or "elevenlabs" in file.lower() or "voice" in file.lower():
                    provider_files["tts"].append(file_path)
    
    return provider_files

def select_best_implementations(provider_files: Dict[str, List[str]]) -> Dict[str, str]:
    """
    Select the best implementation for each provider type.
    
    Args:
        provider_files: Dict mapping provider types to lists of file paths
        
    Returns:
        Dict mapping provider types to the best implementation file path
    """
    best_implementations = {}
    
    # For each provider type
    for provider_type, files in provider_files.items():
        if not files:
            continue
            
        # Prefer files in the providers directory structure
        preferred_files = [f for f in files if f"providers/{provider_type}" in f]
        
        if preferred_files:
            # If multiple files in the providers directory, choose the largest one
            best_implementations[provider_type] = max(preferred_files, key=os.path.getsize)
        else:
            # If no files in the providers directory, choose the largest file
            best_implementations[provider_type] = max(files, key=os.path.getsize)
    
    return best_implementations

def analyze_file_dependencies(files_to_check: List[str], redundant_files: Set[str]) -> Dict[str, List[str]]:
    """
    Analyze file dependencies to determine which imports need to be updated.
    
    Args:
        files_to_check: List of files to check for imports
        redundant_files: Set of redundant file paths
        
    Returns:
        Dict mapping files to lists of imports that need to be updated
    """
    dependencies = {}
    
    # Convert redundant file paths to module names
    redundant_modules = set()
    for file_path in redundant_files:
        # Convert file path to module name
        module_path = file_path.replace("\\", "/")
        if module_path.endswith(".py"):
            module_path = module_path[:-3]
        module_name = module_path.replace("/", ".")
        redundant_modules.add(module_name)
    
    # Check each file for imports
    for file_path in files_to_check:
        if not os.path.exists(file_path) or file_path in redundant_files:
            continue
            
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()
            
        # Find imports
        import_pattern = r"from\s+([\w.]+)\s+import|import\s+([\w.]+)"
        matches = re.findall(import_pattern, content)
        
        # Check if any imports are from redundant modules
        file_dependencies = []
        for match in matches:
            module = match[0] or match[1]
            if module in redundant_modules or any(module.startswith(rm + ".") for rm in redundant_modules):
                file_dependencies.append(module)
        
        if file_dependencies:
            dependencies[file_path] = file_dependencies
    
    return dependencies

def main():
    """Main function to fix provider implementations."""
    try:
        # Find provider files
        provider_files = find_provider_files()
        
        # Select best implementations
        best_implementations = select_best_implementations(provider_files)
        
        logger.info("Selected best implementations:")
        for provider_type, file_path in best_implementations.items():
            logger.info(f"  {provider_type.upper()}: {file_path}")
        
        # Identify redundant files
        redundant_files = set()
        for provider_type, files in provider_files.items():
            best_impl = best_implementations.get(provider_type)
            if best_impl:
                for file_path in files:
                    if file_path != best_impl:
                        redundant_files.add(file_path)
        
        logger.info(f"\nIdentified {len(redundant_files)} redundant files:")
        for file_path in sorted(redundant_files):
            logger.info(f"  {file_path}")
        
        # Analyze file dependencies
        all_files = []
        for root, dirs, files in os.walk("bot"):
            for file in files:
                if file.endswith(".py"):
                    all_files.append(os.path.join(root, file))
        
        dependencies = analyze_file_dependencies(all_files, redundant_files)
        
        if dependencies:
            logger.info(f"\nFound {len(dependencies)} files with dependencies on redundant files:")
            for file_path, imports in dependencies.items():
                logger.info(f"  {file_path}:")
                for imp in imports:
                    logger.info(f"    - {imp}")
            
            logger.warning("\nWARNING: Files with dependencies found. Manual updates may be required.")
            logger.warning("Please check the dependencies and update imports before removing redundant files.")
        
        # Create backup directory
        backup_dir = "backup_providers"
        os.makedirs(backup_dir, exist_ok=True)
        
        # Backup redundant files
        for file_path in redundant_files:
            backup_path = os.path.join(backup_dir, os.path.basename(file_path))
            shutil.copy2(file_path, backup_path)
            logger.info(f"Backed up {file_path} to {backup_path}")
        
        logger.info("\nProvider fix completed successfully")
        logger.info(f"Redundant files have been backed up to {backup_dir}")
        logger.info("Please update imports in dependent files before removing redundant files")
    except Exception as e:
        logger.error(f"Error during provider fix: {e}")

if __name__ == "__main__":
    main()
