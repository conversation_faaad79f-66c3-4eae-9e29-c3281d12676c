#!/usr/bin/env python3
"""
Run the VoicePal bot with a fixed database.
"""

import os
import sys
import logging
from datetime import datetime

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

def create_fixed_database():
    """
    Create a fixed database without the login_count column.
    """
    # Create a new database file
    db_path = "voicepal_fixed.db"

    # Remove existing database if it exists
    if os.path.exists(db_path):
        os.remove(db_path)
        logger.info(f"Removed existing database: {db_path}")

    # Import the schema module
    sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
    from bot.database.schema import initialize_database

    # Initialize the database
    conn = initialize_database(db_path)
    conn.close()

    logger.info(f"Created new database: {db_path}")

    return db_path

def run_bot_with_fixed_database():
    """
    Run the bot with a fixed database.
    """
    # Create a fixed database
    db_path = create_fixed_database()

    # Set environment variable to use the fixed database
    os.environ["DATABASE_FILE"] = db_path

    # Run the bot
    logger.info(f"Running bot with fixed database: {db_path}")

    # Import the run_bot module
    from run_bot import main
    import asyncio

    # Run the bot
    asyncio.run(main())

if __name__ == "__main__":
    run_bot_with_fixed_database()
