"""
Performance optimization module for VoicePal.

This module provides performance optimization functionality.
"""

from bot.performance.database_pool import DatabaseConnectionPool
from bot.performance.redis_optimizer import RedisOptimizer
from bot.performance.async_processor import AsyncProcessor
from bot.performance.cache_manager import CacheManager
from bot.performance.performance_monitor import PerformanceMonitor
from bot.performance.optimization_manager import OptimizationManager

__all__ = [
    'DatabaseConnectionPool',
    'RedisOptimizer',
    'AsyncProcessor',
    'CacheManager',
    'PerformanceMonitor',
    'OptimizationManager'
]
