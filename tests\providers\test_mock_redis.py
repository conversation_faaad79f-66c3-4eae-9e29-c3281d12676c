"""
Simple test using mocks to verify that pytest and mocking are working.
"""

import pytest
from unittest.mock import MagicMock

class MockRedisProvider:
    """Mock Redis provider for testing."""
    
    def __init__(self, config=None):
        """Initialize the Redis provider."""
        self.config = config or {}
        self._redis = None
        self._ratelimit = None
        self._initialized = False
        
    def is_available(self):
        """Check if Redis is available."""
        return self._initialized
        
    def initialize(self):
        """Initialize Redis."""
        self._initialized = True
        return True
        
    def set(self, key, value, ttl=None):
        """Set a value in Redis."""
        if not self._initialized:
            return False
        return True
        
    def get(self, key):
        """Get a value from Redis."""
        if not self._initialized:
            return None
        return "test_value"


@pytest.fixture
def redis_provider():
    """Create a MockRedisProvider instance."""
    provider = MockRedisProvider({"url": "redis://localhost:6379"})
    provider.initialize()
    return provider


class TestMockRedisProvider:
    """Test cases for the MockRedisProvider class."""
    
    def test_initialization(self, redis_provider):
        """Test initialization."""
        assert redis_provider.is_available() is True
        
    def test_set_get(self, redis_provider):
        """Test set and get methods."""
        assert redis_provider.set("test_key", "test_value") is True
        assert redis_provider.get("test_key") == "test_value"
        
    def test_with_mock(self):
        """Test with a mock."""
        mock_redis = MagicMock()
        mock_redis.set.return_value = True
        mock_redis.get.return_value = "mocked_value"
        
        provider = MockRedisProvider()
        provider._redis = mock_redis
        provider._initialized = True
        
        assert provider.is_available() is True
        assert provider.get("any_key") == "test_value"  # Using the mock implementation
