"""
Message analyzer utility for VoicePal.

This module provides functions to analyze user messages and determine their complexity,
intent, and other characteristics to help the bot adapt its responses.
"""

import re
import logging
from typing import Dict, Any, List, Optional

# Set up logging
logger = logging.getLogger(__name__)

# Common greetings and simple phrases
GREETINGS = [
    "hi", "hello", "hey", "howdy", "hola", "greetings", "yo",
    "good morning", "good afternoon", "good evening", "good day",
    "what's up", "sup", "how are you", "how's it going", "how are things",
    "how do you do", "nice to meet you", "pleased to meet you"
]

SIMPLE_QUESTIONS = [
    "who are you", "what are you", "what's your name", "what can you do",
    "how old are you", "where are you from", "what time is it",
    "what day is it", "what's the weather", "how's the weather",
    "what's new", "what's happening", "what's going on"
]

# Short responses that should maintain context
SHORT_RESPONSES = [
    "lol", "haha", "yes", "no", "ok", "okay", "sure", "thanks",
    "thank you", "cool", "nice", "great", "awesome", "wow",
    "hmm", "huh", "what", "why", "how", "when", "where", "who",
    "?", "!", "..."
]

def analyze_message_complexity(message: str) -> Dict[str, Any]:
    """
    Analyze the complexity of a user message.

    Args:
        message: The user message to analyze

    Returns:
        Dict containing analysis results:
            - complexity: 'simple', 'medium', or 'complex'
            - word_count: Number of words in the message
            - is_greeting: Whether the message is a greeting
            - is_question: Whether the message is a question
            - estimated_response_length: Suggested response length in words
    """
    # Clean and normalize the message
    cleaned_message = message.lower().strip()

    # Count words
    words = re.findall(r'\b\w+\b', cleaned_message)
    word_count = len(words)

    # Check if it's a greeting
    is_greeting = any(greeting in cleaned_message for greeting in GREETINGS)

    # Check if it's a simple question
    is_simple_question = any(question in cleaned_message for question in SIMPLE_QUESTIONS)

    # Check if it's a short response that should maintain context
    is_short_response = cleaned_message in SHORT_RESPONSES or len(cleaned_message) <= 5

    # Check if it's a question
    is_question = '?' in message or cleaned_message.startswith(('what', 'who', 'where', 'when', 'why', 'how', 'can', 'could', 'would', 'should', 'is', 'are', 'do', 'does', 'did'))

    # Count sentences
    sentences = re.split(r'[.!?]+', cleaned_message)
    sentence_count = sum(1 for s in sentences if s.strip())

    # Determine complexity
    if word_count <= 5 or is_greeting or is_simple_question:
        complexity = 'simple'
        estimated_response_length = 20  # Very short response
    elif word_count <= 15 and sentence_count <= 2:
        complexity = 'medium'
        estimated_response_length = 50  # Medium response
    else:
        complexity = 'complex'
        estimated_response_length = 100  # Longer response

    # Adjust estimated response length based on question type
    if is_question and not is_simple_question and not is_greeting:
        estimated_response_length = max(50, estimated_response_length)  # Questions usually need more detailed answers

    # Log the analysis
    logger.debug(f"Message analysis: complexity={complexity}, word_count={word_count}, "
                f"is_greeting={is_greeting}, is_question={is_question}, "
                f"is_short_response={is_short_response}, "
                f"estimated_response_length={estimated_response_length}")

    return {
        "complexity": complexity,
        "word_count": word_count,
        "is_greeting": is_greeting,
        "is_question": is_question,
        "is_short_response": is_short_response,
        "estimated_response_length": estimated_response_length
    }

def get_response_guidance(analysis: Dict[str, Any]) -> str:
    """
    Generate response guidance based on message analysis.

    Args:
        analysis: The message analysis from analyze_message_complexity

    Returns:
        String containing guidance for the AI response
    """
    complexity = analysis["complexity"]
    is_greeting = analysis["is_greeting"]
    is_question = analysis["is_question"]
    is_short_response = analysis.get("is_short_response", False)
    estimated_length = analysis["estimated_response_length"]
    word_count = analysis["word_count"]

    # For very short messages, match the brevity
    if is_short_response:
        return f"Keep your response very brief (under 15 words). This is a very short message of only {word_count} words. Match the user's brevity."

    if is_greeting:
        return f"Keep your response very brief (under {estimated_length} words). This is a simple greeting."

    if complexity == "simple":
        return f"Keep your response concise (around {estimated_length} words). This is a simple message."

    if complexity == "medium":
        if is_question:
            return f"Provide a clear, direct answer (around {estimated_length} words). This is a straightforward question."
        return f"Keep your response moderate in length (around {estimated_length} words). This is a medium-complexity message."

    # Complex message
    return f"You can provide a more detailed response (around {estimated_length} words). This is a complex message that may require elaboration."
