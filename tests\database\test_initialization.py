"""
Tests for database initialization.

This module tests the database initialization process.
"""

import os
import unittest
import tempfile
import sqlite3
from pathlib import Path

from bot.database.database_manager import DatabaseManager
from bot.database.schema_manager import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CURRENT_SCHEMA_VERSION
from bot.database.core.connection import DatabaseConnection
from bot.database.core.exceptions import (
    DatabaseError,
    DatabaseConnectionError,
    DatabaseSchemaError
)

class TestDatabaseInitialization(unittest.TestCase):
    """Test case for database initialization."""
    
    def setUp(self):
        """Set up test environment."""
        # Create temporary directory for database
        self.temp_dir = tempfile.TemporaryDirectory()
        self.db_path = Path(self.temp_dir.name) / "test.db"
    
    def tearDown(self):
        """Clean up test environment."""
        # Remove temporary directory
        self.temp_dir.cleanup()
    
    def test_database_initialization(self):
        """Test database initialization."""
        # Initialize database
        db = DatabaseManager(self.db_path)
        
        # Check if database file exists
        self.assertTrue(self.db_path.exists())
        
        # Check if schema is valid
        self.assertTrue(db.validate_schema())
        
        # Check if current version is correct
        self.assertEqual(db.get_current_version(), CURRENT_SCHEMA_VERSION)
        
        # Close database
        db.close()
    
    def test_database_initialization_with_existing_file(self):
        """Test database initialization with existing file."""
        # Create empty database file
        conn = sqlite3.connect(self.db_path)
        conn.close()
        
        # Initialize database
        db = DatabaseManager(self.db_path)
        
        # Check if schema is valid
        self.assertTrue(db.validate_schema())
        
        # Close database
        db.close()
    
    def test_database_initialization_with_non_existent_directory(self):
        """Test database initialization with non-existent directory."""
        # Create path with non-existent directory
        db_path = Path(self.temp_dir.name) / "non_existent" / "test.db"
        
        # Initialize database
        db = DatabaseManager(db_path)
        
        # Check if database file exists
        self.assertTrue(db_path.exists())
        
        # Check if schema is valid
        self.assertTrue(db.validate_schema())
        
        # Close database
        db.close()
    
    def test_database_initialization_with_invalid_path(self):
        """Test database initialization with invalid path."""
        # Create invalid path
        db_path = Path("/non_existent_root_directory/test.db")
        
        # Try to initialize database
        with self.assertRaises(Exception):
            DatabaseManager(db_path)
    
    def test_database_reinitialization(self):
        """Test database reinitialization."""
        # Initialize database
        db1 = DatabaseManager(self.db_path)
        
        # Create a table
        db1.execute("CREATE TABLE test (id INTEGER PRIMARY KEY, name TEXT)")
        
        # Insert a row
        db1.execute("INSERT INTO test (id, name) VALUES (?, ?)", (1, "Test"))
        
        # Close database
        db1.close()
        
        # Reinitialize database
        db2 = DatabaseManager(self.db_path)
        
        # Check if table exists
        cursor = db2.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='test'")
        self.assertIsNotNone(cursor.fetchone())
        
        # Check if row exists
        cursor = db2.execute("SELECT * FROM test WHERE id = ?", (1,))
        row = cursor.fetchone()
        self.assertIsNotNone(row)
        self.assertEqual(row["id"], 1)
        self.assertEqual(row["name"], "Test")
        
        # Check if schema is valid
        self.assertTrue(db2.validate_schema())
        
        # Close database
        db2.close()
    
    def test_database_connection_error(self):
        """Test database connection error."""
        # Create database
        db = DatabaseManager(self.db_path)
        
        # Close database
        db.close()
        
        # Try to execute query
        with self.assertRaises(DatabaseConnectionError):
            db.execute("SELECT 1")
    
    def test_database_schema_error(self):
        """Test database schema error."""
        # Create database
        db = DatabaseManager(self.db_path)
        
        # Drop a table
        db.execute("DROP TABLE users")
        
        # Check if schema is invalid
        self.assertFalse(db.validate_schema())
        
        # Close database
        db.close()
    
    def test_database_context_manager(self):
        """Test database context manager."""
        # Use database as context manager
        with DatabaseManager(self.db_path) as db:
            # Check if schema is valid
            self.assertTrue(db.validate_schema())
            
            # Create a table
            db.execute("CREATE TABLE test (id INTEGER PRIMARY KEY, name TEXT)")
            
            # Insert a row
            db.execute("INSERT INTO test (id, name) VALUES (?, ?)", (1, "Test"))
            
            # Check if row exists
            cursor = db.execute("SELECT * FROM test WHERE id = ?", (1,))
            row = cursor.fetchone()
            self.assertIsNotNone(row)
            self.assertEqual(row["id"], 1)
            self.assertEqual(row["name"], "Test")
        
        # Check if database is closed
        with self.assertRaises(Exception):
            db.execute("SELECT 1")

if __name__ == "__main__":
    unittest.main()
