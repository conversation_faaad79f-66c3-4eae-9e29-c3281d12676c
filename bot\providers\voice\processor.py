"""
Voice processing module for VoicePal.

This module handles all voice processing operations including:
- Speech-to-text conversion using Deepgram API
- Text-to-speech conversion using multiple providers (Deepgram, Google TTS)
- Voice personality matching

The module is designed to be easily replaceable with other voice processing
solutions if needed.
"""

import os
import tempfile
import logging
import json
from typing import Optional, Dict, Any, Union, List, Tuple
import asyncio
from pathlib import Path

# Set up logging first
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# Define a generic exception class for compatibility
class DeepgramError(Exception):
    """Generic exception for Deepgram errors"""
    pass

# Import Deepgram for speech-to-text
# We'll determine the SDK version at runtime when initializing the client
# This is more reliable than trying to detect it at import time
try:
    # Try importing DeepgramClient (v3.x/v4.x)
    from deepgram import DeepgramClient, PrerecordedOptions, SpeakOptionsClient
    logger.info("Deepgram SDK v3.x/v4.x available")
except ImportError:
    # If that fails, try importing Deepgram (v2.x)
    try:
        from deepgram import DeepgramClient, PrerecordedOptions, SpeakOptions
        logger.info("Deepgram SDK v2.x available")
    except ImportError:
        logger.error("Failed to import any Deepgram SDK")

# Import TTS providers
from bot.providers.tts.deepgram_provider import DeepgramTTSProvider
from bot.providers.tts.google_provider import GoogleTTSProvider

# Try to import ElevenLabsProvider, but make it optional
try:
    from bot.providers.tts.elevenlabs_provider import ElevenLabsProvider
    ELEVENLABS_AVAILABLE = True
except ImportError:
    logger.warning("ElevenLabsProvider not available. elevenlabs package may have compatibility issues.")
    ELEVENLABS_AVAILABLE = False

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class VoiceProcessor:
    """Voice processing handler for VoicePal."""

    def __init__(self, deepgram_api_key: Optional[str] = None,
                default_language: str = 'en',
                default_voice: str = 'en',
                tts_provider: str = 'google',
                tts_provider_options: Optional[Dict[str, Any]] = None,
                personality: str = 'friendly'):
        """
        Initialize the voice processor.

        Args:
            deepgram_api_key: Deepgram API key (defaults to DEEPGRAM_API_KEY env var)
            default_language: Default language for speech recognition
            default_voice: Default voice for speech synthesis
            tts_provider: TTS provider type (google, huggingface)
            tts_provider_options: Additional options for the TTS provider
            personality: AI personality type (friendly, witty, calm, motivational, thoughtful)
        """
        # Initialize Deepgram client
        self.deepgram_api_key = deepgram_api_key or os.getenv("DEEPGRAM_API_KEY")
        if not self.deepgram_api_key:
            logger.warning("No Deepgram API key provided. Speech-to-text functionality will be limited.")

        # Set up Deepgram client based on SDK version
        if self.deepgram_api_key:
            try:
                # First try to initialize with v2.x SDK for better compatibility
                try:
                    from deepgram import DeepgramClient, PrerecordedOptions, SpeakOptions
                    self.deepgram = Deepgram(self.deepgram_api_key)
                    logger.info("Initialized Deepgram client with v2.x SDK")
                except (ImportError, Exception) as e:
                    logger.warning(f"Failed to initialize with v2.x SDK: {e}")

                    # Fall back to v3.x/v4.x SDK if v2.x fails
                    try:
                        from deepgram import DeepgramClient, PrerecordedOptions, SpeakOptionsClient
                        self.deepgram = DeepgramClient(self.deepgram_api_key)
                        logger.info("Initialized Deepgram client with v3.x/v4.x SDK")
                    except Exception as e2:
                        logger.error(f"Failed to initialize with v3.x/v4.x SDK: {e2}")
                        self.deepgram = None
            except Exception as e:
                logger.error(f"Failed to initialize any Deepgram client: {e}")
                self.deepgram = None
        else:
            logger.warning("No Deepgram API key provided, STT functionality will be limited")
            self.deepgram = None

        # Set default language and voice
        self.default_language = default_language
        self.default_voice = default_voice

        # Set personality
        self.personality = personality

        # Initialize TTS provider
        self.tts_provider_type = "deepgram"  # Always set Deepgram as the primary provider type
        self.tts_provider_options = tts_provider_options or {}
        self.requested_provider_type = tts_provider.lower()  # Store the requested provider for reference
        self.fallback_tts_providers = {}

        # Initialize Deepgram TTS provider as the primary provider
        try:
            # Get API key from options or environment
            api_key = self.tts_provider_options.get("api_key") or os.getenv("DEEPGRAM_API_KEY")

            # Create Deepgram TTS provider
            self.tts_provider = DeepgramTTSProvider(
                api_key=api_key,
                voice_id=self.tts_provider_options.get("voice_id", "aura-thalia-en"),
                model_id="aura-2"  # Use the latest model
            )

            logger.info(f"Initialized Deepgram TTS provider as primary provider with voice {self.tts_provider_options.get('voice_id', 'aura-thalia-en')}")
        except Exception as e:
            logger.error(f"Error initializing Deepgram TTS provider: {e}")

            # If Deepgram fails, try to initialize fallback providers
            if self._initialize_fallback_tts_provider():
                logger.info(f"Successfully initialized fallback TTS provider: {self.tts_provider_type}")
            else:
                logger.error("Failed to initialize any TTS provider")
                # Use Google TTS as a last resort since it doesn't require an API key
                try:
                    self.tts_provider = GoogleTTSProvider()
                    self.tts_provider_type = "google"
                    logger.info("Initialized Google TTS provider as a last resort")
                except Exception as e2:
                    logger.error(f"Failed to initialize Google TTS provider: {e2}")
                    raise RuntimeError("Failed to initialize any TTS provider")

        # Initialize fallback providers if requested provider is different from Deepgram
        if self.requested_provider_type != "deepgram":
            self._initialize_fallback_tts_provider(self.requested_provider_type)

        # Get available TTS languages
        self.available_tts_langs = self.tts_provider.get_available_languages()
        logger.info(f"Initialized voice processor with {len(self.available_tts_langs)} available TTS languages using {tts_provider} provider")

    async def transcribe_audio(self, audio_file_path: Union[str, Path],
                              language: Optional[str] = None,
                              model: str = "nova-3",
                              confidence_threshold: float = 0.0) -> Dict[str, Any]:
        """
        Transcribe audio file to text using Deepgram.

        Args:
            audio_file_path: Path to the audio file
            language: Language code (e.g., 'en-US', 'fr', etc.)
            model: Deepgram model to use
            confidence_threshold: Minimum confidence score (0.0 to 1.0) to accept transcript

        Returns:
            Dict containing transcript and metadata
        """
        # Use language detection to get both transcript and language
        transcript, detected_language = await self.transcribe_audio_with_language_detection(
            audio_file_path=audio_file_path,
            model=model,
            confidence_threshold=confidence_threshold
        )

        return {
            "transcript": transcript or "",
            "language": detected_language
        }

    async def transcribe_audio_with_language_detection(self, audio_file_path: Union[str, Path],
                                                     model: str = "nova-3",
                                                     confidence_threshold: float = 0.0) -> Tuple[Optional[str], str]:
        """
        Transcribe audio file to text using Deepgram with automatic language detection.

        Args:
            audio_file_path: Path to the audio file
            model: Deepgram model to use
            confidence_threshold: Minimum confidence score (0.0 to 1.0) to accept transcript

        Returns:
            Tuple of (transcribed_text, detected_language_code) or (None, default_language) if transcription failed
        """
        try:
            # Check if Deepgram client is initialized
            if not self.deepgram:
                logger.warning("Deepgram client not initialized. Cannot transcribe audio.")
                return None, self.default_language

            # Read audio file
            with open(audio_file_path, "rb") as audio:
                buffer_data = audio.read()

            # Determine mimetype based on file extension
            file_ext = Path(audio_file_path).suffix.lower()
            mimetype = "audio/ogg"  # Default for Telegram voice messages
            if file_ext == ".mp3":
                mimetype = "audio/mpeg"
            elif file_ext == ".wav":
                mimetype = "audio/wav"
            elif file_ext == ".m4a":
                mimetype = "audio/mp4"
            elif file_ext == ".ogg" or file_ext == ".oga":
                mimetype = "audio/ogg"

            logger.info(f"Using mimetype: {mimetype} for file with extension: {file_ext}")

            # Set up transcription options with language detection
            options = {
                "model": model,
                "smart_format": True,
                "detect_language": True,  # Enable language detection
                "punctuate": True,
            }

            # Transcribe audio using the most compatible approach
            try:
                # Try the standard v2.x SDK approach first
                # This works with both v2.x and most v3.x SDKs
                try:
                    source = {"buffer": buffer_data, "mimetype": mimetype}
                    response = await self.deepgram.transcription.prerecorded(source, options)
                except Exception as e:
                    logger.error(f"Error with standard transcription: {e}")
                    import traceback
                    logger.error(traceback.format_exc())

                    # Try alternative approach if the first one fails
                    try:
                        logger.info("Trying alternative transcription approach...")
                        # Some versions might have a different API structure
                        if hasattr(self.deepgram, 'listen') and hasattr(self.deepgram.listen, 'prerecorded'):
                            # Import necessary classes if available
                            try:
                                from deepgram import BufferSource
                                source = BufferSource(buffer=buffer_data, mimetype=mimetype)
                                result = await self.deepgram.listen.prerecorded.v("1").transcribe(source, options)
                                response = result.results
                                logger.info("Alternative transcription approach succeeded")
                            except ImportError:
                                logger.error("Failed to import BufferSource for alternative approach")
                                response = {}
                        else:
                            logger.error("Alternative transcription approach not available")
                            response = {}
                    except Exception as e2:
                        logger.error(f"Error with alternative transcription approach: {e2}")
                        response = {}
            except Exception as e:
                logger.error(f"Error transcribing audio: {e}")
                import traceback
                logger.error(traceback.format_exc())
                response = {}

            # Check if response is valid
            if response and "results" in response and "channels" in response["results"]:
                # Extract transcript
                transcript = response["results"]["channels"][0]["alternatives"][0]["transcript"]
                confidence = response["results"]["channels"][0]["alternatives"][0]["confidence"]

                # Extract detected language
                detected_language = "en"  # Default fallback
                if "detected_language" in response["results"]:
                    detected_language = response["results"]["detected_language"]
                    logger.info(f"Deepgram detected language: {detected_language}")

                # Check confidence threshold
                if confidence < confidence_threshold:
                    logger.warning(f"Transcription confidence {confidence} below threshold {confidence_threshold}")
                    return None, self.default_language

                logger.info(f"Successfully transcribed audio file: {audio_file_path} (confidence: {confidence:.2f}, language: {detected_language})")
                return transcript, detected_language
            else:
                logger.warning(f"Invalid response format from Deepgram: {response}")
                return None, self.default_language

        except DeepgramError as e:
            logger.error(f"Deepgram error transcribing audio: {e}")
            return None, self.default_language
        except Exception as e:
            logger.error(f"Error transcribing audio: {e}")
            return None, self.default_language

    async def transcribe_audio_with_params(self, audio_file_path: Union[str, Path],
                                         model: str = "nova-3",
                                         language: Optional[str] = None,
                                         smart_format: bool = True,
                                         diarize: bool = False,
                                         detect_topics: bool = False,
                                         punctuate: bool = True,
                                         profanity_filter: bool = False,
                                         redact: Optional[List[str]] = None,
                                         confidence_threshold: float = 0.0,
                                         **additional_params) -> Optional[str]:
        """
        Transcribe audio file to text using Deepgram with custom parameters.

        Args:
            audio_file_path: Path to the audio file
            model: Deepgram model to use
            language: Language code (e.g., 'en-US', 'fr', etc.)
            smart_format: Whether to apply smart formatting (numbers, punctuation)
            diarize: Whether to identify different speakers
            detect_topics: Whether to detect topics in the audio
            punctuate: Whether to add punctuation
            profanity_filter: Whether to filter profanity
            redact: List of terms to redact from transcript
            confidence_threshold: Minimum confidence score (0.0 to 1.0) to accept transcript
            additional_params: Additional parameters to pass to Deepgram API

        Returns:
            Transcribed text or None if transcription failed or below confidence threshold
        """
        if not self.deepgram:
            logger.error("Cannot transcribe audio: No Deepgram client available")
            return None

        try:
            # Convert Path to string if needed
            if isinstance(audio_file_path, Path):
                audio_file_path = str(audio_file_path)

            # Read audio file
            with open(audio_file_path, "rb") as audio:
                buffer_data = audio.read()

            # Determine mimetype based on file extension
            file_ext = os.path.splitext(audio_file_path)[1].lower()
            mimetype = None
            if file_ext == '.wav':
                mimetype = 'audio/wav'
            elif file_ext == '.mp3':
                mimetype = 'audio/mpeg'
            elif file_ext == '.ogg':
                mimetype = 'audio/ogg'
            elif file_ext == '.flac':
                mimetype = 'audio/flac'
            elif file_ext == '.m4a':
                mimetype = 'audio/mp4'
            else:
                # Default to wav if unknown
                mimetype = 'audio/wav'

            logger.info(f"Using mimetype: {mimetype} for file with extension: {file_ext}")

            # Set up transcription options
            options = {
                "model": model,
                "smart_format": smart_format,
                "language": language or self.default_language,
                "punctuate": punctuate,
            }

            # Add optional parameters
            if diarize:
                options["diarize"] = True

            if detect_topics:
                options["detect_topics"] = True

            if profanity_filter:
                options["profanity_filter"] = True

            if redact:
                options["redact"] = redact

            # Add any additional parameters
            options.update(additional_params)

            # Transcribe audio using the most compatible approach
            try:
                # Try the standard v2.x SDK approach first
                # This works with both v2.x and most v3.x SDKs
                try:
                    source = {"buffer": buffer_data, "mimetype": mimetype}
                    response = await self.deepgram.transcription.prerecorded(source, options)
                except Exception as e:
                    logger.error(f"Error with standard transcription: {e}")
                    import traceback
                    logger.error(traceback.format_exc())

                    # Try alternative approach if the first one fails
                    try:
                        logger.info("Trying alternative transcription approach...")
                        # Some versions might have a different API structure
                        if hasattr(self.deepgram, 'listen') and hasattr(self.deepgram.listen, 'prerecorded'):
                            # Import necessary classes if available
                            try:
                                from deepgram import BufferSource
                                source = BufferSource(buffer=buffer_data, mimetype=mimetype)
                                result = await self.deepgram.listen.prerecorded.v("1").transcribe(source, options)
                                response = result.results
                                logger.info("Alternative transcription approach succeeded")
                            except ImportError:
                                logger.error("Failed to import BufferSource for alternative approach")
                                response = {}
                        else:
                            logger.error("Alternative transcription approach not available")
                            response = {}
                    except Exception as e2:
                        logger.error(f"Error with alternative transcription approach: {e2}")
                        response = {}
            except Exception as e:
                logger.error(f"Error transcribing audio: {e}")
                import traceback
                logger.error(traceback.format_exc())
                response = {}

            # Extract transcript and confidence
            if response and "results" in response and "channels" in response["results"]:
                alternatives = response["results"]["channels"][0]["alternatives"]

                if not alternatives:
                    logger.warning(f"No alternatives found for audio file: {audio_file_path}")
                    return None

                transcript = alternatives[0]["transcript"]
                confidence = alternatives[0].get("confidence", 0.0)

                if not transcript:
                    logger.warning(f"Empty transcript for audio file: {audio_file_path}")
                    return None

                # Check confidence threshold
                if confidence < confidence_threshold:
                    logger.warning(f"Transcript confidence ({confidence:.2f}) below threshold ({confidence_threshold:.2f})")
                    return None

                # Extract additional information if available
                if diarize and "speakers" in response["results"]:
                    speakers = response["results"]["speakers"]
                    logger.info(f"Detected {speakers} speakers in audio")

                if detect_topics and "topics" in response["results"]:
                    topics = response["results"]["topics"]
                    logger.info(f"Detected topics: {topics}")

                logger.info(f"Successfully transcribed audio file: {audio_file_path} (confidence: {confidence:.2f})")
                return transcript
            else:
                logger.warning(f"Invalid response format from Deepgram: {response}")
                return None

        except DeepgramError as e:
            logger.error(f"Deepgram error transcribing audio: {e}")
            return None
        except Exception as e:
            logger.error(f"Error transcribing audio: {e}")
            return None

    def generate_voice_response(self, text: str,
                               language: Optional[str] = None,
                               voice: Optional[str] = None,
                               personality: Optional[str] = None,
                               **kwargs) -> Optional[str]:
        """
        Generate voice response from text using the configured TTS provider.

        Args:
            text: Text to convert to speech
            language: Language code (e.g., 'en', 'fr', 'it', etc.)
            voice: Voice identifier for providers that support multiple voices
            personality: AI personality type (friendly, witty, calm, motivational, thoughtful)
            **kwargs: Additional provider-specific parameters

        Returns:
            Path to the generated audio file or None if generation failed
        """
        # Log the request
        logger.info(f"Generating voice response with provider: {self.tts_provider_type}")
        logger.info(f"Text length: {len(text)} characters")
        logger.info(f"Language: {language}, Voice: {voice}, Personality: {personality}")

        # Clean text of any emoji descriptions or characters that might cause issues
        import re

        # Save original text for debugging
        original_text = text

        # Remove emoji descriptions like (smile), (laughing), etc.
        text = re.sub(r'\([a-zA-Z]+\)', '', text)
        # Remove other problematic patterns that might be verbalized
        text = re.sub(r':[a-zA-Z_]+:', '', text)  # Remove :emoji_name: format
        # Remove any other special characters that might cause issues
        text = re.sub(r'[^\w\s.,?!;:\-\'"\(\)]+', '', text)

        # Ensure text is not empty after cleaning
        if not text or text.strip() == "":
            logger.error("Empty text after cleaning. Cannot generate voice response.")
            logger.error(f"Original text: {original_text}")
            return None

        # Log the cleaned text
        logger.info(f"Cleaned text: {text[:100]}..." if len(text) > 100 else f"Cleaned text: {text}")

        try:
            # Use default language if not specified
            lang = language or self.default_voice

            # Map Deepgram language codes to standard language codes if needed
            # Deepgram sometimes returns language codes like 'en-US' or 'italian'
            lang_map = {
                'en-US': 'en',
                'en-GB': 'en',
                'it-IT': 'it',
                'italian': 'it',
                'fr-FR': 'fr',
                'french': 'fr',
                'es-ES': 'es',
                'spanish': 'es',
                'de-DE': 'de',
                'german': 'de',
                'pt-PT': 'pt',
                'portuguese': 'pt',
                'ru-RU': 'ru',
                'russian': 'ru',
                'zh-CN': 'zh',
                'chinese': 'zh',
                'ja-JP': 'ja',
                'japanese': 'ja',
                'ko-KR': 'ko',
                'korean': 'ko',
                'ar-AR': 'ar',
                'arabic': 'ar',
                'hi-IN': 'hi',
                'hindi': 'hi',
                'pl-PL': 'pl',
                'polish': 'pl'
            }

            # Normalize language code
            if lang in lang_map:
                lang = lang_map[lang]

            # Log the language being used
            logger.info(f"Generating voice response in language: {lang}")

            # Use default voice if not specified
            # Don't use self.default_voice as it might be a language code
            voice_id = voice or "Bella"

            # Use current personality if not specified
            personality_type = personality or self.personality

            # Adjust TTS parameters based on personality
            tts_params = self._get_personality_tts_params(personality_type)

            # Merge with provided kwargs
            if kwargs:
                tts_params.update(kwargs)

            # Log detailed information for debugging
            logger.info(f"TTS Provider: {self.tts_provider.__class__.__name__}")
            logger.info(f"TTS Provider Type: {self.tts_provider_type}")
            logger.info(f"Language: {lang}")
            logger.info(f"Voice ID: {voice_id}")
            logger.info(f"Personality: {personality_type}")
            logger.info(f"TTS Params: {tts_params}")

            # Try to generate speech using the configured provider (which should be Deepgram)
            try:
                # Generate speech using the provider
                # Note: We're not passing personality explicitly since it's already in tts_params
                result = self.tts_provider.generate_speech(
                    text=text,
                    language=lang,
                    voice=voice_id,
                    **tts_params
                )

                # Log the result
                if result:
                    logger.info("Successfully generated voice response with Deepgram")
                    return result
                else:
                    logger.error("Failed to generate voice response with Deepgram")
            except Exception as e:
                logger.error("Error generating voice response with Deepgram: %s", str(e))
                import traceback
                logger.error(traceback.format_exc())
                result = None

            # If Deepgram failed, try fallback providers
            if not result:
                # Try each fallback provider in order
                for provider_name, provider in self.fallback_tts_providers.items():
                    try:
                        logger.info("Trying fallback provider: %s", provider_name)
                        fallback_result = provider.generate_speech(
                            text=text,
                            language=lang,
                            voice=voice_id,
                            **tts_params
                        )

                        if fallback_result:
                            logger.info("Successfully generated voice response with fallback provider: %s", provider_name)
                            return fallback_result
                    except Exception as e:
                        logger.error("Error with fallback provider %s: %s", provider_name, str(e))

                # If all fallbacks failed, try Google TTS as a last resort
                logger.info("Trying Google TTS as final fallback")
                try:
                    from gtts import gTTS
                    import tempfile

                    # Create a temporary file
                    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".mp3")
                    temp_file_path = temp_file.name
                    temp_file.close()

                    # Generate speech with Google TTS
                    tts = gTTS(text=text, lang=lang[:2], slow=False)  # Use first 2 chars of lang code
                    tts.save(temp_file_path)

                    logger.info(f"Successfully generated voice response with Google TTS fallback: {temp_file_path}")
                    return temp_file_path
                except Exception as e:
                    logger.error(f"Error generating voice response with Google TTS fallback: {e}")
                    import traceback
                    logger.error(traceback.format_exc())
                    return None

            return result

        except Exception as e:
            logger.error(f"Error generating voice response: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return None

    def _get_personality_tts_params(self, personality: str) -> Dict[str, Any]:
        """
        Get TTS parameters based on personality.

        Args:
            personality: AI personality type

        Returns:
            Dictionary of TTS parameters
        """
        # Default parameters for Google TTS
        params = {
            "slow": False,
            "personality": personality  # Pass personality to all providers
        }

        # Adjust parameters based on personality for Google TTS
        if personality == "friendly":
            # Friendly: Medium speed, slightly higher pitch
            params["slow"] = False
        elif personality == "witty":
            # Witty: Faster speed, slightly higher pitch
            params["slow"] = False
        elif personality == "calm":
            # Calm: Slower speed, lower pitch
            params["slow"] = True
        elif personality == "motivational":
            # Motivational: Medium speed, higher energy
            params["slow"] = False
        elif personality == "thoughtful":
            # Thoughtful: Slower speed, thoughtful pauses
            params["slow"] = True

        # Add Dia-specific formatting based on personality
        # This will be used by the Dia TTS provider
        if self.tts_provider_type == "dia":
            # Dia uses speaker tags to influence the speech style
            if personality == "friendly":
                params["speaker_style"] = "friendly"
            elif personality == "witty":
                params["speaker_style"] = "witty"
            elif personality == "calm":
                params["speaker_style"] = "calm"
            elif personality == "motivational":
                params["speaker_style"] = "motivational"
            elif personality == "thoughtful":
                params["speaker_style"] = "thoughtful"

        # Add ElevenLabs-specific formatting based on personality
        # ElevenLabs can use the personality parameter directly
        if self.tts_provider_type == "elevenlabs":
            # Pass the personality directly to ElevenLabs
            params["personality"] = personality

        # Add Deepgram-specific formatting based on personality
        # Deepgram can use the personality parameter directly
        if self.tts_provider_type == "deepgram":
            # Pass the personality directly to Deepgram
            params["personality"] = personality

        return params

    def set_personality(self, personality: str) -> None:
        """
        Set the AI personality for voice generation.

        Args:
            personality: AI personality type
        """
        self.personality = personality
        logger.info("Set voice personality to %s", personality)

    def _initialize_fallback_tts_provider(self, provider_type: Optional[str] = None) -> bool:
        """
        Initialize a fallback TTS provider.

        Args:
            provider_type: Provider type to initialize (e.g., 'elevenlabs', 'google')
                           If None, will try all available providers in order of preference

        Returns:
            bool: True if a fallback provider was successfully initialized, False otherwise
        """
        # If no specific provider type is requested, try all available providers
        if provider_type is None:
            # Try providers in order of preference (after Deepgram)
            for provider in ["elevenlabs", "google"]:
                if self._initialize_fallback_tts_provider(provider):
                    return True
            return False

        try:
            # Create provider based on type
            if provider_type.lower() == "elevenlabs" and ELEVENLABS_AVAILABLE:
                try:
                    provider = ElevenLabsProvider(
                        api_key=self.tts_provider_options.get("api_key"),
                        voice_id=self.tts_provider_options.get("voice_id", "21m00Tcm4TlvDq8ikWAM"),  # Rachel voice ID
                        model_id=self.tts_provider_options.get("model_id", "eleven_multilingual_v2")
                    )
                    # Store as fallback provider
                    self.fallback_tts_providers["elevenlabs"] = provider
                    logger.info(f"Initialized ElevenLabs as fallback TTS provider")
                    return True
                except Exception as e:
                    logger.error(f"Failed to initialize ElevenLabs fallback provider: {e}")
                    return False
            elif provider_type.lower() == "google":
                try:
                    provider = GoogleTTSProvider()
                    # Store as fallback provider
                    self.fallback_tts_providers["google"] = provider
                    logger.info(f"Initialized Google TTS as fallback provider")
                    return True
                except Exception as e:
                    logger.error(f"Failed to initialize Google TTS fallback provider: {e}")
                    return False
            else:
                logger.warning(f"Unsupported fallback provider type: {provider_type}")
                return False
        except Exception as e:
            logger.error(f"Error initializing fallback TTS provider {provider_type}: {e}")
            return False

    def set_tts_provider(self, provider_type: str, **provider_options) -> bool:
        """
        Change the TTS provider.

        Args:
            provider_type: Type of provider (elevenlabs, deepgram, google)
            **provider_options: Additional provider-specific parameters

        Returns:
            bool: True if provider was changed successfully, False otherwise
        """
        try:
            # Always prioritize Deepgram regardless of requested provider
            if provider_type.lower() in ["deepgram", "elevenlabs", "google"]:
                # If provider is not Deepgram, log that we're using Deepgram anyway
                if provider_type.lower() != "deepgram":
                    logger.info("Requested %s but using Deepgram as primary provider", provider_type)

                    # Store the requested provider as fallback if available
                    if provider_type.lower() == "elevenlabs" and ELEVENLABS_AVAILABLE:
                        try:
                            fallback = ElevenLabsProvider(
                                api_key=provider_options.get("api_key"),
                                voice_id=provider_options.get("voice_id", "21m00Tcm4TlvDq8ikWAM"),
                                model_id=provider_options.get("model_id", "eleven_multilingual_v2")
                            )
                            self.fallback_tts_providers["elevenlabs"] = fallback
                            logger.info("Stored ElevenLabs as fallback provider")
                        except Exception as e:
                            logger.warning("Failed to initialize ElevenLabs fallback: %s", str(e))

                    elif provider_type.lower() == "google":
                        try:
                            fallback = GoogleTTSProvider()
                            self.fallback_tts_providers["google"] = fallback
                            logger.info("Stored Google TTS as fallback provider")
                        except Exception as e:
                            logger.warning("Failed to initialize Google TTS fallback: %s", str(e))

                # Create Deepgram provider
                api_key = provider_options.get("api_key")
                voice_id = provider_options.get("voice_id", "aura-thalia-en")

                if not api_key:
                    logger.error("No API key provided for Deepgram")
                    return False

                new_provider = DeepgramTTSProvider(
                    api_key=api_key,
                    voice_id=voice_id,
                    model_id="aura-2"  # Use the latest model
                )

                # Update provider
                self.tts_provider = new_provider
                self.tts_provider_type = "deepgram"
                self.tts_provider_options = provider_options

                # Update available languages
                self.available_tts_langs = self.tts_provider.get_available_languages()

                logger.info("Set TTS provider to Deepgram with voice %s", voice_id)
                return True

            # If provider is not recognized, try Google TTS as fallback
            else:
                logger.warning("Unrecognized TTS provider: %s. Trying Google TTS fallback.", provider_type)
                try:
                    # Create Google TTS provider as fallback
                    new_provider = GoogleTTSProvider()

                    # Update provider
                    self.tts_provider = new_provider
                    self.tts_provider_type = "google"
                    self.tts_provider_options = {}

                    # Update available languages
                    self.available_tts_langs = self.tts_provider.get_available_languages()

                    logger.info("Set TTS provider to Google TTS as fallback")
                    return True
                except Exception as e:
                    logger.error("Failed to initialize Google TTS fallback: %s", str(e))
                    return False

        except Exception as e:
            logger.error("Error setting TTS provider: %s", str(e))
            return False

    def get_available_languages(self) -> Dict[str, str]:
        """
        Get available TTS languages from the current provider.

        Returns:
            Dictionary of language codes and names
        """
        return self.tts_provider.get_available_languages()

    def get_available_voices(self, language: Optional[str] = None) -> Dict[str, str]:
        """
        Get available voices from the current provider.

        Args:
            language: Language code to filter voices

        Returns:
            Dictionary of voice identifiers and names
        """
        return self.tts_provider.get_available_voices(language)

    async def stream_voice_response(self, text: str,
                                  language: Optional[str] = None,
                                  voice: Optional[str] = None,
                                  personality: Optional[str] = None,
                                  **kwargs) -> bool:
        """
        Stream voice response directly without saving to a file.

        Args:
            text: Text to convert to speech
            language: Language code
            voice: Voice identifier
            personality: AI personality type
            **kwargs: Additional parameters

        Returns:
            bool: True if streaming was successful, False otherwise
        """
        # Clean text of any emoji descriptions or characters that might cause issues
        import re
        # Remove emoji descriptions like (smile), (laughing), etc.
        text = re.sub(r'\([a-zA-Z]+\)', '', text)
        # Remove other problematic patterns that might be verbalized
        text = re.sub(r':[a-zA-Z_]+:', '', text)  # Remove :emoji_name: format

        # Ensure text is not empty after cleaning
        if not text or text.strip() == "":
            logger.error("Empty text after cleaning. Cannot generate voice response.")
            return False

        try:
            # Use default language if not specified
            lang = language or self.default_voice

            # Map Deepgram language codes to standard language codes if needed
            lang_map = {
                'en-US': 'en',
                'en-GB': 'en',
                'it-IT': 'it',
                'italian': 'it',
                'fr-FR': 'fr',
                'french': 'fr',
                'es-ES': 'es',
                'spanish': 'es',
                'de-DE': 'de',
                'german': 'de',
                'pt-PT': 'pt',
                'portuguese': 'pt',
                'ru-RU': 'ru',
                'russian': 'ru',
                'zh-CN': 'zh',
                'chinese': 'zh',
                'ja-JP': 'ja',
                'japanese': 'ja',
                'ko-KR': 'ko',
                'korean': 'ko',
                'ar-AR': 'ar',
                'arabic': 'ar',
                'hi-IN': 'hi',
                'hindi': 'hi',
                'pl-PL': 'pl',
                'polish': 'pl'
            }

            # Normalize language code
            if lang in lang_map:
                lang = lang_map[lang]

            # Use default voice if not specified
            voice_id = voice or "Bella"

            # Use current personality if not specified
            personality_type = personality or self.personality

            # Adjust TTS parameters based on personality
            tts_params = self._get_personality_tts_params(personality_type)

            # Merge with provided kwargs
            if kwargs:
                tts_params.update(kwargs)

            logger.info(f"Streaming voice response in language: {lang}")
            logger.info(f"TTS Provider: {self.tts_provider.__class__.__name__}")
            logger.info(f"Voice ID: {voice_id}")
            logger.info(f"Personality: {personality_type}")

            # Use the stream_speech method if available
            try:
                # Check if the provider supports streaming
                if hasattr(self.tts_provider, 'stream_speech'):
                    result = await self.tts_provider.stream_speech(
                        text=text,
                        language=lang,
                        voice=voice_id,
                        **tts_params
                    )

                    if result:
                        logger.info("Successfully streamed voice response")
                        return True
                    else:
                        logger.error("Failed to stream voice response")
                else:
                    logger.warning("TTS provider does not support streaming. Falling back to file-based TTS.")
                    # Fall back to file-based TTS
                    file_path = self.generate_voice_response(
                        text=text,
                        language=lang,
                        voice=voice_id,
                        personality=personality_type,
                        **kwargs
                    )

                    if file_path:
                        # Play the file using a subprocess
                        try:
                            import subprocess
                            subprocess.run(["ffplay", "-autoexit", "-nodisp", file_path],
                                          stdout=subprocess.DEVNULL,
                                          stderr=subprocess.DEVNULL)
                            return True
                        except Exception as e:
                            logger.error(f"Error playing audio file: {e}")
                            return False
                    return False
            except Exception as e:
                logger.error(f"Error streaming voice response: {e}")
                import traceback
                logger.error(traceback.format_exc())
                return False

            return False
        except Exception as e:
            logger.error(f"Error in stream_voice_response: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False

    def cleanup_temp_file(self, file_path: Optional[str]) -> None:
        """
        Clean up temporary file.

        Args:
            file_path: Path to the file to clean up
        """
        if file_path and os.path.exists(file_path):
            try:
                os.unlink(file_path)
                logger.debug("Cleaned up temporary file: %s", file_path)
            except Exception as e:
                logger.error("Error cleaning up temporary file %s: %s", file_path, str(e))

    async def close(self) -> None:
        """Close the voice processor and release resources."""
        # Currently, no specific cleanup needed